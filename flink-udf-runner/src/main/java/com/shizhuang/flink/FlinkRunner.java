package com.shizhuang.flink;

import org.apache.calcite.config.Lex;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlNodeList;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.validate.SqlConformance;
import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.Options;
import org.apache.commons.io.FileUtils;
import org.apache.flink.api.dag.Transformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.CoreOptions;
import org.apache.flink.core.fs.UnsupportedFileSystemSchemeException;
import org.apache.flink.sql.parser.validate.FlinkSqlConformance;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.graph.StreamGraph;
import org.apache.flink.table.api.*;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.table.api.bridge.java.internal.StreamTableEnvironmentImpl;
import org.apache.flink.table.api.config.TableConfigOptions;
import org.apache.flink.table.client.gateway.SqlExecutionException;
import org.apache.flink.table.operations.ModifyOperation;
import org.apache.flink.table.operations.Operation;
import org.apache.flink.table.operations.ddl.CreateViewOperation;
import org.apache.flink.table.planner.delegation.FlinkSqlParserFactories;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class FlinkRunner {
    private static final Logger LOG = LoggerFactory.getLogger(FlinkRunner.class);
    private StreamExecutionEnvironment streamExecutionEnvironment;
    private StreamTableEnvironmentImpl streamTableEnvironment;
    private SqlDialect currentSqlDialect = SqlDialect.DEFAULT;

    private StreamGraph streamGraph;

    private final List<ModifyOperation> modifyOperations = new ArrayList<>();

    public void initEnvironment(String mode) {
        Configuration configuration = new Configuration();
        if (mode.equalsIgnoreCase("local")) {
            configuration.set(CoreOptions.DEFAULT_PARALLELISM, 1);
            streamExecutionEnvironment = StreamExecutionEnvironment.createLocalEnvironmentWithWebUI(
                    configuration);
            streamExecutionEnvironment.enableCheckpointing(
                    60 * 1000,
                    CheckpointingMode.EXACTLY_ONCE);

        } else {
            streamExecutionEnvironment = StreamExecutionEnvironment.getExecutionEnvironment(
                    configuration);
        }

        EnvironmentSettings environmentSettings = EnvironmentSettings
                .newInstance()
                .inStreamingMode()
                .build();

        streamTableEnvironment = (StreamTableEnvironmentImpl) StreamTableEnvironment.create(
                streamExecutionEnvironment,
                environmentSettings);

        initTableConfig();
    }

    public void initTableConfig() {
        Configuration configuration = streamTableEnvironment.getConfig().getConfiguration();
        configuration.setString("table.exec.source.idle-timeout", "30 s");
        configuration.setString("table.exec.state.ttl", "36 hours");
        configuration.setString(TableConfigOptions.LOCAL_TIME_ZONE, "Asia/Shanghai");
//        configuration.setString("table.local-time-zone", "Asia/Tokyo");

        Map<String, String> tableConfMap = System.getenv();
        for (Map.Entry<String, String> entry : tableConfMap.entrySet()) {
            if (entry.getKey().toLowerCase().startsWith("table.exec")) {
                configuration.setString(entry.getKey(), entry.getValue());
                LOG.info("set table config {}={}", entry.getKey(), entry.getValue());
            }

        }
    }

    public void execute(String sqlContent) throws IOException {
        long startTime = System.currentTimeMillis();
        BufferedReader bufferedReader = new BufferedReader(new StringReader(sqlContent));
        StringBuilder statement = new StringBuilder();
        String line = null;
        SqlCommandParser.SqlCommandCall sqlCommandCall = null;
        while ((line = bufferedReader.readLine()) != null) {
            try {
                sqlCommandCall = SqlCommandParser.parse(line);
            } catch (SqlParserException e) {
                //Ignore SqlCommandParser Exception
                //去除注释内容
                if (!line.matches("^\\s*--[\\s\\S]*")) {
                    statement.append(System.lineSeparator()).append(line);
                }
                continue;
            }

            if (sqlCommandCall.command.equals(SqlCommandParser.SqlCommand.SET)) {
                String key = sqlCommandCall.operands[0];
                String value = sqlCommandCall.operands[1];
                if (key.equals("table.sql-dialect")) {
                    if (value.equalsIgnoreCase("hive")) {
                        executeSql(statement.toString(), this.currentSqlDialect);
                        statement = new StringBuilder();
                        this.currentSqlDialect = SqlDialect.HIVE;
                    } else if (value.equalsIgnoreCase("default")) {
                        executeSql(statement.toString(), this.currentSqlDialect);
                        statement = new StringBuilder();
                        this.currentSqlDialect = SqlDialect.DEFAULT;
                    } else {
                        throw new SqlExecutionException(
                                "table.sql-dialect : " + value + " not support yet");
                    }
                } else {
                    throw new SqlExecutionException("sql not support yet. " + line);
                }
            } else {
                statement.append(System.lineSeparator()).append(line);
            }

        }
        if (statement.length() > 0) {
            executeSql(statement.toString(), this.currentSqlDialect);
        }

        LOG.info("sql execute cost {} ms", System.currentTimeMillis() - startTime);

        // 编译 DAG
        try{
            long startTranslateTime = System.currentTimeMillis();
            List<Transformation<?>> transformations = streamTableEnvironment
                    .getPlanner()
                    .translate(modifyOperations);
            LOG.info("transformations translate cost {} ms", System.currentTimeMillis() - startTranslateTime);

            for (Transformation<?> transformation : transformations) {
                streamExecutionEnvironment.addOperator(transformation);
            }
        } catch (Exception exception) {
            LOG.error("translate modifyOperations cause exception", exception);
            throw new RuntimeException(exception);
        }

        //获取 StreamGraph
        this.streamGraph = streamExecutionEnvironment.getStreamGraph();
    }

    public void executeSql(String sqlContent, SqlDialect sqlDialect) {
        if (sqlContent.trim().length() > 0) {
            try {
                List<String> sqlPartList = splitSqlContext(sqlContent, sqlDialect);
                if (sqlDialect.equals(SqlDialect.DEFAULT)) {
                    executeFlinkSql(sqlPartList);
                } else {
                    throw new RuntimeException("SqlDialect " + sqlDialect + " not support yet");
                }
            } catch (SqlParseException sqlParseException) {
                throw new SqlParserException(sqlContent, sqlParseException);
            } catch (Exception e) {
                throw new SqlExecutionException(sqlContent, e);
            }
        }
    }

    public void executeFlinkSql(List<String> sqlPartList) {

        for (String sqlPart : sqlPartList) {
            List<Operation> operations = null;
            try {
                operations = streamTableEnvironment.getParser().parse(sqlPart);
            } catch (SqlParserException sqlParserException) {
                throw new RuntimeException(sqlPart, sqlParserException);
            }
            if (operations != null && operations.size() == 1) {
                Operation operation = operations.get(0);
                if (operation instanceof CreateViewOperation) {
                    CreateViewOperation createViewOperation = (CreateViewOperation) operation;
                    String query = createViewOperation.getCatalogView().getExpandedQuery();
                    String viewName = createViewOperation.getViewIdentifier().getObjectName();
                    Table table = streamTableEnvironment.sqlQuery(query);
                    streamTableEnvironment.createTemporaryView(viewName, table);
                } else if (operation instanceof ModifyOperation) {
                    ModifyOperation modifyOperation = (ModifyOperation) operation;
                    modifyOperations.add(modifyOperation);
                } else {
                    streamTableEnvironment.executeSql(sqlPart).print();
                }
            } else {
                throw new RuntimeException("sql not support yet. \n" + sqlPart);
            }
        }


    }

    public void deploy() throws Exception {

        //deploy
        streamExecutionEnvironment.execute(streamGraph);
    }

    public static void main(String[] args) throws Exception {

        LOG.info("FlinkSqlJobClient main args [{}] ", String.join(" ", args));
        Options options = FlinkSqlJobOptions.getFlinkSqlJobOptions();

        CommandLineParser commandLineParser = new DefaultParser();
        CommandLine cmd = commandLineParser.parse(options, args);

        FlinkRunner flinkRunner = new FlinkRunner();


        flinkRunner.initEnvironment(cmd.getOptionValue(FlinkSqlJobOptions.MODE.getOpt(), "kubernetes"));

        //执行用户的业务逻辑sql
        if (cmd.hasOption(FlinkSqlJobOptions.SQL_PATH.getOpt())) {
            String sqlPath = cmd.getOptionValue(FlinkSqlJobOptions.SQL_PATH.getOpt());
            String sqlContent = getResourceContent(sqlPath);
            LOG.info("sqlContent: \n" + sqlContent);
            flinkRunner.execute(sqlContent);
        } else {
            throw new RuntimeException(String.format(
                    "param %s or %s is required",
                   FlinkSqlJobOptions.SQL_PATH.getOpt(),
                    FlinkSqlJobOptions.JOB_CONTENT.getOpt()));
        }
        flinkRunner.deploy();

    }

    public static String getResourceContent(String resourcePath) {
        try {
            URI sqlPathURI = new URI(resourcePath);
            String scheme = sqlPathURI.getScheme();
            if (scheme == null || scheme.equals("file") || scheme.equals("local")) {
                return FileUtils.readFileToString(
                        new File(sqlPathURI.getPath()),
                        StandardCharsets.UTF_8);

            } else {
                throw new UnsupportedFileSystemSchemeException(resourcePath);
            }
        } catch (Throwable throwable) {
            throw new RuntimeException("获取资源失败 " + resourcePath, throwable);
        }

    }

    public static List<String> splitSqlContext(
            String sqlContent,
            SqlDialect sqlDialect) throws SqlParseException {
        SqlParser.Config parserConfig = getCurrentSqlParserConfig(sqlDialect);
        SqlParser sqlParser = SqlParser.create(sqlContent, parserConfig);
        SqlNodeList sqlNodeList = sqlParser.parseStmtList();
        List<String> sqlNodeInfoList = new ArrayList<>();
        for (int i = 0; i < sqlNodeList.size(); i++) {
            SqlNode sqlNode = sqlNodeList.get(i);
            sqlNodeInfoList.add(sqlNode.toString());
        }
        return sqlNodeInfoList;
    }

    public static SqlParser.Config getCurrentSqlParserConfig(SqlDialect sqlDialect) {
        SqlConformance conformance = getSqlConformance(sqlDialect);
        return SqlParser.config()
                .withParserFactory(FlinkSqlParserFactories.create(conformance))
                .withConformance(conformance)
                .withLex(Lex.JAVA)
                .withIdentifierMaxLength(256);
    }

    public static FlinkSqlConformance getSqlConformance(SqlDialect sqlDialect) {
        switch (sqlDialect) {
            case HIVE:
                return FlinkSqlConformance.HIVE;
            case DEFAULT:
                return FlinkSqlConformance.DEFAULT;
            default:
                throw new TableException("Unsupported SQL dialect: " + sqlDialect);
        }
    }
}
