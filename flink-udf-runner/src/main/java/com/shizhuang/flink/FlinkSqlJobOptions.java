package com.shizhuang.flink;

import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;

public class FlinkSqlJobOptions {

    public static final Option JOB_CONTENT = new Option("jobContent", "jobContent", true, "jobContent");

    public static final Option SQL_PATH = new Option("sqlPath", "sqlPath", true, "sqlPath");

    public static final Option MODE = new Option("mode", "mode", true, "mode");

    public static final Option DISABLE_OPERATOR_CHAINING = new Option("disableOperatorChaining", "disableOperatorChaining", true, "disableOperatorChaining");

    public static final Option FORM_SAVEPOINT = new Option("fromSavepoint", "fromSavepoint", true, "fromSavepoint");

    public static final Option ALLOW_NON_RESTORED_STATE = new Option("allowNonRestoredState", "allowNonRestoredState", true, "allowNonRestoredState");

    public static Options getFlinkSqlJobOptions() {
        Options options = new Options();
        options.addOption(SQL_PATH);
        options.addOption(JOB_CONTENT);
        options.addOption(MODE);
        options.addOption(DISABLE_OPERATOR_CHAINING);

        options.addOption(FORM_SAVEPOINT);
        options.addOption(ALLOW_NON_RESTORED_STATE);

        return options;
    }
}
