/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.shizhuang.flink;

import org.apache.flink.table.api.SqlParserException;
import org.apache.flink.table.client.gateway.SqlExecutionException;
import org.apache.flink.table.delegation.Parser;
import org.apache.flink.table.operations.*;
import org.apache.flink.table.operations.ddl.*;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/** Simple parser for determining the type of command and its parameters. */
public final class SqlCommandParser {

    private SqlCommandParser() {
        // private
    }




    public static SqlCommandCall parse(String stmt) {
        // normalize
        stmt = stmt.trim();
        // remove ';' at the end
        if (stmt.endsWith(";")) {
            stmt = stmt.substring(0, stmt.length() - 1).trim();
        }

        // parse statement via regex matching first
        Optional<SqlCommandCall> callOpt = parseByRegexMatching(stmt);
        if (callOpt.isPresent()) {
            return callOpt.get();
        } else {
            throw new SqlParserException("can not parse By Regex Matching." + stmt);
        }
    }


    private static Optional<SqlCommandCall> parseByRegexMatching(String stmt) {
        // parse statement via regex matching
        for (SqlCommand cmd : SqlCommand.values()) {
            if (cmd.hasRegexPattern()) {
                final Matcher matcher = cmd.pattern.matcher(stmt);
                if (matcher.matches()) {
                    final String[] groups = new String[matcher.groupCount()];
                    for (int i = 0; i < groups.length; i++) {
                        groups[i] = matcher.group(i + 1);
                    }
                    return cmd.operandConverter
                            .apply(groups)
                            .map(
                                    (operands) -> {
                                        String[] newOperands = operands;
                                        if (cmd == SqlCommand.EXPLAIN) {
                                            // convert `explain xx` to `explain plan for xx`
                                            // which can execute through executeSql method
                                            newOperands =
                                                    new String[] {
                                                        "EXPLAIN PLAN FOR "
                                                                + operands[0]
                                                                + " "
                                                                + operands[1]
                                                    };
                                        }
                                        return new SqlCommandCall(cmd, newOperands);
                                    });
                }
            }
        }
        return Optional.empty();
    }

    // --------------------------------------------------------------------------------------------

    private static final Function<String[], Optional<String[]>> NO_OPERANDS =
            (operands) -> Optional.of(new String[0]);

    private static final Function<String[], Optional<String[]>> SINGLE_OPERAND =
            (operands) -> Optional.of(new String[] {operands[0]});

    private static final int DEFAULT_PATTERN_FLAGS = Pattern.CASE_INSENSITIVE | Pattern.DOTALL;

    /** Supported SQL commands. */
    public enum SqlCommand {
        QUIT("(QUIT|EXIT)", NO_OPERANDS),

        CLEAR("CLEAR", NO_OPERANDS),

        HELP("HELP", NO_OPERANDS),

        SHOW_CATALOGS,

        SHOW_CURRENT_CATALOG,

        SHOW_DATABASES,

        SHOW_CURRENT_DATABASE,

        SHOW_TABLES,

        SHOW_FUNCTIONS,

        // FLINK-17396
        SHOW_MODULES("SHOW\\s+MODULES", NO_OPERANDS),

        SHOW_PARTITIONS,

        USE_CATALOG,

        USE,

        CREATE_CATALOG,

        DROP_CATALOG,

        DESC("DESC\\s+(.*)", SINGLE_OPERAND),

        DESCRIBE,

        // supports both `explain xx` and `explain plan for xx` now
        // TODO should keep `explain xx` ?
        // only match "EXPLAIN SELECT xx" and "EXPLAIN INSERT xx" here
        // "EXPLAIN PLAN FOR xx" should be parsed via sql parser
        EXPLAIN(
                "EXPLAIN\\s+(SELECT|INSERT)\\s+(.*)",
                (operands) -> {
                    return Optional.of(new String[] {operands[0], operands[1]});
                }),

        CREATE_DATABASE,

        DROP_DATABASE,

        ALTER_DATABASE,

        CREATE_TABLE,

        DROP_TABLE,

        ALTER_TABLE,

        CREATE_VIEW,

        DROP_VIEW,

        ALTER_VIEW,

        CREATE_FUNCTION,

        DROP_FUNCTION,

        ALTER_FUNCTION,

        SELECT,

        INSERT_INTO,

        INSERT_OVERWRITE,

        SET(
                "SET(\\s+(\\S+)\\s*=(.*))?", // whitespace is only ignored on the left side of '='
                (operands) -> {
                    if (operands.length < 3) {
                        return Optional.empty();
                    } else if (operands[0] == null) {
                        return Optional.of(new String[0]);
                    }
                    return Optional.of(new String[] {operands[1], operands[2]});
                }),

        RESET("RESET", NO_OPERANDS),

        SOURCE("SOURCE\\s+(.*)", SINGLE_OPERAND);

        public final @Nullable Pattern pattern;
        public final @Nullable Function<String[], Optional<String[]>> operandConverter;

        SqlCommand() {
            this.pattern = null;
            this.operandConverter = null;
        }

        SqlCommand(String matchingRegex, Function<String[], Optional<String[]>> operandConverter) {
            this.pattern = Pattern.compile(matchingRegex, DEFAULT_PATTERN_FLAGS);
            this.operandConverter = operandConverter;
        }

        @Override
        public String toString() {
            return super.toString().replace('_', ' ');
        }

        public boolean hasOperands() {
            return operandConverter != NO_OPERANDS;
        }

        public boolean hasRegexPattern() {
            return pattern != null;
        }
    }

    /** Call of SQL command with operands and command type. */
    public static class SqlCommandCall {
        public final SqlCommand command;
        public final String[] operands;

        public SqlCommandCall(SqlCommand command, String[] operands) {
            this.command = command;
            this.operands = operands;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            SqlCommandCall that = (SqlCommandCall) o;
            return command == that.command && Arrays.equals(operands, that.operands);
        }

        @Override
        public int hashCode() {
            int result = Objects.hash(command);
            result = 31 * result + Arrays.hashCode(operands);
            return result;
        }

        @Override
        public String toString() {
            return command + "(" + Arrays.toString(operands) + ")";
        }
    }
}
