package com.poizon.userportrait.udf;

import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.LinkedHashMap;
import java.util.Map;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class CutAdvTitleService extends ScalarFunction {
    private static final OkHttpClient client = new OkHttpClient();
    private static final String url = "http://model.algorithm.dewu.com/model/nlp/cut/query";
    private static final MediaType JSONType = MediaType.parse("application/json; charset=utf-8");
    private static ObjectMapper objectMapper = new ObjectMapper();
    public   String eval(String advTitle) {
        String trim = advTitle.replace("\"", "").replace("”","")
                .replace("\t","").trim();
        String json = String.format("{\"query\": \"%s\"}",trim);

        try {
            final Request request = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(JSONType, json))
                    .build();
            Response response = client.newCall(request).execute();
            Map map = objectMapper.readValue(response.body().string(), Map.class);
            Object  data = map.get("data");
            LinkedHashMap<Object, Object> dataMap = new LinkedHashMap<>();
            if (data instanceof LinkedHashMap){
                dataMap = (LinkedHashMap) data;
            }
            return dataMap.get("cut_list").toString();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("切词出错,传入的词:"+advTitle);
        }
        return advTitle;
    }

    public static void main(String[] args) {
        String  adv_title = "空军一号那里买";
        System.out.println(new CutAdvTitleService().eval(adv_title));
    }
}
