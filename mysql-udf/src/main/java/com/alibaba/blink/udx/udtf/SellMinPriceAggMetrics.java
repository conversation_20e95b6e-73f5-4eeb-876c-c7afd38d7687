package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.util.MysqlUtil;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 最低出价指标
 * sku,spu下的最低出价
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
@Deprecated
public class SellMinPriceAggMetrics extends TableFunction<Row> {
    public void eval(String data) throws Exception {
        List<SellMinPrice> rdsSellMinPriceList;

        try {
            if (!JSONObject.isValidObject(data)) {
                //非法json异常
                JSONException jsonException = new JSONException();
                throw jsonException;
            }

            JSONObject currentInventoryObject = JSONObject.parseObject(data);
            SellMinPrice currentSellMinPrice = SellMinPrice.builder()
                    .id(currentInventoryObject.getLong("id"))
                    .spu_id(currentInventoryObject.getLong("spu_id"))
                    .sku_id(currentInventoryObject.getLong("sku_id"))
                    .price(currentInventoryObject.getLong("price"))
                    .status(currentInventoryObject.getInteger("status"))
                    .build();

            rdsSellMinPriceList = getSellMinPriceList(currentSellMinPrice);
            if (rdsSellMinPriceList.size() != 0) {
                List<String> list = dealWithSellMinPriceList(rdsSellMinPriceList, currentSellMinPrice);
                for (String s : list) {
                    Row row = new Row(1);
                    row.setField(0, s);
                    collect(row);
                }
            } else {
                resetMinPriceMetric(currentSellMinPrice).stream().forEach(o -> {
                    Row row = new Row(1);
                    row.setField(0, o);
                    collect(row);
                });
            }
        } catch (Exception e) {
            if (e instanceof JSONException) {
                throw e;
            }
            e.printStackTrace();
        }
    }


    /**
     * 处理核心函数
     *
     * @param rdsSellMinPriceList
     * @param currentSellMinPrice
     * @return list
     */
    private static List<String> dealWithSellMinPriceList(List<SellMinPrice> rdsSellMinPriceList, SellMinPrice currentSellMinPrice) {
        List<String> resultListCommon = new ArrayList<>();
        Optional<SellMinPrice> spuMinPriceOption = rdsSellMinPriceList.stream().min((o1, o2) -> (int) (o1.price - o2.price));
        //1。 spu 级别的最低出价
        if (spuMinPriceOption.isPresent()) {
            long spuMinPrice = spuMinPriceOption.get().getPrice();
            Map<String, Object> map1 = new HashMap<>();
            map1.put("spu_id", spuMinPriceOption.get().getSpu_id());
            map1.put("min_sell_price", spuMinPrice);
            map1.put("status", 0);
            map1.put("metric_name", "common_spu_dim_sell_min_price");
            resultListCommon.add(JSON.toJSONString(map1));
        }

//		2。计算sku的最低价格
        Map<Long, Optional<SellMinPrice>> collectSkuMinPrice = rdsSellMinPriceList.stream().collect(Collectors.groupingBy(p -> p.getSku_id(), Collectors.minBy((o1, o2) -> (int) (o1.price - o2.price))));

//        collectSkuMinPrice.forEach((skuId, sellMinPrice1) -> {
//            long skuMinprice = sellMinPrice1.get().getPrice();
//
//            Map<String, Object> map = new HashMap<>();
//            map.put("spu_id", sellMinPrice1.get().getSpu_id());
//            map.put("sku_id", skuId);
//            map.put("min_sell_price", skuMinprice);
//            map.put("status", 0);
//            map.put("metric_name", "common_sku_dim_sell_min_price");
//            resultListCommon.add(JSON.toJSONString(map));
//        });
        // 只发送当前sku的变动最低价
        if (collectSkuMinPrice.containsKey(currentSellMinPrice.getSku_id())) {
            Optional<SellMinPrice> sellMinPrice = collectSkuMinPrice.get(currentSellMinPrice.getSku_id());
            long skuMinprice = sellMinPrice.get().getPrice();
            Map<String, Object> map = new HashMap<>();
            map.put("spu_id", currentSellMinPrice.getSpu_id());
            map.put("sku_id", currentSellMinPrice.getSku_id());
            map.put("min_sell_price", skuMinprice);
            map.put("status", 0);
            map.put("metric_name", "common_sku_dim_sell_min_price");
            resultListCommon.add(JSON.toJSONString(map));
        }



            // 需要判断当前binlog 是否有效的被包含在sku的集合里 且 当前binlog的状态是无效的需要将当前的sku的最低价状态置于无效状态 1表示无效状态
        if (!collectSkuMinPrice.containsKey(currentSellMinPrice.getSku_id()) && currentSellMinPrice.getStatus() == 1) {
            //这里最低价不透传出去，因为历史最低价就不知道，更新时候只更新状态

            Map<String, Object> map = new HashMap<>();
            map.put("spu_id", currentSellMinPrice.getSpu_id());
            map.put("sku_id", currentSellMinPrice.getSku_id());
            //过期数据
            map.put("status", 1);
            map.put("metric_name", "common_sku_dim_sell_min_price");
            resultListCommon.add(JSON.toJSONString(map));
        }

        return resultListCommon;
    }


    /**
     * 获取有效最低出价集合.
     *
     * @return List<SellMinPrice>
     * @throws Exception 异常抛出
     */
    private static List<SellMinPrice> getSellMinPriceList(SellMinPrice currentSellMinPrice) throws Exception {
        List<SellMinPrice> inventorySaleList = new ArrayList<>();

        Connection conn = MysqlUtil.getConnection();
        //有效状态下的最低出价数据
        PreparedStatement pst = conn.prepareStatement("SELECT id, `spu_id`,`sku_id`, price   FROM `ods_binlog_sell_sku_min_price` where `spu_id`=" + currentSellMinPrice.spu_id + " and `status` =0");
        ResultSet rs = pst.executeQuery();

        while (rs.next()) {
            long id = rs.getLong("id");
            long spu_id = rs.getLong("spu_id");
            long sku_id = rs.getLong("sku_id");
            long price = rs.getLong("price");
//			 int status = rs.getInt("status");

            if (id != currentSellMinPrice.getId()) {
                SellMinPrice inventorySale = SellMinPrice.builder()
                        .id(id)
                        .spu_id(spu_id)
                        .sku_id(sku_id)
                        .price(price)
                        .status(0)
                        .build();

                inventorySaleList.add(inventorySale);
            }

        }
        //处理当前binlog的数据在本次筛选结果中,如果binlog是正常上架状态则加入计算行列
        if (currentSellMinPrice.getStatus() == 0) {
            inventorySaleList.add(
                    SellMinPrice.builder()
                            .price(currentSellMinPrice.getPrice())
                            .id(currentSellMinPrice.getId())
                            .spu_id(currentSellMinPrice.getSpu_id())
                            .sku_id(currentSellMinPrice.getSku_id())
                            .price(currentSellMinPrice.getPrice())
                            .status(0)
                            .build()
            );
        }


        MysqlUtil.release(rs, pst, conn);

        return inventorySaleList;
    }

    @Data
    @Builder
    static class SellMinPrice {
        private long id;
        private long spu_id;
        private long sku_id;
        private long price;
        // 0 表示有效 1 表示无效
        private int status;
    }


    /**
     * 重置无数据状态
     *
     * @param sellMinPrice
     * @return
     */
    private static List<String> resetMinPriceMetric(SellMinPrice sellMinPrice) {
        List<String> resetMetricList = new ArrayList<>();
        //spu的情况下无数据发生
        Map<String, Object> map1 = new HashMap<>();
        map1.put("spu_id", sellMinPrice.getSpu_id());
        map1.put("status", 1);
        map1.put("metric_name", "common_spu_dim_sell_min_price");
        resetMetricList.add(JSON.toJSONString(map1));

        // sku 下的最低出价是空情况
        Map<String, Object> map = new HashMap<>();
        map.put("spu_id", sellMinPrice.getSpu_id());
        map.put("sku_id", sellMinPrice.getSku_id());
        //过期数据
        map.put("status", 1);
        map.put("metric_name", "common_sku_dim_sell_min_price");
        resetMetricList.add(JSON.toJSONString(map));

        return resetMetricList;
    }

//    public static void main(String[] args) throws Exception {
//        String data = "{\"id\":\"3\",\"sku_id\":\"65328631\",\"price\":\"22900\",\"spu_id\":\"10889\",\"status\":\"1\"}";
//        new SellMinPriceAggMetrics().eval(data);
//
//    }
}
