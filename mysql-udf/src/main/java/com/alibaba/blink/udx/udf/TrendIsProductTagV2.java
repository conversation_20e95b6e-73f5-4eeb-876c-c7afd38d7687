package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.sql.*;

/**
 * 判断trend 是否是商卡
 * Author: <PERSON>_Lin
 */
@FunctionHint(
		input = @DataTypeHint("STRING"),
		output = @DataTypeHint("STRING")
)
public class TrendIsProductTagV2 extends ScalarFunction {

	private Connection conn;

	@Override
	public void open(FunctionContext context) throws Exception {
		String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
		String url = context.getJobParameter("jdbc.url", "****************************************************************************************************************************************************************");
		String username = context.getJobParameter("jdbc.username", "du_flink");
		String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");

		conn = getConnection(driverClassName, url, username, password);
	}

	public String eval(String contentId) throws SQLException {
		return trendIsProductTag(contentId);
	}


	/**
	 * 获取数据库连接.
	 */
	private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
		try {
			Class.forName(driverClassName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
		return DriverManager.getConnection(url, username, password);
	}

	/**
	 * 判断trend是否含有商卡
	 */
	public String trendIsProductTag(String contentId) throws SQLException {
		String sql = "SELECT count(1) as tagNum FROM dw_sns_cnt_basic_sns_content_media_label where content_id = '" + contentId + "' and is_del='0'";
		PreparedStatement pst = conn.prepareStatement(sql);
		ResultSet rs = pst.executeQuery();

		if (rs.next()) {
			int tagNum = rs.getInt("tagNum");
			try {
				pst.close();
				rs.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			return tagNum == 0 ? "0" : "1";
		} else {
			return null;
		}
	}


	@Override
	public void close() throws Exception {
		if (conn != null) {
			conn.close();
		}
	}
}
