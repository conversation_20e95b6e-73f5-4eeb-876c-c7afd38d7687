package com.alibaba.blink.udx.udtf;

/**
 * 无效库存,需剔除
 */
public enum InvalidBidSourceType {

    ORIGINAL_RAFFLE(1, "GWD_LS_01", "原价购", true),

    SEC_KILL(2, "SEC_KILL", "秒杀活动", true),

    LIMITED_SALE(3, "GWD_LS_02", "限定发售", true),

    BLIND_BOX(4, "20", "盲盒", true),

    CROWD_FUND(5, "crowdfunding", "众筹", true),

    AUCTION(6, "AUCTION", "拍卖", true),


    DEPOSIT_SALE(7, "depositSale", "新定金预售", true),


    LIVE(8, "LIVE", "直播", true),
    /**
     * 枚举BLIND_BOX准确来说叫趣开箱，和潮玩盲盒机是不同的活动，注意区分
     */
    ART_TOY_BLIND_BOX(9, "42", "潮玩盲盒机", true),

    PROMOTION_FIXED_PRICE(10, "PROMOTION_FIXED_PRICE", "一口价促销", true),


    CONVENIENT_PURCHASE(11, "CONVENIENT_PURCHASE", "顺手买", true);

    private String sourceTypeDesc;
    private boolean isInvalid;
    private String sourceType;
    private int code;

    public String getSourceType() {
        return sourceType;
    }

    public boolean isInvalid() {
        return isInvalid;
    }


    InvalidBidSourceType(int code, String sourceType, String sourceTypeDesc, boolean isInvalid) {
        this.code = code;
        this.sourceType = sourceType;
        this.sourceTypeDesc = sourceTypeDesc;
        this.isInvalid = isInvalid;
    }


}
