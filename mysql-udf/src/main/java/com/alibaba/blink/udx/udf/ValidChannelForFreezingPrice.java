package com.alibaba.blink.udx.udf;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mysql.cj.util.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class ValidChannelForFreezingPrice extends ScalarFunction {
	public static final ObjectMapper OBJECTMAPPER = new ObjectMapper();
	private static Logger LOG = LoggerFactory.getLogger(ValidChannelForFreezingPrice.class);

	/**
	 * sample
	 * {"tradeChannelLimit":[0,1,2,3,4,5,8,9],"identityLimit":{"element":[5]},"minPriceLimit":0}
	 *
	 * @param threshold, channel
	 * @return
	 */
	public boolean eval(String threshold, Integer channel) throws IOException {
		if (StringUtils.isEmptyOrWhitespaceOnly(threshold) || null == channel) {
			LOG.warn("meet empty infos when ValidChannelForFreezingPrice : {}, {}", threshold, channel);
			return false;
		}
		Map<Object, Object> values = OBJECTMAPPER.readValue(threshold, Map.class);
		if (values.containsKey("tradeChannelLimit")) {
			List<Integer> channels = (List<Integer>) (values.get("tradeChannelLimit"));
			return channels.contains(channel);
		}
		return false;
	}

	public static void main(String[] args) throws IOException {
		System.out.println(new ValidChannelForFreezingPrice().eval("{\"tradeChannelLimit\":[0,1,2,3,4,5,8,9],\"identityLimit\":{\"element\":[5]},\"minPriceLimit\":0}", 1));
	}
}
