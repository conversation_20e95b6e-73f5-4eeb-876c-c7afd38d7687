package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小仓库出价指标
 * table inventory_sale_with_no_fun_box
 * https://poizon.feishu.cn/wiki/YG7BwmSiRi6kxXk7z4UccOxpnFd
 * https://poizon.feishu.cn/docx/M5cmd0wVMorGOAxuAkAcmfGRnLb
 * bidding_type in (0,5,6,7,12,13,14) and source_type = 'poizon'
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class MiniwareHousesAggMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(MiniwareHousesAggMetrics.class);
    private static Connection conn;

    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*******************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_flink");
        String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");
        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        List<InventorySale> inventorySaleList;


        if (!JSONObject.isValidObject(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        Long skuId = currentInventoryObject.getLong("sku_id");

        inventorySaleList = getInventorySaleList(skuId);
        if (inventorySaleList.size() != 0) {
            List<String> list = dealWithInventorySaleList(inventorySaleList, skuId);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
                collect(row);
            }
        } else {
            //处理没有查询到数据的情况下发remain_quantity=0的情况，分场景下发
            resetInventoryMetric(skuId).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
                collect(row);
            });
        }
    }


    private static List<String> dealWithInventorySaleList(List<InventorySale> inventorySaleList, Long skuId) {
        return dealWithInventorySaleListCommon(inventorySaleList, skuId);
    }

    /**
     * 聚合计算逻辑处理.
     *
     * @param inventorySaleList InventorySale集合
     */
    private static List<String> dealWithInventorySaleListCommon(List<InventorySale> inventorySaleList, Long skuId) {
        List<String> resultListCommon = new ArrayList<>();
        //sku全局最低价
        long overallSituationSkuMinPrice = inventorySaleList.stream().min((o1, o2) -> (int) (o1.price - o2.price)).get().getPrice();
        long removeTheSmallWarehouseSkuMinPrice = 0L;
        long smallWarehouseSkuMinPrice = 0L;
        long firstSaleSellerId = 0L;
//        剔出小仓范围的--最低价
//        user_id not in (7,1611623266,1759762941,1759757371,1847347814,1900191994)
        if (inventorySaleList.stream().filter(r ->
                r.getSellerId() != 7 && r.getSellerId() != 1611623266 && r.getSellerId() != 1759762941 && r.getSellerId() != 1759757371 && r.getSellerId() != 1847347814 && r.getSellerId() != 1900191994
        ).min((o1, o2) -> (int) (o1.price - o2.price)).isPresent()) {
            removeTheSmallWarehouseSkuMinPrice = inventorySaleList.stream().filter(r ->
                    r.getSellerId() != 7 && r.getSellerId() != 1611623266 && r.getSellerId() != 1759762941 && r.getSellerId() != 1759757371 && r.getSellerId() != 1847347814 && r.getSellerId() != 1900191994
            ).min((o1, o2) -> (int) (o1.price - o2.price)).get().getPrice();
        }

        if (inventorySaleList.stream().filter(r ->
                r.getSellerId() == 7 || r.getSellerId() == 1611623266 || r.getSellerId() == 1759762941 || r.getSellerId() == 1759757371 || r.getSellerId() == 1847347814 || r.getSellerId() == 1900191994
        ).min((o1, o2) -> (int) (o1.price - o2.price)).isPresent()) {
            //小仓库卖家的最低出价
            smallWarehouseSkuMinPrice = inventorySaleList.stream().filter(r ->
                    r.getSellerId() == 7 || r.getSellerId() == 1611623266 || r.getSellerId() == 1759762941 || r.getSellerId() == 1759757371 || r.getSellerId() == 1847347814 || r.getSellerId() == 1900191994
            ).min((o1, o2) -> (int) (o1.price - o2.price)).get().getPrice();
        }

//        剔出小仓范围的--库存
        Integer removeTheSmallWarehouseSkuRemainQuantity = inventorySaleList.stream().filter(r ->
                r.getSellerId() != 7 && r.getSellerId() != 1611623266 && r.getSellerId() != 1759762941 && r.getSellerId() != 1759757371 && r.getSellerId() != 1847347814 && r.getSellerId() != 1900191994
        ).map(o -> o.remainQuantity).reduce(0, Integer::sum);
        //获取第一个出价的用户是否是小仓出价卖家
        if (inventorySaleList.stream().filter(r -> r.price == overallSituationSkuMinPrice).sorted(Comparator.comparingLong(InventorySale::getCreateTime)).findFirst().isPresent()) {
            firstSaleSellerId = inventorySaleList.stream().filter(r -> r.price == overallSituationSkuMinPrice).sorted(Comparator.comparingLong(InventorySale::getCreateTime)).findFirst().get().getSellerId();
        }

        //sku全局剩余库存
        Integer overallSituationSkuRemainQuantity = inventorySaleList.stream().map(o -> o.remainQuantity).reduce(0, Integer::sum);
        // 按照price 计算对应的库存,剔除小仓
        Map<Long, Integer> perPriceQuc = inventorySaleList.stream().filter(r ->
                r.getSellerId() != 7 && r.getSellerId() != 1611623266 && r.getSellerId() != 1759762941 && r.getSellerId() != 1759757371 && r.getSellerId() != 1847347814 && r.getSellerId() != 1900191994
        ).collect(Collectors.groupingBy(InventorySale::getPrice, Collectors.summingInt(InventorySale::getRemainQuantity)));
        // 返回经过排序的Map
        Map<Long, Integer> sortedPriceMap = perPriceQuc.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        JSONArray jsonArray = new JSONArray();
        long rank = 1;
        for (Map.Entry<Long, Integer> entry : sortedPriceMap.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("price", entry.getKey());
            jsonObject.put("quantity", entry.getValue());
            jsonObject.put("rank", rank++);
            jsonArray.add(jsonObject);
        }

        JSONObject result = new JSONObject();
        result.put("remove_small_ware_min_price", removeTheSmallWarehouseSkuMinPrice);
        result.put("small_ware_min_price", smallWarehouseSkuMinPrice); //小仓库的库存数

        result.put("rank_data", jsonArray);
        result.put("sku_id", skuId);


        result.put("remove_small_ware_remain_inventory", removeTheSmallWarehouseSkuRemainQuantity);
        result.put("first_sale_seller_id", firstSaleSellerId);
        result.put("remain_inventory", overallSituationSkuRemainQuantity);
        result.put("min_sell_price", overallSituationSkuMinPrice);
        result.put("global_now_time", getNowTime());
        result.put("metric_name", "mini_warehouse_inv_metric");
        String jsonString = result.toString();

        resultListCommon.add(jsonString);

        // 回收
        sortedPriceMap.clear();
        perPriceQuc.clear();
        result.clear();
        inventorySaleList.clear();

        return resultListCommon;
    }

    /**
     * 重置指标 废弃
     *
     * @return
     */
    private static List<String> resetInventoryMetric(Long skuId) {
        List<String> resetMetricList = new ArrayList<>();
        JSONObject result = new JSONObject();
        result.put("rank_data", null);
        result.put("sku_id", skuId);
        result.put("remove_small_ware_min_price", null);
        result.put("remove_small_ware_remain_inventory", null);
        result.put("small_ware_min_price", null);
        result.put("first_sale_seller_id", null);
        result.put("remain_inventory", 0);
        result.put("min_sell_price", 0);
        result.put("global_now_time", getNowTime());
        result.put("metric_name", "mini_warehouse_inv_metric");
        String jsonString = result.toString();
        resetMetricList.add(jsonString);

        return resetMetricList;
    }

    private static List<InventorySale> getInventorySaleList(long skuId) throws Exception {
        String sql = "SELECT id, `price`,`bidding_type`,source_type,  `quantity` - `use_quantity` as `remain_quantity`,uid as seller_id, unix_timestamp(create_time) as create_time  FROM `inventory_sale_with_no_fun_box` where `inv_status` = 1 and bidding_type in (0,5,6,7,12,13,14) and `sku_id`=" + skuId;
        List<InventorySale> inventorySaleList = new ArrayList<>();

        PreparedStatement pst = conn.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            String sourceType = rs.getString("source_type");
            // 提取当前符合小仓库条件的库存明细数据
            if (sourceType != null && sourceType.equals("poizon")) {
                InventorySale inventorySale = InventorySale.builder()
                        .price(rs.getLong("price"))
                        .biddingType(rs.getInt("bidding_type"))
                        .remainQuantity(rs.getInt("remain_quantity"))
                        .skuId(skuId)
                        .sourceType(sourceType)
                        .sellerId(rs.getLong("seller_id"))
                        .createTime(rs.getLong("create_time"))
                        .build();
                inventorySaleList.add(inventorySale);
            }
        }

        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }

        return inventorySaleList;
    }

    /**
     * 获取微秒时间
     *
     * @return String
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }

    @Data
    @Builder
    static class InventorySale {
        //主键id
        private long id;
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
        //uid 卖家id
        private long sellerId;
        //状态
        private int status;
        //出价来源
        private String sourceType;
        //出价时间
        private Long createTime;
    }

    @Data
    @AllArgsConstructor
    static class SkuIdAndBiddingType {
        //出价类型
        private int biddingType;
        //sku_id
        private long skuId;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }


    public static void main(String[] args) throws Exception {
        MiniwareHousesAggMetrics inventoryAggMetrics = new MiniwareHousesAggMetrics();
        String data = "{\"id\":\"102747345253\",\"sku_id\":\"630974939\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"331164\",\"bidding_type\":\"15\",\"status\":\"10\"}";
        inventoryAggMetrics.eval(data);

    }
}
