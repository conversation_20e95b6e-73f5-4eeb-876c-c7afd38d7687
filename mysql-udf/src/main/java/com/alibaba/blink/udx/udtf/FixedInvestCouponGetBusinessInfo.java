package com.alibaba.blink.udx.udtf;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.sql.*;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Slf4j
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<order_no STRING, order_name STRING, user_id STRING, merchant_id STRING, brand_id STRING, brand_name STRING, category_ids STRING>")
)
public class FixedInvestCouponGetBusinessInfo extends TableFunction<Row> {
    private String driverClassName;
    private String url;
    private String username;
    private String password;
    private Cache<String, Row> cache;

    @Override
    public void open(FunctionContext context) throws Exception {
        driverClassName = context.getJobParameter("dim.join.jdbc.driver.classname", "com.mysql.jdbc.Driver");
        url = context.getJobParameter("dim.join.jdbc.url", "******************************************************************************************************************************************************************");
        username = context.getJobParameter("dim.join.jdbc.username", "realtime");
        password = context.getJobParameter("dim.join.jdbc.password", "Nmt6ns4ZP%H^W8pX");

        log.info("driverClassName:{}, url:{}, username:{}, password:{}", driverClassName, url, username, password);

        cache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(3600, TimeUnit.SECONDS)
                .build();
    }

    public void eval(String order_no) throws SQLException {
        if (StringUtils.isEmpty(order_no)) {
            log.error("FixedInvestCouponGetBusinessInfo order_no 为空 :{}", order_no);
            collect(new Row(7));
            return;
        }

        try {
            Row row = cache.getIfPresent(order_no);
            if (row == null) {
                row = query(order_no);
            }

            if (row != null) {
                cache.put(order_no, row);
                collect(row);
            } else {
                log.error("FixedInvestCouponGetBusinessInfo 查询为空 order_no:{}", order_no);
                collect(new Row(7));
            }
        } catch (Exception e) {
            log.error("FixedInvestCouponGetBusinessInfo 查询失败 order_no:{}", order_no, e);
        }
    }

    public Row query(String order_no) throws SQLException {
        String sql = "SELECT a.order_no, a.name order_name, a.user_id, b.merchant_id, b.brand_id, b.brand_name, GROUP_CONCAT(c.category_id) category_ids " +
                "FROM business_order a " +
                "LEFT JOIN business_demand b on a.id = b.order_id AND b.del = 0 " +
                "LEFT JOIN business_demand_category c on b.id = c.demand_id AND c.del = 0 " +
                "WHERE a.del = 0 AND a.order_no = ? " +
                "GROUP BY a.order_no, a.name, a.user_id, b.merchant_id, b.brand_id, b.brand_name " +
                "LIMIT 1;";

        try (Connection conn = getConnection(); PreparedStatement preparedStatement = conn.prepareStatement(sql)) {
            preparedStatement.setString(1, order_no);
            ResultSet rs = preparedStatement.executeQuery();

            if (rs.next()) {
                Row row = new Row(7);
                row.setField(0, rs.getString("order_no"));
                row.setField(1, rs.getString("order_name"));
                row.setField(2, rs.getString("user_id"));
                row.setField(3, rs.getString("merchant_id"));
                row.setField(4, rs.getString("brand_id"));
                row.setField(5, rs.getString("brand_name"));
                row.setField(6, rs.getString("category_ids"));
                return row;
            }
        } catch (SQLException e) {
            log.error("查询数据库时发生错误: ", e);
            throw e;
        }

        return null;
    }

    private Connection getConnection() {
        try {
            Class.forName(driverClassName);
            return DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            log.error("FixedInvestCouponGetBusinessInfo getConnection 异常", e);
            throw new RuntimeException(e);
        }
    }

}
