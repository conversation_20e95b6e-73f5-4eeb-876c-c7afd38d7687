package com.alibaba.blink.udx.udtf.bid.oversea;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 美国本对本、中国出口到美国指标
 * 剔除营销活动、隐藏库存等出价指标,剔除逻辑参考文档口径
 * // @高阳 @云路
 * https://poizon.feishu.cn/wiki/UgIdwDyWBibjP2k0t6FcchfwnAc
 * //    select  global_sku_id
 * //            ,currency=USD
 * //       ,case when country_code=delivery_country_code
 * //    then '美国本对本'
 * //            else '中国出口美国'
 * //    end
 * //    as merc_region
 * //       ,min(price/100)       as sku_price
 * //    from    dw_overseas.ods_du_trade_oversea_inventory_sale_region_1hour
 * //    where   pt='${phdate}'
 * //    and     country_code='US'
 * //    and     global_sku_id>0
 * //    and     delivery_country_code in ('CN','US')
 * //    group by global_sku_id,currency,merc_region
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class OverseaInventorySaleRegionMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(OverseaInventorySaleRegionMetrics.class);
    private static Connection conn;


    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "**************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "dw_bigdata_oversea");
        String password = context.getJobParameter("jdbc.password", "GU97(bqSMddcDuFm");

//        String url = "*********************************************************************************************************************************************************************************";
//        String username = "root";
//        String password = "password";
//        String driverClassName = "com.mysql.jdbc.Driver";

        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        if (!JSON.isValid(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        USInventorySale currentInventorySaleBase = USInventorySale.builder()
                .biddingType(currentInventoryObject.getInteger("bidding_type"))
                .globalSkuId(currentInventoryObject.getLong("global_sku_id"))
                .build();

        List<USInventorySale> allInventorySaleList = getInventorySaleRegionList(currentInventorySaleBase.globalSkuId);

        collectMessage(allInventorySaleList, currentInventorySaleBase);
        //释放
        allInventorySaleList.clear();

    }


    private static List<String> dealWithInventorySaleListCommon(List<USInventorySale> inventorySaleBaseList, Long globalSkuId) {
        List<String> resultListCommon = new ArrayList<>();
//        美国本对本
        List<USInventorySale> USPeerToPeerSaleList = inventorySaleBaseList.stream().filter(r -> r.mercRegion.equals("美国本对本")).collect(Collectors.toList());
        if (USPeerToPeerSaleList.size() > 0) {


            List<String> regionResList = calcCommonInvList(USPeerToPeerSaleList, "美国本对本", globalSkuId);
            resultListCommon.addAll(regionResList);

            USPeerToPeerSaleList.clear();
        } else {
//            重置
            resultListCommon.add(processByRegion(globalSkuId, -1, 1, "预售", "美国本对本"));
            resultListCommon.add(processByRegion(globalSkuId, -1, 1, "现货", "美国本对本"));
            resultListCommon.add(processByRegion(globalSkuId, -1, 1, "寄售", "美国本对本"));
        }


        // 中国出口美国
        List<USInventorySale> ChinaExportsToUSSaleList = inventorySaleBaseList.stream().filter(r -> r.mercRegion.equals("中国出口美国")).collect(Collectors.toList());
        if (ChinaExportsToUSSaleList.size() > 0) {
            List<String> regionResList = calcCommonInvList(ChinaExportsToUSSaleList, "中国出口美国", globalSkuId);
            resultListCommon.addAll(regionResList);

            ChinaExportsToUSSaleList.clear();
        } else {
            resultListCommon.add(processByRegion(globalSkuId, -1, 1, "预售", "中国出口美国"));
            resultListCommon.add(processByRegion(globalSkuId, -1, 1, "现货", "中国出口美国"));
            resultListCommon.add(processByRegion(globalSkuId, -1, 1, "寄售", "中国出口美国"));
        }


        inventorySaleBaseList.clear();
        return resultListCommon;
    }


    /**
     * 预售、现货、寄售 指标
     *
     * @param commonInvList
     * @param mercRegion    商家区域
     * @return
     */
    private static List<String> calcCommonInvList(List<USInventorySale> commonInvList, String mercRegion, Long globalSkuId) {
        List<String> resultList = new ArrayList<>();
        //        case when sale_type = 7 then '预售'
        //        when bidding_type = 25 then '现货'
        //else '寄售' end
        //预售
        List<USInventorySale> preSaleInvList = commonInvList.stream().filter(r -> r.getSaleType() == 7).collect(Collectors.toList());
        if (preSaleInvList.size() > 0) {
            long minPrice = preSaleInvList.stream().min((o1, o2) -> (int) (o1.getPrice() - o2.getPrice())).get().getPrice();

            resultList.add(processByRegion(globalSkuId, minPrice, 0, "预售", mercRegion));
        } else {
            resultList.add(processByRegion(globalSkuId, -1, 1, "预售", mercRegion));
        }

        //寄售
        List<Integer> spotBid = Arrays.asList(25);
        List<USInventorySale> spotBidInvList = commonInvList.stream().filter(r -> spotBid.contains(r.getBiddingType())).collect(Collectors.toList());

        if (spotBidInvList.size() > 0) {
            long minPrice = spotBidInvList.stream().min((o1, o2) -> (int) (o1.getPrice() - o2.getPrice())).get().getPrice();

            resultList.add(processByRegion(globalSkuId, minPrice, 0, "寄售", mercRegion));

        } else {
            resultList.add(processByRegion(globalSkuId, -1, 1, "寄售", mercRegion));
        }

        // 现货
        List<USInventorySale> consignmentInvList = commonInvList.stream().filter(r -> (r.getSaleType() != 7) && (!spotBid.contains(r.getBiddingType()))).collect(Collectors.toList());
        if (consignmentInvList.size() > 0) {
            long minPrice = consignmentInvList.stream().min((o1, o2) -> (int) (o1.getPrice() - o2.getPrice())).get().getPrice();
            resultList.add(processByRegion(globalSkuId, minPrice, 0, "现货", mercRegion));
        } else {
            //重置
            resultList.add(processByRegion(globalSkuId, -1, 1, "现货", mercRegion));
        }


        preSaleInvList.clear();
        spotBidInvList.clear();
        consignmentInvList.clear();

        return resultList;
    }


    /**
     * "美国本对本" : "中国出口美国"
     *
     * @param globalSkuId  当前sku
     * @param minPrice     当前sku最低出价
     * @param invalidFlag  是否有效出价
     * @param newChannel   (寄售,现货,预售)
     * @param commonRegion 当前区域函数
     * @return
     */
    public static String processByRegion(long globalSkuId, long minPrice, Integer invalidFlag, String newChannel, String commonRegion) {
        if ("美国本对本".equals(commonRegion)) {
            return calcUSPeerToPeerSkuMetric(globalSkuId, minPrice, invalidFlag, newChannel);
        } else {
//            中国出口美国
            return calcChinaExportsToUSSkuMetric(globalSkuId, minPrice, invalidFlag, newChannel);
        }

    }


    /**
     * 美国本对本 指标
     *
     * @param minPrice
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcUSPeerToPeerSkuMetric(long globalSkuId, long minPrice, Integer invalidFlag, String newChannel) {
        // 美国本对本 指标
        String metricName = "us_peer_to_peer_sku_inv_metric";

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
//        json.put("sku_id", globalSkuId);
        json.put("global_sku_id", globalSkuId);
        // 维度补充
        json.put("seller_perspective", "US");//卖家视角
        json.put("buyer_perspective", "US");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "美国本对本");//老渠道定义
        json.put("currency", "USD");


        //指标
        json.put("min_sell_price", minPrice);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效

        //ext
        json.put("global_now_time", getNowTime());

        return JSON.toJSONString(json);

    }


    /**
     * 中国出口美国最低价指标
     *
     * @param globalSkuId
     * @param minPrice
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcChinaExportsToUSSkuMetric(long globalSkuId, long minPrice, Integer invalidFlag, String newChannel) {
        // 中国出口美国 指标
        String metricName = "ch_exports_to_us_sku_inv_metric";

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
//        json.put("sku_id", globalSkuId);
        json.put("global_sku_id", globalSkuId);
        // 维度补充
        json.put("seller_perspective", "CN");//卖家视角
        json.put("buyer_perspective", "US");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "中国出口美国");//老渠道定义


        //指标
        json.put("min_sell_price", minPrice);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "USD");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * 纳秒时间戳
     *
     * @return
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }


    /**
     * emit message
     *
     * @param inventorySaleList
     * @param currentInventorySaleBase
     */
    private void collectMessage(List<USInventorySale> inventorySaleList, USInventorySale currentInventorySaleBase) {
        if (inventorySaleList.size() > 0) {
            List<String> list = dealWithInventorySaleListCommon(inventorySaleList, currentInventorySaleBase.getGlobalSkuId());
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
//                System.out.println(row);
                collect(row);
            }
        } else {
            resetAllRegionInventoryMetric(currentInventorySaleBase.getGlobalSkuId()).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
//                System.out.println(row);
                collect(row);
            });
        }
    }


    /**
     * 全局重置指标
     *
     * @param globalSkuId
     * @return
     */
    public static List<String> resetAllRegionInventoryMetric(Long globalSkuId) {
        List<String> resetMetricList = new ArrayList<>();

        resetMetricList.add(processByRegion(globalSkuId, -1, 1, "预售", "美国本对本"));
        resetMetricList.add(processByRegion(globalSkuId, -1, 1, "现货", "美国本对本"));
        resetMetricList.add(processByRegion(globalSkuId, -1, 1, "寄售", "美国本对本"));

        resetMetricList.add(processByRegion(globalSkuId, -1, 1, "预售", "中国出口美国"));
        resetMetricList.add(processByRegion(globalSkuId, -1, 1, "现货", "中国出口美国"));
        resetMetricList.add(processByRegion(globalSkuId, -1, 1, "寄售", "中国出口美国"));

        return resetMetricList;
    }


    /**
     * @param globalSkuId
     * @return
     * @throws Exception
     */
    private static List<USInventorySale> getInventorySaleRegionList(long globalSkuId) throws Exception {
        List<USInventorySale> importsInventorySaleList = new ArrayList<>();
        List<String> needDeliveryCountrys = Arrays.asList("CN", "US");

        String sql = "SELECT " +
                "`price`," +
                "`bidding_type`, " +
                "global_sku_id," +
                "currency," +
                "sale_type," +
                "sub_status," +

                "country_code, " +
                "delivery_country_code " +
                " FROM `inventory_sale_region` " +
                "where `status` = 1 and `global_sku_id`=" + globalSkuId;

        PreparedStatement pst = conn.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            int subStatus = rs.getInt("sub_status");
            String countryCode = rs.getString("country_code");
            String deliveryCountryCode = rs.getString("delivery_country_code");
            String mercRegion = countryCode.equals(deliveryCountryCode) ? "美国本对本" : "中国出口美国";


            //剔除隐藏出价部分
            if (subStatus == 0 && countryCode.equals("US") && needDeliveryCountrys.contains(deliveryCountryCode)) {
                String currency = rs.getString("currency");
                int biddingType = rs.getInt("bidding_type");
                Integer saleType = rs.getInt("sale_type");
                Long price = rs.getLong("price");

                USInventorySale importsInventorySale = USInventorySale.builder()
                        .price(price)
                        .biddingType(biddingType)
                        .currency(currency)
                        .mercRegion(mercRegion)
                        .saleType(saleType)
                        .globalSkuId(globalSkuId)
                        .build();

                importsInventorySaleList.add(importsInventorySale);
            }

        }

        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }

        return importsInventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }


    @Data
    @Builder
    static class USInventorySale {
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量 -- 无效
//        private int remainQuantity;
        //是否隐藏
        private int subStatus;
        // 销售类型
        private int saleType;
        //global_sku_id
        private long globalSkuId;
        //币种
        private String currency;
        //所在城市
        private String countryCode;
        private String mercRegion;
        //发货城市
        private String deliveryCountryCode;

    }


    public static void main(String[] args) throws Exception {

        OverseaInventorySaleRegionMetrics inventoryAggMetrics = new OverseaInventorySaleRegionMetrics();
        inventoryAggMetrics.open(null);

        // 需要包含商家区域binlog 单独处理
        String data = "{\"spu_id\":\"11493071\",\"global_sku_id\":\"10000114613\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"1986314538\",\"bidding_type\":\"14\",\"status\":\"10\"}";
        inventoryAggMetrics.eval(data);

    }
}
