package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;

/**
 * 剔出趣开箱随心省Spu相关出价指标
 * table inventory_sale_with_no_fun_box
 * https://poizon.feishu.cn/wiki/wikcnwkI6YibZbZrvX2tCpYdiEd
 * ,case when a.bidding_type = 0 then '普通现货'
 * when a.bidding_type = 1 then '普通预售'
 * when a.bidding_type = 2 then '立即变现'
 * when a.bidding_type = 3 then '跨境'
 * when a.bidding_type = 4 then 'ssp'
 * when a.bidding_type = 5 then '寄售'
 * when a.bidding_type = 6 then '入仓'
 * when a.bidding_type = 7 then '极速现货'
 * when a.bidding_type = 8 then '极速预售'
 * when a.bidding_type = 9 then '入仓定金预售'
 * when a.bidding_type = 10 then '(非在仓)定金预售'
 * when a.bidding_type = 11 then '汽车出价'
 * when a.bidding_type = 12 then '品牌直供现货'  品牌专供 约5天到货
 * when a.bidding_type = 13 then '品牌直供入仓'   品牌专供 约2天到货
 * when a.bidding_type = 14 then '品牌直发'
 * when a.bidding_type = 15 then '虚拟商品出价'
 * when a.bidding_type = 20 then '海外'
 * when a.bidding_type = 21 then '欧洲现货'
 * when a.bidding_type = 22 then '香港寄售'
 * when a.bidding_type = 23 then '保税仓'
 * when a.bidding_type = 100 then '限时折扣活动'
 * when a.bidding_type = 101 then '定制服务'
 * when a.bidding_type = 200 then '月卡'
 * when a.bidding_type = 24 then '跨境极速'
 * when a.bidding_type = 25 then '跨境寄售'
 * when a.bidding_type = 26 then '品牌保税'
 * when a.bidding_type = 27 then '跨境直发'
 * when a.bidding_type = 28 then '欧洲品牌'
 * when a.bidding_type = 29 then '保税直发'
 * end
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class SpuInventoryAndBidingMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(SpuInventoryAndBidingMetrics.class);
    private static Connection conn;

    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*******************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_flink");
        String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");
        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        List<InventorySale> inventorySaleList;

        if (!JSON.isValid(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        InventorySale currentInventorySale = InventorySale.builder()
                .biddingType(currentInventoryObject.getInteger("bidding_type"))
                .skuId(currentInventoryObject.getLong("sku_id"))
                .spuId(currentInventoryObject.getLong("spu_id"))
                .sellerId(currentInventoryObject.getLong("uid"))
                .price(currentInventoryObject.getLong("price"))
                .build();

        inventorySaleList = getInventorySaleList(currentInventorySale.getSpuId());
        if (inventorySaleList.size() != 0) {
            List<String> list = dealWithInventorySaleList(inventorySaleList, currentInventorySale);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
                collect(row);
            }
        } else {
            //处理没有查询到数据的情况下发remain_quantity=0的情况，分场景下发
            resetAllInventoryMetric(currentInventorySale).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
                collect(row);
            });
        }
        //释放
        inventorySaleList.clear();
    }

    private static List<String> dealWithInventorySaleList(List<InventorySale> inventorySaleList, InventorySale currentInventorySale) {
        return dealWithInventorySaleListCommon(inventorySaleList, currentInventorySale);
    }

    /**
     * 聚合计算逻辑处理.
     *
     * @param inventorySaleList InventorySale集合
     *                          按照离线的库存计算,spu最多有2500条出价记录
     */
    private static List<String> dealWithInventorySaleListCommon(List<InventorySale> inventorySaleList, InventorySale currentInventorySale) {
        List<String> resultListCommon = new ArrayList<>();

//        1. spu全渠道最低价
        Optional<InventorySale> spuMinPriceOpt = inventorySaleList.stream().min((o1, o2) -> (int) (o1.price - o2.price));

        if (spuMinPriceOpt.isPresent()) {
            long spuMinPrice = spuMinPriceOpt.get().getPrice();
//        2.spu全渠道最高价
            long spuMaxPrice = inventorySaleList.stream().max((o1, o2) -> (int) (o1.price - o2.price)).get().getPrice();
//        3.spu全渠道库存
            Integer spuRemainQuantity = inventorySaleList.stream().map(o -> o.remainQuantity).reduce(0, Integer::sum);

            resultListCommon.add(calcPlatformSpuMetric(currentInventorySale.getSpuId(), spuMinPrice, spuMaxPrice, spuRemainQuantity));
        }


//        4.spu+商家 最低价
        Optional<InventorySale> mercSpuMinPriceOpt = inventorySaleList.stream().filter(inv -> currentInventorySale.getSellerId() == inv.getSellerId()).min((o1, o2) -> (int) (o1.price - o2.price));

        if (mercSpuMinPriceOpt.isPresent()) {
            long mercSpuMinPrice = mercSpuMinPriceOpt.get().getPrice();
//        5.spu+商家 最高价
            long mercSpuMaxPrice = inventorySaleList.stream().filter(inv -> currentInventorySale.getSellerId() == inv.getSellerId()).max((o1, o2) -> (int) (o1.price - o2.price)).get().getPrice();
//        6.spu+商家 库存数
            Integer mercSpuRemainQuantity = inventorySaleList.stream().filter(inv -> currentInventorySale.getSellerId() == inv.getSellerId()).map(o -> o.remainQuantity).reduce(0, Integer::sum);

            resultListCommon.add(calcPlatformSpuAndMercMetric(currentInventorySale.getSpuId(), currentInventorySale.getSellerId(), mercSpuMinPrice, mercSpuMaxPrice, mercSpuRemainQuantity));

        } else {
//            invalid 当前商家下架操作
            resultListCommon.add(resetCalcPlatformSpuAndMercMetric(currentInventorySale.getSpuId(), currentInventorySale.getSellerId()));
        }


//        7.spu+渠道 最低价
        Optional<InventorySale> bidSpuMinPriceOpt = inventorySaleList.stream().filter(inv -> currentInventorySale.getBiddingType() == inv.getBiddingType()).min((o1, o2) -> (int) (o1.price - o2.price));

        if (bidSpuMinPriceOpt.isPresent()) {
            long bidSpuMinPrice = bidSpuMinPriceOpt.get().getPrice();
//        8.spu+渠道 最高价
            long bidSpuMaxPrice = inventorySaleList.stream().filter(inv -> currentInventorySale.getBiddingType() == inv.getBiddingType()).max((o1, o2) -> (int) (o1.price - o2.price)).get().getPrice();
//        9.spu+渠道 库存数
            Integer bidSpuRemainQuantity = inventorySaleList.stream().filter(inv -> currentInventorySale.getBiddingType() == inv.getBiddingType()).map(o -> o.remainQuantity).reduce(0, Integer::sum);

            resultListCommon.add(calcPlatformSpuAndBidMetric(currentInventorySale.getSpuId(), currentInventorySale.getBiddingType(), bidSpuMinPrice, bidSpuMaxPrice, bidSpuRemainQuantity));

        } else {
            //invalid 当前渠道下架操作
            resultListCommon.add(resetCalcPlatformSpuAndBidMetric(currentInventorySale.getSpuId(), currentInventorySale.getBiddingType()));
        }

        //回收
        inventorySaleList.clear();
        return resultListCommon;
    }

    /**
     * 平台spu出价指标计算函数
     *
     * @param spuId
     * @param spuMinPrice
     * @param spuMaxPrice
     * @param spuRemainQuantity
     * @return
     */
    private static String calcPlatformSpuMetric(long spuId, long spuMinPrice, long spuMaxPrice, Integer spuRemainQuantity) {
        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("min_sell_price", spuMinPrice);
        json.put("max_sell_price", spuMaxPrice);
        json.put("remain_inventory", spuRemainQuantity);

        json.put("global_now_time", getNowTime());
        json.put("metric_name", "platform_spu_dim_inv_metric");

        return JSON.toJSONString(json);
    }

    /**
     * 平台商家spu出价指标计算函数
     *
     * @param spuId
     * @param sellerId
     * @param mercSpuMinPrice
     * @param mercSpuMaxPrice
     * @param mercSpuRemainQuantity
     * @return
     */
    private static String calcPlatformSpuAndMercMetric(long spuId, long sellerId, long mercSpuMinPrice, long mercSpuMaxPrice, Integer mercSpuRemainQuantity) {

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("seller_id", sellerId);
        json.put("min_sell_price", mercSpuMinPrice);
        json.put("max_sell_price", mercSpuMaxPrice);
        json.put("remain_inventory", mercSpuRemainQuantity);

        json.put("global_now_time", getNowTime());
        json.put("metric_name", "platform_spu_and_seller_id_dim_inv_metric");

        return JSON.toJSONString(json);
    }

    /**
     * 平台渠道spu出价指标计算函数
     *
     * @param spuId
     * @param bidType
     * @param bidSpuMinPrice
     * @param bidSpuMaxPrice
     * @param bidSpuRemainQuantity
     * @return
     */
    private static String calcPlatformSpuAndBidMetric(long spuId, Integer bidType, long bidSpuMinPrice, long bidSpuMaxPrice, Integer bidSpuRemainQuantity) {

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("bidding_type", bidType);
        json.put("min_sell_price", bidSpuMinPrice);
        json.put("max_sell_price", bidSpuMaxPrice);
        json.put("remain_inventory", bidSpuRemainQuantity);

        json.put("global_now_time", getNowTime());
        json.put("metric_name", "platform_spu_and_bidding_type_dim_inv_metric");

        return JSON.toJSONString(json);
    }


    /**
     * 重置平台spu的计算函数
     *
     * @param spuId
     * @return
     */
    private static String resetcalcPlatformSpuMetric(long spuId) {
        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("min_sell_price", 0);
        json.put("max_sell_price", 0);
        json.put("remain_inventory", 0);

        json.put("invalid_flag", 1);//无效标识
        json.put("global_now_time", getNowTime());
        json.put("metric_name", "platform_spu_dim_inv_metric");

        return JSON.toJSONString(json);
    }


    /**
     * 重置平台商家spu出价指标计算函数
     *
     * @param spuId
     * @param sellerId
     * @return
     */
    private static String resetCalcPlatformSpuAndMercMetric(long spuId, long sellerId) {
        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("seller_id", sellerId);
        json.put("min_sell_price", 0);
        json.put("max_sell_price", 0);
        json.put("remain_inventory", 0);

        json.put("invalid_flag", 1);
        json.put("global_now_time", getNowTime());
        json.put("metric_name", "platform_spu_and_seller_id_dim_inv_metric");

        return JSON.toJSONString(json);
    }


    /**
     * 重置平台渠道spu出价指标计算函数
     *
     * @param spuId
     * @param bidType
     * @return
     */
    private static String resetCalcPlatformSpuAndBidMetric(long spuId, Integer bidType) {
        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("bidding_type", bidType);
        json.put("min_sell_price", 0);
        json.put("max_sell_price", 0);
        json.put("remain_inventory", 0);

        json.put("invalid_flag", 1);//无效标识
        json.put("global_now_time", getNowTime());
        json.put("metric_name", "platform_spu_and_bidding_type_dim_inv_metric");

        return JSON.toJSONString(json);
    }


    /**
     * 全局重置操作
     *
     * @param currentInventorySale
     * @return
     */
    private static List<String> resetAllInventoryMetric(InventorySale currentInventorySale) {
        List<String> resetMetricList = new ArrayList<>();
        resetMetricList.add(resetcalcPlatformSpuMetric(currentInventorySale.getSpuId()));
        resetMetricList.add(resetCalcPlatformSpuAndMercMetric(currentInventorySale.getSpuId(), currentInventorySale.getSellerId()));
        resetMetricList.add(resetCalcPlatformSpuAndBidMetric(currentInventorySale.getSpuId(), currentInventorySale.getBiddingType()));
        return resetMetricList;
    }

    /**
     * 获取inventorySaleList.
     *
     * @param spuId sku_id
     * @return List<InventorySale>
     * @throws Exception 异常抛出
     */
    private static List<InventorySale> getInventorySaleList(long spuId) throws Exception {
        String sql = "SELECT id, `price`,`bidding_type`,  `quantity` - `use_quantity` as `remain_quantity`,uid as seller_id,sku_id  FROM `inventory_sale_with_no_fun_box` where `inv_status` = 1 and `spu_id`=" + spuId;
        List<InventorySale> inventorySaleList = new ArrayList<>();

        PreparedStatement pst = conn.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            InventorySale inventorySale = InventorySale.builder()
                    .price(rs.getLong("price"))
                    .biddingType(rs.getInt("bidding_type"))
                    .remainQuantity(rs.getInt("remain_quantity"))
                    .skuId(rs.getLong("sku_id"))
                    .spuId(spuId)
                    .sellerId(rs.getLong("seller_id"))
                    .build();

            inventorySaleList.add(inventorySale);
        }

        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }

        return inventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }

    /**
     * 纳秒时间
     *
     * @return
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }

    @Data
    @Builder
    static class InventorySale {
        //主键id
        private long id;
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
        //spu_id
        private long spuId;
        //uid 卖家id
        private long sellerId;
        //状态
        private int status;
    }

    public static void main(String[] args) throws Exception {

        SpuInventoryAndBidingMetrics inventoryAggMetrics = new SpuInventoryAndBidingMetrics();
        String data = "{\"spu_id\":\"6033236\",\"sku_id\":\"630976621\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"1522127864\",\"bidding_type\":\"5\",\"status\":\"10\"}";
        inventoryAggMetrics.eval(data);

    }
}
