package com.alibaba.blink.udx.udtf.bid.oversea;

import static com.alibaba.blink.udx.udtf.bid.oversea.OverseaCalcInvMetricUtils.*;

import com.alibaba.blink.udx.udtf.InvalidBidSourceType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;


/**
 * 海外进口各维度出价指标集合
 * 剔除营销活动、隐藏库存等出价指标,剔除逻辑参考文档口径
 * // @高阳 @云路
 * https://poizon.feishu.cn/wiki/UgIdwDyWBibjP2k0t6FcchfwnAc
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class OverseaInvExcludeMarketingEventsMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(OverseaInvExcludeMarketingEventsMetrics.class);
    private static Connection conn;


    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "**************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "dw_bigdata_oversea");
        String password = context.getJobParameter("jdbc.password", "GU97(bqSMddcDuFm");

//        String url = "*********************************************************************************************************************************************************************************";
//        String username = "root";
//        String password = "password";
//        String driverClassName = "com.mysql.jdbc.Driver";

        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        if (!JSON.isValid(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        ImportsInventorySaleBase currentInventorySaleBase = ImportsInventorySaleBase.builder()
                .biddingType(currentInventoryObject.getInteger("bidding_type"))
                .skuId(currentInventoryObject.getLong("sku_id"))
                .spuId(currentInventoryObject.getLong("spu_id"))
                .sellerId(currentInventoryObject.getLong("uid"))
                .build();

        List<ImportsInventorySaleBase> allInventorySaleList = getInventorySaleBaseList(currentInventorySaleBase.getSkuId());

        collectMessage(allInventorySaleList, currentInventorySaleBase);

        //释放
        allInventorySaleList.clear();

    }

    /**
     * 指标集核心
     *
     * @param inventorySaleBaseList
     * @param currentInventorySaleBase
     * @return
     */
    private static List<String> dealWithInventorySaleBaseList(List<ImportsInventorySaleBase> inventorySaleBaseList, ImportsInventorySaleBase currentInventorySaleBase) {
        return dealOverSeaWithInventorySaleBaseListCommon(inventorySaleBaseList, currentInventorySaleBase);
    }

    /**
     * emit message
     *
     * @param inventorySaleList
     * @param currentInventorySaleBase
     */
    private void collectMessage(List<ImportsInventorySaleBase> inventorySaleList, ImportsInventorySaleBase currentInventorySaleBase) {
        if (inventorySaleList.size() > 0) {
            List<String> list = dealWithInventorySaleBaseList(inventorySaleList, currentInventorySaleBase);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
//                System.out.println(row);
                collect(row);
            }
        } else {
            //处理没有查询到数据的情况下发remain_quantity=0的情况，分场景下发
            resetAllRegionInventoryMetric(currentInventorySaleBase).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
//                System.out.println(row);
                collect(row);
            });
        }
    }


    /**
     * @param skuId
     * @return
     * @throws Exception
     */
    private static List<ImportsInventorySaleBase> getInventorySaleBaseList(long skuId) throws Exception {
        List<ImportsInventorySaleBase> importsInventorySaleList = new ArrayList<>();

        String sql = "SELECT " +
                "id, " +
                "`price`," +
                "`bidding_type`, " +
                " `quantity` - `use_quantity` as `remain_quantity`," +
                "uid as seller_id," +
                "sku_id," +
                "source_type," +
                "sale_type," +
                "sub_status," +
                "final_price, " +
                "supply_region, " +
                "region ," +
                "delivery_country_code " +
                " FROM `oversea_inventory_sale` " +
                "where `status` = 1 and `sku_id`=" + skuId;

        PreparedStatement pst = conn.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            String sourceType = rs.getString("source_type");
            int biddingType = rs.getInt("bidding_type");
            String saleType = rs.getString("sale_type");
            int subStatus = rs.getInt("sub_status");
            Long finalPrice = rs.getLong("final_price");
            Long price = rs.getLong("price");

            String supplyRegion = rs.getString("supply_region").trim();
            String region = rs.getString("region").trim();

            String mercRegion = supplyRegion == null || supplyRegion.equals("") ? region : supplyRegion;

            //修正dim 表历史原因遗漏finalprice情况
            if (finalPrice == null || finalPrice == 0) {
                finalPrice = rs.getLong("price");
            }

            if (valited(sourceType, biddingType, saleType, subStatus)) {
                //包含 if (biddingType == 3 || (biddingType >= 20 && biddingType <= 30))
                ImportsInventorySaleBase importsInventorySale = ImportsInventorySaleBase.builder()
                        .price(price)
                        .biddingType(rs.getInt("bidding_type"))
                        .remainQuantity(rs.getInt("remain_quantity"))
                        .skuId(rs.getLong("sku_id"))
                        .sellerId(rs.getLong("seller_id"))
                        .finalPrice(finalPrice)
                        .deliveryCountryCode(rs.getString("delivery_country_code").trim())
//                        .supplyRegion(rs.getString("supply_region"))
//                        .region(rs.getString("region"))
                        .mercRegion(mercRegion)
                        .saleType(saleType)
                        .build();

                importsInventorySaleList.add(importsInventorySale);
            }

        }

        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }

        return importsInventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }


    @Data
    @SuperBuilder
    @ToString
    static class ImportsInventorySaleBase extends InventorySaleBase {

    }

    @Data
    @SuperBuilder
    static class InventorySaleBase {
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
        //spu_id
        private long spuId;
        //uid 卖家id
        private long sellerId;
        //最终出价
        private long finalPrice;
        // 商家货源地
//        private String supplyRegion;
        // 发货城市
        private String deliveryCountryCode;
//        //商家注册地
//        private String region;
        // coalesce(supplyRegion,region)
        private String mercRegion;
        //销售类型
        private String saleType;
    }


    /**
     * 校验是否有效出价
     *
     * @param sourceType
     * @param biddingType
     * @param saleType
     * @param subStatus
     * @return
     */
    public static boolean valited(String sourceType, int biddingType, String saleType, int subStatus) {
        boolean isValid = true;

        //隐藏出价
        if (subStatus == 1) {
            return false;
        }

        //无效sourceType
        for (InvalidBidSourceType type : InvalidBidSourceType.values()) {
            if (sourceType.equals(type.getSourceType())) {
                return false;
            }
        }

        return isValid;
    }


    public static void main(String[] args) throws Exception {

        OverseaInvExcludeMarketingEventsMetrics inventoryAggMetrics = new OverseaInvExcludeMarketingEventsMetrics();
        inventoryAggMetrics.open(null);

        // 需要包含商家区域binlog 单独处理
        String data = "{\"spu_id\":\"11493071\",\"sku_id\":\"13698631\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"1986314538\",\"bidding_type\":\"14\",\"status\":\"10\"}";
        inventoryAggMetrics.eval(data);

    }
}
