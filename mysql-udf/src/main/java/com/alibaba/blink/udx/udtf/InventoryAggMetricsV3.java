package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存变动的时候统计相关sku剩余库存、最低出价指标
 * InventoryAggMetricsV2当前binlog参与计算会影响结果
 * by <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON>
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class InventoryAggMetricsV3 extends TableFunction<Row> {

    private static final Logger LOG = LoggerFactory.getLogger(InventoryAggMetricsV3.class);
    private static Connection conn;

    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*******************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_flink");
        String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");
        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        List<InventorySale> inventorySaleList;

        if (!JSONObject.isValidObject(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        InventorySale currentInventorySale = InventorySale.builder()
                .id(currentInventoryObject.getLong("id"))
                .price(currentInventoryObject.getLong("price"))
                .biddingType(currentInventoryObject.getInteger("bidding_type"))
                .remainQuantity(currentInventoryObject.getInteger("quantity") - currentInventoryObject.getInteger("use_quantity"))
                .skuId(currentInventoryObject.getLong("sku_id"))
                .sellerId(currentInventoryObject.getLong("uid"))
                .status(currentInventoryObject.getInteger("status"))
                .build();
        Long skuId = currentInventorySale.getSkuId();

        inventorySaleList = getInventorySaleList(skuId, currentInventorySale);
        if (inventorySaleList.size() != 0) {
            List<String> list = dealWithInventorySaleList(inventorySaleList, skuId, currentInventorySale);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
                collect(row);
            }
        } else {
            //处理没有查询到数据的情况下发remain_quantity=0的情况，分场景下发
            resetInventoryMetric(currentInventorySale).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
                collect(row);
            });
        }
    }


    private static List<String> dealWithInventorySaleList(List<InventorySale> inventorySaleList, Long skuId, InventorySale currentInventorySale) {
        return dealWithInventorySaleListCommon(inventorySaleList, skuId, currentInventorySale);
    }

    /**
     * 聚合计算逻辑处理.
     *
     * @param inventorySaleList InventorySale集合
     */
    private static List<String> dealWithInventorySaleListCommon(List<InventorySale> inventorySaleList, Long skuId, InventorySale currentInventorySale) {
        List<String> resultListCommon = new ArrayList<>();

        /*1、 计算sku维度下的最低出价、剩余库存、商家id集合*/
        //获取最低价对应的库存明细
        Optional<InventorySale> skuMinPriceInventory = inventorySaleList.stream().min((o1, o2) -> (int) (o1.price - o2.price));
        //sku最低价
        long skuMinPrice = skuMinPriceInventory.get().getPrice();
        //sku的剩余库存
        Integer skuRemainQuantity = inventorySaleList.stream().map(o -> o.remainQuantity).reduce(0, Integer::sum);
        // 计算sku最低出价的商家,加一层去重逻辑
        Optional<String> skuMinPriceWithSellerIds = inventorySaleList.stream().filter(item -> item.getPrice() == skuMinPrice).map(o -> String.valueOf(o.sellerId)).distinct().reduce((partialString, element) -> partialString + "," + element);


        //2、按照sku_id维度聚合
        //2.1、只计算所有价格等于最低出价的
        Map<Long, Integer> remainQuantityMap1MinPrice = inventorySaleList.stream().filter(item -> item.getPrice() == skuMinPrice).collect(Collectors.groupingBy(InventorySale::getSkuId, Collectors.summingInt(InventorySale::getRemainQuantity)));

        Map<String, Object> map1 = new HashMap<>();
        map1.put("sku_id", skuId);
        map1.put("remain_inventory", remainQuantityMap1MinPrice.get(skuId));
        map1.put("min_sell_price", skuMinPrice);
        map1.put("seller_ids", skuMinPriceWithSellerIds.get());
        map1.put("global_now_time", getNowTime());
        map1.put("metric_name", "special_sku_dim_by_min_price");
        resultListCommon.add(JSON.toJSONString(map1));

        //2.2、计算sku维度所有出价的
//        Map<Long, Integer> remainQuantityMap1AllPrice = inventorySaleList.stream().collect(Collectors.groupingBy(InventorySale::getSkuId, Collectors.summingInt(InventorySale::getRemainQuantity)));
        Map<String, Object> map2 = new HashMap<>();
        map2.put("sku_id", skuId);
//        map2.put("remain_inventory", remainQuantityMap1AllPrice.get(skuId));
        map2.put("remain_inventory", skuRemainQuantity);
        map2.put("min_sell_price", skuMinPrice);
        map2.put("seller_ids", skuMinPriceWithSellerIds.get());
        map2.put("global_now_time", getNowTime());
        map2.put("metric_name", "common_sku_dim");
        resultListCommon.add(JSON.toJSONString(map2));


        //3.2、 计算sku,bid_type下维度下的最低出价、剩余库存、商家id集合
        Map<SkuIdAndBiddingType, Integer> remainQuantityMap2AllPrice = inventorySaleList.stream().collect(Collectors.groupingBy(p -> new SkuIdAndBiddingType(p.getBiddingType(), p.getSkuId()), Collectors.summingInt(InventorySale::getRemainQuantity)));

        Map<SkuIdAndBiddingType, Optional<InventorySale>> skuAndBiddingTypeMinPriceMap = inventorySaleList.stream().collect(Collectors.groupingBy(p -> new SkuIdAndBiddingType(p.getBiddingType(), p.getSkuId()), Collectors.minBy((o1, o2) -> (int) (o1.price - o2.price))));
//       每个渠道的最低价对应的商家集合
//        Map<SkuIdAndBiddingType, Optional<String>> skuAndBiddingTypeSellerIdsMap = inventorySaleList.stream().collect(Collectors.groupingBy(p -> new SkuIdAndBiddingType(p.getBiddingType(), p.getSkuId()), Collectors.mapping(o -> String.valueOf(o.getSellerId()), Collectors.reducing( (partialString, element) -> partialString + "," + element))));
        boolean isEmptyForCurrentSkuAndBidType = true;
        for (Map.Entry<SkuIdAndBiddingType, Integer> entry : remainQuantityMap2AllPrice.entrySet()) {
            SkuIdAndBiddingType key = entry.getKey();
            Integer value = entry.getValue();
            //获取每个渠道的最低出价的详情
            InventorySale skuAndBiddingTypeMinPriceSale = skuAndBiddingTypeMinPriceMap.get(key).get();

            //过滤当前渠道的最低价对应的商家
            Optional<String> skuAndBiddingTypOptionSellerIds = inventorySaleList.stream().filter(item -> item.getPrice() == skuAndBiddingTypeMinPriceSale.getPrice() && item.getBiddingType() == skuAndBiddingTypeMinPriceSale.getBiddingType()).distinct().collect(Collectors.mapping(o -> String.valueOf(o.getSellerId()), Collectors.reducing((partialString, element) -> partialString + "," + element)));


            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("sku_id", key.skuId);
            tempMap.put("bidding_type", key.biddingType);
            tempMap.put("remain_inventory", value);
            tempMap.put("min_sell_price", skuAndBiddingTypeMinPriceSale.getPrice());
            tempMap.put("seller_ids", skuAndBiddingTypOptionSellerIds.get());
            tempMap.put("global_now_time", getNowTime());

            tempMap.put("metric_name", "common_sku_and_bid_type_dim");
            resultListCommon.add(JSON.toJSONString(tempMap));

            if (key.getBiddingType() == currentInventorySale.getBiddingType()) {
                isEmptyForCurrentSkuAndBidType = false;
            }
        }

        //对当前binlog的下架状态做核查,当前渠道下架商品且不在计算结果中的，要重置0
        if (isEmptyForCurrentSkuAndBidType) {
            Map<String, Object> tempMapZero = new HashMap<>();
            tempMapZero.put("sku_id", currentInventorySale.getSkuId());
            tempMapZero.put("bidding_type", currentInventorySale.getBiddingType());
            tempMapZero.put("remain_inventory", 0);
            tempMapZero.put("min_sell_price", 0);
            tempMapZero.put("seller_ids", null);
            tempMapZero.put("global_now_time", getNowTime());

            tempMapZero.put("metric_name", "common_sku_and_bid_type_dim");
            resultListCommon.add(JSON.toJSONString(tempMapZero));
        }

        return resultListCommon;
    }

    /**
     * 重置指标 废弃
     *
     * @return
     */
    private static List<String> resetInventoryMetric(InventorySale currentInventorySale) {
        List<String> resetMetricList = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("sku_id", currentInventorySale.getSkuId());
        map1.put("remain_inventory", 0);
        map1.put("min_sell_price", 0);
        map1.put("seller_ids", null);
        map1.put("global_now_time", getNowTime());
        map1.put("metric_name", "special_sku_dim_by_min_price");
        resetMetricList.add(JSON.toJSONString(map1));

        Map<String, Object> map2 = new HashMap<>();
        map2.put("sku_id", currentInventorySale.getSkuId());
        map2.put("remain_inventory", 0);
        map2.put("min_sell_price", 0);
        map2.put("seller_ids", null);
        map2.put("metric_name", "common_sku_dim");
        map2.put("global_now_time", getNowTime());

        resetMetricList.add(JSON.toJSONString(map2));

        Map<String, Object> tempMap = new HashMap<>();
        tempMap.put("sku_id", currentInventorySale.getSkuId());
        tempMap.put("bidding_type", currentInventorySale.getBiddingType());
        tempMap.put("remain_inventory", 0);
        tempMap.put("min_sell_price", 0);
        tempMap.put("seller_ids", null);
        tempMap.put("global_now_time", getNowTime());
        tempMap.put("metric_name", "common_sku_and_bid_type_dim");
        resetMetricList.add(JSON.toJSONString(tempMap));

        return resetMetricList;
    }

    /**
     * 获取inventorySaleList.
     *
     * @param skuId sku_id
     * @return List<InventorySale>
     * @throws Exception 异常抛出
     */
    private static List<InventorySale> getInventorySaleList(long skuId, InventorySale currentInventorySale) throws Exception {
        List<InventorySale> inventorySaleList = new ArrayList<>();

        PreparedStatement pst = conn.prepareStatement("SELECT id, `price`,`bidding_type`,  `quantity` - `use_quantity` as `remain_quantity`,uid as seller_id  FROM `ods_binlog_inventory_sale` where `sku_id`=" + skuId + " and `status` =1");
        ResultSet rs = pst.executeQuery();

        while (rs.next()) {
            long price = rs.getLong("price");
            int biddingType = rs.getInt("bidding_type");
            int remainQuantity = rs.getInt("remain_quantity");
            long sellerId = rs.getLong("seller_id");
            long id = rs.getLong("id");//为了和binlog合并去重

            InventorySale inventorySale = InventorySale.builder()
                    .price(price)
                    .biddingType(biddingType)
                    .remainQuantity(remainQuantity)
                    .skuId(skuId)
                    .sellerId(sellerId)
                    .build();

            inventorySaleList.add(inventorySale);

        }


        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }


        return inventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }

    /**
     * 获取微秒时间
     *
     * @return String
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }

    @Data
    @Builder
    static class InventorySale {
        //主键id
        private long id;
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
        //uid 卖家id
        private long sellerId;
        //状态
        private int status;
    }

    @Data
    @AllArgsConstructor
    static class SkuIdAndBiddingType {
        //出价类型
        private int biddingType;
        //sku_id
        private long skuId;
    }


    public static void main(String[] args) throws Exception {
        long skuId = 611393923;

        InventoryAggMetricsV3 inventoryAggMetrics = new InventoryAggMetricsV3();

//		 如果需要准确计算 传主键id，skuId，（ `quantity` - `use_quantity`）remain_quantity,bidding_type,uid,status,price,为了方便需要传json包含这些字段即可
        String data = "{\"id\":\"102747345253\",\"sku_id\":\"106254627\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"331164\",\"bidding_type\":\"0\",\"status\":\"10\"}";

        inventoryAggMetrics.eval(data);

        //验证数据商家uid拼接的有效性
//        List<Integer> letters = Arrays.asList(5, 2, 2,4, 5);
//        Optional<String> s = letters
//                .stream().filter(o->o==2).map(o -> String.valueOf(o)).distinct()
//                .reduce((partialString, element) -> partialString +","+ element);
//
//        System.out.println( s.get());
//        //   验证最低价计算
//        Optional<Integer> m = letters.stream().min((o1, o2) -> (int) (o1 - o2));
//
//        System.out.println(m.get());
//
//        //验证分组获取sellerids
//        Map<SkuIdAndBiddingType, Optional<String>> test = letters.stream().distinct().collect(Collectors.groupingBy(p -> new SkuIdAndBiddingType(p, p), Collectors.mapping(o -> String.valueOf(o), Collectors.reducing((partialString, element) -> partialString + "," + element))));
//
//        System.out.println(test.get(new SkuIdAndBiddingType(2,2) ).get());
//        System.out.println( System.nanoTime());
//        System.out.println( System.nanoTime());
//        for (int i = 0; i <10 ; i++) {
//            System.out.println(getNowTime());
//
//        }

    }
}
