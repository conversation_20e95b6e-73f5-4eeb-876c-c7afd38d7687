package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 剔出趣开箱随心省出价指标
 * table inventory_sale_with_no_fun_box
 * https://poizon.feishu.cn/wiki/wikcnwkI6YibZbZrvX2tCpYdiEd
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class UnboxingBidInventoryAggMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(UnboxingBidInventoryAggMetrics.class);
    private static Connection conn;

    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*******************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_flink");
        String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");
        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }


    public void eval(String data) throws Exception {
        List<InventorySale> inventorySaleList;


        if (!JSONObject.isValidObject(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        InventorySale currentInventorySale = InventorySale.builder()
                .id(currentInventoryObject.getLong("id"))
                .price(currentInventoryObject.getLong("price"))
                .biddingType(currentInventoryObject.getInteger("bidding_type"))
                .remainQuantity(currentInventoryObject.getInteger("quantity") - currentInventoryObject.getInteger("use_quantity"))
                .skuId(currentInventoryObject.getLong("sku_id"))
                .sellerId(currentInventoryObject.getLong("uid"))
                .status(currentInventoryObject.getInteger("status"))
                .build();
        Long skuId = currentInventorySale.getSkuId();

        inventorySaleList = getInventorySaleList(skuId);
        if (inventorySaleList.size() != 0) {
            List<String> list = dealWithInventorySaleList(inventorySaleList, skuId, currentInventorySale);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
                collect(row);
            }
        } else {
            //处理没有查询到数据的情况下发remain_quantity=0的情况，分场景下发
            resetInventoryMetric(currentInventorySale).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
                collect(row);
            });
        }
    }


    private static List<String> dealWithInventorySaleList(List<InventorySale> inventorySaleList, Long skuId, InventorySale currentInventorySale) {
        return dealWithInventorySaleListCommon(inventorySaleList, skuId, currentInventorySale);
    }

    /**
     * 聚合计算逻辑处理.
     *
     * @param inventorySaleList InventorySale集合
     */
    private static List<String> dealWithInventorySaleListCommon(List<InventorySale> inventorySaleList, Long skuId, InventorySale currentInventorySale) {
        List<String> resultListCommon = new ArrayList<>();

        /*1、 计算sku维度下的最低出价、剩余库存、商家id集合*/
        //获取最低价对应的库存明细
        Optional<InventorySale> skuMinPriceInventory = inventorySaleList.stream().min((o1, o2) -> (int) (o1.price - o2.price));
        //sku最低价
        long skuMinPrice = skuMinPriceInventory.get().getPrice();
        //sku的剩余库存
        Integer skuRemainQuantity = inventorySaleList.stream().map(o -> o.remainQuantity).reduce(0, Integer::sum);
        // 计算sku最低出价的商家,加一层去重逻辑
        Optional<String> skuMinPriceWithSellerIds = inventorySaleList.stream().filter(item -> item.getPrice() == skuMinPrice).map(o -> String.valueOf(o.sellerId)).distinct().reduce((partialString, element) -> partialString + "," + element);

        //2、按照sku_id维度聚合只计算所有价格等于最低出价的
        Map<Long, Integer> remainQuantityMap1MinPrice = inventorySaleList.stream().filter(item -> item.getPrice() == skuMinPrice).collect(Collectors.groupingBy(InventorySale::getSkuId, Collectors.summingInt(InventorySale::getRemainQuantity)));

        Map<String, Object> map1 = new HashMap<>();
        map1.put("sku_id", skuId);
        map1.put("remain_inventory", remainQuantityMap1MinPrice.get(skuId));
        map1.put("min_sell_price", skuMinPrice);
        map1.put("seller_ids", skuMinPriceWithSellerIds.get());
        map1.put("global_now_time", getNowTime());
        map1.put("metric_name", "with_no_fun_box_special_sku_dim_by_min_price");
        resultListCommon.add(JSON.toJSONString(map1));
        // 回收
        map1.clear();

        //2.2、计算sku维度所有出价的
        Map<String, Object> map2 = new HashMap<>();
        map2.put("sku_id", skuId);
        map2.put("remain_inventory", skuRemainQuantity);
        map2.put("min_sell_price", skuMinPrice);
        map2.put("seller_ids", skuMinPriceWithSellerIds.get());
        map2.put("global_now_time", getNowTime());
        map2.put("metric_name", "with_no_fun_box_common_sku_dim");
        resultListCommon.add(JSON.toJSONString(map2));
        // 回收
        map2.clear();

        //3. 过滤出当前渠道的 sku,bid_type 的最低出价、剩余库存、商家id集合
        Integer remainInvQuc = inventorySaleList.stream().filter(inv -> inv.biddingType == currentInventorySale.biddingType).collect(Collectors.summingInt(InventorySale::getRemainQuantity));
        //当前渠道的最低价对应的商家集合
        if (remainInvQuc == null || remainInvQuc == 0) {
            // 空状态补0操作
            Map<String, Object> tempMapZero = new HashMap<>();
            tempMapZero.put("sku_id", currentInventorySale.getSkuId());
            tempMapZero.put("bidding_type", currentInventorySale.getBiddingType());
            tempMapZero.put("remain_inventory", 0);
            tempMapZero.put("min_sell_price", 0);
            tempMapZero.put("seller_ids", null);
            tempMapZero.put("global_now_time", getNowTime());
            tempMapZero.put("metric_name", "with_no_fun_box_common_sku_and_bid_type_dim");
            resultListCommon.add(JSON.toJSONString(tempMapZero));
            tempMapZero.clear();
        } else {
            Optional<InventorySale> currSkuAndBiddingMinPrice = inventorySaleList.stream().filter(inv -> inv.biddingType == currentInventorySale.biddingType).collect(Collectors.minBy((o1, o2) -> (int) (o1.price - o2.price)));
            //过滤当前渠道的最低价对应的商家
            Optional<String> skuAndBiddingTypOptionSellerIds = inventorySaleList.stream().filter(item -> item.getPrice() == currSkuAndBiddingMinPrice.get().getPrice() && item.getBiddingType() == currentInventorySale.biddingType).distinct().collect(Collectors.mapping(o -> String.valueOf(o.getSellerId()), Collectors.reducing((partialString, element) -> partialString + "," + element)));
            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("sku_id", currentInventorySale.getSkuId());
            tempMap.put("bidding_type", currentInventorySale.getBiddingType());
            tempMap.put("remain_inventory", remainInvQuc);
            tempMap.put("min_sell_price", currSkuAndBiddingMinPrice.get().getPrice());
            tempMap.put("seller_ids", skuAndBiddingTypOptionSellerIds.get());
            tempMap.put("global_now_time", getNowTime());
            tempMap.put("metric_name", "with_no_fun_box_common_sku_and_bid_type_dim");
            resultListCommon.add(JSON.toJSONString(tempMap));
            tempMap.clear();
        }

//        4. 过滤出 sku,bid_type,seller_id 的最低价和库存数
        Optional<InventorySale> mercSkuBidMinPriceOpt = inventorySaleList.stream().filter(inv -> currentInventorySale.getSellerId() == inv.getSellerId() && inv.biddingType == currentInventorySale.biddingType).min((o1, o2) -> (int) (o1.price - o2.price));
        if (mercSkuBidMinPriceOpt.isPresent()) {
            Integer mercSkuBidRemainQuantity = inventorySaleList.stream().filter(inv -> currentInventorySale.getSellerId() == inv.getSellerId() && inv.biddingType == currentInventorySale.biddingType).map(o -> o.remainQuantity).reduce(0, Integer::sum);
            Map<String, Object> tempMercSkuBidMap = new HashMap<>();
            tempMercSkuBidMap.put("sku_id", currentInventorySale.getSkuId());
            tempMercSkuBidMap.put("bidding_type", currentInventorySale.getBiddingType());
            tempMercSkuBidMap.put("seller_id", currentInventorySale.sellerId);
            tempMercSkuBidMap.put("remain_inventory", mercSkuBidRemainQuantity);
            tempMercSkuBidMap.put("min_sell_price", mercSkuBidMinPriceOpt.get().getPrice());
            tempMercSkuBidMap.put("global_now_time", getNowTime());
            tempMercSkuBidMap.put("metric_name", "with_no_fun_box_merc_sku_and_bid_type_dim");

            resultListCommon.add(JSON.toJSONString(tempMercSkuBidMap));
            tempMercSkuBidMap.clear();

        } else {
            Map<String, Object> tempMercSkuBidMap = new HashMap<>();
            tempMercSkuBidMap.put("sku_id", currentInventorySale.getSkuId());
            tempMercSkuBidMap.put("bidding_type", currentInventorySale.getBiddingType());
            tempMercSkuBidMap.put("seller_id", currentInventorySale.sellerId);
            tempMercSkuBidMap.put("remain_inventory", 0);
            tempMercSkuBidMap.put("min_sell_price", 0);
            tempMercSkuBidMap.put("invalid_flag", 1);//无效标识
            tempMercSkuBidMap.put("global_now_time", getNowTime());
            tempMercSkuBidMap.put("metric_name", "with_no_fun_box_merc_sku_and_bid_type_dim");

            resultListCommon.add(JSON.toJSONString(tempMercSkuBidMap));
            tempMercSkuBidMap.clear();

        }

//        5.现货渠道下的sku最低价、库存数 -- 现货: 0,3,5,6,7,12,13,14,20,21,22,23,24,25,26,27,28,29
        Optional<InventorySale> spotChannelSaleOpt = inventorySaleList.stream().filter(
                item -> item.getBiddingType() == 0 ||
                        item.getBiddingType() == 3 ||
                        item.getBiddingType() == 5 ||
                        item.getBiddingType() == 6 ||
                        item.getBiddingType() == 7 ||
                        item.getBiddingType() == 12 ||
                        item.getBiddingType() == 13 ||
                        item.getBiddingType() == 14 ||
                        item.getBiddingType() == 20 ||
                        item.getBiddingType() == 21 ||
                        item.getBiddingType() == 22 ||
                        item.getBiddingType() == 23 ||
                        item.getBiddingType() == 24 ||
                        item.getBiddingType() == 25 ||
                        item.getBiddingType() == 26 ||
                        item.getBiddingType() == 27 ||
                        item.getBiddingType() == 28 ||
                        item.getBiddingType() == 29
        ).min((o1, o2) -> (int) (o1.price - o2.price));
        if (spotChannelSaleOpt.isPresent()) {
            Integer invSaleNum = inventorySaleList.stream().filter(
                    item -> item.getBiddingType() == 0 ||
                            item.getBiddingType() == 3 ||
                            item.getBiddingType() == 5 ||
                            item.getBiddingType() == 6 ||
                            item.getBiddingType() == 7 ||
                            item.getBiddingType() == 12 ||
                            item.getBiddingType() == 13 ||
                            item.getBiddingType() == 14 ||
                            item.getBiddingType() == 20 ||
                            item.getBiddingType() == 21 ||
                            item.getBiddingType() == 22 ||
                            item.getBiddingType() == 23 ||
                            item.getBiddingType() == 24 ||
                            item.getBiddingType() == 25 ||
                            item.getBiddingType() == 26 ||
                            item.getBiddingType() == 27 ||
                            item.getBiddingType() == 28 ||
                            item.getBiddingType() == 29
            ).map(o -> o.remainQuantity).reduce(0, Integer::sum);

            Map<String, Object> spotChannelMap = new HashMap<>();
            spotChannelMap.put("sku_id", skuId);
            spotChannelMap.put("remain_inventory", invSaleNum);
            spotChannelMap.put("min_sell_price", spotChannelSaleOpt.get().getPrice());
            spotChannelMap.put("global_now_time", getNowTime());
            spotChannelMap.put("metric_name", "with_no_fun_box_spot_channel_sku_dim");
            resultListCommon.add(JSON.toJSONString(spotChannelMap));

        } else {
            Map<String, Object> spotChannelMap = new HashMap<>();
            spotChannelMap.put("sku_id", skuId);
            spotChannelMap.put("remain_inventory", 0);
            spotChannelMap.put("min_sell_price", 0);
            spotChannelMap.put("global_now_time", getNowTime());
            spotChannelMap.put("metric_name", "with_no_fun_box_spot_channel_sku_dim");
            resultListCommon.add(JSON.toJSONString(spotChannelMap));
        }

        return resultListCommon;
    }

    /**
     * 重置指标 废弃
     *
     * @return
     */
    private static List<String> resetInventoryMetric(InventorySale currentInventorySale) {
        List<String> resetMetricList = new ArrayList<>();
        Map<String, Object> map1 = new HashMap<>();
        map1.put("sku_id", currentInventorySale.getSkuId());
        map1.put("remain_inventory", 0);
        map1.put("min_sell_price", 0);
        map1.put("seller_ids", null);
        map1.put("global_now_time", getNowTime());
        map1.put("metric_name", "with_no_fun_box_special_sku_dim_by_min_price");
        resetMetricList.add(JSON.toJSONString(map1));

        Map<String, Object> map2 = new HashMap<>();
        map2.put("sku_id", currentInventorySale.getSkuId());
        map2.put("remain_inventory", 0);
        map2.put("min_sell_price", 0);
        map2.put("seller_ids", null);
        map2.put("metric_name", "with_no_fun_box_common_sku_dim");
        map2.put("global_now_time", getNowTime());
        resetMetricList.add(JSON.toJSONString(map2));

        Map<String, Object> tempMap = new HashMap<>();
        tempMap.put("sku_id", currentInventorySale.getSkuId());
        tempMap.put("bidding_type", currentInventorySale.getBiddingType());
        tempMap.put("remain_inventory", 0);
        tempMap.put("min_sell_price", 0);
        tempMap.put("seller_ids", null);
        tempMap.put("global_now_time", getNowTime());
        tempMap.put("metric_name", "with_no_fun_box_common_sku_and_bid_type_dim");
        resetMetricList.add(JSON.toJSONString(tempMap));

        Map<String, Object> tempMercSkuBidMap = new HashMap<>();
        tempMercSkuBidMap.put("sku_id", currentInventorySale.getSkuId());
        tempMercSkuBidMap.put("bidding_type", currentInventorySale.getBiddingType());
        tempMercSkuBidMap.put("seller_id", currentInventorySale.sellerId);
        tempMercSkuBidMap.put("remain_inventory", 0);
        tempMercSkuBidMap.put("min_sell_price", 0);
        tempMercSkuBidMap.put("invalid_flag", 1);//无效标识
        tempMercSkuBidMap.put("global_now_time", getNowTime());
        tempMercSkuBidMap.put("metric_name", "with_no_fun_box_merc_sku_and_bid_type_dim");
        resetMetricList.add(JSON.toJSONString(tempMercSkuBidMap));

        Map<String, Object> spotChannelMap = new HashMap<>();
        spotChannelMap.put("sku_id", currentInventorySale.getSkuId());
        spotChannelMap.put("remain_inventory", 0);
        spotChannelMap.put("min_sell_price", 0);
        spotChannelMap.put("global_now_time", getNowTime());
        spotChannelMap.put("metric_name", "with_no_fun_box_spot_channel_sku_dim");
        resetMetricList.add(JSON.toJSONString(spotChannelMap));

        map1.clear();
        map2.clear();
        tempMap.clear();
        tempMercSkuBidMap.clear();
        spotChannelMap.clear();
        return resetMetricList;
    }

    /**
     * 获取inventorySaleList.
     *
     * @param skuId sku_id
     * @return List<InventorySale>
     * @throws Exception 异常抛出
     */
    private static List<InventorySale> getInventorySaleList(long skuId) throws Exception {
        String sql = "SELECT id, `price`,`bidding_type`,  `quantity` - `use_quantity` as `remain_quantity`,uid as seller_id  FROM `inventory_sale_with_no_fun_box` where `inv_status` = 1 and `sku_id`=" + skuId;
        List<InventorySale> inventorySaleList = new ArrayList<>();

        PreparedStatement pst = conn.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            InventorySale inventorySale = InventorySale.builder()
                    .price(rs.getLong("price"))
                    .biddingType(rs.getInt("bidding_type"))
                    .remainQuantity(rs.getInt("remain_quantity"))
                    .skuId(skuId)
                    .sellerId(rs.getLong("seller_id"))
                    .build();

            inventorySaleList.add(inventorySale);
        }


        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }


        return inventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }


    /**
     * 获取微秒时间
     *
     * @return String
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }

    @Data
    @Builder
    static class InventorySale {
        //主键id
        private long id;
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
        //uid 卖家id
        private long sellerId;
        //状态
        private int status;
    }

    @Data
    @AllArgsConstructor
    static class SkuIdAndBiddingType {
        //出价类型
        private int biddingType;
        //sku_id
        private long skuId;
    }


    public static void main(String[] args) throws Exception {

        UnboxingBidInventoryAggMetrics inventoryAggMetrics = new UnboxingBidInventoryAggMetrics();

//		 如果需要准确计算 传主键id，skuId，（ `quantity` - `use_quantity`）remain_quantity,bidding_type,uid,status,price,为了方便需要传json包含这些字段即可
        String data = "{\"id\":\"102747345253\",\"sku_id\":\"630976621\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"1655531055\",\"bidding_type\":\"14\",\"status\":\"10\"}";

        inventoryAggMetrics.eval(data);


    }
}
