package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 最低出价指标
 * sku,spu下的最低出价,因上游使用延迟binlog，原udf功能已经不适用
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class SkuAndSpuMinPriceMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(SkuAndSpuMinPriceMetrics.class);
    private static Connection conn;

    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*******************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_flink");
        String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");
        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        List<SellMinPrice> rdsSellMinPriceList;

        if (!JSONObject.isValidObject(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        SellMinPrice currentSellMinPrice = SellMinPrice.builder()
                .id(currentInventoryObject.getLong("id"))
                .spu_id(currentInventoryObject.getLong("spu_id"))
                .sku_id(currentInventoryObject.getLong("sku_id"))
                .price(currentInventoryObject.getLong("price"))
                .status(currentInventoryObject.getInteger("status"))
                .build();

        rdsSellMinPriceList = getSellMinPriceList(currentSellMinPrice);
        if (rdsSellMinPriceList.size() != 0) {
            List<String> list = dealWithSellMinPriceList(rdsSellMinPriceList, currentSellMinPrice);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
                collect(row);
            }
        } else {
            resetMinPriceMetric(currentSellMinPrice).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
                collect(row);
            });
        }
    }

    /**
     * 处理核心函数
     *
     * @param rdsSellMinPriceList
     * @param currentSellMinPrice
     * @return list
     */
    private static List<String> dealWithSellMinPriceList(List<SellMinPrice> rdsSellMinPriceList, SellMinPrice currentSellMinPrice) {
        List<String> resultListCommon = new ArrayList<>();
        Optional<SellMinPrice> spuMinPriceOption = rdsSellMinPriceList.stream().min((o1, o2) -> (int) (o1.price - o2.price));
        //1。 spu 级别的最低出价
        if (spuMinPriceOption.isPresent()) {
            long spuMinPrice = spuMinPriceOption.get().getPrice();
            Map<String, Object> map1 = new HashMap<>();
            map1.put("spu_id", spuMinPriceOption.get().getSpu_id());
            map1.put("min_sell_price", spuMinPrice);
            map1.put("status", 0);
            map1.put("metric_name", "common_spu_dim_sell_min_price");
            resultListCommon.add(JSON.toJSONString(map1));
        }

//		2。计算sku的最低价格
        Map<Long, Optional<SellMinPrice>> collectSkuMinPrice = rdsSellMinPriceList.stream().collect(Collectors.groupingBy(p -> p.getSku_id(), Collectors.minBy((o1, o2) -> (int) (o1.price - o2.price))));
        // 只发送当前sku的变动最低价
        if (collectSkuMinPrice.containsKey(currentSellMinPrice.getSku_id())) {
            Optional<SellMinPrice> sellMinPrice = collectSkuMinPrice.get(currentSellMinPrice.getSku_id());
            long skuMinprice = sellMinPrice.get().getPrice();
            Map<String, Object> map = new HashMap<>();
            map.put("spu_id", currentSellMinPrice.getSpu_id());
            map.put("sku_id", currentSellMinPrice.getSku_id());
            map.put("min_sell_price", skuMinprice);
            map.put("status", 0);
            map.put("metric_name", "common_sku_dim_sell_min_price");
            resultListCommon.add(JSON.toJSONString(map));
        } else {
            // 不包含的情况下需要将当前sku的最低出价置为 无效
            Map<String, Object> map = new HashMap<>();
            map.put("spu_id", currentSellMinPrice.getSpu_id());
            map.put("sku_id", currentSellMinPrice.getSku_id());
            //过期数据
            map.put("status", 1);
            map.put("metric_name", "common_sku_dim_sell_min_price");
            resultListCommon.add(JSON.toJSONString(map));
        }
        return resultListCommon;
    }


    /**
     * 获取有效最低出价集合.
     *
     * @return List<SellMinPrice>
     * @throws Exception 异常抛出
     */
    private static List<SellMinPrice> getSellMinPriceList(SellMinPrice currentSellMinPrice) throws Exception {
        List<SellMinPrice> inventorySaleList = new ArrayList<>();

        //有效状态下的最低出价数据
        PreparedStatement pst = conn.prepareStatement("SELECT id, `spu_id`,`sku_id`, price  FROM `sell_sku_min_price` where `spu_id`=" + currentSellMinPrice.spu_id + " and `sync_status` =0");
        ResultSet rs = pst.executeQuery();

        while (rs.next()) {
            SellMinPrice inventorySale = SellMinPrice.builder()
                    .id(rs.getLong("id"))
                    .spu_id(rs.getLong("spu_id"))
                    .sku_id(rs.getLong("sku_id"))
                    .price(rs.getLong("price"))
                    .status(0)
                    .build();

            inventorySaleList.add(inventorySale);

        }

        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }

        return inventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }

    @Data
    @Builder
    static class SellMinPrice {
        private long id;
        private long spu_id;
        private long sku_id;
        private long price;
        // 0 表示有效 1 表示无效
        private int status;
    }


    /**
     * 重置无数据状态
     *
     * @param sellMinPrice
     * @return
     */
    private static List<String> resetMinPriceMetric(SellMinPrice sellMinPrice) {
        List<String> resetMetricList = new ArrayList<>();
        //spu的无效 将status置1 表示无效
        Map<String, Object> map1 = new HashMap<>();
        map1.put("spu_id", sellMinPrice.getSpu_id());
        map1.put("status", 1);
        map1.put("metric_name", "common_spu_dim_sell_min_price");
        resetMetricList.add(JSON.toJSONString(map1));

        // sku 下的最低出价是空情况
        Map<String, Object> map = new HashMap<>();
        map.put("spu_id", sellMinPrice.getSpu_id());
        map.put("sku_id", sellMinPrice.getSku_id());
        //过期数据
        map.put("status", 1);
        map.put("metric_name", "common_sku_dim_sell_min_price");
        resetMetricList.add(JSON.toJSONString(map));

        return resetMetricList;
    }

    public static void main(String[] args) throws Exception {
        String data = "{\"id\":\"3\",\"sku_id\":\"65328631\",\"price\":\"22900\",\"spu_id\":\"10889\",\"status\":\"1\"}";
        new SkuAndSpuMinPriceMetrics().eval(data);

    }
}
