package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.*;
import java.util.stream.Collectors;


import com.alibaba.blink.udx.udtf.InvExcludeMarketingEventsMetrics.InventorySaleBase;
import com.alibaba.blink.udx.udtf.InvExcludeMarketingEventsMetrics.DomesticInventorySaleBase;


public class CalcInvMetricUtils {
    /**
     * 核心指标计算层
     *
     * @param inventorySaleBaseList
     * @param currentInventorySaleBase
     * @param type
     * @return
     */
    public static List<String> dealWithInventorySaleBaseListCommon(List<DomesticInventorySaleBase> inventorySaleBaseList, InventorySaleBase currentInventorySaleBase, String type) {
        List<String> resultListCommon = new ArrayList<>();
        //A1. Spu维度指标s
        Optional<? extends InventorySaleBase> spuMinPriceOpt = inventorySaleBaseList.stream().min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
        if (spuMinPriceOpt.isPresent()) {
            long spuMinPrice = spuMinPriceOpt.get().getFinalPrice();
            long spuMaxPrice = inventorySaleBaseList.stream().max((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice())).get().getFinalPrice();
            Integer spuRemainQuantity = inventorySaleBaseList.stream().map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
            resultListCommon.add(calcPlatformSpuMetric(currentInventorySaleBase.getSpuId(), spuMinPrice, spuMaxPrice, spuRemainQuantity, type));
        } else {
            resultListCommon.add(resetcalcPlatformSpuMetric(currentInventorySaleBase.getSpuId(), type));
        }

        //A2. spu+商家 维度指标
        Optional<? extends InventorySaleBase> mercSpuMinPriceOpt = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId()).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
        if (mercSpuMinPriceOpt.isPresent()) {
            long mercSpuMinPrice = mercSpuMinPriceOpt.get().getFinalPrice();
            long mercSpuMaxPrice = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId()).max((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice())).get().getFinalPrice();
            Integer mercSpuRemainQuantity = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId()).map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
            resultListCommon.add(calcPlatformSpuAndMercMetric(currentInventorySaleBase.getSpuId(), currentInventorySaleBase.getSellerId(), mercSpuMinPrice, mercSpuMaxPrice, mercSpuRemainQuantity, type));
        } else {
            resultListCommon.add(resetCalcPlatformSpuAndMercMetric(currentInventorySaleBase.getSpuId(), currentInventorySaleBase.getSellerId(), type));
        }

        //A3. spu+渠道 维度指标
        Optional<? extends InventorySaleBase> bidSpuMinPriceOpt = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getBiddingType() == inv.getBiddingType()).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
        if (bidSpuMinPriceOpt.isPresent()) {
            long bidSpuMinPrice = bidSpuMinPriceOpt.get().getFinalPrice();
            long bidSpuMaxPrice = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getBiddingType() == inv.getBiddingType()).max((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice())).get().getFinalPrice();
            Integer bidSpuRemainQuantity = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getBiddingType() == inv.getBiddingType()).map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
            resultListCommon.add(calcPlatformSpuAndBidMetric(currentInventorySaleBase.getSpuId(), currentInventorySaleBase.getBiddingType(), bidSpuMinPrice, bidSpuMaxPrice, bidSpuRemainQuantity, type));
        } else {
            //invalid 当前渠道下架操作
            resultListCommon.add(resetCalcPlatformSpuAndBidMetric(currentInventorySaleBase.getSpuId(), currentInventorySaleBase.getBiddingType(), type));
        }

//        ========================新增当前sku变动部分===============================
        List<DomesticInventorySaleBase> skuInventorySaleBaseList = inventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSkuId() == inv.getSkuId()).collect(Collectors.toList());

        if (skuInventorySaleBaseList.size() > 0) {
            //B1. sku 维度
            Optional<DomesticInventorySaleBase> skuMinPrice = skuInventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSkuId() == inv.getSkuId()).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
            if (skuMinPrice.isPresent()) {
                //sku的剩余库存
                Integer skuRemainQuantity = skuInventorySaleBaseList.stream().map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
                // 计算sku最低出价的商家,加一层去重逻辑
                String skuMinPriceWithSellerIds = skuInventorySaleBaseList.stream().filter(item -> item.getFinalPrice() == skuMinPrice.get().getFinalPrice()).map(o -> String.valueOf(o.getSellerId())).distinct().reduce((partialString, element) -> partialString + "," + element).get();

                resultListCommon.add(calcPlatformSkuMetric(currentInventorySaleBase.getSkuId(), skuMinPrice.get().getFinalPrice(), skuRemainQuantity, skuMinPriceWithSellerIds, type));
            } else {
                // sku 下架状态
                resultListCommon.add(resetCalcPlatformSkuMetric(currentInventorySaleBase.getSkuId(), type));
            }

            //B2. sku+渠道
            Optional<DomesticInventorySaleBase> skuAndbidMinPrice = skuInventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getBiddingType() == inv.getBiddingType()).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
            if (skuAndbidMinPrice.isPresent()) {
                Integer remainInvQuc = skuInventorySaleBaseList.stream().filter(inv -> inv.getBiddingType() == currentInventorySaleBase.getBiddingType()).collect(Collectors.summingInt(DomesticInventorySaleBase::getRemainQuantity));
                String skuAndBiddingTypSellerIds = skuInventorySaleBaseList.stream().filter(item -> item.getFinalPrice() == skuAndbidMinPrice.get().getFinalPrice() && item.getBiddingType() == currentInventorySaleBase.getBiddingType()).distinct().collect(Collectors.mapping(o -> String.valueOf(o.getSellerId()), Collectors.reducing((partialString, element) -> partialString + "," + element))).get();
                resultListCommon.add(calcPlatformSkuAndBidMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getBiddingType(), skuAndbidMinPrice.get().getFinalPrice(), remainInvQuc, skuAndBiddingTypSellerIds, type));
            } else {
                resultListCommon.add(resetCalcPlatformSkuAndBidMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getBiddingType(), type));
            }

            //B3. sku+商家
            Optional<DomesticInventorySaleBase> skuAndMercMinPrice = skuInventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId()).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
            if (skuAndMercMinPrice.isPresent()) {
                Integer skuAndMercRemainInvQuc = skuInventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId()).collect(Collectors.summingInt(DomesticInventorySaleBase::getRemainQuantity));
                resultListCommon.add(calcPlatformSkuAndMercMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getSellerId(), skuAndMercMinPrice.get().getFinalPrice(), skuAndMercRemainInvQuc, type));

            } else {
                resultListCommon.add(resetCalcPlatformSkuAndMercMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getSellerId(), type));
            }

            //B4. sku,bid_type,seller_id  维度

            Optional<DomesticInventorySaleBase> skuMercAndBidMinPrice = skuInventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId() && currentInventorySaleBase.getBiddingType() == inv.getBiddingType()).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
            if (skuMercAndBidMinPrice.isPresent()) {
                Integer skuMercAndBidRemainInvQuc = skuInventorySaleBaseList.stream().filter(inv -> currentInventorySaleBase.getSellerId() == inv.getSellerId() && currentInventorySaleBase.getBiddingType() == inv.getBiddingType()).collect(Collectors.summingInt(DomesticInventorySaleBase::getRemainQuantity));
                resultListCommon.add(calcPlatformSkuBidTypeAndMercMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getSellerId(), currentInventorySaleBase.getBiddingType(), skuMercAndBidMinPrice.get().getFinalPrice(), skuMercAndBidRemainInvQuc, type));
            } else {
                resultListCommon.add(resetCalcPlatformSkuBidTypeAndMercMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getSellerId(), currentInventorySaleBase.getBiddingType(), type));

            }


            // 限定国内渠道指标
            if (type.equals("domestic_pured")) {

                //B5. sku现货(0,7,12)
                if ((currentInventorySaleBase.getBiddingType() == 0 || currentInventorySaleBase.getBiddingType() == 7 || currentInventorySaleBase.getBiddingType() == 12)) {
                    Optional<DomesticInventorySaleBase> skuSpotBidMinPricre = skuInventorySaleBaseList.stream().filter(inv -> (inv.getBiddingType() == 0 || inv.getBiddingType() == 7 || inv.getBiddingType() == 12)).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
                    if (skuSpotBidMinPricre.isPresent()) {
                        Integer skuSpotBidsRemainInvQuc = skuInventorySaleBaseList.stream().filter(inv -> (inv.getBiddingType() == 0 || inv.getBiddingType() == 7 || inv.getBiddingType() == 12)).collect(Collectors.summingInt(DomesticInventorySaleBase::getRemainQuantity));
                        resultListCommon.add(calcPlatformSkuSpotBidsAndMetric(currentInventorySaleBase.getSkuId(), skuSpotBidMinPricre.get().getFinalPrice(), skuSpotBidsRemainInvQuc, type));
                    } else {
                        resultListCommon.add(resetCalcPlatformSkuSpotBidsAndMetric(currentInventorySaleBase.getSkuId(), type));
                    }
                }


                //B6. sku寄售(5,6,13)
                if ((currentInventorySaleBase.getBiddingType() == 5 || currentInventorySaleBase.getBiddingType() == 6 || currentInventorySaleBase.getBiddingType() == 13)) {
                    Optional<DomesticInventorySaleBase> consignmentBidMinPricre = skuInventorySaleBaseList.stream().filter(inv -> (inv.getBiddingType() == 5 || inv.getBiddingType() == 6 || inv.getBiddingType() == 13)).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
                    if (consignmentBidMinPricre.isPresent()) {
                        Integer consignmentBidsRemainInvQuc = skuInventorySaleBaseList.stream().filter(inv -> (inv.getBiddingType() == 5 || inv.getBiddingType() == 6 || inv.getBiddingType() == 13)).collect(Collectors.summingInt(DomesticInventorySaleBase::getRemainQuantity));
                        resultListCommon.add(calcPlatformConsignmentBidsMetric(currentInventorySaleBase.getSkuId(), consignmentBidMinPricre.get().getFinalPrice(), consignmentBidsRemainInvQuc, type));
                    } else {
                        resultListCommon.add(resetCalcPlatformConsignmentBidsMetric(currentInventorySaleBase.getSkuId(), type));
                    }
                }

            }

            // 无限免单商品上新 Unlimited
            if (type.equals("common_pured")) {
                //B7. @张严文 多渠道下的sku最低价、库存数 -- 现货: 0,3,5,6,7,12,13,14,20,21,22,23,24,25,26,27,28,29
                List<Integer> validBiddingTypes = Arrays.asList(0, 3, 5, 6, 7, 12, 13, 14, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29);
                if (validBiddingTypes.contains(currentInventorySaleBase.getBiddingType())) {
                    Optional<DomesticInventorySaleBase> spotChannelSaleOpt = skuInventorySaleBaseList.stream().filter(item -> validBiddingTypes.contains(item.getBiddingType())).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));
                    if (spotChannelSaleOpt.isPresent()) {
                        Integer invSaleNum = skuInventorySaleBaseList.stream().filter(item -> validBiddingTypes.contains(item.getBiddingType())).map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
                        resultListCommon.add(calcPlatformSkuUnlimitedSpotBidsMetric(currentInventorySaleBase.getSkuId(), spotChannelSaleOpt.get().getFinalPrice(), invSaleNum, type));
                    } else {
                        resultListCommon.add(resetCalcPlatformSkuUnlimitedSpotBidsMetric(currentInventorySaleBase.getSkuId(), type));
                    }
                }

            }
        }


        //回收
        skuInventorySaleBaseList.clear();
        inventorySaleBaseList.clear();

        return resultListCommon;
    }

    /**
     * 无限免单商品上新 Unlimited 出价指标
     *
     * @param skuId
     * @param finalPrice
     * @param invSaleNum
     * @param type
     * @return
     */
    private static String calcPlatformSkuUnlimitedSpotBidsMetric(long skuId, long finalPrice, Integer invSaleNum, String type) {
        String metricName = String.format("%s_sku_unlimited_spot_bids_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", finalPrice);
        json.put("remain_inventory", invSaleNum);
        json.put("invalid_flag", 0);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("bidding_types", "0,3,5,6,7,12,13,14,20,21,22,23,24,25,26,27,28,29");
        json.put("dims", "sku_id,bidding_types");
        return JSON.toJSONString(json);
    }

    static String resetCalcPlatformSkuUnlimitedSpotBidsMetric(long skuId, String type) {
        String metricName = String.format("%s_sku_unlimited_spot_bids_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("bidding_types", "0,3,5,6,7,12,13,14,20,21,22,23,24,25,26,27,28,29");
        json.put("dims", "sku_id,bidding_types");
        return JSON.toJSONString(json);
    }


    /**
     * sku寄售渠道(5,6,13)
     *
     * @param skuId
     * @param finalPrice
     * @param skuSpotBidsRemainInvQuc
     * @param type
     * @return
     */
    private static String calcPlatformConsignmentBidsMetric(long skuId, long finalPrice, Integer skuSpotBidsRemainInvQuc, String type) {
        String metricName = String.format("%s_sku_consignment_bids_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", finalPrice);
        json.put("remain_inventory", skuSpotBidsRemainInvQuc);
        json.put("invalid_flag", 0);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("bidding_types", "5,6,13");
        json.put("dims", "sku_id,bidding_types");
        return JSON.toJSONString(json);
    }

    /**
     * sku寄售渠道(5,6,13),仅限制国内渠道计算,重置
     *
     * @param skuId
     * @param type
     * @return
     */
    static String resetCalcPlatformConsignmentBidsMetric(long skuId, String type) {
        String metricName = String.format("%s_sku_consignment_bids_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("bidding_types", "5,6,13");
        json.put("dims", "sku_id,bidding_types");
        return JSON.toJSONString(json);
    }


    /**
     * sku现货(0,7,12)
     *
     * @param skuId
     * @param finalPrice
     * @param skuSpotBidsRemainInvQuc
     * @param type
     * @return
     */
    private static String calcPlatformSkuSpotBidsAndMetric(long skuId, long finalPrice, Integer skuSpotBidsRemainInvQuc, String type) {
        String metricName = String.format("%s_sku_spot_bids_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", finalPrice);
        json.put("remain_inventory", skuSpotBidsRemainInvQuc);
        json.put("invalid_flag", 0);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("bidding_types", "0,7,12");
        json.put("dims", "sku_id,bidding_types");
        return JSON.toJSONString(json);
    }

    /**
     * 现货渠道最低出价计算,仅限制国内渠道计算
     *
     * @param skuId
     * @param type
     * @return
     */
    static String resetCalcPlatformSkuSpotBidsAndMetric(long skuId, String type) {
        String metricName = String.format("%s_sku_spot_bids_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("bidding_types", "0,7,12");
        json.put("dims", "sku_id,bidding_types");
        return JSON.toJSONString(json);
    }

    /**
     * sku、商家出价指标
     *
     * @param skuId
     * @param sellerId
     * @param finalPrice
     * @param skuAndMercRemainInvQuc
     * @param type
     * @return
     */
    private static String calcPlatformSkuBidTypeAndMercMetric(long skuId, long sellerId, int biddingType, long finalPrice, Integer skuAndMercRemainInvQuc, String type) {
        String metricName = String.format("%s_sku_bid_and_seller_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("seller_id", sellerId);
        json.put("bidding_type", biddingType);
        json.put("min_sell_price", finalPrice);
        json.put("remain_inventory", skuAndMercRemainInvQuc);
        json.put("invalid_flag", 0);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id,seller_id,bidding_type");

        return JSON.toJSONString(json);
    }

    /**
     * 重置sku、商家出价指标
     *
     * @param skuId
     * @param sellerId
     * @param type
     * @return
     */
    static String resetCalcPlatformSkuBidTypeAndMercMetric(long skuId, long sellerId, int biddingType, String type) {
        String metricName = String.format("%s_sku_bid_and_seller_inv_metric", type);
        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("seller_id", sellerId);
        json.put("bidding_type", biddingType);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id,seller_id,bidding_type");

        return JSON.toJSONString(json);
    }


    /**
     * sku、商家出价指标
     *
     * @param skuId
     * @param sellerId
     * @param finalPrice
     * @param skuAndMercRemainInvQuc
     * @param type
     * @return
     */
    private static String calcPlatformSkuAndMercMetric(long skuId, long sellerId, long finalPrice, Integer skuAndMercRemainInvQuc, String type) {
        String metricName = String.format("%s_sku_and_seller_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("seller_id", sellerId);
        json.put("min_sell_price", finalPrice);
        json.put("remain_inventory", skuAndMercRemainInvQuc);
        json.put("invalid_flag", 0);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id,seller_id");

        return JSON.toJSONString(json);
    }

    /**
     * 重置sku、商家出价指标
     *
     * @param skuId
     * @param sellerId
     * @param type
     * @return
     */
    static String resetCalcPlatformSkuAndMercMetric(long skuId, long sellerId, String type) {
        String metricName = String.format("%s_sku_and_seller_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("seller_id", sellerId);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//0 表示有效, 1表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id,seller_id");

        return JSON.toJSONString(json);
    }

    /**
     * sku 、渠道重置程序
     *
     * @param skuId
     * @param biddingType
     * @param type
     * @return
     */
    static String resetCalcPlatformSkuAndBidMetric(long skuId, int biddingType, String type) {
        String metricName = String.format("%s_sku_and_bid_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("bidding_type", biddingType);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//0 表示有效 1 表示无效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id,bidding_type");

        return JSON.toJSONString(json);
    }

    /**
     * sku、渠道最低价指标
     *
     * @param skuId
     * @param biddingType
     * @param finalPrice
     * @param remainInvQuc
     * @param skuAndBiddingTypSellerIds
     * @param type
     * @return
     */
    private static String calcPlatformSkuAndBidMetric(long skuId, int biddingType, long finalPrice, Integer remainInvQuc, String skuAndBiddingTypSellerIds, String type) {
        String metricName = String.format("%s_sku_and_bid_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("bidding_type", biddingType);
        json.put("min_sell_price", finalPrice);
        json.put("remain_inventory", remainInvQuc);
        json.put("invalid_flag", 0);//0 表示有效
        json.put("seller_ids", skuAndBiddingTypSellerIds);
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id,bidding_type");

        return JSON.toJSONString(json);
    }


    /**
     * 平台spu出价指标计算函数
     *
     * @param spuId
     * @param spuMinPrice
     * @param spuMaxPrice
     * @param spuRemainQuantity
     * @return
     */
    public static String calcPlatformSpuMetric(long spuId, long spuMinPrice, long spuMaxPrice, Integer spuRemainQuantity, String type) {
        String metricName = String.format("%s_spu_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("min_sell_price", spuMinPrice);
        json.put("max_sell_price", spuMaxPrice);
        json.put("remain_inventory", spuRemainQuantity);
        json.put("invalid_flag", 0);//0 表示有效

        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "spu_id");

        return JSON.toJSONString(json);
    }

    /**
     * 重置平台spu的计算函数
     *
     * @param spuId
     * @return
     */
    public static String resetcalcPlatformSpuMetric(long spuId, String type) {
        String metricName = String.format("%s_spu_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("min_sell_price", 0);
        json.put("max_sell_price", 0);
        json.put("remain_inventory", 0);

        json.put("invalid_flag", 1);//无效标识
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "spu_id");

        return JSON.toJSONString(json);
    }

    /**
     * 平台商家spu出价指标计算函数
     *
     * @param spuId
     * @param sellerId
     * @param mercSpuMinPrice
     * @param mercSpuMaxPrice
     * @param mercSpuRemainQuantity
     * @return
     */
    public static String calcPlatformSpuAndMercMetric(long spuId, long sellerId, long mercSpuMinPrice, long mercSpuMaxPrice, Integer mercSpuRemainQuantity, String type) {
        String metricName = String.format("%s_spu_and_seller_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("seller_id", sellerId);
        json.put("min_sell_price", mercSpuMinPrice);
        json.put("max_sell_price", mercSpuMaxPrice);
        json.put("remain_inventory", mercSpuRemainQuantity);
        json.put("invalid_flag", 0);//0 表示有效
        json.put("dims", "spu_id,seller_id");

        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);

        return JSON.toJSONString(json);
    }


    /**
     * 重置平台商家spu出价指标计算函数
     *
     * @param spuId
     * @param sellerId
     * @return
     */
    public static String resetCalcPlatformSpuAndMercMetric(long spuId, long sellerId, String type) {
        String metricName = String.format("%s_spu_and_seller_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("seller_id", sellerId);
        json.put("min_sell_price", 0);
        json.put("max_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("dims", "spu_id,seller_id");

        json.put("invalid_flag", 1);
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);

        return JSON.toJSONString(json);
    }


    /**
     * 平台渠道spu出价指标计算函数
     *
     * @param spuId
     * @param bidType
     * @param bidSpuMinPrice
     * @param bidSpuMaxPrice
     * @param bidSpuRemainQuantity
     * @return
     */
    public static String calcPlatformSpuAndBidMetric(long spuId, Integer bidType, long bidSpuMinPrice, long bidSpuMaxPrice, Integer bidSpuRemainQuantity, String type) {

        String metricName = String.format("%s_spu_and_bid_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("bidding_type", bidType);
        json.put("min_sell_price", bidSpuMinPrice);
        json.put("max_sell_price", bidSpuMaxPrice);
        json.put("remain_inventory", bidSpuRemainQuantity);
        json.put("invalid_flag", 0);//0 表示有效
        json.put("dims", "spu_id,bidding_type");

        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);

        return JSON.toJSONString(json);
    }

    /**
     * 重置平台渠道spu出价指标计算函数
     *
     * @param spuId
     * @param bidType
     * @return
     */
    public static String resetCalcPlatformSpuAndBidMetric(long spuId, Integer bidType, String type) {
        String metricName = String.format("%s_spu_and_bid_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("spu_id", spuId);
        json.put("bidding_type", bidType);
        json.put("min_sell_price", 0);
        json.put("max_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("dims", "spu_id,bidding_type");

        json.put("invalid_flag", 1);//无效标识
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);

        return JSON.toJSONString(json);
    }


    /**
     * sku 出价指标
     *
     * @param skuId
     * @param skuMinPrice
     * @param skuRemainQuantity
     * @param type
     * @return
     */
    public static String calcPlatformSkuMetric(long skuId, long skuMinPrice, Integer skuRemainQuantity, String skuMinPriceWithSellerIds, String type) {
        String metricName = String.format("%s_sku_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", skuMinPrice);
        json.put("seller_ids", skuMinPriceWithSellerIds);
        json.put("remain_inventory", skuRemainQuantity);
        json.put("invalid_flag", 0);//0 表示有效
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id");

        return JSON.toJSONString(json);
    }

    public static String resetCalcPlatformSkuMetric(long skuId, String type) {
        String metricName = String.format("%s_sku_inv_metric", type);

        JSONObject json = new JSONObject();
        json.put("sku_id", skuId);
        json.put("min_sell_price", 0);
        json.put("remain_inventory", 0);
        json.put("invalid_flag", 1);//无效标识
        json.put("global_now_time", getNowTime());
        json.put("metric_name", metricName);
        json.put("dims", "sku_id");

        return JSON.toJSONString(json);
    }


    /**
     * 纳秒时间
     *
     * @return
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }

}
