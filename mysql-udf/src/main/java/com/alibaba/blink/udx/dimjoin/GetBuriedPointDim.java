package com.alibaba.blink.udx.dimjoin;

import com.github.rholder.retry.*;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.HashMap;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

public class GetBuriedPointDim extends ScalarFunction {
    private static Logger LOG = LoggerFactory.getLogger(GetBuriedPointDim.class);

    private HashMap<String, HashMap<String, String>> cacheAll;
    private HashMap<String, Long> lastCacheAllTime;
    private String driverClassName;
    private String url;
    private String username;
    private String password;

    private Retryer retryer;

    /**
     * 数据库连接参数
     * @param context
     * @throws SQLException
     */
    @Override
    public void open(FunctionContext context) throws SQLException {
        driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        url = context.getJobParameter("jdbc.url", "******************************************************************************************************************************************************************");
        username = context.getJobParameter("jdbc.username", "du_bigdata");
        password = context.getJobParameter("jdbc.password", "W%eZIrLl%Huksx*B");
        cacheAll = new HashMap<>(32);
        lastCacheAllTime = new HashMap<>(32);

        //重试5次, 每次建个5S
        retryer = RetryerBuilder.newBuilder()
                .retryIfExceptionOfType(SQLException.class)
                .withStopStrategy(StopStrategies.stopAfterAttempt(5))
                .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
                .build();
    }

    /**
     * 数据库连接
     * @param driverClassName
     * @param url
     * @param username
     * @param password
     * @return
     * @throws SQLException
     */
    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        //数据库连接
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        Connection conn = DriverManager.getConnection(url, username,password);
        return conn;
    }

    /**
     * 带重试机制的cacheAll
     * @param tableName
     * @param pkName
     * @param valueName
     * @throws ExecutionException
     * @throws RetryException
     */
    private void cacheAllTryable(String tableName, String pkName, String valueName) throws ExecutionException, RetryException {
        Callable<Void> callable = () -> {
            cacheAll(tableName, pkName, valueName);
            return null;
        };

        retryer.call(callable);
    }

    /**
     * 获取数据库连接，缓存查数据库后的数据
     * @param tableName
     * @param pkName
     * @param valueName
     * @throws SQLException
     */
    private void cacheAll(String tableName, String pkName, String valueName) throws SQLException {
        HashMap<String, String> tableCache = initTableCache(tableName);
        Connection conn = getConnection(driverClassName,url,username,password);
        String sql = "select " + pkName + "," + valueName + " from " + tableName;
        PreparedStatement pstmt = conn.prepareStatement(sql);
        ResultSet resultSet = pstmt.executeQuery();
        while (resultSet.next()) {//只能存在唯一关系
            String pk = resultSet.getString(pkName);
            String value = resultSet.getString(valueName);
            tableCache.put(pk, value);
        }
        lastCacheAllTime.put(tableName, System.currentTimeMillis());
        if (conn != null){
            conn.close();
        }
    }

    /**
     * 初始化表
     * @param tableName
     * @return
     */
    private HashMap<String, String> initTableCache(String tableName) {
        if (cacheAll.containsKey(tableName)) {
            HashMap<String, String> tableCache = cacheAll.get(tableName);
            tableCache.clear();
            return tableCache;
        } else {
            HashMap<String, String> tableCache = new HashMap<>(2048);
            cacheAll.put(tableName, tableCache);
            return tableCache;
        }
    }

    /**
     * 查数据库，返回对应的字段
     * @param tableName  表名bp_page_dict_value_view
     * @param pkName     表中的值dict_value
     * @param pkValue    所要查询的字段值current_page
     * @param valueName  表中的值对应的字段名element_name
     * @return
     * @throws SQLException
     */
    public String eval(String tableName, String pkName, String pkValue, String valueName) throws ExecutionException, RetryException {
        if (!cacheAll.containsKey(tableName) || lastCacheAllTime.getOrDefault(tableName, 0L) == 0 || (System.currentTimeMillis() - lastCacheAllTime.getOrDefault(tableName, 0L) > 3600000)) {//cache 1小时
            cacheAllTryable(tableName, pkName, valueName);
        }
        HashMap<String, String> tableCache = cacheAll.get(tableName);
        return tableCache.get(pkValue);
    }

    @Override
    public void close() {
    }

    public static void main (String[] args) {

        HashMap<String, Long> lastCacheAllTime = new HashMap<>();
        GetBuriedPointDim getBuriedPointDim = new GetBuriedPointDim();
        try {
            getBuriedPointDim.lastCacheAllTime = lastCacheAllTime;
            getBuriedPointDim.cacheAll = new HashMap<String, HashMap<String, String>>();
            String res1 = getBuriedPointDim.eval("bp_block_dict_value_view","dict_value","1","element_name");
            String res2 = getBuriedPointDim.eval("bp_page_dict_value_view","dict_value","1","element_name");
            System.out.println(res1);
        }
        catch (Exception throwables) {
            throwables.printStackTrace();
        }
    }
}
