package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.sql.*;

/**
 * 通过下单的时间和strem_log_id获取对应切片id.
 * Author: <PERSON><PERSON><PERSON>
 */
@FunctionHint(
		input = {@DataTypeHint("BIGINT"), @DataTypeHint("BIGINT")},
        output = @DataTypeHint("BIGINT")
)
public class GetCommentateId extends ScalarFunction {

	private Connection conn;

	@Override
	public void open(FunctionContext context) throws Exception {
		String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
		String url = context.getJobParameter("jdbc.url", "******************************************************************************************************************************************************");
		String username = context.getJobParameter("jdbc.username", "du_flink");
		String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");

		conn = getConnection(driverClassName, url, username, password);
	}

	public Long eval(Long streamLogId, Long spuId) {

		try {
			return getCommentateId(streamLogId, spuId);
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}

	}

	/**
	 * 获取数据库连接.
	 */
	private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
		try {
			Class.forName(driverClassName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
		return DriverManager.getConnection(url, username, password);
	}

	/**
	 * 获取切片id.
	 */
	public Long getCommentateId(Long streamLogId, Long spuId) throws SQLException {
		String sql = "SELECT `id` as commentate_id FROM `live_product_commentate` WHERE `stream_log_id`= " + streamLogId + " and product_id = " + spuId + " and is_del = 0";
		PreparedStatement pst = conn.prepareStatement(sql);
		ResultSet rs = pst.executeQuery();

		if (rs.next()) {
			return rs.getLong("commentate_id");
		} else {
			return null;
		}
	}

	@Override
	public void close() throws Exception {
		if (conn != null) {
			conn.close();
		}
	}

}
