package com.alibaba.blink.udx.udtf.bid.oversea;

import com.alibaba.blink.udx.udtf.bid.oversea.OverseaInvExcludeMarketingEventsMetrics.ImportsInventorySaleBase;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


public class OverseaCalcInvMetricUtils {
    //进口出价数据
    private static final String IMPORTSPURED = "imports_pured";

    /**
     * 全局重置操作
     *
     * @param currentInventorySaleBase
     * @return
     */
    public static List<String> resetAllRegionInventoryMetric(ImportsInventorySaleBase currentInventorySaleBase) {
        List<String> resetMetricList = new ArrayList<>();
        //保税
        resetMetricList.add(calcBondedSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1));

        //hk
        resetMetricList.add(calcHKSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
        resetMetricList.add(calcHKSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
        resetMetricList.add(calcHKSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        //it
        resetMetricList.add(calcITSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
        resetMetricList.add(calcITSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
        resetMetricList.add(calcITSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        //jp
        resetMetricList.add(calcJPSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
        resetMetricList.add(calcJPSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
        resetMetricList.add(calcJPSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        //kr
        resetMetricList.add(calcKRSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
        resetMetricList.add(calcKRSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
        resetMetricList.add(calcKRSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        //us
        resetMetricList.add(calcUSSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
        resetMetricList.add(calcUSSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
        resetMetricList.add(calcUSSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));
        return resetMetricList;
    }

    /**
     * 核心指标
     *
     * @param inventorySaleBaseList
     * @param currentInventorySaleBase 当前待查询的出价信息
     * @return
     */
    public static List<String> dealOverSeaWithInventorySaleBaseListCommon(List<ImportsInventorySaleBase> inventorySaleBaseList, ImportsInventorySaleBase currentInventorySaleBase) {
        long skuId = currentInventorySaleBase.getSkuId();

        List<String> resultListCommon = new ArrayList<>();
//        A1: 国内保税 指标 getBiddingType:29,23,26
        Optional<ImportsInventorySaleBase> bondedSkuMinPriceOpt = inventorySaleBaseList.stream().filter(
                inv -> inv.getBiddingType() == 23 || inv.getBiddingType() == 26 || inv.getBiddingType() == 29
        ).min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice()));

        if (bondedSkuMinPriceOpt.isPresent()) {
            long minPrice = bondedSkuMinPriceOpt.get().getFinalPrice();

            Integer remainQuantity = inventorySaleBaseList.stream().filter(
                    inv -> inv.getBiddingType() == 23 || inv.getBiddingType() == 26 || inv.getBiddingType() == 29
            ).map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);

            resultListCommon.add(calcBondedSkuMetric(currentInventorySaleBase.getSkuId(), minPrice, remainQuantity, 0));
        } else {
            resultListCommon.add(calcBondedSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1));
        }


//        A2 : 'HK'区域 分为:寄售,现货,预售    merc_region in （"HK","TW","MO","SG","MY") then 'HK' and deliveryCountryCode<>'IT'
        List<String> HKList = Arrays.asList("HK", "TW", "MO", "SG", "MY");
        final List<ImportsInventorySaleBase> hkList = inventorySaleBaseList.stream().filter(r -> HKList.contains(r.getMercRegion()) && (!r.getDeliveryCountryCode().equals("IT"))).collect(Collectors.toList());
        if (hkList.size() > 0) {
            List<String> regionResList = calcCommonInvList(hkList, skuId, "HK");
            resultListCommon.addAll(regionResList);

            hkList.clear();
        } else {
            //需要重置  寄售,现货,预售 三个渠道的出价数据
            resultListCommon.add(calcHKSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
            resultListCommon.add(calcHKSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
            resultListCommon.add(calcHKSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));
        }


//        A3.欧洲区域出价 'IT'
//        新口径  when t1.delivery_country_code ='IT' or bidding_type in (21,27,28)
//                then 'IT'
        List<Integer> itBidTypes = Arrays.asList(21, 27, 28);
//        List<String> itMercRegions = Arrays.asList("IT", "DE", "RU");
        List<ImportsInventorySaleBase> ITInvList = inventorySaleBaseList.stream().filter(r ->
                (itBidTypes.contains(r.getBiddingType()) || (r.getDeliveryCountryCode().equals("IT")))

        ).collect(Collectors.toList());
        if (ITInvList.size() > 0) {
            List<String> regionResList = calcCommonInvList(ITInvList, skuId, "IT");
            resultListCommon.addAll(regionResList);

            ITInvList.clear();
        } else {
            //需要重置  寄售,现货,预售 三个渠道的出价数据
            resultListCommon.add(calcITSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
            resultListCommon.add(calcITSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
            resultListCommon.add(calcITSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));
        }


//       JP区域
        List<ImportsInventorySaleBase> JPInvList = inventorySaleBaseList.stream().filter(r ->
                r.getMercRegion().equals("JP") && (!r.getDeliveryCountryCode().equals("IT")) ).collect(Collectors.toList());
        if (JPInvList.size() > 0) {
            List<String> regionResList = calcCommonInvList(JPInvList, skuId, "JP");
            resultListCommon.addAll(regionResList);

            JPInvList.clear();
        } else {
            resultListCommon.add(calcJPSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
            resultListCommon.add(calcJPSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
            resultListCommon.add(calcJPSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        }


        //       KR区域
        List<ImportsInventorySaleBase> KRInvList = inventorySaleBaseList.stream().filter(r -> r.getMercRegion().equals("KR") && (!r.getDeliveryCountryCode().equals("IT"))).collect(Collectors.toList());
        if (KRInvList.size() > 0) {
            List<String> regionResList = calcCommonInvList(KRInvList, skuId, "KR");
            resultListCommon.addAll(regionResList);

            KRInvList.clear();
        } else {
            resultListCommon.add(calcKRSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
            resultListCommon.add(calcKRSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
            resultListCommon.add(calcKRSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        }


        //       US区域
        List<ImportsInventorySaleBase> USInvList = inventorySaleBaseList.stream().filter(r -> r.getMercRegion().equals("US") && (!r.getDeliveryCountryCode().equals("IT"))).collect(Collectors.toList());
        if (USInvList.size() > 0) {
            List<String> regionResList = calcCommonInvList(USInvList, skuId, "US");

            resultListCommon.addAll(regionResList);
            USInvList.clear();
        } else {
            resultListCommon.add(calcUSSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "预售"));
            resultListCommon.add(calcUSSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "现货"));
            resultListCommon.add(calcUSSkuMetric(currentInventorySaleBase.getSkuId(), -1, -1, 1, "寄售"));

        }


        inventorySaleBaseList.clear();
        return resultListCommon;
    }


    /**
     * 预售、现货、寄售 指标
     *
     * @param commonInvList
     * @param skuId
     * @return
     */
    private static List<String> calcCommonInvList(List<ImportsInventorySaleBase> commonInvList, long skuId, String commonRegion) {
        List<String> resultList = new ArrayList<>();

        //预售
        List<ImportsInventorySaleBase> preSaleInvList = commonInvList.stream().filter(r -> r.getSaleType().equals("7")).collect(Collectors.toList());
        if (preSaleInvList.size() > 0) {
            long minPrice = preSaleInvList.stream().min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice())).get().getFinalPrice();
            Integer remainQuantity = preSaleInvList.stream().map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
            resultList.add(processByRegion(skuId, minPrice, remainQuantity, 0, "预售", commonRegion));
        } else {
            resultList.add(processByRegion(skuId, -1, -1, 1, "预售", commonRegion));
        }

        // 现货
        List<Integer> spotBid = Arrays.asList(3, 20, 21, 27, 28);
        List<ImportsInventorySaleBase> spotBidInvList = commonInvList.stream().filter(r -> spotBid.contains(r.getBiddingType())).collect(Collectors.toList());
        if (spotBidInvList.size() > 0) {
            long minPrice = spotBidInvList.stream().min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice())).get().getFinalPrice();
            Integer remainQuantity = spotBidInvList.stream().map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
            resultList.add(processByRegion(skuId, minPrice, remainQuantity, 0, "现货", commonRegion));

        } else {
            resultList.add(processByRegion(skuId, -1, -1, 1, "现货", commonRegion));
        }

        //寄售
        List<ImportsInventorySaleBase> consignmentInvList = commonInvList.stream().filter(r -> (!r.getSaleType().equals("7")) && (!spotBid.contains(r.getBiddingType()))).collect(Collectors.toList());
        if (consignmentInvList.size() > 0) {
            long minPrice = consignmentInvList.stream().min((o1, o2) -> (int) (o1.getFinalPrice() - o2.getFinalPrice())).get().getFinalPrice();
            Integer remainQuantity = consignmentInvList.stream().map(o -> o.getRemainQuantity()).reduce(0, Integer::sum);
            resultList.add(processByRegion(skuId, minPrice, remainQuantity, 0, "寄售", commonRegion));
        } else {
            //重置
            resultList.add(processByRegion(skuId, -1, -1, 1, "寄售", commonRegion));
        }

        preSaleInvList.clear();
        spotBidInvList.clear();
        consignmentInvList.clear();

        return resultList;
    }


    /**
     * HK,IT,JP,KR,US 区域指标函数
     *
     * @param skuId          当前sku
     * @param minPrice       当前sku最低出价
     * @param remainQuantity 当前sku 剩余库存
     * @param invalidFlag    是否有效出价
     * @param newChannel     (寄售,现货,预售)
     * @param commonRegion   当前区域函数
     * @return
     */
    public static String processByRegion(long skuId, long minPrice, int remainQuantity, Integer invalidFlag, String newChannel, String commonRegion) {
        if ("HK".equals(commonRegion)) {
            return calcHKSkuMetric(skuId, minPrice, remainQuantity, invalidFlag, newChannel);
        } else if ("IT".equals(commonRegion)) {
            return calcITSkuMetric(skuId, minPrice, remainQuantity, invalidFlag, newChannel);
        } else if ("JP".equals(commonRegion)) {
            return calcJPSkuMetric(skuId, minPrice, remainQuantity, invalidFlag, newChannel);
        } else if ("KR".equals(commonRegion)) {
            return calcKRSkuMetric(skuId, minPrice, remainQuantity, invalidFlag, newChannel);
        } else if ("US".equals(commonRegion)) {
            return calcUSSkuMetric(skuId, minPrice, remainQuantity, invalidFlag, newChannel);
        }

        return null;
    }


    /**
     * US 区域定价
     *
     * @param skuId
     * @param minPrice
     * @param remainQuantity
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcUSSkuMetric(long skuId, long minPrice, Integer remainQuantity, int invalidFlag, String newChannel) {
        String metricName = String.format("%s_sku_us_inv_metric", IMPORTSPURED);

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
        json.put("sku_id", skuId);
        // 维度补充
        json.put("seller_perspective", "US");//卖家视角
        json.put("buyer_perspective", "CN");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "US");//老渠道定义


        //指标
        json.put("min_sell_price", minPrice);
        json.put("remain_inventory", remainQuantity);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "CNY");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * KR 区域定价
     *
     * @param skuId
     * @param minPrice
     * @param remainQuantity
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcKRSkuMetric(long skuId, long minPrice, Integer remainQuantity, int invalidFlag, String newChannel) {
        String metricName = String.format("%s_sku_kr_inv_metric", IMPORTSPURED);

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
        json.put("sku_id", skuId);
        // 维度补充
        json.put("seller_perspective", "KR");//卖家视角
        json.put("buyer_perspective", "CN");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "KR");//老渠道定义


        //指标
        json.put("min_sell_price", minPrice);
        json.put("remain_inventory", remainQuantity);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "CNY");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * JP 区域定价
     *
     * @param skuId
     * @param minPrice
     * @param remainQuantity
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcJPSkuMetric(long skuId, long minPrice, Integer remainQuantity, int invalidFlag, String newChannel) {
        String metricName = String.format("%s_sku_jp_inv_metric", IMPORTSPURED);

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
        json.put("sku_id", skuId);
        // 维度补充
        json.put("seller_perspective", "JP");//卖家视角
        json.put("buyer_perspective", "CN");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "JP");//老渠道定义


        //指标
        json.put("min_sell_price", minPrice);
        json.put("remain_inventory", remainQuantity);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "CNY");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * 欧洲区域定价
     *
     * @param skuId
     * @param minPrice
     * @param remainQuantity
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcITSkuMetric(long skuId, long minPrice, Integer remainQuantity, int invalidFlag, String newChannel) {
        String metricName = String.format("%s_sku_it_inv_metric", IMPORTSPURED);

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
        json.put("sku_id", skuId);
        // 维度补充
        json.put("seller_perspective", "IT，RU，DE");//卖家视角
        json.put("buyer_perspective", "CN");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "IT");//老渠道定义


        //指标
        json.put("min_sell_price", minPrice);
        json.put("remain_inventory", remainQuantity);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "CNY");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * 香港渠道定价
     *
     * @param skuId
     * @param minPrice
     * @param remainQuantity
     * @param invalidFlag
     * @param newChannel
     * @return
     */
    private static String calcHKSkuMetric(long skuId, long minPrice, Integer remainQuantity, Integer invalidFlag, String newChannel) {
        // 指标
        String metricName = String.format("%s_sku_hk_inv_metric", IMPORTSPURED);

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
        json.put("sku_id", skuId);
        // 维度补充
        json.put("seller_perspective", "HK,TW, MO,SG,MY");//卖家视角
        json.put("buyer_perspective", "CN");//买家视角
        json.put("new_channel", newChannel);//新渠道定义
        json.put("old_channel", "HK");//老渠道定义


        //指标
        json.put("min_sell_price", minPrice);
        json.put("remain_inventory", remainQuantity);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "CNY");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * 保税渠道
     *
     * @param skuId
     * @param minPrice       最低价
     * @param remainQuantity 剩余库存
     * @param invalidFlag    是否有效 0:有效 1:无效
     * @return
     */
    private static String calcBondedSkuMetric(long skuId, long minPrice, Integer remainQuantity, Integer invalidFlag) {
        // 保税渠道指标
        String metricName = String.format("%s_sku_bonded_inv_metric", IMPORTSPURED);

        JSONObject json = new JSONObject();
        // 指标名称
        json.put("metric_name", metricName);

        //维度
        json.put("sku_id", skuId);
//        json.put("bidding_types", "29,23,26");
        // 维度补充
        json.put("seller_perspective", "ANY");
        json.put("buyer_perspective", "CN");
        json.put("new_channel", "保税");
        json.put("old_channel", "保税");
//        json.put("dims", "sku_id,bidding_types,seller_perspective,buyer_perspective,new_channel,old_channel");


        //指标
        json.put("min_sell_price", minPrice);
        json.put("remain_inventory", remainQuantity);
        json.put("invalid_flag", invalidFlag);//0:表示有效 1:无效
        json.put("currency", "CNY");

        //ext
        json.put("global_now_time", getNowTime());


        return JSON.toJSONString(json);

    }


    /**
     * 纳秒时间
     *
     * @return
     */
    public static Long getNowTime() {
        long cutTime = System.currentTimeMillis() * 1000;
        long nanoTime = System.nanoTime();
        return cutTime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
    }

}
