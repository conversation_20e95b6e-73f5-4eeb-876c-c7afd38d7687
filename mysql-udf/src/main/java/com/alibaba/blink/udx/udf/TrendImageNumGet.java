package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.sql.*;

/**
 * 获取社区动态图片数量UDF.
 * Author: <PERSON>_<PERSON>
 */
@FunctionHint(
		input =  @DataTypeHint("LONG"),
        output = @DataTypeHint("INT")
)
public class TrendImageNumGet extends ScalarFunction {

	private Connection conn;


	@Override
	public void open(FunctionContext context) throws Exception {
		String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
		String url = context.getJobParameter("jdbc.url", "***************************************************************************************************************************************************************");
		String username = context.getJobParameter("jdbc.username", "du_flink");
		String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");

		conn = getConnection(driverClassName, url, username, password);
	}


	public Integer eval(Long contentId) throws SQLException {
		return getTrendImageCount(contentId);
	}


	/**
	 * 获取数据库连接.
	 */
	private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
		try {
			Class.forName(driverClassName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
		return DriverManager.getConnection(url, username, password);
	}


	/**
	 * 获取动态的图片数量.
	 */
	public Integer getTrendImageCount(Long contentId) throws SQLException {
		String sql = "SELECT COUNT(1) as image_num FROM trendimage WHERE trendId = " + contentId + " and is_del=0";
		PreparedStatement pst = conn.prepareStatement(sql);
		ResultSet rs = pst.executeQuery();

		if (rs.next()) {
			int image_num = rs.getInt("image_num");
			try {
				pst.close();
				rs.close();
			} catch (SQLException e) {
				e.printStackTrace();
			}
			return image_num;
		} else {
			return null;
		}
	}


	@Override
	public void close() throws Exception {
		if (conn != null) {
			conn.close();
		}
	}
}
