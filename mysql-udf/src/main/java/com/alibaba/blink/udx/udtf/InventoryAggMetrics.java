package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.util.MysqlUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存变动的时候统计相关sku剩余库存指标
 * 维度：sku_id、sku_id,bidding_type
 * note that: 根据is_only_min_price 来判断是否根据所有类型出价中的最低出价对应的记录进行聚合
 * <p>
 * by StephenLin
 */
@FunctionHint(
        input = {@DataTypeHint("BIGINT")},
        output = @DataTypeHint("ROW<s STRING>")
)
@Deprecated
public class InventoryAggMetrics extends TableFunction<Row> {

    public void eval(long skuId) throws Exception {
        List<InventorySale> inventorySaleList;

        try {
            inventorySaleList = getInventorySaleList(skuId);
            if (inventorySaleList.size() != 0) {
                List<String> list = dealWithInventorySaleList(inventorySaleList, skuId);
                for (String s : list) {
                    Row row = new Row(1);
                    row.setField(0, s);
                    collect(row);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 聚合计算逻辑处理.
     *
     * @param inventorySaleList InventorySale集合
     */
    private static List<String> dealWithInventorySaleList(List<InventorySale> inventorySaleList, Long skuId) {

        List<String> resultList = new ArrayList<>();

        //1、计算当前所有出价类型里面的最低价
        Map<Long, Optional<InventorySale>> priceJudge = inventorySaleList.stream().collect(Collectors.groupingBy(InventorySale::getSkuId, Collectors.minBy(new Comparator<InventorySale>() {
            @Override
            public int compare(InventorySale o1, InventorySale o2) {
                return (int) (o1.getPrice() - o2.getPrice());
            }
        })));

        long minPrice = priceJudge.get(skuId).get().getPrice();

        //2、按照sku_id维度聚合
        //2.1、只计算所有价格等于最低出价的
        Map<Long, Integer> remainQuantityMap1MinPrice = inventorySaleList.stream().filter(item -> item.getPrice() == minPrice).collect(Collectors.groupingBy(InventorySale::getSkuId, Collectors.summingInt(InventorySale::getRemainQuantity)));

        Map<String, Object> map1 = new HashMap<>();
        map1.put("sku_id", skuId);
        map1.put("remain_inventory", remainQuantityMap1MinPrice.get(skuId));
        map1.put("dim", "sku_id");
        map1.put("is_only_min_price", "1");
        resultList.add(JSON.toJSONString(map1));

        //2.2、计算所有出价的
        Map<Long, Integer> remainQuantityMap1AllPrice = inventorySaleList.stream().collect(Collectors.groupingBy(InventorySale::getSkuId, Collectors.summingInt(InventorySale::getRemainQuantity)));
        Map<String, Object> map2 = new HashMap<>();
        map2.put("sku_id", skuId);
        map2.put("remain_inventory", remainQuantityMap1AllPrice.get(skuId));
        map2.put("dim", "sku_id");
        map2.put("is_only_min_price", "0");
        resultList.add(JSON.toJSONString(map2));

        //3、按照sku_id、bidding_type维度聚合
        //3.1、只计算所有价格等于最低出价的
        Map<SkuIdAndBiddingType, Integer> remainQuantityMap2MinPrice = inventorySaleList.stream().filter(item -> item.getPrice() == minPrice).collect(Collectors.groupingBy(p -> new SkuIdAndBiddingType(p.getBiddingType(), p.getSkuId()), Collectors.summingInt(InventorySale::getRemainQuantity)));

        for (Map.Entry<SkuIdAndBiddingType, Integer> entry : remainQuantityMap2MinPrice.entrySet()) {
            SkuIdAndBiddingType key = entry.getKey();
            Integer value = entry.getValue();

            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("sku_id", key.skuId);
            tempMap.put("bidding_type", key.biddingType);
            tempMap.put("remain_inventory", value);
            tempMap.put("dim", "sku_id_and_bidding_type");
            tempMap.put("is_only_min_price", "1");

            resultList.add(JSON.toJSONString(tempMap));
        }

        //3.2、计算所有出价的
        Map<SkuIdAndBiddingType, Integer> remainQuantityMap2AllPrice = inventorySaleList.stream().collect(Collectors.groupingBy(p -> new SkuIdAndBiddingType(p.getBiddingType(), p.getSkuId()), Collectors.summingInt(InventorySale::getRemainQuantity)));
        for (Map.Entry<SkuIdAndBiddingType, Integer> entry : remainQuantityMap2AllPrice.entrySet()) {
            SkuIdAndBiddingType key = entry.getKey();
            Integer value = entry.getValue();

            Map<String, Object> tempMap = new HashMap<>();
            tempMap.put("sku_id", key.skuId);
            tempMap.put("bidding_type", key.biddingType);
            tempMap.put("remain_inventory", value);
            tempMap.put("dim", "sku_id_and_bidding_type");
            tempMap.put("is_only_min_price", "0");

            resultList.add(JSON.toJSONString(tempMap));
        }

        return resultList;
    }

    /**
     * 获取inventorySaleList.
     *
     * @param skuId sku_id
     * @return List<InventorySale>
     * @throws Exception 异常抛出
     */
    private static List<InventorySale> getInventorySaleList(long skuId) throws Exception {
        List<InventorySale> inventorySaleList = new ArrayList<>();

        Connection conn = MysqlUtil.getConnection();
        PreparedStatement pst = conn.prepareStatement("SELECT `price`,`bidding_type`,  `quantity` - `use_quantity` as `remain_quantity`  FROM `ods_binlog_inventory_sale` where `sku_id`=" + skuId + " and `status` =1");
        ResultSet rs = pst.executeQuery();

        while (rs.next()) {
            long price = rs.getLong("price");
            int biddingType = rs.getInt("bidding_type");
            int remainQuantity = rs.getInt("remain_quantity");

            InventorySale inventorySale = InventorySale.builder()
                    .price(price)
                    .biddingType(biddingType)
                    .remainQuantity(remainQuantity)
                    .skuId(skuId)
                    .build();

            inventorySaleList.add(inventorySale);
        }

        MysqlUtil.release(rs, pst, conn);

        return inventorySaleList;
    }

    @Data
    @Builder
    static class InventorySale {
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
    }

    @Data
    @AllArgsConstructor
    static class SkuIdAndBiddingType {
        //出价类型
        private int biddingType;
        //sku_id
        private long skuId;
    }

    public static void main(String[] args) throws Exception {
        long skuId = 606318168L;

        InventoryAggMetrics inventoryAggMetrics = new InventoryAggMetrics();

        inventoryAggMetrics.eval(skuId);
    }
}
