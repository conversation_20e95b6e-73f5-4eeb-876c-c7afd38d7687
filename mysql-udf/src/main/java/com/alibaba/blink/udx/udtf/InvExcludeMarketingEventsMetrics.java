package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.blink.udx.udtf.CalcInvMetricUtils.*;

/**
 * 各维度出价指标集合,包含20个指标(剔除国际和包含国际两种指标)
 * 剔除营销活动、隐藏库存等出价指标,剔除逻辑参考文档口径
 * https://poizon.feishu.cn/wiki/wikcnwkI6YibZbZrvX2tCpYdiEd
 */
@FunctionHint(
        input = {@DataTypeHint("String")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class InvExcludeMarketingEventsMetrics extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(InvExcludeMarketingEventsMetrics.class);
    private static Connection conn;
    //只包含国内渠道的出价
    private static final String DOMESTICPURED = "domestic_pured";
    //如果是使用通用模式(包含了国际渠道出价)
    private static final String COMMONPURED = "common_pured";


    @Override
    public void open(FunctionContext context) throws Exception {
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*******************************************************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_flink");
        String password = context.getJobParameter("jdbc.password", "bPW20)P@gM0+=)_");

//        String url = "*********************************************************************************************************************************************************************************";
//        String username = "root";
//        String password = "password";
//        String driverClassName = "com.mysql.jdbc.Driver";

        conn = getConnection(driverClassName, url, username, password);
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            LOG.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }

    public void eval(String data) throws Exception {
        if (!JSON.isValid(data)) {
            //非法json异常
            JSONException jsonException = new JSONException();
            throw jsonException;
        }

        JSONObject currentInventoryObject = JSONObject.parseObject(data);
        InventorySaleBase currentInventorySaleBase = InventorySaleBase.builder()
                .biddingType(currentInventoryObject.getInteger("bidding_type"))
                .skuId(currentInventoryObject.getLong("sku_id"))
                .spuId(currentInventoryObject.getLong("spu_id"))
                .sellerId(currentInventoryObject.getLong("uid"))
                .build();

        //全部参与计算
        List<DomesticInventorySaleBase> allInventorySaleList = getInventorySaleBaseList(currentInventorySaleBase.getSpuId());

        //剔除国际渠道的库存
        List<DomesticInventorySaleBase> norInternationalBidSaleList = allInventorySaleList.stream().filter(inv -> !(inv.getBiddingType() == 3 || (inv.getBiddingType() >= 20 && inv.getBiddingType() <= 30))).collect(Collectors.toList());


        //包含全部渠道
        collectMessage(allInventorySaleList, currentInventorySaleBase, COMMONPURED);

        //只限制国内渠道
        collectMessage(norInternationalBidSaleList, currentInventorySaleBase, DOMESTICPURED);

        //释放
        allInventorySaleList.clear();
        norInternationalBidSaleList.clear();

    }

    /**
     * emit message
     *
     * @param inventorySaleList
     * @param currentInventorySaleBase
     * @param typePrefix
     */
    private void collectMessage(List<DomesticInventorySaleBase> inventorySaleList, InventorySaleBase currentInventorySaleBase, String typePrefix) {
        if (inventorySaleList.size() > 0) {
            List<String> list = dealWithInventorySaleBaseList(inventorySaleList, currentInventorySaleBase, typePrefix);
            for (String s : list) {
                Row row = new Row(1);
                row.setField(0, s);
//                System.out.println(row);
                collect(row);
            }
        } else {
            //处理没有查询到数据的情况下发remain_quantity=0的情况，分场景下发
            resetAllInventoryMetric(currentInventorySaleBase, typePrefix).stream().forEach(o -> {
                Row row = new Row(1);
                row.setField(0, o);
//                System.out.println(row);
                collect(row);
            });
        }
    }

    private static List<String> dealWithInventorySaleBaseList(List<DomesticInventorySaleBase> inventorySaleBaseList, InventorySaleBase currentInventorySaleBase, String type) {
        return dealWithInventorySaleBaseListCommon(inventorySaleBaseList, currentInventorySaleBase, type);
    }


    /**
     * 全局重置操作
     *
     * @param currentInventorySaleBase
     * @return
     */
    private static List<String> resetAllInventoryMetric(InventorySaleBase currentInventorySaleBase, String type) {
        List<String> resetMetricList = new ArrayList<>();
        //spu 指标重置
        resetMetricList.add(resetcalcPlatformSpuMetric(currentInventorySaleBase.getSpuId(), type));
        resetMetricList.add(resetCalcPlatformSpuAndMercMetric(currentInventorySaleBase.getSpuId(), currentInventorySaleBase.getSellerId(), type));
        resetMetricList.add(resetCalcPlatformSpuAndBidMetric(currentInventorySaleBase.getSpuId(), currentInventorySaleBase.getBiddingType(), type));

        // sku 指标重置
        resetMetricList.add(resetCalcPlatformSkuMetric(currentInventorySaleBase.getSkuId(), type));
        resetMetricList.add(resetCalcPlatformSkuAndBidMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getBiddingType(), type));
        resetMetricList.add(resetCalcPlatformSkuAndMercMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getSellerId(), type));
        resetMetricList.add(resetCalcPlatformSkuBidTypeAndMercMetric(currentInventorySaleBase.getSkuId(), currentInventorySaleBase.getSellerId(), currentInventorySaleBase.getBiddingType(), type));
        // 仅国内渠道
        if (type.equals("domestic_pured")) {
            // 现货渠道
            resetMetricList.add(resetCalcPlatformSkuSpotBidsAndMetric(currentInventorySaleBase.getSkuId(), type));
            //寄售渠道
            resetMetricList.add(resetCalcPlatformConsignmentBidsMetric(currentInventorySaleBase.getSkuId(), type));
        }

        // 包含国外渠道
        if (type.equals("common_pured")) {
            //B7. @张严文 多渠道下的sku最低价、库存数 -- 现货: 0,3,5,6,7,12,13,14,20,21,22,23,24,25,26,27,28,29
            resetCalcPlatformSkuUnlimitedSpotBidsMetric(currentInventorySaleBase.getSkuId(), type);
        }


        return resetMetricList;
    }

    /**
     * spu下全部有效出价部分
     *
     * @param spuId
     * @return
     * @throws Exception
     */
    private static List<DomesticInventorySaleBase> getInventorySaleBaseList(long spuId) throws Exception {
        List<DomesticInventorySaleBase> domesticInventorySaleList = new ArrayList<>();

        String sql = "SELECT " +
                "id, " +
                "`price`," +
                "`bidding_type`, " +
                " `quantity` - `use_quantity` as `remain_quantity`," +
                "uid as seller_id," +
                "sku_id," +
                "source_type," +
                "sale_type," +
                "sub_status," +
                "final_price " +
                " FROM `ods_binlog_inventory_sale` " +
                "where `status` = 1 and `spu_id`=" + spuId;

        PreparedStatement pst = conn.prepareStatement(sql);
        ResultSet rs = pst.executeQuery();
        while (rs.next()) {
            String sourceType = rs.getString("source_type");
            int biddingType = rs.getInt("bidding_type");
            String saleType = rs.getString("sale_type");
            int subStatus = rs.getInt("sub_status");
            Long finalPrice = rs.getLong("final_price");
            Long price = rs.getLong("price");

            //修正dim 表历史原因遗漏finalprice情况
            if (finalPrice == null || finalPrice == 0) {
                finalPrice = rs.getLong("price");
            }

            if (valited(sourceType, biddingType, saleType, subStatus)) {
                //包含 if (biddingType == 3 || (biddingType >= 20 && biddingType <= 30))
                DomesticInventorySaleBase domesticInventorySale = DomesticInventorySaleBase.builder()
                        .price(price)
                        .biddingType(rs.getInt("bidding_type"))
                        .remainQuantity(rs.getInt("remain_quantity"))
                        .skuId(rs.getLong("sku_id"))
                        .spuId(spuId)
                        .sellerId(rs.getLong("seller_id"))
                        .finalPrice(finalPrice)
                        .build();

                domesticInventorySaleList.add(domesticInventorySale);
            }

        }

        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                LOG.error("close rs error: " + e);
            }
        }

        if (pst != null) {
            try {
                pst.close();
            } catch (SQLException e) {
                LOG.error("close pst error: " + e);
            }
        }

        return domesticInventorySaleList;
    }

    @Override
    public void close() throws Exception {
        if (conn != null) {
            conn.close();
        }
    }


    @Data
    @SuperBuilder
    static class DomesticInventorySaleBase extends InventorySaleBase {

    }

//    @Data
//    @SuperBuilder
//    static class InternationalInventorySaleBase extends InventorySaleBase {
//
//    }

    @Data
    @SuperBuilder
    static class InventorySaleBase {
        //        //主键id
//        private long id;
        //价格
        private long price;
        //出价类型
        private int biddingType;
        //剩余数量
        private int remainQuantity;
        //sku_id
        private long skuId;
        //spu_id
        private long spuId;
        //uid 卖家id
        private long sellerId;
        //最终出价
        private long finalPrice;
    }


    /**
     * 校验是否有效出价
     *
     * @param sourceType
     * @param biddingType
     * @param saleType
     * @param subStatus
     * @return
     */
    public static boolean valited(String sourceType, int biddingType, String saleType, int subStatus) {
        boolean isValid = true;
        //定制服务、月卡和虚拟商品出价
        if (biddingType == 101 || biddingType == 200 || (biddingType == 15 && saleType.equals("8"))) {
            return false;
        }
        //隐藏出价
        if (subStatus == 1) {
            return false;
        }

        //无效sourceType
        for (InvalidBidSourceType type : InvalidBidSourceType.values()) {
            if (sourceType.equals(type.getSourceType())) {
                return false;
            }
        }

        return isValid;
    }


    public static void main(String[] args) throws Exception {

        InvExcludeMarketingEventsMetrics inventoryAggMetrics = new InvExcludeMarketingEventsMetrics();
        inventoryAggMetrics.open(null);
        String data = "{\"spu_id\":\"11493071\",\"sku_id\":\"676228501\",\"quantity\":\"1\",\"use_quantity\":\"0\",\"price\":\"519900\",\"uid\":\"1986314538\",\"bidding_type\":\"14\",\"status\":\"10\"}";
        inventoryAggMetrics.eval(data);

    }
}
