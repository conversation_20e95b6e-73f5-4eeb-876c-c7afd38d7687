package com.alibaba.blink.udx.udf;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.poizon.realtime.ARKUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.sql.*;
import java.util.concurrent.TimeUnit;


/**
 * 使用前创建建表 table
 * CREATE TABLE `starrocks_dict_about_***` (
 *   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '字典的键',
 *   `field` varchar(255) NOT NULL COMMENT '字段',
 *   `text` varchar(255) NOT NULL COMMENT '字典的值',
 *   PRIMARY KEY (`id`),
 *   UNIQUE KEY `un` (`field`,`text`)
 * )
 */
@Slf4j
public class StarRocksDict extends ScalarFunction {

    final Cache<String, Integer> dict = CacheBuilder.newBuilder()
            .maximumSize(100000)  // 设置最大缓存条目数量
            .expireAfterWrite(100, TimeUnit.DAYS)  // 设置缓存项的过期时间
            .build();
    static final String DB_URL = "***********************************************************************************************";
    static String USER;
    static String PASSWORD;

    @Override
    public void open(FunctionContext context) throws Exception {
        synchronized (StarRocksDict.class) {
            if (USER == null) {
                final ARKUtils.AccessKeyValue deEncrypt = ARKUtils.getAccessKeyValue("rds.dw_algo_monitor_dict.user",false,"rds.dw_algo_monitor_dict.password", true);
                log.info("deEncrypt key {} and value {}", deEncrypt.getKey(), deEncrypt.getValue());
                USER = deEncrypt.getKey();
                PASSWORD = deEncrypt.getValue();
                Class.forName("com.mysql.jdbc.Driver");
            }
        }
    }

    public Integer eval(String table, String field, String text) {
        String value = getValue(table, field, text);
        Integer id = dict.getIfPresent(value);
        if (id == null) {
            id = getId(table, field, text);
            dict.put(value, id);

            log.info("映射 id:{} value:{}", id, value);
        }

        return id;
    }

    private static String getValue(String table, String field, String text) {
        return String.format("table:%s field:%s text:%s", table, field, text);
    }


    private static Integer getId(String table, String field, String text) {
        try (Connection connection = DriverManager.getConnection(DB_URL, USER, PASSWORD)) {
            String querySQL = String.format("SELECT `id` FROM %s WHERE `field` = ? AND `text`= ?", table);
            PreparedStatement queryStatement = connection.prepareStatement(querySQL);
            queryStatement.setString(1, field);
            queryStatement.setString(2, text);

            ResultSet resultSet = queryStatement.executeQuery();
            if (resultSet.next()) {
                return resultSet.getInt(1);
            } else {
                // 生成新的字典值
                PreparedStatement insertStatement = connection.prepareStatement(String.format("INSERT IGNORE INTO %s (`field`,`text`) VALUES (?, ?);", table));
                insertStatement.setString(1, field);
                insertStatement.setString(2, text);
                insertStatement.execute();

                resultSet = queryStatement.executeQuery();
                if (resultSet.next()) {
                    log.info("字典插入 field:{}, text:{}, id:{}", field, text, resultSet.getInt(1));
                    return resultSet.getInt(1);
                }

            }
        } catch (SQLException e) {
            log.error("字典表查询失败", e);
            throw new RuntimeException(e);
        }
        throw new RuntimeException("字典操作失败");
    }

}
