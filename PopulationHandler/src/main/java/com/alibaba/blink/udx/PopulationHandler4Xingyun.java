package com.alibaba.blink.udx;

import com.util.population.DynamicPopulationHandler4Xingyun;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * @Auther: zhiping
 * @Date: 2022/5/10 10:54
 */
public class PopulationHandler4Xingyun extends ScalarFunction {
	private static final Logger LOG = LoggerFactory.getLogger(PopulationHandler4Xingyun.class);
	private DynamicPopulationHandler4Xingyun dynamicPopulationHandler;

	//构造函数初始化aviator
	@Override
	public void open(FunctionContext context) {
		String hbaseAddress = context.getJobParameter("hbase.address", "");
		String hbaseUserName = context.getJobParameter("hbase.username", "");
		String hbasePassword = context.getJobParameter("hbase.password", "");
		dynamicPopulationHandler = new DynamicPopulationHandler4Xingyun(hbaseAddress, hbaseUserName, hbasePassword);
	}

	/**
	 * 判断用户是否在人群中,返回命中第一个
	 *
	 * @param userId         用户id
	 * @param populationType 0：静态人群，1：动态人群
	 * @return
	 */
	public String eval(String userId, String tableName, int populationType, String expression, String searchCodes) throws IOException {
		if (populationType == 0) {
			LOG.error("xingyun not support static population.");
			return "-1";
		} else {
			return dynamicPopulationHandler.existPopulation(userId, expression, searchCodes) + "";
		}
	}

	@Override
	public void close() {
		LOG.info("Close population connection!");
		dynamicPopulationHandler.close();
	}
}
