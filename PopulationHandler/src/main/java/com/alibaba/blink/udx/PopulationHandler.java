package com.alibaba.blink.udx;

import com.mongodb.ServerAddress;
import com.util.population.DynamicPopulationHandler;
import com.util.population.StaticPopulationHandler;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: wuweiwei
 * @Date: 2020/6/24 10:54
 */
public class PopulationHandler extends ScalarFunction {
    private static final Logger LOG = LoggerFactory.getLogger(PopulationHandler.class);
    private DynamicPopulationHandler dynamicPopulationHandler;
    private StaticPopulationHandler staticPopulationHandler;

    //构造函数初始化aviator
    @Override
    public void open(FunctionContext context) {
        String mongoAddress = context.getJobParameter("mongo.address", "");
        String mongoUserName = context.getJobParameter("mongo.username", "");
        String mongoPassword = context.getJobParameter("mongo.password", "");
        String mongoDbName = context.getJobParameter("mongo.database.name", "");

        String hbaseAddress = context.getJobParameter("hbase.address", "");
        String hbaseUserName = context.getJobParameter("hbase.username", "");
        String hbasePassword = context.getJobParameter("hbase.password", "");

        LOG.info("get the hbase address {}", hbaseAddress);

        List<ServerAddress> addrList = new ArrayList<>();
        String[] addrArray = mongoAddress.split(",");
        for (String address : addrArray) {
            String[] hostPortPair = address.split(":");
            ServerAddress serverAddress = new ServerAddress(hostPortPair[0], Integer.parseInt(hostPortPair[1]));
            addrList.add(serverAddress);
        }

        staticPopulationHandler = new StaticPopulationHandler(addrList, mongoUserName, mongoPassword, mongoDbName);
        dynamicPopulationHandler = new DynamicPopulationHandler(hbaseAddress, hbaseUserName, hbasePassword);

    }

    /**
     * 判断用户是否在人群中,返回命中第一个
     * @param userId    用户id
     * @param populationType  0：静态人群，1：动态人群
     * @return
     */
    public String eval(String userId, String tableName, int populationType, String expression, String searchCodes) throws IOException {
        if (populationType == 0) {
            return staticPopulationHandler.existPopulation(Long.parseLong(userId), tableName)+"";
        } else {
            return dynamicPopulationHandler.existPopulation(userId, expression, searchCodes)+"";
        }
    }

    @Override
    public void close() {
        LOG.info("Close population connection!");
        staticPopulationHandler.close();
        dynamicPopulationHandler.close();
    }
}
