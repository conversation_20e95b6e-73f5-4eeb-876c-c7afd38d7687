package com.util.population;

public class Population {
    /**
     * 主键
     */
    private Long id;

    /**
     * 人群名称
     */
    private String populationName;

    /**
     * 关联标签’,‘分隔
     */
    private String labelIds;

    /**
     * 刷新类型（0：非动态刷星；1：动态刷新）
     */
    private Integer freshType;

    /**
     * 创建类型（1：系统默认，2：标签筛选；3：手动导入）
     */
    private Integer createType;

    /**
     * 人群状态(0:未启用，1：已启用，2：禁用)
     */
    private Integer populationState;

    /**
     * 人群公式
     */
    private String expression;

    /**
     * Es查询表达式
     */
    private String esExpression;

    /**
     * 预估人数
     */
    private Integer personNum;

    /**
     * 人群有效期-结束时间
     */
    private Long endTime;

    /**
     * 人群快照关联表
     */
    private String snapshotTable;

    /**
     * 快照版本号
     */
    private Integer snapshotVersion;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 修改时间
     */
    private Long updateTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否删除(0:有效，1：删除)
     */
    private Integer isDel;

    /**
     * hbase表达式
     */
    private String hbaseExpression;
    /**
     * 标签searchCodes
     */
    private String searchCodes;

    /**
     * 实时标签列表
     */
    private String onLineList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPopulationName() {
        return populationName;
    }

    public void setPopulationName(String populationName) {
        this.populationName = populationName;
    }

    public String getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(String labelIds) {
        this.labelIds = labelIds;
    }

    public Integer getFreshType() {
        return freshType;
    }

    public void setFreshType(Integer freshType) {
        this.freshType = freshType;
    }

    public Integer getCreateType() {
        return createType;
    }

    public void setCreateType(Integer createType) {
        this.createType = createType;
    }

    public Integer getPopulationState() {
        return populationState;
    }

    public void setPopulationState(Integer populationState) {
        this.populationState = populationState;
    }

    public String getExpression() {
        return expression;
    }

    public void setExpression(String expression) {
        this.expression = expression;
    }

    public String getEsExpression() {
        return esExpression;
    }

    public void setEsExpression(String esExpression) {
        this.esExpression = esExpression;
    }

    public Integer getPersonNum() {
        return personNum;
    }

    public void setPersonNum(Integer personNum) {
        this.personNum = personNum;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getSnapshotTable() {
        return snapshotTable;
    }

    public void setSnapshotTable(String snapshotTable) {
        this.snapshotTable = snapshotTable;
    }

    public Integer getSnapshotVersion() {
        return snapshotVersion;
    }

    public void setSnapshotVersion(Integer snapshotVersion) {
        this.snapshotVersion = snapshotVersion;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public String getHbaseExpression() {
        return hbaseExpression;
    }

    public void setHbaseExpression(String hbaseExpression) {
        this.hbaseExpression = hbaseExpression;
    }

    public String getSearchCodes() {
        return searchCodes;
    }

    public void setSearchCodes(String searchCodes) {
        this.searchCodes = searchCodes;
    }

    public String getOnLineList() {
        return onLineList;
    }

    public void setOnLineList(String onLineList) {
        this.onLineList = onLineList;
    }
}
