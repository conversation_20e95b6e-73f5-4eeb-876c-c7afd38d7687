package com.util.population;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: wuweiwei
 * @Date: 2020/6/24 10:54
 */
public class DynamicPopulationHandler {
    private static final Logger log = LoggerFactory.getLogger(DynamicPopulationHandler.class);
    private CrmHbaseUtil hbaseUtil;

    //构造函数初始化aviator
    public DynamicPopulationHandler(String hbaseUrl, String username, String password) {
        hbaseUtil = CrmHbaseUtil.getHbaseUtil(hbaseUrl, username, password);
        //添加自定义交集函数
        AviatorEvaluator.addFunction(new IntersectionFunction());
        AviatorEvaluator.compile("123&456",true);
        log.info("构造函数初始化aviator...");
    }

    /**
     * 判断用户是否在人群中
     * @param userId    用户id
     * @param expression  动态人群表达式
     * @param searchCodes  查询类型
     * @return
     */
    public int existPopulation(String userId, String expression, String searchCodes) throws IOException {

        String rowKey = MD5Utils.md5(userId,"utf-8") + "_" + userId;
        Map<String, Object> result = hbaseUtil.selectValue(rowKey);
        Map<String, Object> env = new HashMap<>();
        initEnvMap(searchCodes, env, result);

        Expression compiledExp = AviatorEvaluator.compile(expression,true);
        return ((Boolean) compiledExp.execute(env)) ? 1 : 0;
    }

    /**
     * 判断用户是否在人群中,返回命中第一个
     * @param userId    用户id
     * @param populationList    人群集合
     * @return
     */
    public Long existPopulationOne(String userId, List<Population> populationList) throws IOException {
        long startTime = System.currentTimeMillis();
        String rowKey = MD5Utils.md5(userId,"utf-8") + "_" + userId;
        Map<String, Object> result = hbaseUtil.selectValue(rowKey);

        try {
            Map<String, Object> env = new HashMap<>();
            for(Population population : populationList){
                //封装表达式参数
                initEnvMap(population.getSearchCodes(),env,result);

                String expression = population.getHbaseExpression();
                Expression compiledExp = AviatorEvaluator.compile(expression,true);
                if((Boolean) compiledExp.execute(env)){
                    return population.getId();
                }
            }
        }catch (Exception e){
            log.error("调用人群查询接口失败...",e);
        }

        log.info("hbase表达式逻辑判断existPopulationOne耗时: {}",System.currentTimeMillis()-startTime);
        return null;
    }

    /**
     * 标签时间与当前时间差值（自然日）
     * @param time
     * @return
     */
    private Long timeToDay(String time){
        //当前时间戳
        Long currentTime = DateUtils.getCurrentTime();
        //标签时间戳
        Long labelTime = Long.parseLong(time);
        //指定标签0点时间戳
        Long zeroTime = DateUtils.getZeroClockTimeStamps(labelTime);

        return (currentTime - zeroTime) / 86400;
    }

    /**
     * 封装env集合
     * @param searchCodes 标签
     * @param env
     * @param result hbase数据
     */
    private void initEnvMap(String searchCodes, Map<String, Object> env, Map<String, Object> result){
        for(String searchCode : searchCodes.split(",")){
            String[] kv = searchCode.split(":");
            String value = "";
            if (result.get(kv[0]) != null) {
                value = result.get(kv[0]).toString();
            }
            value = value == null ? "" : value;

            if("enum".equals(kv[1]) || "array".equals(kv[1])){
                env.put(kv[0],value);
            }else if(value == null || value.length() == 0){
                env.put(kv[0],-1.0);
            }else if("number".equals(kv[1])){
                env.put(kv[0], Double.parseDouble(value));
            }else if("time".equals(kv[1])){
                env.put(kv[0], timeToDay(value));
            }
        }
    }

    public void close () {
        hbaseUtil.close();
    }

    public static void main (String[] args) {
        String searchCode = "";
        String[] kv = searchCode.split(":");
        System.out.println("");
    }
}
