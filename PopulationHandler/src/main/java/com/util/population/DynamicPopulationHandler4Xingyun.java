package com.util.population;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Auther: zhiping
 * @Date: 2022/5/10 10:54
 */
public class DynamicPopulationHandler4Xingyun {
	private static final Logger log = LoggerFactory.getLogger(DynamicPopulationHandler4Xingyun.class);
	private CrmHbaseUtil hbaseUtil;
	//数值类标签类型转换  * 逻辑类型： 1-枚举 2-布尔  3-数值  4-字符串 5-日期  6-区间  7-数组 9-金额 99-未定义
	private static final List<String> numberLabel = Arrays.asList("3", "5", "9");
	private static Cache<String, Map<String, Object>> cache;

	//构造函数初始化aviator
	public DynamicPopulationHandler4Xingyun(String hbaseUrl, String username, String password) {
		hbaseUtil = CrmHbaseUtil.getHbaseUtil(hbaseUrl, username, password);
		//添加自定义交集函数
		AviatorEvaluator.addFunction(new IntersectionFunction());
		AviatorEvaluator.compile("123&456", true);
		log.info("构造函数初始化aviator...");
		cache = CacheBuilder.newBuilder().maximumSize(1_000_000L).expireAfterWrite(60L, TimeUnit.SECONDS).build();
		log.info("构造函数初始化cache...");
	}

	/**
	 * 判断用户是否在人群中
	 *
	 * @param userId      用户id
	 * @param expression  动态人群表达式
	 * @param searchCodes 查询类型
	 * @return
	 */
	public int existPopulation(String userId, String expression, String searchCodes) throws IOException {
		if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(expression) || StringUtils.isEmpty(searchCodes))
			return 0;
		String rowKey = MD5Utils.md5(userId, "utf-8") + "_" + userId;
		Map<String, Object> result = cache.getIfPresent(rowKey);
		if (null == result) {
			log.info("missing hbase cache : " + rowKey + "\n" + cache.stats());
			result = hbaseUtil.selectValue(rowKey);
			cache.put(rowKey, result);
		}
		Map<String, Object> env = new HashMap<>();
		initEnvMap(searchCodes, env, result, rowKey);

		Expression compiledExp = AviatorEvaluator.compile(expression, true);
		return ((Boolean) compiledExp.execute(env)) ? 1 : 0;
	}

	/**
	 * 封装env集合
	 *
	 * @param searchCodes 标签
	 * @param env
	 * @param result      hbase数据
	 */
	private void initEnvMap(String searchCodes, Map<String, Object> env, Map<String, Object> result, String rowkey) {
		for (String searchCode : searchCodes.split(",")) {
			String[] kv = searchCode.split(":");
			if (!env.containsKey(kv[0])) {
				String value = result.get(kv[0]) == null ? "" : String.valueOf(result.get(kv[0]));
				if (!numberLabel.contains(kv[1])) {
					env.put(kv[0], value);
				} else if (StringUtils.isBlank(value)) {
					env.put(kv[0], -1.0);
				} else {
					try {
						env.put(kv[0], Double.parseDouble(value));
					} catch (Exception e) {
						log.error(rowkey + " meet exception with searchCodes : [" + searchCodes + "] at [" + searchCode + "],env : [" + env + "],result : [" + result + "], at " + kv[0] +" with " + value, e);
					}
				}
			}
		}


	}

	public void close() {
		hbaseUtil.close();
	}
}
