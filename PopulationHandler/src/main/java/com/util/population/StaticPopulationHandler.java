package com.util.population;

import com.mongodb.ServerAddress;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.util.MongoDBUtil;
import com.util.cache.LRUHashMap;
import com.util.cache.PojoWithTTL;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: wuweiwei
 * @Date: 2020/6/24 10:54
 */
public class StaticPopulationHandler {
    private static final Logger LOG = LoggerFactory.getLogger(StaticPopulationHandler.class);
    private MongoDBUtil mongoDBUtil;
    String databaseName;
    MongoDatabase mongoDatabase;
    LRUHashMap<Long, UserStaticPopluation> userPopulationCache;

    //构造函数初始化aviator
    public StaticPopulationHandler(List<ServerAddress> adds, String userName, String password, String dbName) {
        mongoDBUtil = new MongoDBUtil(adds, userName, dbName, password);
        System.out.println("addr is: " + adds.toString() + ", username is: " + userName + "pass is: " + password + ", db name is : " + dbName);
        databaseName = dbName;
        userPopulationCache = new LRUHashMap<Long, UserStaticPopluation>();
    }

    public StaticPopulationHandler() {
        userPopulationCache = new LRUHashMap<Long, UserStaticPopluation>();
    }

    //静态人群查找
    public int existPopulation(Long userId, String collectionName) {
        if (existsInCache(userId, collectionName)) {
            System.out.println("Uid: " + userId + "exists in pop cache");
            return 1;
        } else {
            boolean res = existsInTable(userId, collectionName);
            if (res) {
                addCache(userId, collectionName);
            }
            return res ? 1: 0;
        }
    }

    public boolean existsInCache(Long userId, String collectionName) {
        boolean res = userPopulationCache.containsKey(userId) && userPopulationCache.get(userId).populationList.contains(collectionName);
        return res;
    }
    private boolean existsInTable(Long userId, String collectionName) {
        if (mongoDatabase == null) {
            LOG.info("Init mongo connection!");
            mongoDatabase = mongoDBUtil.getConnection().getDatabase(databaseName);
        }
        MongoCollection mongoCollection = mongoDatabase.getCollection(collectionName);
        Document document = new Document();
        document.put("userId", userId);
        long count = mongoCollection.count(document);
        if (count > 0) {
            System.out.println("Uid: " + userId + "exists in pop table: " + collectionName + ", count is :" + count);
        }
        return count > 0L ? true : false;
    }

    public void addCache(Long userId, String collectionName) {
        if (userPopulationCache.containsKey(userId)) {
            UserStaticPopluation userStaticPopluation = userPopulationCache.get(userId);
            userStaticPopluation.populationList.add(collectionName);
        } else {
            List<String> list = new ArrayList<>();
            list.add(collectionName);
            userPopulationCache.put(userId, new UserStaticPopluation(userId, list));
        }
    }

    public class UserStaticPopluation extends PojoWithTTL {
        Long uid;
        List<String> populationList;

        public UserStaticPopluation(Long uid, List<String> populationList) {
            this.uid = uid;
            this.populationList = populationList;
        }
    }

    public void close () {
        mongoDBUtil.close();
    }
}
