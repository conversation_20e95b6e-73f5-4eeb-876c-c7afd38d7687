package com.util.population;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Auther: wuweiwei
 * @Date: 2020/6/18 14:00
 */
public class IntersectionFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
        String leftStr = FunctionUtils.getStringValue(arg1,env);
        String rightStr = FunctionUtils.getStringValue(arg2,env);

        List<String> leftList = Arrays.asList(leftStr.split(","));
        List<String> rightList = Arrays.asList(rightStr.split(","));

        for(String right : rightList){
            if(leftList.contains(right)){
                return AviatorBoolean.valueOf(true);
            }
        }
        return AviatorBoolean.valueOf(false);
    }

    @Override
    public String getName() {
        return "interFunc";
    }
}
