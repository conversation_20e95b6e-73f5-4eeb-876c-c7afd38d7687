package com.util.population;


import com.alibaba.blink.udx.JDBCUtils;
import org.apache.flink.util.function.RunnableWithException;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Author: WangLin
 * Date: 2022/12/28 上午11:30
 * Description:查询field对应的列簇名称工具类
 */
public class FieldAndCfUtil {

	private static final String QUERY_URL = "SELECT field_name,hbase_family FROM sourcedata_field_metadata where  hbase_family != '' GROUP BY field_name";

	public static List<FieldAndCf> fieldAndCfList;

	private static ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

	static {
		try {
			fieldAndCfList = getInfo();
		} catch (SQLException e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(), e);
		}

		scheduledExecutorService.scheduleWithFixedDelay(
				() -> {
					try {
						fieldAndCfList = getInfo();
					} catch (Exception e) {
						e.printStackTrace();
						throw new RuntimeException(e.getMessage(), e);
					}
				},
				0, 1000 * 3600 * 24, TimeUnit.MILLISECONDS);
	}

	/**
	 * 判断字段是否应该从离线标签里面取数
	 */
	public static boolean isOffline(String fieldName) {
		for (FieldAndCf fieldAndCf : fieldAndCfList) {
			if (fieldAndCf.getFiledName().equals(fieldName)) {
				return fieldAndCf.getCf().equals("offline_label");
			}
		}
		return false;
	}

	/**
	 * 判断字段是否应该从实时标签里面取数
	 */
	public static boolean isRealtime(String fieldName) {
		for (FieldAndCf fieldAndCf : fieldAndCfList) {
			if (fieldAndCf.getFiledName().equals(fieldName)) {
				return fieldAndCf.getCf().equals("realtime_label");
			}
		}
		return false;
	}

	/**
	 * 判断字段是否应该从新离线标签里面取数
	 */
	public static boolean isNewOffline(String fieldName) {
		for (FieldAndCf fieldAndCf : fieldAndCfList) {
			if (fieldAndCf.getFiledName().equals(fieldName)) {
				return fieldAndCf.getCf().equals("new_offline_label");
			}
		}
		return false;
	}

	/**
	 * 获取字段和列簇的对应关系
	 */
	public static List<FieldAndCf> getInfo() throws SQLException {

		Properties pro = new Properties();
		String driverClassName = "com.mysql.jdbc.Driver";
		String url = "**********************************************************************************************************************************";
		String username = "bigdata_datacenter_sourcedata";
		String password = "6B1WmJ85vMX4Bck8";
		String initialSize = "5";
		String maxActive = "1000";
		String maxWait = "3000";

		pro.put("driverClassName", driverClassName);
		pro.put("url", url);
		pro.put("username", username);
		pro.put("password", password);
		pro.put("initialSize", initialSize);
		pro.put("maxActive", maxActive);
		pro.put("maxWait", maxWait);

		JDBCUtils jdbcUtils = new JDBCUtils(pro);

		List<FieldAndCf> list = new ArrayList<>();

		try (Connection conn = jdbcUtils.getConnection();
			 PreparedStatement preparedStatement = conn.prepareStatement(QUERY_URL);
			 ResultSet resultSet = preparedStatement.executeQuery()) {

			while (resultSet.next()) {
				String fieldName = resultSet.getString("field_name");
				String hbaseFamily = resultSet.getString("hbase_family");

				FieldAndCf fieldAndCf = new FieldAndCf(fieldName, hbaseFamily);
				list.add(fieldAndCf);
			}
		}

		return list;
	}

	/**
	 * 字段和列簇的对应关系
	 */
	public static class FieldAndCf {
		private String filedName;
		private String cf;

		public FieldAndCf(String filedName, String cf) {
			this.filedName = filedName;
			this.cf = cf;
		}

		public String getFiledName() {
			return filedName;
		}

		public String getCf() {
			return cf;
		}
	}
}
