package com.util.population;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.shaded.hadoop.hbase.HBaseConfiguration;
import org.shaded.hadoop.hbase.TableName;
import org.shaded.hadoop.hbase.client.*;
import org.shaded.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;


public class CrmHbaseUtil {
	private static final Logger log = LoggerFactory.getLogger(CrmHbaseUtil.class);
	private static String TABLENAME = "crm:st_usr_crowd_management_lab";
	private static String OFFFAMILY = "offline_label";
	private static String REALFAMILY = "realtime_label";
	private static String NEWOFFFAMILY = "new_offline_label";
	private final static AtomicInteger usageCnt = new AtomicInteger(0);


	private static ExecutorService pool = Executors.newScheduledThreadPool(20);    //设置连接池
	private static volatile Connection connection = null;
	private static final Object lock = new Object();
	private static volatile CrmHbaseUtil INSTANCE;
	private Table table;
	private Configuration conf;

	private CrmHbaseUtil(String hbaseUrl, String username, String password) {

		Configuration conf = new Configuration();
		conf.setClassLoader(HBaseConfiguration.class.getClassLoader());
		conf.setBoolean("hbase.defaults.for.version.skip", true);
//		Configuration conf = HBaseConfiguration.create();

		conf.set("hbase.zookeeper.quorum", hbaseUrl);
		//增强版hbase
		conf.set("hbase.client.username", username);
		conf.set("hbase.client.password", password);

		this.conf = conf;
	}

	public static CrmHbaseUtil getHbaseUtil(String hbaseUrl, String username, String password) {
		if (null == INSTANCE) {
			synchronized (CrmHbaseUtil.class) {
				if (null == INSTANCE) {
					INSTANCE = new CrmHbaseUtil(hbaseUrl, username, password);
				}
			}
		}
		log.info("take a hbase util once more, and now usage : " + usageCnt.incrementAndGet());
		return INSTANCE;
	}

	public Connection getConnection() {
		if (connection == null || connection.isClosed()) {
			synchronized (lock) {
				if (connection == null || connection.isClosed()) {
					try {
						connection = ConnectionFactory.createConnection(conf, pool);
						log.info("HbaseUtils实例初始化success");
					} catch (IOException e) {
						log.error("HbaseUtils实例初始化失败！错误信息为：" + e.getMessage(), e);
					}
				}
			}
		}
		return connection;
	}

	public void close() {
		try {
			log.warn("closing hbase connection.\n" + Arrays.toString(Thread.currentThread().getStackTrace()));
			log.info("release a usage for hbase util, and now usage : " + usageCnt.decrementAndGet());
			if (usageCnt.get() <= 0) {
				if (connection != null) {
					connection.close();
				}
			}
		} catch (IOException e) {
			log.error("Close hbase connection error: ", e);
		}
	}


	/**
	 * 查找标签数据
	 * *
	 *
	 * @param rowKey 行名
	 * @return
	 */
	public Map<String, Object> selectValue(String rowKey) throws IOException {
		Map<String, Object> env = new HashMap<>();

		TableName name = TableName.valueOf(TABLENAME);
		if (table == null) {
			table = getConnection().getTable(name);
		}
		Get g = new Get(rowKey.getBytes());
		Result rs = table.get(g);

		//获取离线标签数据
		Map<byte[], byte[]> offFamilyMap = rs.getFamilyMap(Bytes.toBytes(OFFFAMILY));
		if (offFamilyMap != null) {
			byte[] offValue = offFamilyMap.get("offline_result".getBytes());
			String jsonStr = Bytes.toString(offValue);
			if (StringUtils.isNotBlank(jsonStr)) {
				Map<String, Object> localEnv = new HashMap<>(JSONObject.parseObject(jsonStr));
				for (Map.Entry<String, Object> stringObjectEntry : localEnv.entrySet()) {
					String key = stringObjectEntry.getKey();
					if (FieldAndCfUtil.isOffline(key)) {
						env.put(key, stringObjectEntry.getValue());
					}
				}
			} else {
				log.info("hbase查询标签数据为空: {}", rowKey);
			}
		}

		//获取实时标签数据
		Map<byte[], byte[]> realFamilyMap = rs.getFamilyMap(Bytes.toBytes(REALFAMILY));
		if (realFamilyMap != null) {
			for (Map.Entry<byte[], byte[]> entry : realFamilyMap.entrySet()) {
				String key = Bytes.toString(entry.getKey());
				if (FieldAndCfUtil.isRealtime(key)) {
					env.put(key, Bytes.toString(entry.getValue()));
				}
			}
		}

		//获取新离线标签数据
		Map<byte[], byte[]> newOffFamilyMap = rs.getFamilyMap(Bytes.toBytes(NEWOFFFAMILY));
		if (newOffFamilyMap != null) {
			for (Map.Entry<byte[], byte[]> entry : newOffFamilyMap.entrySet()) {
				String key = Bytes.toString(entry.getKey());
				if (FieldAndCfUtil.isNewOffline(key)) {
					env.put(key, Bytes.toString(entry.getValue()));
				}
			}
		}

		return env;
	}
}
