package com.util.cache;

import java.util.LinkedHashMap;
import java.util.Map;

public class LRUHashMap<K,V> extends LinkedHashMap<K,V> {
    private static int MAX_SIZE = 1000000;

    public static int getMaxSize() {
        return MAX_SIZE;
    }

    public static void setMaxSize(int maxSize) {
        MAX_SIZE = maxSize;
    }
    protected boolean removeEldestEntry(Map.Entry eldest) {
        return size() > MAX_SIZE;
    }

    public LRUHashMap(int initialCapacity) {
        super(initialCapacity);
    }

    public LRUHashMap() {
    }
}
