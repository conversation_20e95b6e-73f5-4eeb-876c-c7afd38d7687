package com.util.cache;

/**
 * only support lru now.
 * User's pojo must
 * @param <K>
 */
public class LocalCache<K> {

    LRUHashMap<K, PojoWithTTL> cache;
    int size = 50000;

    public LocalCache() {
        int mapSize = initCacheSize(size);
        cache = new LRUHashMap<>(mapSize);
    }

    public LocalCache(int size) {
        this.size = size;
        int mapSize = initCacheSize(size);
        cache = new LRUHashMap<>(mapSize);
    }

    private int initCacheSize(int size) {
        return (int) (Math.ceil(size * 3 / 4) + 1);

    }

    public void put (K key, PojoWithTTL pojo) {
        cache.put(key, pojo);
    }

    public PojoWithTTL get (K key) {
        if (cache.containsKey(key)) {
            PojoWithTTL pojo = cache.get(key);
            if (pojo.isExpired()) {
                cache.remove(key);
                return null;
            } else {
                return pojo;
            }
        } else {
            return null;
        }

    }

    public boolean containsKey(K key) {
        return cache.containsKey(key);
    }
}
