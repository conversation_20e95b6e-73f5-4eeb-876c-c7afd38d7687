package com.util.timeutils.common;


import com.util.timeutils.common.base.*;

import java.time.format.DateTimeFormatter;

public class DateTimeFormatters {
    /**
     * "yyyy-MM-dd HH:mm:ss.SSS"
     */
    public static final DateTimeFormatter Pattern_SSS = FormatterSss.INSTANCE.getInstance();
    /**
     * "yyyy-MM-dd HH:mm:ss"
     */
    public static final DateTimeFormatter Pattern_SS = FormatterSs.INSTANCE.getInstance();
    /**
     * "yyyy-MM-dd HH:mm"
     */
    public static final DateTimeFormatter Pattern_MIN = FormatterMin.INSTANCE.getInstance();
    /**
     * "yyyy-MM-dd HH"
     */
    public static final DateTimeFormatter Pattern_HH = FormatterHH.INSTANCE.getInstance();
    /**
     * "yyyy-MM-dd"
     */
    public static final DateTimeFormatter Pattern_DAY = FormatterDay.INSTANCE.getInstance();
    /**
     * "yyyyMMdd"
     */
    public static final DateTimeFormatter Pattern_yyyyMMdd = FormatterDayyyyMMdd.INSTANCE.getInstance();

}
