package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.MergeMetricACC;
import com.util.HBaseClient;
import com.util.timeutils.TimeUtils;
import com.util.timeutils.common.DateTimeFormatters;
import com.util.treadpoolutils.TreadPoolUtils;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.hadoop.conf.Configuration;
import org.shaded.hadoop.hbase.HBaseConfiguration;
import org.shaded.hadoop.hbase.client.Result;
import org.shaded.hadoop.hbase.client.ResultScanner;
import org.shaded.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.concurrent.ThreadPoolExecutor;

public class MergeMetricToHistoryAndRealtime extends AggregateFunction<Long, MergeMetricACC>{
    private static final Logger logger = LoggerFactory.getLogger(MergeMetricToHistoryAndRealtime.class);
    private  ThreadPoolExecutor threadPoolExecutor;
    private volatile HBaseClient hBaseClient;
    private Configuration conf;
    private String tableName;
    private String columnFamily;
    private String qualifier;
    /**
     *
     * @return 初使化一个累加器
     */
    @Override
    public MergeMetricACC createAccumulator() {
        return new MergeMetricACC();
    }

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        String zookeeper = context.getJobParameter("merge.metric.hbase.zookeeper.quorum", "");
        String username = context.getJobParameter("merge.metric.hbase.client.username", "");
        String password = context.getJobParameter("merge.metric.hbase.client.password", "");
        tableName = context.getJobParameter("merge.metric.hbase.table.name", "");
        columnFamily = context.getJobParameter("merge.metric.hbase.columnFamily", "");
        qualifier = context.getJobParameter("merge.metric.hbase.qualifier", "");
        conf= new Configuration();
        conf.setClassLoader(HBaseConfiguration.class.getClassLoader());
        conf.addResource("hbase-default.xml");
        conf.addResource("hbase-site.xml");
        conf.setBoolean("hbase.defaults.for.version.skip",true);

        conf.set("hbase.zookeeper.quorum", zookeeper);
        //增强版hbase
        conf.set("hbase.client.username", username);
        conf.set("hbase.client.password", password);
        threadPoolExecutor = TreadPoolUtils.getThreadPoolExecutor();
        hBaseClient=new HBaseClient(conf, tableName, threadPoolExecutor) ;
        logger.info("hbase.zookeeper.quorum:{}",zookeeper);
    }

    @Override
    public void close() throws Exception {
        super.close();
        hBaseClient.close();
        threadPoolExecutor.shutdown();
    }

    @Override
    public Long getValue(MergeMetricACC accumulator) {
        long historyBuff = accumulator.getHistoryBuff() == null ? 0L : accumulator.getHistoryBuff();
        long todayBuff = accumulator.getTodayBuff() == null ? 0L : accumulator.getTodayBuff();
        return historyBuff+todayBuff;
    }

    /**
     *
     * @param accumulator 累加器
     * @param value 入参
     * @param key 当前key
     * @param time_day 处理时间的当天 case： yyyyMMdd
     * @param diffValue 指标需要累积的天数
     */
    public void accumulate(MergeMetricACC accumulator, Long value,String key ,String time_day,Long diffValue) {
        //初始化累加器中的值
        //第一次初始化 或者 第二天的时候更新缓存
        if (accumulator.getHistoryBuff()==null && !time_day.equals(accumulator.getHistoryBuffUpTime())){
            Long accumulateValue = innitAccumulateValue( key,time_day,diffValue);

            accumulator.setHistoryBuff(accumulateValue);
            accumulator.setHistoryBuffUpTime(time_day);
        }

        long todayValue = accumulator.getTodayBuff() == null ? value : accumulator.getTodayBuff() + value;
        accumulator.setTodayBuff(todayValue);

    }

    public void merge(MergeMetricACC accumulator,Iterable<MergeMetricACC> iterables) {
        iterables.forEach(accumulator::merge);
    }
    public void retract(MergeMetricACC accumulator, Long value,String key ,String time_day,Long diffValue) {
        long l = accumulator.getTodayBuff() - value;
        accumulator.setTodayBuff(l);
        logger.info("retract!!!!!!!!!!!!!!");
    }

    public Long innitAccumulateValue(String key,String time_day,Long diffValue){
        Long timeStampByDay = TimeUtils.strToTimeStamp(time_day, DateTimeFormatters.Pattern_yyyyMMdd);

        Long firstTime = TimeUtils.timeSubtract(timeStampByDay, diffValue, ChronoUnit.DAYS);
        Long endTime = TimeUtils.timeAdd(timeStampByDay, 1L, ChronoUnit.DAYS);
        String firstTimeFormatByDay = TimeUtils.timeFormatByDayyyyMMdd(firstTime);
        //初始化历史状态
        long sum=0L;
        getHBaseClient();
        String spu_id = stringForMart(key, 20);
        String rowKeyStart=spu_id+"_"+firstTimeFormatByDay;
        String rowKeyEnd=spu_id+"_"+time_day;
        try {

            ResultScanner scanner = hBaseClient.scanRowKeyByFamilyTimeRange(
                    rowKeyStart,
                    rowKeyEnd,
                    firstTime * 1000,
                    endTime * 1000,
                    columnFamily
            );

            for (Result result : scanner) {
                byte[] bytes = result.getValue(Bytes.toBytes(columnFamily), Bytes.toBytes(qualifier));
                long v= Bytes.toLong(bytes);
                sum += v;
            }

            scanner.close();

//            logger.info("spuID:{}初始化成功历史:{}",key,sum);

        } catch (IOException e) {
            logger.error("spuID:{}初始化成功历史 失败",key);
            throw new RuntimeException(e);
        }
        return sum;
    }

    public void  getHBaseClient(){
        if (hBaseClient == null){
            synchronized (HBaseClient.class){
                if (hBaseClient==null){
                    hBaseClient=new HBaseClient(conf, tableName, threadPoolExecutor) ;
                }
            }
        }
    }

    public  String stringForMart(String value,Integer maxSize){
        int userIdSize = value.length();
        String s="";
        if (userIdSize >maxSize){
            logger.error("userId:{} 长度超过{}",value,maxSize);
        }else {
            char[] c = new char[maxSize-userIdSize];
            Arrays.fill(c, '0');
            s = new String(c);
        }
        return s+value;
    }


}
