package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.MergeDistinctMetricACC;
import com.util.HBaseClient;
import com.util.timeutils.TimeUtils;
import com.util.timeutils.common.DateTimeFormatters;
import com.util.treadpoolutils.TreadPoolUtils;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.hadoop.conf.Configuration;
import org.shaded.hadoop.hbase.CompareOperator;
import org.shaded.hadoop.hbase.HBaseConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @ClassName: MergeDistinctMetricToHistoryAndRealtime
 * @Description:
 * @author: scl
 * @date: 2022/4/19  12:52 下午
 */
public class MergeDistinctMetricToHistoryAndRealtime extends AggregateFunction<Long, MergeDistinctMetricACC> {
	private static final Logger logger = LoggerFactory.getLogger(MergeDistinctMetricToHistoryAndRealtime.class);
	private ThreadPoolExecutor threadPoolExecutor;
	private volatile HBaseClient hBaseClient;
	private Configuration conf;
	private String tableName;
	private String columnFamily;
	private String filterQualifier;
	private String compareOperator;
	/**
	 *
	 * @return 初使化一个累加器
	 */
	@Override
	public MergeDistinctMetricACC createAccumulator() {
		return new MergeDistinctMetricACC();
	}

	@Override
	public void open(FunctionContext context) throws Exception {
		super.open(context);
		String zookeeper = context.getJobParameter("merge.distinct.metric.hbase.zookeeper.quorum", "");
		String username = context.getJobParameter("merge.distinct.metric.hbase.client.username", "");
		String password = context.getJobParameter("merge.distinct.metric.hbase.client.password", "");
		tableName = context.getJobParameter("merge.distinct.metric.hbase.table.name", "");
		columnFamily = context.getJobParameter("merge.distinct.metric.hbase.columnFamily", "");
		compareOperator = context.getJobParameter("merge.distinct.metric.hbase.filter.compareOperator", "");
		filterQualifier = context.getJobParameter("merge.distinct.metric.hbase.filter.qualifier", "");
		conf= new Configuration();
		conf.setClassLoader(HBaseConfiguration.class.getClassLoader());
		conf.addResource("hbase-default.xml");
		conf.addResource("hbase-site.xml");
		conf.setBoolean("hbase.defaults.for.version.skip",true);

		conf.set("hbase.zookeeper.quorum", zookeeper);
		//增强版hbase
		conf.set("hbase.client.username", username);
		conf.set("hbase.client.password", password);
		threadPoolExecutor = TreadPoolUtils.getThreadPoolExecutor();
		hBaseClient=new HBaseClient(conf, tableName, threadPoolExecutor) ;
		logger.info("hbase.zookeeper.quorum:{}",zookeeper);
	}

	@Override
	public void close() throws Exception {
		super.close();
		hBaseClient.close();
		threadPoolExecutor.shutdown();
	}

	@Override
	public Long getValue(MergeDistinctMetricACC accumulator) {
		return accumulator.getValue();
	}

	public void accumulate(MergeDistinctMetricACC accumulator, String value,String key ,String time_day,Long diffValue) {
		//初始化累加器中的值
		//第一次初始化 或者 第二天的时候更新缓存
		if ( !time_day.equals(accumulator.historyUpTime)){
			Long accumulateValue = innitAccumulateValue( key,time_day,diffValue);
			accumulator.historyUpTime=time_day;
			accumulator.historyCount=accumulateValue;
		}
		try {
			accumulator.add(value);
		} catch (Exception e) {
			logger.error("mapview put failed:{}",value);
			throw new RuntimeException(e);
		}
	}

	public void merge(MergeDistinctMetricACC accumulator,Iterable<MergeDistinctMetricACC> iterables) {
		for (MergeDistinctMetricACC iterable : iterables) {
			try {
				accumulator.merge(iterable);
			} catch (Exception e) {
				logger.error("mapview merge failed");
				throw new RuntimeException(e);
			}
		}

	}
	public void retract(MergeDistinctMetricACC accumulator, String value,String key ,String time_day,Long diffValue) {
		try {
			accumulator.remove(value);
		} catch (Exception e) {
			logger.error("mapview remove failed:{}",value);
			throw new RuntimeException(e);
		}
	}

	public Long innitAccumulateValue(String key ,String time_day,Long diffValue){
		//初始化历史状态
		Long count=0L;
		getHBaseClient();
		String spu_id = stringForMart(key, 20);
		String zero = "00000000000000000000";
		String rowKeyStart=spu_id+"_"+zero;
		String rowKeyEnd=spu_id+"|"+zero;

		Long timeStampByDay = TimeUtils.strToTimeStamp(time_day, DateTimeFormatters.Pattern_yyyyMMdd);
		Long firstTime = TimeUtils.timeSubtract(timeStampByDay, diffValue, ChronoUnit.DAYS);
		Long endTime = TimeUtils.timeAdd(timeStampByDay, 1L, ChronoUnit.DAYS);
		String firstTimeFormatByDay = TimeUtils.timeFormatByDayyyyMMdd(firstTime);
		try {

			count = hBaseClient.getScanRowKeyByFamilyTimeRangeAndColumnValueFilterCount(
					rowKeyStart,
					rowKeyEnd,
					firstTime * 1000,
					endTime * 1000,
					columnFamily,
					getCompareOperator(compareOperator),
					filterQualifier,
					firstTimeFormatByDay
			);

//            logger.info("spuID:{}初始化成功历史UV:{}",key,count);

		} catch (IOException e) {
			logger.error("spuID:{}初始化成功历史 失败",key);
			throw new RuntimeException(e);
		}
		return count;
	}

	public void  getHBaseClient(){
		if (hBaseClient == null){
			synchronized (HBaseClient.class){
				if (hBaseClient==null){
					hBaseClient=new HBaseClient(conf, tableName, threadPoolExecutor) ;
				}
			}
		}
	}

	public  String stringForMart(String value,Integer maxSize){
		int userIdSize = value.length();
		String s="";
		if (userIdSize >maxSize){
			logger.error("userId:{} 长度超过{}",value,maxSize);
		}else {
			char[] c = new char[maxSize-userIdSize];
			Arrays.fill(c, '0');
			s = new String(c);
		}
		return s+value;
	}
	// Keeps same names as the enums over in filter's CompareOp intentionally.
	// The convertion of operator to protobuf representation is via a name comparison.
	/* less than */
	// LESS
	/* less than or equal to */
	// LESS_OR_EQUAL
	/* equals */
	// EQUAL
	/* not equal */
	// NOT_EQUAL
	/* greater than or equal to */
	// GREATER_OR_EQUAL
	/* greater than */
	// GREATER
	/* no operation */
	// NO_OP
	public CompareOperator getCompareOperator(String value){
		switch(value){
			case "LESS" :
				return CompareOperator.LESS;
			case "LESS_OR_EQUAL" :
				return CompareOperator.LESS_OR_EQUAL;
			case "GREATER_OR_EQUAL" :
				return CompareOperator.GREATER_OR_EQUAL;
			case "GREATER" :
				return CompareOperator.GREATER;
			case "EQUAL" :
				return CompareOperator.EQUAL;
			case "NO_OP" :
				return CompareOperator.NO_OP;
			case "NOT_EQUAL" :
				return CompareOperator.NOT_EQUAL;
		}
		logger.error("CompareOperator is null");
		throw new RuntimeException("AggregateFunction  CompareOperator is null");
	}

	public static void main(String[] args) {
		String zero = "00000000000000000000";
		System.out.println(zero.length());
	}
}
