package com.alibaba.blink.udx.udaf.acc;


import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.table.api.dataview.MapView;

import java.util.Map;
import java.util.Set;


public class MergeDistinctMetricACC {
    public  MapView<String,Long> distinctValueMap;
    public  long mapCount;
    public  long historyCount;
    public  String historyUpTime;


    public MergeDistinctMetricACC() {
        this.distinctValueMap = new MapView<>(Types.STRING,Types.LONG);
    }

    public long getValue(){
        return mapCount+historyCount;
    }
    public void add(String params) throws Exception {
        Long currentCnt = distinctValueMap.get(params);
        if (currentCnt != null) {
            distinctValueMap.put(params, currentCnt + 1L);
        } else {
            distinctValueMap.put(params, 1L);
            mapCount++;
        }
    }

    public void add(String params,Long count) throws Exception {
        Long currentCnt = distinctValueMap.get(params);
        if (currentCnt != null) {
            distinctValueMap.put(params, currentCnt + count);
        } else {
            distinctValueMap.put(params, count);
            mapCount++;
        }
    }
    public void remove(String params) throws Exception {
        Long currentCnt = distinctValueMap.get(params);
        if (currentCnt == 1) {
            distinctValueMap.remove(params);
            mapCount--;
        } else {
            distinctValueMap.put(params, currentCnt - 1L);
        }
    }

    public void merge(MergeDistinctMetricACC otherAcc) throws Exception {
        this.historyUpTime=otherAcc.historyUpTime;
        this.historyCount=otherAcc.historyCount;
        for (Map.Entry<String, Long> element : otherAcc.elements()) {
            String key = element.getKey();
            long l = this.distinctValueMap.get(key) == null ? 0L : this.distinctValueMap.get(key);
            this.distinctValueMap.put(key,l+element.getValue());
        }
    }

    public void reset(){
        distinctValueMap.clear();
        mapCount=0L;
    }

    public Set<Map.Entry<String, Long>> elements(){
        return distinctValueMap.getMap().entrySet();
    }

}