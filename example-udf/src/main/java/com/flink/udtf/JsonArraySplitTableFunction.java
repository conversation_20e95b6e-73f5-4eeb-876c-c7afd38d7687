package com.flink.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class JsonArraySplitTableFunction extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(JsonArraySplitTableFunction.class);

    private transient Counter dataLossCounter;

    @Override
    public void open(FunctionContext context) throws Exception {
        MetricGroup metricGroup = context.getMetricGroup().addGroup("table_function", this.functionIdentifier());
        this.dataLossCounter = metricGroup.counter("data_loss");
    }

    public void eval(String jsonArrayString, String... keys) {
        if (jsonArrayString != null && jsonArrayString.trim().length() > 0) {
            try {
                Object object = JSON.parse(jsonArrayString);
                if (object instanceof JSONArray) {
                    JSONArray jsonArray = (JSONArray) object;
                    if (!jsonArray.isEmpty()) {
                        for (int index = 0; index < jsonArray.size(); index++) {
                            String content = jsonArray.getString(index);
                            if (keys == null || keys.length == 0) {
                                Row row = new Row(1);
                                row.setField(0, content);
                                collect(row);
                            } else {
                                Object contentObject = JSON.parse(content);
                                Row row = new Row(keys.length);
                                for (int i = 0; i < keys.length; i++) {
                                    Object valueObject = JSONPath.eval(contentObject, keys[i]);
                                    if (valueObject == null) {
                                        row.setField(i, null);
                                    } else if (valueObject instanceof JSON) {
                                        row.setField(i, ((JSON) object).toJSONString());
                                    } else {
                                        row.setField(i, valueObject.toString());
                                    }
                                }
                                collect(row);
                            }
                        }
                    }
                } else if (object instanceof JSONObject) {
                    if (keys == null || keys.length == 0) {
                        Row row = new Row(1);
                        row.setField(0, jsonArrayString);
                        collect(row);
                    } else {
                        Row row = new Row(keys.length);
                        for (int i = 0; i < keys.length; i++) {
                            Object valueObject = JSONPath.eval(object, keys[i]);
                            if (valueObject == null) {
                                row.setField(i, null);
                            } else if (valueObject instanceof JSON) {
                                row.setField(i, ((JSON) object).toJSONString());
                            } else {
                                row.setField(i, valueObject.toString());
                            }
                        }
                        collect(row);
                    }
                } else {
                    //不能解析的数据
                    this.dataLossCounter.inc();
                }
            } catch (Exception e) {
                LOG.error(
                        String.format(
                                "JsonArraySplit parse value exception: %s\njsonString = %s\n",
                                e.getMessage(),
                                jsonArrayString)
                        , e);
                //非json数据
                this.dataLossCounter.inc();
            }

        } else {
            //空数据
            this.dataLossCounter.inc();
        }
    }

    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() > 1) {
                        DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1];
                        for (int i = 0; i < argumentDataTypes.size(); i++) {
                            DataType parameterDataType = argumentDataTypes.get(i);
                            if (!parameterDataType
                                    .getLogicalType()
                                    .getTypeRoot()
                                    .getFamilies()
                                    .contains(
                                            LogicalTypeFamily.CHARACTER_STRING)) {
                                throw new ValidationException(String.format(
                                        "function %s argument %s data type is not %s",
                                        functionName,
                                        i + 1,
                                        DataTypes.STRING().toString()));
                            }
                            if (i > 0) {
                                outputDataTypes[i - 1] = DataTypes.STRING();
                            }
                        }
                        return Optional.of(DataTypes.ROW(outputDataTypes));
                    } else if (argumentDataTypes.size() == 1) {
                        return Optional.of(DataTypes.ROW(DataTypes.STRING()));
                    } else {
                        throw new ValidationException(String.format(
                                "function %s argument is empty",
                                functionName));
                    }
                })
                .build();
    }
}
