package com.flink.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;

import java.util.List;
import java.util.Optional;

public class JsonDeleteValueFunction extends ScalarFunction {

    public String eval(String jsonString, String... jsonPaths) {
        JSON json = null;
        try {
            Object object = JSON.parse(jsonString);
            if (object instanceof JSON) {
                json = (JSON)object;
            } else {
                throw new ValidationException(String.format(
                        "jsonString %s is not json",
                        jsonString));
            }
        } catch (Throwable throwable) {
            throw new ValidationException(String.format(
                    "jsonString %s is not json",
                    jsonString));
        }

        for (String jsonPath : jsonPaths) {
            JSONPath.remove(json, jsonPath);
        }
        return json.toJSONString();
    }


    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() < 2) {
                        throw new ValidationException(String.format(
                                "function %s argument should be (STRING json, STRING jsonPath ...)",
                                functionName));
                    }

                    //校验jsonpath 字段类型为STRING
                    List<DataType> jsonPathValues = argumentDataTypes.subList(
                            1,
                            argumentDataTypes.size());
                    for (DataType dataType : jsonPathValues) {
                        if (!dataType
                                .getLogicalType()
                                .getTypeRoot()
                                .getFamilies()
                                .contains(
                                        LogicalTypeFamily.CHARACTER_STRING)) {
                            throw new ValidationException(String.format(
                                    "function %s argument should be (STRING json, STRING jsonPath ...)",
                                    functionName));
                        }
                    }

                    return Optional.of(DataTypes.STRING());
                })
                .build();
    }
}
