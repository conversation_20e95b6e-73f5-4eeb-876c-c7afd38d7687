package com.flink.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONPath;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;

import java.util.List;
import java.util.Optional;

public class JsonUpdateValueFunction extends ScalarFunction {

    public String eval(String jsonString, Object... keyValues) {
        JSON json = null;
        try {
            Object object = JSON.parse(jsonString);
            if (object instanceof JSON) {
                json = (JSON) object;
            } else {
                throw new ValidationException(String.format(
                        "jsonString %s is not json",
                        jsonString));
            }
        } catch (Exception exception) {
            throw new ValidationException(String.format("jsonString %s is not json", jsonString));
        }

        String jsonPath = null;
        for (int i = 0; i < keyValues.length; i++) {
            Object object = keyValues[i];
            if (i % 2 == 0) {
                jsonPath = (String) object;
            } else {
                if (object instanceof String) {
                    try {
                        Object parsedObj = JSON.parse((String)object);
                        JSONPath.set(json, jsonPath, parsedObj);
                    } catch (JSONException e) {
                        JSONPath.set(json, jsonPath, object);
                    }
                } else {
                    JSONPath.set(json, jsonPath, object);
                }
            }
        }

        return json.toJSONString();

    }

    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() % 2 == 0 || argumentDataTypes.size() < 3) {
                        throw new ValidationException(String.format(
                                "function %s argument should be (STRING json, STRING jsonPath, Object value ...)",
                                functionName));
                    }

                    //校验jsonpath 字段类型为STRING
                    List<DataType> jsonPathValues = argumentDataTypes.subList(
                            1,
                            argumentDataTypes.size());
                    for (int i = 0; i < jsonPathValues.size(); i++) {
                        DataType dataType = jsonPathValues.get(i);
                        if (i % 2 == 0 && !dataType
                                .getLogicalType()
                                .getTypeRoot()
                                .getFamilies()
                                .contains(
                                        LogicalTypeFamily.CHARACTER_STRING)) {
                            throw new ValidationException(String.format(
                                    "index %s argument is jsonPath should be STRING",
                                    i + 1));
                        }
                    }

                    return Optional.of(DataTypes.STRING());
                })
                .build();
    }

    public static void main(String[] args) {
        String out = "{\"_track_id\":-1765615153,\"login_id\":\"62444444\",\"lib\":{\"$lib_method\":\"code\",\"$lib\":\"Android\",\"$lib_version\":\"6.0.1\",\"$lib_detail\":\"com.sensorsdata.analytics.android.sdk.AbstractSensorsDataAPI##trackEventInternal##AbstractSensorsDataAPI.java##93\",\"$app_version\":\"4.82.5\"},\"distinct_id\":\"62444444\",\"anonymous_id\":\"79221998dca6317f\",\"_flush_time\":1640001829904,\"time\":1640001829805,\"type\":\"track\",\"event\":\"trade_product_detail_block_exposure\",\"properties\":{\"$screen_orientation\":\"portrait\",\"block_type\":\"1704\",\"$os\":\"Android\",\"$wifi\":true,\"$network_type\":\"WIFI\",\"$ip\":\"**************\",\"$screen_height\":2160,\"device_uuid\":\"79221998dca6317f\",\"dw_userid\":\"62444444\",\"$device_id\":\"79221998dca6317f\",\"block_position\":6,\"$app_id\":\"com.shizhuang.duapp\",\"sk\":\"KAOUtrE7oa9OZzmEosIIjRs%2BKDMWwqp28YuB4lxhauiTahXILL1Nya1iQbMBCwBa\",\"ipvx\":\"**************\",\"spu_id\":1378545,\"_time\":1640001829821,\"oaid\":\"f59fcb3e-b17b-20dd-f3f9-debfdbb7c584\",\"user_agent\":\"/duapp/4.82.5(android;10)\",\"current_page\":\"400000\",\"$lib_method\":\"code\",\"$os_version\":\"10\",\"time_offset\":461,\"$is_first_day\":false,\"$model\":\"BKL-AL20\",\"receive_time\":1640001830590,\"$screen_width\":1080,\"session_id\":\"79221998dca6317f1640001686309\",\"android_channel\":\"huawei\",\"$brand\":\"HONOR\",\"$app_version\":\"4.82.5\",\"$lib\":\"Android\",\"app_build\":\"*********\",\"$app_name\":\"得物(毒)\",\"visit_mode\":\"1\",\"$lib_version\":\"6.0.1\",\"$timezone_offset\":-480,\"shumei_id\":\"20200906120237bc8dbab41e7cb5125e1b41f30f776acb01684482bc36fe60\",\"imei\":\"\",\"android_id\":\"79221998dca6317f\",\"$manufacturer\":\"HUAWEI\",\"screen_ratio\":0.88}}";
        String inside = "{\"$screen_orientation\":\"portrait\",\"block_type\":\"1704\",\"$os\":\"Android\",\"$wifi\":true,\"$network_type\":\"WIFI\",\"$ip\":\"**************\",\"$screen_height\":2160,\"device_uuid\":\"79221998dca6317f\",\"dw_userid\":\"62444444\",\"$device_id\":\"79221998dca6317f\",\"block_position\":6,\"$app_id\":\"com.shizhuang.duapp\",\"sk\":\"KAOUtrE7oa9OZzmEosIIjRs%2BKDMWwqp28YuB4lxhauiTahXILL1Nya1iQbMBCwBa\",\"ipvx\":\"**************\",\"spu_id\":1378545,\"_time\":1640001829821,\"oaid\":\"f59fcb3e-b17b-20dd-f3f9-debfdbb7c584\",\"user_agent\":\"/duapp/4.82.5(android;10)\",\"current_page\":\"400000\",\"$lib_method\":\"code\",\"$os_version\":\"10\",\"time_offset\":461,\"$is_first_day\":false,\"$model\":\"BKL-AL20\",\"receive_time\":1640001830590,\"$screen_width\":1080,\"session_id\":\"79221998dca6317f1640001686309\",\"android_channel\":\"huawei\",\"$brand\":\"HONOR\",\"$app_version\":\"4.82.5\",\"$lib\":\"Android\",\"app_build\":\"*********\",\"$app_name\":\"得物(毒)\",\"visit_mode\":\"1\",\"$lib_version\":\"6.0.1\",\"$timezone_offset\":-480,\"shumei_id\":\"20200906120237bc8dbab41e7cb5125e1b41f30f776acb01684482bc36fe60\",\"imei\":\"\",\"android_id\":\"79221998dca6317f\",\"$manufacturer\":\"HUAWEI\",\"screen_ratio\":0.88}";
//        String inside = "aaa";


        JsonUpdateValueFunction function = new JsonUpdateValueFunction();
        function.eval(out, "properties", inside);
    }

}
