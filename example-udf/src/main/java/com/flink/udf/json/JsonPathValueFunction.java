package com.flink.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import org.apache.flink.metrics.Counter;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class JsonPathValueFunction extends ScalarFunction {

    private static final long serialVersionUID = 1L;

    private transient Counter counter;

    @Override
    public boolean isDeterministic() {
        return true;
    }

    @Override
    public void open(FunctionContext context) throws Exception {
        counter = context.getMetricGroup().counter("json_value_exception");
    }

    /**
     * json_path_value
     *
     * @param jsonString
     * @param jsonPath
     * @return
     */
    public String eval(String jsonString, String jsonPath) {
        String errorMessagePrefix = "function json_path_value(String jsonString, String jsonPath) ";
        if (jsonPath == null || jsonPath.trim().equals("")) {
            throw new RuntimeException(errorMessagePrefix + " jsonPath must not be null or empty.");
        }
        if (jsonString == null || jsonString.trim().equals("")) {
//            throw new RuntimeException(errorMessagePrefix + " jsonString must not be null or empty.");
        }

        Object object;
        try {
            object = JSON.parse(jsonString);
        } catch (Exception e) {
            if (counter != null) {
                counter.inc();
            }
            return null;
        }

        if (object instanceof JSON) {
            Object valueObject = JSONPath.eval(object, jsonPath);
            if (valueObject == null) {
                return null;
            } else if (valueObject instanceof JSON) {
                return ((JSON) valueObject).toJSONString();
            } else {
                return valueObject.toString();
            }
        } else {
            if (counter != null) {
                counter.inc();
            }
            return null;
        }
    }

}
