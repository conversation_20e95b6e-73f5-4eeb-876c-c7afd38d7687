package com.flink.udf;


import org.apache.flink.table.functions.ScalarFunction;


public class conv extends ScalarFunction {

    public String eval(String input, Integer fromFormat, Integer toFormat) {
        if (null == input || null == fromFormat || null == toFormat || input.startsWith("-")) {
            return null;
        }

        if (!fromFormat.equals(2) || !toFormat.equals(10)) {
            throw new UnsupportedOperationException("only support convert from 2 to 10");
        }

        Integer decimal = Integer.parseInt(input, 2);

        return decimal.toString();
    }

    public static void main(String[] args) {
        System.err.println(new conv().eval("9.1", 2, 10));
    }
}