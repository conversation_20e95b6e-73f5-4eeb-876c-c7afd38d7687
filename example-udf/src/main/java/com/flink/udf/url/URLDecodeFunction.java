package com.flink.udf.url;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.net.URLDecoder;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class URLDecodeFunction extends ScalarFunction {

    public String eval(String urlDecodeContent) {
        if (urlDecodeContent != null) {
            return URLDecoder.decode(urlDecodeContent);
        }
        return null;
    }
}
