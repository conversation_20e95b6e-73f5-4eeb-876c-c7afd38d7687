package com.flink.udf.url;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.net.URLEncoder;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class URLEncodeFunction extends ScalarFunction {

    public String eval(String urlContent) {
        if (urlContent != null) {
            return URLEncoder.encode(urlContent);
        }
        return null;
    }
}
