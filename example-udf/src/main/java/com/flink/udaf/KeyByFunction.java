package com.flink.udaf;

import org.apache.flink.table.api.dataview.ListView;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/11/30 4:16 下午
 */
public class KeyByFunction extends AggregateFunction<String, KeyByFunction.CollectAccumulator<String>> {
    /**
     * 初始化AggregateFunction的accumulator
     * 系统在第一次做aggregate计算之前调用一次这个方法
     * @return
     */
    @Override
    public CollectAccumulator<String> createAccumulator() {
        final CollectAccumulator<String> acc = new CollectAccumulator<>();
        acc.listView = new ListView<>();
        return acc;
    }

    @Override
    public String getValue(CollectAccumulator<String> accumulator) {
        if (accumulator.listView.getList().isEmpty()) {
            return null;
        } else {
            return accumulator.listView.getList().remove(0);
        }
    }

    public void accumulate(CollectAccumulator<String> accumulator, String value) throws Exception {
        accumulator.listView.add(value);
    }

    public void resetAccumulator(CollectAccumulator<String> accumulator) {
        accumulator.listView.clear();
    }


    // --------------------------------------------------------------------------------------------
    // Runtime
    // --------------------------------------------------------------------------------------------

    /** Accumulator for COLLECT. */
    public static class CollectAccumulator<T> {
        public ListView<T> listView;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            CollectAccumulator<?> that = (CollectAccumulator<?>) o;
            return Objects.equals(listView, that.listView);
        }
    }
}
