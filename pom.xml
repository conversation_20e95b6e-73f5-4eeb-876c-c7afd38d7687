<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shizhuang</groupId>
    <artifactId>du-tech-data-udf</artifactId>
    <packaging>pom</packaging>
    <version>1.0</version>
    <modules>
        <module>common-udf</module>
        <module>example-udf</module>
        <module>mysql-udf</module>
        <module>activate-udtf</module>
        <module>MergeMetricToHistoryAndRealtime</module>
        <module>getbucket-udf</module>
        <module>AbTestDimJoin</module>
        <module>PopulationHandler</module>
        <module>CutAdvTitleService</module>
        <module>flink-udf-runner</module>
        <module>sensors-deal-udf</module>
        <module>RunRdsSQL</module>
        <module>protobuf-udf</module>
        <module>service-udf-16</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <lombok.version>1.18.6</lombok.version>
        <flink-table-common.version>du-1.16-SNAPSHOT</flink-table-common.version>
        <fastjson.version>1.2.83</fastjson.version>
        <ip2region.version>1.7.2</ip2region.version>
        <gson-parent.version>2.8.5</gson-parent.version>
        <flink-shaded-jackson.version>2.12.1-13.0</flink-shaded-jackson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-table-common</artifactId>
                <version>${flink-table-common.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-shaded-jackson</artifactId>
                <version>${flink-shaded-jackson.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.poizon.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.poizon.com/nexus/content/repositories/releases/</url>
        </repository>
    </distributionManagement>
</project>
