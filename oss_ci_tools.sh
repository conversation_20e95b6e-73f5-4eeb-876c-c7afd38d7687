#!/bin/bash

mvn clean package -Dmaven.test.skip=true -pl common-udf

HOST="oss-cn-hangzhou-internal.aliyuncs.com"
HOST_TEST="oss-cn-shanghai.aliyuncs.com"

BUCKET="bigdata-libra-bucket"
BUCKET_TEST="dw-platform-libra"

PART0="L"
PART1="TAI5tGE45r"
PART2="AfYZfx4swJ2Z2"
SPART1="lbU1X3yTmQATlBOh"
SPART2="VKTScBhXBAx8u9"
ACCESS_ID=$PART0$PART1$PART2
ACCESS_KEY=$SPART1$SPART2

LIBRA_URL="https://libra.shizhuang-inc.com/libra/web/jar/addJar"
LIBRA_URL_TEST="http://t1-bigdata-libra.shizhuang-inc.net/libra/web/jar/addJar"

creatr_email=""

param_num=$#
path_pwd=`pwd`
echo "current path:  ${path_pwd}"

echo "ci oss param num: ${param_num}"
echo $6

function upload_oss() {
  file_from=${1}
  file_to=${2}
  content_type=`file -ib ${file_from} | awk -F ";" '{print $1}'`
  resource="/${BUCKET}/${file_to}"
  date_value="`TZ=GMT env LANG=en_US.UTF-8 date +'%a, %d %b %Y %H:%M:%S GMT'`"
  sign="PUT\n\n${content_type}\n${date_value}\n${resource}"
  signature=`echo -en ${sign} | openssl sha1 -hmac ${ACCESS_KEY} -binary | base64`
  url=http://${BUCKET}.${HOST}/${file_to}
  echo "url is: ${url}"
  curl -i -q -X PUT -T "${file_from}" \
    -H "Host: ${BUCKET}.${HOST}" \
    -H "Date: ${date_value}" \
    -H "Content-Type: ${content_type}" \
    -H "Authorization: OSS ${ACCESS_ID}:${signature}" \
    ${url}
}

function libra_add(){
  echo "libra url is: ${LIBRA_URL}"
  curl -i -X POST \
     -H "Content-Type: application/json" \
     -d "{\"projectName\": \"${1}\", \"taskName\": \"${2}\", \"jarName\": \"${3}\", \"jarPath\": \"${4}\", \"creator\": \"${5}\"}" \
     ${LIBRA_URL}
}

function upload() {
  echo "upload param: ${1} ${2} ${3}"
  path_from=${3}
  file_from="$(basename ${path_from})"
  file_from_ext="${file_from##*.}"
  file_from_name="${file_from%.*}"
  current_time=`date "+%Y%m%d%H%M%S"`
  path_to=${2}
  path_to_arr=(${path_to//,/ })

  if [[ ${1} == '-j' ]]
  then
    for task_name in ${path_to_arr[@]}
    do
      oss_file_name="ci-job-${file_from_name}-${current_time}"
      oss_file_path="libra/user/jars/ci/job/${task_name}/${oss_file_name}.${file_from_ext}"
      upload_oss "${path_pwd}/${path_from}" "${oss_file_path}"
      libra_add "" "${task_name}" "${oss_file_name}" "${oss_file_path}" "${creatr_email}"
    done
  elif [[ ${1} == '-p' ]]
  then
    for project_name in ${path_to_arr[@]}
    do
      oss_file_name="ci-project-${file_from_name}-${current_time}"
      oss_file_path="libra/user/jars/ci/project/${project_name}/${oss_file_name}.${file_from_ext}"
      upload_oss "${path_pwd}/${path_from}" "${oss_file_path}"
      libra_add "${project_name}" "" "${oss_file_name}" "${oss_file_path}" "${creatr_email}"

    done
  else
    echo "param is invalid ${1}"
  fi
}

function upload_wrap() {
  if [[ ${1} == '-j' || ${1} == '-p' ]]
  then
    upload ${1} ${2} ${3}
  elif [[ ${1} == '-e' ]]
  then
    if [[ ${2} == 'test' ]]
    then
      LIBRA_URL=${LIBRA_URL_TEST}
      HOST=${HOST_TEST}
      BUCKET=${BUCKET_TEST}
    fi
  elif [[ ${1} == '-u' ]]
  then
    creatr_email=${2}
  else
    echo echo "param is invalid ${1}, ${2}"
  fi
}

if [[ ${param_num} == 5 ]]
then
  upload_wrap ${1} ${2} ${5}
  upload_wrap ${3} ${4} ${5}
fi

if [[ ${param_num} == 7 ]]
then
  upload_wrap ${1} ${2} ${7}
  upload_wrap ${3} ${4} ${7}
  upload_wrap ${5} ${6} ${7}
fi

if [[ ${param_num} == 9 ]]
then
  upload_wrap ${1} ${2} ${9}
  upload_wrap ${3} ${4} ${9}
  upload_wrap ${5} ${6} ${9}
  upload_wrap ${7} ${8} ${9}
fi
