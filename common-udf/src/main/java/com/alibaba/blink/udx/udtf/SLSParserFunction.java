package com.alibaba.blink.udx.udtf;

import com.alibaba.blink.udx.entity.AdServingBean;
import com.google.gson.Gson;
import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: Dereck Li
 * @Date: 4/14/22 8:25 下午
 */
@FunctionHint(
    input = {@DataTypeHint("STRING")},
    output = @DataTypeHint("ROW<lv1 STRING, lv2 STRING, lv3 STRING, bid STRING, uid STRING, "
        + "is_new STRING >")
)
@Slf4j
public class SLSParserFunction extends TableFunction<Row> {

  private static final Logger logger = LoggerFactory.getLogger(SLSParserFunction.class);


  /**
   * 功能描述 [algo_tags_false]tags:{CategoryLv1Id:[] CategoryLv2Id:[] CategoryLv3Id:[] BrandId:[]
   * Sex:[]},request:map[ad_name:adq+社区投放+穿搭+空+代理自产+安利+横版大图+空+空+代理选品+0726+包3+得物2+sl+0726-3
   * ad_site:25 adgroup_id:3730747438 adgroup_name:adq+社区投放+穿搭+空+代理自产+安利+横版大图+空+空+代理选品+0726+包3
   * +得物2+sl+0726-3 advertiser_id:19182370 agent_id:19498993 aid:19182370 algo_tags:
   * android_id:ef1107128fde07c69aa207f93d37ab48 app_id:duapp app_type:android appid:1104972716
   * campaign_id:3729958325 campaign_name:adq-社区-横版大图+穿搭-包3-常规多-20210726-1
   * channel_package_id:000116083935393534323134 cid:492940164 click_id:iptrqyi7aaafmhzvalma
   * click_sku_id:2306237453218746709 click_time:1629022050 content_id: creative_id:492940164
   * creative_name:动态创意_202107261433211 deeplink_url: dest_url:http://www.myapp.com
   * device_os_type:android device_os_version:11 event_time:1629022050 four_channel:adq
   * gid:3729958325 imei:6c4650d19278b1116b5cea6994710c37 impression_id:gmdspnqo7hjvk01
   * ip:************* ipua:2f6fb6cd2dce96aead0ada5778c813b8 ipv6: jump_tag: media:guangdiantong
   * module_type:0 muid:6c4650d19278b1116b5cea6994710c37
   * oaid:939D825EE2C41EBBA3D41AE534F42D774C1D4C5B8BEF7DF3EE37A6D9843D6B62
   * os:android package_name:3730747438 pid:3730747438 process_time:1629022019 product_type:12
   * request_id:gmdspnqo7hjvk rid:iptrqyi7aaafmhzvalma site_set:25
   * site_set_name:SITE_SET_QQ_MUSIC_GAME
   * tags: tid:9641ef2fddf28a81165cd088cf22de9d ua:Dalvik/2.1.0 (Linux; U; Android 11; PD2068B
   * Build/RP1A.200720.012) user_agent:Dalvik/2.1.0 (Linux; U; Android 11; PD2068B
   * Build/RP1A.200720.012)]
   * <p>
   * String message = "[algo-tags]value:map[algo:{\"category_lv1_id\":[\"29\"],
   * \"category_lv2_id\":null,\"category_lv3_id\":[\"31\"],\"brand_id\":null,\"sex\":\"\"} content:
   * spu: tags:],request:&{UserId:1678273399 AppId: Os:android PkgChannel:huawei Imei:
   * Oaid:*************-4f7b-b1a7-bffa68b19b55 AndroidId:58d7fa084a7d0551 Idfa: Fcuuid:
   * Uuid:58d7fa084a7d0551 Clipboard: Ua: Ip:***************},isNew:false";
   * Sex的消息格式不统一，有的为数组，有的为字符串，然后需求不需要提取sex标签，该标签就不冗余进来了。 功能描述
   *
   * @param
   * @return
   * <AUTHOR>
   * @date 2021/8/18
   */
  public void eval(String message) throws Exception {
    try {
      int length = message.length();
      Gson gson = new Gson();
      AdServingBean adServingBean = new AdServingBean();
      int startFlag = message.indexOf("[algo-tags]");
      if (startFlag != -1) {
        startFlag = message.indexOf("algo:{");
        if (startFlag != -1) {
          String substring = message.substring(startFlag, length);
          int startIndex = substring.indexOf("{");
          int lastIndex = substring.indexOf("}");
          String substr = substring.substring(startIndex, lastIndex + 1);
          adServingBean = gson.fromJson(substr, AdServingBean.class);
        }
        int userFlag = message.indexOf("UserId:");
        if (userFlag == -1) {
          System.out.println("不存在UserId" + message);
        } else {
          String substring = message.substring(userFlag, length);
          int startIndex = substring.indexOf(":");
          int lastIndex = substring.indexOf(" ");
          String substr = substring.substring(startIndex + 1, lastIndex);
          adServingBean.setUser_id(substr);
          boolean isNew = message.contains("isNew:true");
          if (isNew) {
            adServingBean.setIs_new("true");
          } else {
            adServingBean.setIs_new("false");
          }
        }
      }
      Row row = new Row(6);
      row.setField(0, ArraysToString(adServingBean.getCategory_lv1_id()));
      row.setField(1, ArraysToString(adServingBean.getCategory_lv2_id()));
      row.setField(2, ArraysToString(adServingBean.getCategory_lv3_id()));
      row.setField(3, ArraysToString(adServingBean.getBrand_id()));
//            row.setField(4, ArraysToString(adServingBean.getSex()));
      row.setField(4, adServingBean.getUser_id());
      row.setField(5, adServingBean.getIs_new());
      collect(row);
    } catch (Exception e) {
      ByteArrayOutputStream stream = new ByteArrayOutputStream();
      e.printStackTrace(new PrintStream(stream));
      String exception = stream.toString();
      throw new Exception(exception + " message:" + message);
    }
  }

  public static String ArraysToString(Object[] a) {
    if (a == null) {
      return null;
    }

    int iMax = a.length - 1;
    if (iMax == -1) {
      return null;
    }

    StringBuilder b = new StringBuilder();
    for (int i = 0; ; i++) {
      b.append(String.valueOf(a[i]));
      if (i == iMax) {
        return b.toString();
      }
      b.append(",");
    }
  }

  public static void main(String[] args) throws Exception {
    String message = "[algo-tags]value:map[algo:{\"category_lv1_id\":[],\"category_lv2_id\":null,"
        + "\"category_lv3_id\":[\"31\",\"33\"],\"brand_id\":[\"33\"],\"sex\":\"\"} content: cspu:"
        + " tags:],request:&{UserId:6184357 AppId:duapp Os:ios PkgChannel: Imei: Oaid: "
        + "AndroidId:null Idfa:BA221151-495B-4030-BB85-D669D0583BEC "
        + "Fcuuid:32c5bdad1cef48c899a2150a4ffdfd18 Uuid:BA221151-495B-4030-BB85-D669D0583BEC "
        + "Clipboard: Ua:Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605"
        + ".1.15 (KHTML, like Gecko) Mobile/15E148 Ip:*************** Source:activate "
        + "EventTime:1630917196 ShumeiId: OrderNo:},isNew:false";
    SLSParserFunction slsParserFunction = new SLSParserFunction();

    slsParserFunction.eval(message);

  }

}
