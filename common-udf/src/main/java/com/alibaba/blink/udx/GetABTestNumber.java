package com.alibaba.blink.udx;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * <AUTHOR>
 * @Date 2021/9/23 7:30 下午
 */
@FunctionHint(input = {@DataTypeHint("BIGINT")}, output = @DataTypeHint("BIGINT"))
public class GetABTestNumber extends ScalarFunction {
    public long eval(Long userId){
        return Math.abs((userId + "").hashCode()) % 100;
    }
}
