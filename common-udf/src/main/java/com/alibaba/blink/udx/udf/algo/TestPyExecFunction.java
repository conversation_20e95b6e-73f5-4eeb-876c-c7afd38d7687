package com.alibaba.blink.udx.udf.algo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.flink.table.functions.ScalarFunction;
import pemja.core.PythonInterpreter;
import pemja.core.PythonInterpreterConfig;
import pemja.core.object.PyObject;

import java.io.File;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/25 16:51
 */
public class TestPyExecFunction extends ScalarFunction {

    public String eval(String value) throws Exception {

        String path = "/Users/<USER>/PycharmProjects/yolov8_kserve/";

        PythonInterpreterConfig config = PythonInterpreterConfig
                .newBuilder()
                .setPythonExec("/usr/local/bin/python3.9") // specify python exec
                .addPythonPaths(path) // add path to search path
                .addPythonPaths("/Users/<USER>/Library/Python/3.9/lib/python/site-packages")
                .setExcType(PythonInterpreterConfig.ExecType.SUB_INTERPRETER)
                .build();

        PythonInterpreter pythonInterpreter = new PythonInterpreter(config);

// invoke functions
//        pythonInterpreter.exec("import numpy");
//        pythonInterpreter.exec("import app3");
//        PyObject model = (PyObject)pythonInterpreter.invoke("app2.innitmodel");
//        Object object = model.invokeMethod("predict", arg11);
//        System.out.println(JSON.toJSONString(object));
//        Object object1 = model.invokeMethod("predict", arg11);
//        System.out.println(JSON.toJSONString(object1));
//        model.close();
//        pythonInterpreter.close();
//        pythonInterpreter.exec("a = app3.Yolov()");
//        PyObject model = (PyObject) pythonInterpreter.get("a");
//        Map<Object, Object> arg11 = new HashMap<>();
//        Map<Object, Object> arg12 = new HashMap<>();
//        arg12.put("url","https://ultralytics.com/images/bus.jpg");
//        JSONArray objects = new JSONArray();
//        objects.add(arg12);
//        arg11.put("instances",objects.toArray());
//
//
//        model.invokeMethod("open");
//        Object o = model.invokeMethod("process", arg11);
//        System.out.println(JSON.toJSONString(o));
        return null;
    }

    public static void main(String[] args) throws Exception {
       // System.load("/opt/homebrew/opt/python@3.9/Frameworks/Python.framework/Versions/3.9/lib/python3.9/site-packages/pemja_core.cpython-39-darwin.so");
//        PyExecFunction pyExecFunction = new PyExecFunction();
//
//        pyExecFunction.eval("");

        String pythonModulePath =
                String.join(
                        File.separator,
                        System.getProperty("user.dir"),
                        "src",
                        "main",
                        "python");
        File pythonModuleFile = new File(pythonModulePath);
       System.out.println();
    }
}
