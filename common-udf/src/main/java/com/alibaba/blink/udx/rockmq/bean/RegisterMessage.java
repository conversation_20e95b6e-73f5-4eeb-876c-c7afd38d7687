package com.alibaba.blink.udx.rockmq.bean;

public class RegisterMessage {

    private String appVersion;
    private String channel;
    private String deviceId;
    private String invokeTime;
    private String ip;
    private String oaid;
    private String imei;
    private String platform;
    private String shumeiId;
    private String ua;

//    AppVersion string `json:"appVersion"`
//    Channel    string `json:"channel"`
//    DeviceId   string `json:"deviceId"`
//    InvokeTime string `json:"invokeTime"`
//    Ip         string `json:"ip"`
//    Oaid       string `json:"oaid"`
//    Imei       string `json:"imei"`
//    Platform   string `json:"platform"`
//    ShumeiId   string `json:"shumeiId"`
//    Ua         string `json:"ua"`


    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getInvokeTime() {
        return invokeTime;
    }

    public void setInvokeTime(String invokeTime) {
        this.invokeTime = invokeTime;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getShumeiId() {
        return shumeiId;
    }

    public void setShumeiId(String shumeiId) {
        this.shumeiId = shumeiId;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }
}
