package com.alibaba.blink.udx.udf;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONPath;
import com.alibaba.fastjson2.JSONReader;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class JsonValue2 extends ScalarFunction {

    public String eval(String jsonString, String jsonPath) {
        String errorMessagePrefix = "function json_path_value(String jsonString, String jsonPath) ";
        if (jsonPath == null || jsonPath.trim().equals("")) {
            throw new RuntimeException(errorMessagePrefix + " jsonPath must not be null or empty.");
        }
        try {
            JSONPath path = JSONPath.of(jsonPath);
            JSONReader reader = JSONReader.of(jsonString);

            Object rst = path.extract(reader);
            if (rst == null) {
                return null;
            } else if (rst instanceof JSON) {
                return ((JSON) rst).toJSONString();
            } else {
                return rst.toString();
            }
        } catch (Exception e) {
            return null;
        }

    }

    public static void main(String[] args) {
        JsonValue json = new JsonValue();

        String rst = json.eval("{\"a\":\"b\"}", "$.a");
        System.err.println(rst);
    }
}
