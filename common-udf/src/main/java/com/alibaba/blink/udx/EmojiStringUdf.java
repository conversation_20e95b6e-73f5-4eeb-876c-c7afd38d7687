package com.alibaba.blink.udx;

import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EmojiStringUdf extends ScalarFunction {

    private static final Logger logger = LoggerFactory.getLogger(EmojiStringUdf.class);

    public String eval(String params) {
        if (params != null) {
            Pattern emoji = Pattern.compile("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]|" +
                            "[\ud83e\udd00-\ud83e\uddff]|[\u2300-\u23ff]|[\u2500-\u25ff]|[\u2100-\u21ff]|[\u00a0-\u0fff]|[\u2b00-\u2bff]|[\u2d06]|[\u3030]"
                    , Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(params);
            if (emojiMatcher.find()) {
                params = emojiMatcher.replaceAll("*");
                return params;
            }
            return params;
        }

        return "";
    }
}
