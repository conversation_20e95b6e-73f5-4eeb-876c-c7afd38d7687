package com.alibaba.blink.udx.udf;


import com.alibaba.fastjson.JSONObject;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

//@FunctionHint(
//		input = @DataTypeHint("STRING"),
//		output = @DataTypeHint("LONG")
//)
public class subsidySumFunction extends ScalarFunction {

	public long eval(String message) throws Exception {
		long value=0;
		if (Objects.nonNull(message) && !"{}".equals(message) && message.length() != 0) {
			List<JSONObject> jsonObjectList = JSONObject.parseArray(message, JSONObject.class);
			Iterator<JSONObject> iterator = jsonObjectList.iterator();
			while (iterator.hasNext()) {
				JSONObject jsonObject = iterator.next();
				String returnValue1 = jsonObject.getString("subsidyAmount");
				if(returnValue1 !=null)
				{
					value = value + Long.valueOf(returnValue1);
				}
				String returnValue2 = jsonObject.getString("subsidyValue");
				if(returnValue2 !=null)
				{
					value = value + Long.valueOf(returnValue2);
				}
			}
		}
		return value;
	}

	public static void main(String[] args) throws Exception {
		String str="[{\"funderType\":\"PLATFORM\",\"subsidyType\":\"RATIO\",\"subsidyValue\":80},{\"funderType\":\"INSTALLMENT_LE\",\"subsidyType\":\"RATIO\",\"subsidyValue\":20}]";
		subsidySumFunction jsonParseArray = new subsidySumFunction();
		System.out.println(jsonParseArray.eval(str));

	}
}
