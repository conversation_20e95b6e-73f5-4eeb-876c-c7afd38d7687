package com.alibaba.blink.udx.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

@FunctionHint(
		input = {@DataTypeHint("STRING")},
		output = @DataTypeHint("ROW<s0 STRING,s1 STRING,s2 STRING,s3 STRING,s4 STRING,s5 STRING,s6 STRING,s7 STRING,s8 STRING,s9 STRING,s10 STRING,s11 STRING,s12 STRING,s13 STRING,s14 STRING,s15 STRING,s16 STRING,s17 STRING,s18 STRING,s19 STRING,s20 STRING,s21 STRING,s22 STRING,s23 STRING,s24 STRING,s25 STRING,s26 STRING,s27 STRING,s28 STRING,s29 STRING,s30 STRING,s31 STRING,s32 STRING,s33 STRING,s34 STRING,s35 STRING,s36 STRING,s37 STRING,s38 STRING,s39 STRING,s40 STRING,s41 STRING,s42 STRING,s43 STRING,s44 STRING,s45 STRING,s46 STRING,s47 STRING,s48 STRING,s49 STRING,s50 STRING,s51 STRING,s52 STRING,s53 STRING,s54 STRING>")
)
public class OdsLogParseFieldV2 extends TableFunction<Row> {

	private static final Logger logger = LoggerFactory.getLogger(OdsLogParseFieldV2.class);
	private static final int BASE_ROW_SIZE = 55;

	@Override
	public void open(FunctionContext context) {
	}

	public void eval(String message) {
		Row row = new Row(BASE_ROW_SIZE);
		if (Objects.nonNull(message)) {
			JSONObject messageFields = JSON.parseObject(message);
			//处理外层字段
			String trackId = messageFields.getString("_track_id");
			String event = messageFields.getString("event");
			String time = messageFields.getString("time");
			String flushTime = messageFields.getString("_flush_time");
			String distinctId = messageFields.getString("distinct_id");
			String skid = messageFields.getString("skid");
			//处理lib下的字段
			JSONObject libFields = messageFields.getJSONObject("lib");
			String lib = null;
			String libVersion = null;
			String appVersion = null;
			if (Objects.nonNull(libFields)) {
				lib = libFields.getString("$lib");
				libVersion = libFields.getString("$lib_version");
				appVersion = libFields.getString("$app_version");
			}
			//处理properties
			String appBuild = null;
			String deviceUUID = null;
			String receiveTime = null;
			String deviceId = null;
			String os = null;
			String osVersion = null;
			String model = null;
			String manufacturer = null;
			String screenWidth = null;
			String screenHeight = null;
			String longitude = null;
			String latitude = null;
			String networkType = null;
			String carrier = null;
			String wifi = null;
			String ip = null;
			String appId = null;
			String appName = null;
			String timezoneOffset = null;
			String isFirstDay = null;
			String dwUserid = null;
			String sessionId = null;
			String oaid = null;
			String caid = null;
			String shumeiId = null;
			String imei = null;
			String dwLogId = null;
			String idfa = null;
			String userAgent = null;
			String androidChannel = null;
			String androidId = null;
			String visitMode = null;
			String blockType = null;
			String blockTypeTitle = null;
			String currentPage = null;
			String currentPageTitle = null;
			String screenOrientation = null;
			String fcuuid = null;
			JSONObject propertiesField = messageFields.getJSONObject("properties");
			if (Objects.nonNull(propertiesField)) {
				deviceUUID = propertiesField.getString("device_uuid");
				receiveTime = propertiesField.getString("receive_time");
				deviceId = propertiesField.getString("$device_id");
				os = propertiesField.getString("$os");
				osVersion = propertiesField.getString("$os_version");
				model = propertiesField.getString("$model");
				manufacturer = propertiesField.getString("$manufacturer");
				screenWidth = propertiesField.getString("$screen_width");
				screenHeight = propertiesField.getString("$screen_height");
				longitude = propertiesField.getString("$longitude");
				latitude = propertiesField.getString("$latitude");
				networkType = propertiesField.getString("$network_type");
				carrier = propertiesField.getString("$carrier");
				wifi = propertiesField.getString("$wifi");
				ip = propertiesField.getString("$ip");
				appId = propertiesField.getString("$app_id");
				appName = propertiesField.getString("$app_name");
				timezoneOffset = propertiesField.getString("$timezone_offset");
				isFirstDay = propertiesField.getString("$is_first_day");
				dwUserid = propertiesField.getString("dw_userid");
				sessionId = propertiesField.getString("session_id");
				oaid = propertiesField.getString("oaid");
				caid = propertiesField.getString("caid");
				shumeiId = propertiesField.getString("shumei_id");
				imei = propertiesField.getString("imei");
				idfa = propertiesField.getString("idfa");
				screenOrientation = propertiesField.getString("$screen_orientation");
				userAgent = propertiesField.getString("user_agent");
				androidChannel = propertiesField.getString("android_channel");
				androidId = propertiesField.getString("android_id");
				visitMode = propertiesField.getString("visit_mode");
				blockType = propertiesField.getString("block_type");
				blockTypeTitle = propertiesField.getString("block_type_title");
				currentPage = propertiesField.getString("current_page");
				currentPageTitle = propertiesField.getString("current_page_title");
				appBuild = propertiesField.getString("app_build");
				fcuuid = propertiesField.getString("fcuuid");
			}
			if (propertiesField.containsKey("device_uuid")) propertiesField.remove("device_uuid");
			if (propertiesField.containsKey("receive_time")) propertiesField.remove("receive_time");
			if (propertiesField.containsKey("$os")) propertiesField.remove("$os");
			if (propertiesField.containsKey("$os_version")) propertiesField.remove("$os_version");
			if (propertiesField.containsKey("$model")) propertiesField.remove("$model");
			if (propertiesField.containsKey("$manufacturer")) propertiesField.remove("$manufacturer");
			if (propertiesField.containsKey("$screen_width")) propertiesField.remove("$screen_width");
			if (propertiesField.containsKey("$screen_height")) propertiesField.remove("$screen_height");
			if (propertiesField.containsKey("$longitude")) propertiesField.remove("$longitude");
			if (propertiesField.containsKey("$latitude")) propertiesField.remove("$latitude");
			if (propertiesField.containsKey("$network_type")) propertiesField.remove("$network_type");
			if (propertiesField.containsKey("$carrier")) propertiesField.remove("$carrier");
			if (propertiesField.containsKey("$wifi")) propertiesField.remove("$wifi");
			if (propertiesField.containsKey("$ip")) propertiesField.remove("$ip");
			if (propertiesField.containsKey("$app_id")) propertiesField.remove("$app_id");
			if (propertiesField.containsKey("$app_name")) propertiesField.remove("$app_name");
			if (propertiesField.containsKey("$timezone_offset")) propertiesField.remove("$timezone_offset");
			if (propertiesField.containsKey("$is_first_day")) propertiesField.remove("$is_first_day");
			if (propertiesField.containsKey("dw_userid")) propertiesField.remove("dw_userid");
			if (propertiesField.containsKey("session_id")) propertiesField.remove("session_id");
			if (propertiesField.containsKey("oaid")) propertiesField.remove("oaid");
			if (propertiesField.containsKey("caid")) propertiesField.remove("caid");
			if (propertiesField.containsKey("shumei_id")) propertiesField.remove("shumei_id");
			if (propertiesField.containsKey("imei")) propertiesField.remove("imei");
			if (propertiesField.containsKey("idfa")) propertiesField.remove("idfa");
			if (propertiesField.containsKey("$screen_orientation")) propertiesField.remove("$screen_orientation");
			if (propertiesField.containsKey("user_agent")) propertiesField.remove("user_agent");
			if (propertiesField.containsKey("android_channel")) propertiesField.remove("android_channel");
			if (propertiesField.containsKey("android_id")) propertiesField.remove("android_id");
			if (propertiesField.containsKey("visit_mode")) propertiesField.remove("visit_mode");
			if (propertiesField.containsKey("block_type")) propertiesField.remove("block_type");
			if (propertiesField.containsKey("block_type_title")) propertiesField.remove("block_type_title");
			if (propertiesField.containsKey("current_page")) propertiesField.remove("current_page");
			if (propertiesField.containsKey("current_page_title")) propertiesField.remove("current_page_title");
			if (propertiesField.containsKey("app_build")) propertiesField.remove("app_build");
			if (propertiesField.containsKey("$device_id")) propertiesField.remove("$device_id");
			//remove lib,lib_version,app_version in properties
			if (propertiesField.containsKey("$lib")) propertiesField.remove("$lib");
			if (propertiesField.containsKey("$lib_version")) propertiesField.remove("$lib_version");
			if (propertiesField.containsKey("$app_version")) propertiesField.remove("$app_version");
			if (propertiesField.containsKey("fcuuid")) propertiesField.remove("fcuuid");

			row.setField(0, trackId);
			row.setField(1, event);
			row.setField(2, currentPage);
			row.setField(3, currentPageTitle);
			row.setField(4, blockType);
			row.setField(5, blockTypeTitle);
			row.setField(6, propertiesField.toJSONString());
			row.setField(7, time);
			row.setField(8, dwUserid);
			row.setField(9, distinctId);
			row.setField(10, deviceUUID);
			row.setField(11, appVersion);
			row.setField(12, appBuild);
			row.setField(13, os);
			row.setField(14, osVersion);
			row.setField(15, model);
			row.setField(16, manufacturer);
			row.setField(17, screenWidth);
			row.setField(18, screenHeight);
			row.setField(19, longitude);
			row.setField(20, latitude);
			row.setField(21, carrier);
			row.setField(22, networkType);
			row.setField(23, wifi);
			row.setField(24, ip);
			row.setField(25, appId);
			row.setField(26, appName);
			row.setField(27, sessionId);
			row.setField(28, visitMode);
			row.setField(29, lib);
			row.setField(30, libVersion);
			row.setField(31, timezoneOffset);
			row.setField(32, isFirstDay);
			row.setField(33, deviceId);
			row.setField(34, caid);
			row.setField(35, oaid);
			row.setField(36, shumeiId);
			row.setField(37, imei);
			row.setField(38, androidId);
			row.setField(39, androidChannel);
			row.setField(40, idfa);
			row.setField(41, userAgent);
			row.setField(42, screenOrientation);
			row.setField(43, flushTime);
			row.setField(44, receiveTime);
			//add new 8 column
			//"receive_time_1m":"2021-06-21 10:01",
			//"receive_time_5m":"2021-06-21 10:00",
			//"receive_time_15m":"2021-06-21 10:00",
			//"receive_time_30m":"2021-06-21 10:00",
			//"receive_time_1h":"2021-06-21 10",
			//"receive_time_1d":"2021-06-21",
			//"scene":"trade_series_product_click_5",
			//"action":"click",
			Date date;
			if (StringUtils.isNullOrWhitespaceOnly(receiveTime)) {
				date = new Date(Long.parseLong(time));
			} else {
				date = new Date(Long.parseLong(receiveTime));
			}

			String receiveTime_1d = new SimpleDateFormat("yyyy-MM-dd").format(date);
			String receiveTime_1h = new SimpleDateFormat("yyyy-MM-dd HH").format(date);
			String receiveTime_1m = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date);
			//分钟个位
			String bitPos = receiveTime_1m.substring(15);
			//分钟十位
			String tenPos = receiveTime_1m.substring(14);
			// yyyy-MM-dd HH:m
			String receive_time_1h_with_ten_pos = receiveTime_1m.substring(0, 15);
			//receive_time_5m
			String receiveTime_5m = receive_time_1h_with_ten_pos + (Integer.parseInt(bitPos) >= 5 ? 5 : 0);
			String receiveTime_30m = receiveTime_1h + ":" + (Integer.parseInt(tenPos) >= 30 ? "30" : "00");
			String receiveTime_15m = receiveTime_1h + ":" + (Integer.parseInt(tenPos) >= 30 ? ((Integer.parseInt(tenPos) >= 45 ? "45" : "30")) : ((Integer.parseInt(tenPos) < 15 ? "00" : "15")));
			String scene = event + (StringUtils.isNullOrWhitespaceOnly(currentPage) ? "" : "_" + currentPage) + (StringUtils.isNullOrWhitespaceOnly(blockType) ? "" : "_" + blockType);

			String action;
			if (event.contains("_exposure") || event.contains("_expouse") || event.contains("_expourse") || event.contains("_exposuse")) {
				action = "exposure";
			} else if (event.contains("_pageview")) {
				action = "pageview";
			} else if (event.contains("_click") || event.equals("trade_order_pay") || event.equals("trade_order_place")) {
				action = "click";
			} else {
				action = "other";
			}
			row.setField(45, receiveTime_1m);
			row.setField(46, receiveTime_5m);
			row.setField(47, receiveTime_15m);
			row.setField(48, receiveTime_30m);
			row.setField(49, receiveTime_1h);
			row.setField(50, receiveTime_1d);
			row.setField(51, action);
			row.setField(52, scene);
			row.setField(53, fcuuid);
			row.setField(54, skid);
			collect(row);
		}
	}

	public static void main(String[] args) {
		String message = "{\n" +
				"    \"login_id\":\"1522232491\",\n" +
				"    \"_track_id\":1621737167,\n" +
				"    \"lib\":{\n" +
				"        \"$lib\":\"iOS\",\n" +
				"        \"$lib_method\":\"code\",\n" +
				"        \"$lib_version\":\"2.1.0\",\n" +
				"        \"$app_version\":\"4.74.0\"\n" +
				"    },\n" +
				"    \"distinct_id\":\"1522232491\",\n" +
				"    \"anonymous_id\":\"3F96D169-C798-48A7-B2DE-F237DD857B11\",\n" +
				"    \"_flush_time\":1636526245043,\n" +
				"    \"time\":1636526244215,\n" +
				"    \"event\":\"trade_search_result_expouse\",\n" +
				"    \"type\":\"track\",\n" +
				"    \"properties\":{\n" +
				"        \"$screen_orientation\":\"landscape\",\n" +
				"        \"block_type_title\":\"搜索瀑布流\",\n" +
				"        \"block_type\":\"72\",\n" +
				"        \"$os\":\"iOS\",\n" +
				"        \"$longitude\":\"114.03773539141044\",\n" +
				"        \"acm\":\"1.srh.cspu_376521582.0.t_head_b-t_init_qp_syn_流量桶-t_recall_new_rank-t_sort_v8_new-新人折扣_实验组-cache_exp.rid_be22f00313598417\",\n" +
				"        \"$network_type\":\"WIFI\",\n" +
				"        \"$wifi\":true,\n" +
				"        \"device_uuid\":\"D76315D0-A8A9-473C-A5C2-DFC6D3DD14BE\",\n" +
				"        \"product_category_name\":\"服装\",\n" +
				"        \"search_key_word_type_title\":\"推荐搜索/搜索发现\",\n" +
				"        \"spu_id\":\"1929718\",\n" +
				"        \"product_detail_current_price\":\"23900\",\n" +
				"        \"current_page\":\"36\",\n" +
				"        \"time_offset\":\"440\",\n" +
				"        \"$model\":\"iPhone9,2\",\n" +
				"        \"idfa\":\"D76315D0-A8A9-473C-A5C2-DFC6D3DD14BE\",\n" +
				"        \"receive_time\":1636526245070,\n" +
				"        \"product_brand_name\":\"ETONIC\",\n" +
				"        \"$screen_width\":414,\n" +
				"        \"product_name\":\"ETONIC 字母满印拼接撞色立领保暖宽松加厚棉服外套 男女同款\",\n" +
				"        \"$app_version\":\"4.74.0\",\n" +
				"        \"app_build\":\"*********\",\n" +
				"        \"product_sub_title\":\"6057人付款\",\n" +
				"        \"$app_name\":\"得物(毒)\",\n" +
				"        \"$lib_version\":\"2.1.0\",\n" +
				"        \"shumei_id\":\"20200506104608cf56124757bd53518e140a8f828e821c01d48896f71484e4\",\n" +
				"        \"current_page_title\":\"商品搜索结果页\",\n" +
				"        \"fcuuid\":\"UUIDe4c2f6e11d3049e5a5946f7b725a8b83\",\n" +
				"        \"algorithm_product_property_value\":\"68051060\",\n" +
				"        \"search_position_rule\":\"综合\",\n" +
				"        \"algorithm_request_Id\":\"753cfe89b84ae5a9\",\n" +
				"        \"$ip\":\"*************\",\n" +
				"        \"$screen_height\":736,\n" +
				"        \"dw_userid\":\"1522232491\",\n" +
				"        \"$device_id\":\"D76315D0-A8A9-473C-A5C2-DFC6D3DD14BE\",\n" +
				"        \"$app_id\":\"com.siwuai.duapp\",\n" +
				"        \"$latitude\":\"22.675398124593116\",\n" +
				"        \"search_result_position\":\"22\",\n" +
				"        \"_time\":1636526244656,\n" +
				"        \"user_agent\":\"Mozilla/5.0 (iPhone; CPU iPhone OS 13_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148/duapp/4.74.0\",\n" +
				"        \"$carrier\":\"中国电信\",\n" +
				"        \"$os_version\":\"13.6\",\n" +
				"        \"$is_first_day\":false,\n" +
				"        \"session_id\":\"D76315D0-A8A9-473C-A5C2-DFC6D3DD14BE1636526201110\",\n" +
				"        \"search_key_word_type\":\"5\",\n" +
				"        \"is_on_the_air\":\"0\",\n" +
				"        \"$lib\":\"iOS\",\n" +
				"        \"search_result_type_title\":\"商品\",\n" +
				"        \"search_result_type\":0,\n" +
				"        \"visit_mode\":\"1\",\n" +
				"        \"$timezone_offset\":-480,\n" +
				"        \"$manufacturer\":\"Apple\",\n" +
				"        \"sell_num\":\"6057\",\n" +
				"        \"search_key_word\":\"外套男冬季\"\n" +
				"    }\n" +
				"}";
		OdsLogParseFieldV2 f = new OdsLogParseFieldV2();
		f.eval(message);
//		String event = "trade";
//		String block_type = "5";
//		String current_page = "";
//
//		Long receiveTime = 1624351132000L;
//		Date date = new Date(receiveTime);
//		String receive_time_1d = new SimpleDateFormat("yyyy-MM-dd").format(date);
//		String receive_time_1h = new SimpleDateFormat("yyyy-MM-dd HH").format(date);
//		String receive_time_1m = new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date);
//		//分钟个位
//		String bit_pos = receive_time_1m.substring(15);
//		//分钟十位
//		String ten_pos = receive_time_1m.substring(14);
//		// yyyy-MM-dd HH:m
//		String receive_time_1h_with_ten_pos = receive_time_1m.substring(0, 15);
//		//receive_time_5m
//		String receive_time_5m = receive_time_1h_with_ten_pos + (Integer.parseInt(bit_pos) >= 5 ? 5 : 0);
//
//		String receive_time_30m = receive_time_1h + ":" + (Integer.parseInt(ten_pos) >= 30 ? "30" : "00");
//
//		String receive_time_15m = receive_time_1h + ":" + (Integer.parseInt(ten_pos) >= 30 ? ((Integer.parseInt(ten_pos) >= 45 ? "45" : "30")) : ((Integer.parseInt(ten_pos) < 15 ? "00" : "15")));
//
//
//		System.out.println(receive_time_1m);
//		System.out.println(receive_time_5m);
//		System.out.println(receive_time_15m);
//		System.out.println(receive_time_30m);
//		System.out.println(receive_time_1h);
//		System.out.println(receive_time_1d);
//
//
//		String scene = event + (StringUtils.isNullOrWhitespaceOnly(current_page) ? "" : "_" + current_page) + (StringUtils.isNullOrWhitespaceOnly(block_type) ? "" : "_" + block_type);
//		System.out.println(scene);
//
//		String action;
//		if (event.contains("_exposure") || event.contains("_expouse") || event.contains("_expourse") || event.contains("_exposuse")) {
//			action = "exposure";
//		} else if (event.contains("_pageview")) {
//			action = "pageview";
//		} else if (event.contains("_click") || event.equals("trade_order_pay") || event.equals("trade_order_place")) {
//			action = "click";
//		} else {
//			action = "other";
//		}

//        JSONObject messageFields = JSON.parseObject(message);
//        Long time = (Long) messageFields.get("time");
//        Long page = ((JSONObject) messageFields.get("properties")).getLong("current_page");
//        System.out.println(time);
	}
}