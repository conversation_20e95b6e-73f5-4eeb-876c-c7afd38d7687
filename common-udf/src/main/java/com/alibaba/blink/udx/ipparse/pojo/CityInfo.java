package com.alibaba.blink.udx.ipparse.pojo;


public class CityInfo {
    private String[] data;

    private int size;

    public CityInfo(String[] data) {
        this.data = data;
        this.size = data.length;
    }

    private String get(int index) {
        return (this.size >= index) ? this.data[index - 1] : "";
    }

    public String getCountryName() {
        return get(1);
    }

    public String getRegionName() {
        return get(2);
    }

    public String getCityName() {
        return get(3);
    }

    public String getOwnerDomain() {
        return get(4);
    }

    public String getIspDomain() {
        return get(5);
    }

    public String getLatitude() {
        return get(6);
    }

    public String getLongitude() {
        return get(7);
    }

    public String getTimezone() {
        return get(8);
    }

    public String getUtcOffset() {
        return get(9);
    }

    public String getChinaAdminCode() {
        return get(10);
    }

    public String getIddCode() {
        return get(11);
    }

    public String getCountryCode() {
        return get(12);
    }

    public String getContinentCode() {
        return get(13);
    }

    public String getIDC() {
        return get(14);
    }

    public String getBaseStation() {
        return get(15);
    }

    public String getCountryCode3() {
        return get(16);
    }

    public String getEuropeanUnion() {
        return get(17);
    }

    public String getCurrencyCode() {
        return get(18);
    }

    public String getCurrencyName() {
        return get(19);
    }

    public String getAnycast() {
        return get(20);
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("country_name:");
        sb.append(getCountryName());
        sb.append("\n");
        sb.append("region_name:");
        sb.append(getRegionName());
        sb.append("\n");
        sb.append("city_name:");
        sb.append(getCityName());
        sb.append("\n");
        sb.append("owner_domain:");
        sb.append(getOwnerDomain());
        sb.append("\n");
        sb.append("isp_domain:");
        sb.append(getIspDomain());
        sb.append("\n");
        sb.append("latitude:");
        sb.append(getLatitude());
        sb.append("\n");
        sb.append("longitude:");
        sb.append(getLongitude());
        sb.append("\n");
        sb.append("timezone:");
        sb.append(getTimezone());
        sb.append("\n");
        sb.append("utc_offset:");
        sb.append(getUtcOffset());
        sb.append("\n");
        sb.append("china_admin_code:");
        sb.append(getChinaAdminCode());
        sb.append("\n");
        sb.append("idd_code:");
        sb.append(getIddCode());
        sb.append("\n");
        sb.append("country_code:");
        sb.append(getCountryCode());
        sb.append("\n");
        sb.append("continent_code:");
        sb.append(getContinentCode());
        sb.append("\n");
        sb.append("idc:");
        sb.append(getIDC());
        sb.append("\n");
        sb.append("base_station:");
        sb.append(getBaseStation());
        sb.append("\n");
        sb.append("country_code3:");
        sb.append(getCountryCode3());
        sb.append("\n");
        sb.append("european_union:");
        sb.append(getEuropeanUnion());
        sb.append("\n");
        sb.append("currency_code:");
        sb.append(getCurrencyCode());
        sb.append("\n");
        sb.append("currency_name:");
        sb.append(getCurrencyName());
        sb.append("\n");
        sb.append("anycast:");
        sb.append(getAnycast());
        return sb.toString();
    }
}
