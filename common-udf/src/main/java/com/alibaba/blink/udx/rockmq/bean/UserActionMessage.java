package com.alibaba.blink.udx.rockmq.bean;


public class UserActionMessage {

    private QueryReq request;

    private String eventType;

    public UserActionMessage() {
    }

    public UserActionMessage(QueryReq request, String eventType) {
        this.request = request;
        this.eventType = eventType;
    }

    public QueryReq getRequest() {
        return request;
    }

    public void setRequest(QueryReq request) {
        this.request = request;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
}
