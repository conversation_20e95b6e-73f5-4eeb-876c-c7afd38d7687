package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.util.SensorsDataStringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.guava30.com.google.common.collect.Lists;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s1 STRING, s2 STRING, l1 BIGINT, s3 STRING, s4 STRING>")
)
public class LibraPerformanceTest02 extends TableFunction<Row> {

    private static final String DISTINCT_ID = "distinct_id";
    private static final String PROPERTIES = "properties";
    private static final String IP = "$ip";

    private static final String SENDTYPE_SDK = "sdk";
    private static final String SENDTYPE_URL = "url";

    private static final String CHAR_UNDERLINE = "-";
    private static final String CHAR_AND = "&";

    private static final String SPLIT_STRING_01 = "gzip=1&data_list=";
    private static final String SPLIT_STRING_02 = "data_list=";
    private static final String SPLIT_STRING_03 = "data=";

    private Row row;

    @Override
    public void open(FunctionContext context) {
        row = new Row(5);
    }

    public void eval(String message) throws Exception {
        try {
            if (Objects.nonNull(message)) {
                //String[] dataSplit = message.replaceAll("\"", "").replaceAll("\\\\", "").split("\\+\\+_");
                String[] dataSplit = StringUtils.replace(StringUtils.replace(message, "\"", ""), "\\\\", "").split("\\+\\+_");
                if (dataSplit.length <= 9) {
                    System.out.println(" dataSplit is " + message);
                    Row emptyRow = new Row(5);
                    collect(emptyRow);
                } else {
                    String data = dataSplit[4].trim();
                    String dataList = dataSplit[5].trim();
                    String requestDataList = dataSplit[6].trim();
                    String project = dataSplit[8].trim();
                    // String ip = dataSplit[0].replaceAll("\"", "").trim().split(",")[0];
                    String ip = StringUtils.split(StringUtils.replace(dataSplit[0], "\"", "").trim(), ",")[0];
                    // String timestamp = dataSplit[1].replaceAll("\"", "").replaceAll("\\.", "").trim();
                    String timestamp = StringUtils.replace(StringUtils.replace(dataSplit[1],"\"", ""), ".", "").trim();
                    List<String> logDataList = Lists.newArrayList();
                    String sendType = null;
                    if (!CHAR_UNDERLINE.equals(data)) {
                        String unDecodeData = SensorsDataStringUtil.transShenceFormatToJson(data);
                        logDataList = Lists.newArrayList(unDecodeData);
                        sendType = SENDTYPE_SDK;
                    } else if (!CHAR_UNDERLINE.equals(dataList)) {
                        if (requestDataList.contains(SPLIT_STRING_01)) {
                            // String realDataList = dataList.split(SPLIT_STRING_01)[1];
                            String realDataList = StringUtils.split(dataList, SPLIT_STRING_01)[1];
                            String unGzipData = SensorsDataStringUtil.transShenceGzipFormatToJson(realDataList);
                            logDataList = Optional.ofNullable(JSON.parseArray(unGzipData, String.class)).orElse(Lists.newArrayList());
                            sendType = SENDTYPE_SDK;
                        } else if (requestDataList.contains(SPLIT_STRING_02)) {
                            // String realDataList = dataList.split(SPLIT_STRING_02)[1];
                            String realDataList = StringUtils.split(dataList, SPLIT_STRING_02)[1];
                            String unGzipData = SensorsDataStringUtil.transShenceFormatToJson(realDataList);
                            logDataList = Optional.ofNullable(JSON.parseArray(unGzipData, String.class)).orElse(Lists.newArrayList());
                            sendType = SENDTYPE_SDK;
                        } else if (requestDataList.contains(SPLIT_STRING_03)) {
                            // String realDataList = dataList.split(SPLIT_STRING_03)[1];
                            String realDataList = StringUtils.split(dataList, SPLIT_STRING_03)[1];
                            String unGzipData = SensorsDataStringUtil.transShenceFormatToJson(realDataList);
                            logDataList.add(JSON.parseObject(unGzipData, String.class));
                            sendType = SENDTYPE_SDK;
                        } else {
                            collectErrorData(message);
                            System.out.println("dataList is : " + message);
                        }
                    } else if (!CHAR_UNDERLINE.equals(requestDataList)) {
                        if (requestDataList.contains(SPLIT_STRING_01)) {
                            // String[] split = requestDataList.split(SPLIT_STRING_01);
                            String[] split = StringUtils.split(requestDataList, SPLIT_STRING_01);

                            String realDataList = split[1];

                            if (realDataList.contains(CHAR_AND)) {
                                // realDataList = realDataList.split(CHAR_AND)[0];
                                realDataList = StringUtils.split(realDataList, CHAR_AND)[0];
                                String unGzipData = SensorsDataStringUtil.transShenceGzipFormatToJson(realDataList);
                                logDataList = Optional.ofNullable(JSON.parseArray(unGzipData, String.class)).orElse(Lists.newArrayList());
                                sendType = SENDTYPE_URL;
                            } else {
                                String unGzipData = SensorsDataStringUtil.transShenceGzipFormatToJson(realDataList);
                                logDataList = Optional.ofNullable(JSON.parseArray(unGzipData, String.class)).orElse(Lists.newArrayList());
                                sendType = SENDTYPE_URL;
                            }
                        } else if (requestDataList.contains(SPLIT_STRING_02)) {
                            // String[] split = requestDataList.split(SPLIT_STRING_02);
                            String[] split = StringUtils.split(requestDataList, SPLIT_STRING_02);
                            String realDataList = split[1];

                            if (realDataList.contains(CHAR_AND)) {
                                // realDataList = realDataList.split(CHAR_AND)[0];
                                realDataList = StringUtils.split(realDataList, CHAR_AND)[0];
                                String unGzipData = SensorsDataStringUtil.transShenceFormatToJson(realDataList);
                                logDataList = Optional.ofNullable(JSON.parseArray(unGzipData, String.class)).orElse(Lists.newArrayList());
                                sendType = SENDTYPE_URL;
                            } else {
                                String unGzipData = SensorsDataStringUtil.transShenceFormatToJson(realDataList);
                                logDataList = Optional.ofNullable(JSON.parseArray(unGzipData, String.class)).orElse(Lists.newArrayList());
                                sendType = SENDTYPE_URL;
                            }
                        } else if (requestDataList.contains(SPLIT_STRING_03)) {
                            // String[] split = requestDataList.split(SPLIT_STRING_03);
                            String[] split = StringUtils.split(requestDataList, SPLIT_STRING_03);
                            String realDataList = split[1];

                            if (realDataList.contains(CHAR_AND)) {
                                // realDataList = realDataList.split(CHAR_AND)[0];
                                realDataList = StringUtils.split(realDataList, CHAR_AND)[0];
                                String unGzipData = SensorsDataStringUtil.transShenceFormatToJson(realDataList);
                                logDataList.add(JSON.parseObject(unGzipData, String.class));
                                sendType = SENDTYPE_URL;
                            } else {
                                String unGzipData = SensorsDataStringUtil.transShenceGzipFormatToJson(realDataList);
                                logDataList.add(JSON.parseObject(unGzipData, String.class));
                                sendType = SENDTYPE_URL;
                            }
                        } else {
                            collectErrorData(message);
                            System.out.println("requestDataList is : " + message);
                        }
                    } else {
                        String httpCookie = dataSplit[9];
                        System.out.println("httpCookie is :" + message);
                        logDataList = Lists.newArrayList();
                        sendType = SENDTYPE_URL;
                    }
                    for (String decodeMessage : logDataList) {
                        String distinctId = "";
                        try {
                            distinctId = (String) JSON.parseObject(decodeMessage, Map.class).get(DISTINCT_ID);
                        } catch (ClassCastException e) {
                            System.out.println("ClassCastException message is:" + decodeMessage);
                        }

                        String newDecodeMessage = replaceRealIp(decodeMessage, ip);
                        int pos = 0;
                        row.setField(pos++, distinctId);
                        row.setField(pos++, newDecodeMessage);
                        row.setField(pos++, Long.parseLong(timestamp));
                        row.setField(pos++, project);
                        row.setField(pos, sendType);
                        collect(row);
                    }
                }
            }
        } catch (Exception e) {
            collectErrorData(message);
            System.out.println(" message is :" + message + ", error is :" + e.toString());
        }

    }

    private void collectErrorData(String message) {
        Row emptyRow = new Row(5);
        int pos = 0;
        row.setField(pos++, "");
        row.setField(pos++, message);
        row.setField(pos++, System.currentTimeMillis());
        row.setField(pos++, "error");
        row.setField(pos, "");
        collect(emptyRow);
    }

    private String replaceRealIp(String decodeMessage, String newIp) {
        JSONObject decodeMessageObject = JSON.parseObject(decodeMessage);
        JSONObject properties = (JSONObject) decodeMessageObject.get(PROPERTIES);
        if (Objects.nonNull(properties)) {
            properties.put(IP, newIp);
            decodeMessageObject.put(PROPERTIES, properties);
            return decodeMessageObject.toJSONString();
        }
        return decodeMessage;
    }


    public static void main(String[] args) throws Exception {
        String str = "\"**************, *************\" ++_ \"1630318914.197\" ++_ \"POST\" ++_ \"-\" ++_ \"-\" ++_ \"-\" ++_ \"data=eyJkaXN0aW5jdF9pZCI6IjE3YjQ4OWE3Y2EzOWU3LTA1ZjVlMmVjNjdhYTA4Yy01MzI2MmYyNy0zMDQ1MDAtMTdiNDg5YTdjYTQ4NTMiLCJsaWIiOnsiJGxpYiI6ImpzIiwiJGxpYl9tZXRob2QiOiJjb2RlIiwiJGxpYl92ZXJzaW9uIjoiMS4xOC4yIn0sInByb3BlcnRpZXMiOnsiJHRpbWV6b25lX29mZnNldCI6LTQ4MCwiJHNjcmVlbl9oZWlnaHQiOjgxMiwiJHNjcmVlbl93aWR0aCI6Mzc1LCIkbGliIjoianMiLCIkbGliX3ZlcnNpb24iOiIxLjE4LjIiLCIkbGF0ZXN0X3RyYWZmaWNfc291cmNlX3R5cGUiOiLnm7TmjqXmtYHph48iLCIkbGF0ZXN0X3NlYXJjaF9rZXl3b3JkIjoi5pyq5Y%2BW5Yiw5YC8X%2BebtOaOpeaJk%2BW8gCIsIiRsYXRlc3RfcmVmZXJyZXIiOiIiLCJkd191c2VyaWQiOiIxMjAwMDA1NDUiLCJyZWdpb24iOiJISyIsInBvaXpvbl9sYW5ndWFnZSI6InpoLVRXIiwiY3VycmVudF9wYWdlIjo3NzAsImJsb2NrX3R5cGUiOjE0ODIsInBvaXpvbl9jb250ZW50X2luZm9fbGlzdCI6Ilt7XCJzcHVfaWRcIjpcIjMwMDAyODU2XCIsXCJwb3NpdGlvblwiOlwiMlwifV0iLCIkaXNfZmlyc3RfZGF5IjpmYWxzZSwiJHVybCI6Imh0dHBzOi8vc2hmdDEtYXNpYS1lYXN0LXB1YmxpYy5wb2l6b24ubmV0L292ZXJzZWFzL2FjdGl2aXR5L3NpZ24%2FYWN0aXZpdHlJZD02Jmxhbmc9emgtVFcmYXJlYT1ISyIsIiR0aXRsZSI6IuetvuWIsOaMkeaImCJ9LCJhbm9ueW1vdXNfaWQiOiIxN2I0ODlhN2NhMzllNy0wNWY1ZTJlYzY3YWEwOGMtNTMyNjJmMjctMzA0NTAwLTE3YjQ4OWE3Y2E0ODUzIiwidHlwZSI6InRyYWNrIiwiZXZlbnQiOiJwb2l6b25fdHJhZGVfcmVjb21tZW5kX3JlbGF0ZWRfcHJvZHVjdF9leHBvc3VyZSIsInRpbWUiOjE2MzAzMTg5MTM5OTgsIl90cmFja19pZCI6NzUyODUzOTk4LCJfZmx1c2hfdGltZSI6MTYzMDMxODkxMzk5OH0%3D&ext=crc%3D-1396141736\" ++_ \"Mozilla/5.0 (iPhone; CPU iPhone OS 14_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Poizon/2.6.110(iPhone;14.3)\" ++_ \"hdfs_importer_test\" ++_ \"-\" ++_ \"-\" ++_ \"-\"";
        //        String str = "\"**************, **************\" ++_ \"1606458196.673\" ++_ \"POST\" ++_ \"-\" ++_ \"-\" ++_ \"-\" ++_ \"crc=763937465&gzip=1&data_list=H4sIAAAAAAAAE%2B1TMW%2FTQBj9L6eMieW72Bc7WwIEZaFIIDG0lXWxz%2FFR5846nxNCFKmoYoEVMYDExogEGwv%2Fhkb0X%2FCdHdpE0KFLB9TJvs%2FP79573%2FcdrlCupkJGIkF9hH0cEEw8gtrIiBlHfUxd6vkBDijFbhsxqeRypqqywfcIvef1fNIhwwduxyM47Ax6eNTBoyEdDIZ41CUBUPE5lwbg8Kh4FKtZoSRU4E0a%2B%2BQvClVWmgM0MprFJzU7cXsBCUIaECineVVm0b6mkPph0EaFVgXXRvAS9Vfoit0sCwDbz0kVm1GuFs%2BEycYJ3NJSgEXi4Am8T3IFFzZg6sG3Mtacy2ghEpOhvodtjRVFNOe6FErCj57jU8ca%2B9uKkKmKclFau4erI7Stj%2B8fgW7sB2G3tz62CkQZpUKXJkrYEvVTlpccqjOV8NxKe5wBLyZtbLEJn4uY3yjyluRmofQfY6j70BZjprXgGs4%2Fv385%2F%2Fjj16t3F6cfrJFKayu%2FYFMbAwaszfoliIhUmpYc%2FHS8ACagtRCpuBTcdNQIk9tLNu%2B%2FXnx6uzl7vXnz%2Bfzb6V5AVa3%2B4OnYe%2FT8xEdXOWdcTDOgh05DMWdAViWWzWJyJad7ZzHZ6QNxsOM27dyp4q5Dt9jLJjc6awl%2BSvGEeNSdMJ%2B7Luchi1ma1jwzJquUxQZm0YY0KIqc70hVEJ40oLC%2Bp1AahlUYtJ2Pmh0MO6VYVEw4SQVVtG6jBMZByNj8Y8ea5tQzD8da8epal7uGrpvI%2Bt8ZN5lq1EBy63X7Bkvevd0lxz3fIz4J7cj9L0sO0Xp3S3635Le75Me%2FATydR%2FbLBwAA\" ++_ \"SensorsAnalytics iOS SDK\" ++_ \"production\" ++_ \"smidV2=202006241355436eb2ea661ca94915fe2fe16e346d50ac008d20c36a01f8280; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221737fb4cdef132-0d923208484031-2d7b1f29-370944-1737fb4cdf0b3%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%221737fb4cdef132-0d923208484031-2d7b1f29-370944-1737fb4cdf0b3%22%7D\" ++_ \"-\" ++_ \"-\"";
//        String str = "\"223.104.244.145, 100.120.72.57\" ++_ \"1606462762.622\" ++_ \"GET\" ++_ \"-\" ++_ \"eyJkaXN0aW5jdF9pZCI6IjE3Mzk1OWYwZjEyNTFjLTAyYjIzYTk5ZWJlMjM0OC0yYTdlMTkyOC0yNTAxMjUtMTczOTU5ZjBmMTMzMDYiLCJsaWIiOnsiJGxpYiI6ImpzIiwiJGxpYl9tZXRob2QiOiJjb2RlIiwiJGxpYl92ZXJzaW9uIjoiMS4xNS4xMiJ9LCJwcm9wZXJ0aWVzIjp7IiR0aW1lem9uZV9vZmZzZXQiOi00ODAsIiRzY3JlZW5faGVpZ2h0Ijo2NjcsIiRzY3JlZW5fd2lkdGgiOjM3NSwiJGxpYiI6ImpzIiwiJGxpYl92ZXJzaW9uIjoiMS4xNS4xMiIsIiRsYXRlc3RfdHJhZmZpY19zb3VyY2VfdHlwZSI6IuebtOaOpea1gemHjyIsIiRsYXRlc3Rfc2VhcmNoX2tleXdvcmQiOiLmnKrlj5bliLDlgLxf55u05o6l5omT5byAIiwiJGxhdGVzdF9yZWZlcnJlciI6IiIsIiRyZWZlcnJlciI6IiIsIiR1cmwiOiJodHRwczovL20ucG9pem9uLmNvbS9yb3V0ZXIvcHJvZHVjdC9Cb3V0aXF1ZVJlY29tbWVuZERldGFpbFBhZ2U%2FcmVjb21tZW5kSWQ9MTAwNDE1MiZzcHVJZHM9NDg0MDEiLCIkdXJsX3BhdGgiOiIvcm91dGVyL3Byb2R1Y3QvQm91dGlxdWVSZWNvbW1lbmREZXRhaWxQYWdlIiwiJHRpdGxlIjoiIiwiJGlzX2ZpcnN0X2RheSI6ZmFsc2UsIiRpc19maXJzdF90aW1lIjpmYWxzZSwiJHJlZmVycmVyX2hvc3QiOiIifSwiYW5vbnltb3VzX2lkIjoiMTczOTU5ZjBmMTI1MWMtMDJiMjNhOTllYmUyMzQ4LTJhN2UxOTI4LTI1MDEyNS0xNzM5NTlmMGYxMzMwNiIsInR5cGUiOiJ0cmFjayIsImV2ZW50IjoiJHBhZ2V2aWV3IiwiX3RyYWNrX2lkIjoyMzM1NDIzNjl9\" ++_ \"-\" ++_ \"-\" ++_ \"Mozilla/5.0 (iPhone; CPU iPhone OS 13_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148/duapp/4.36.1\" ++_ \"production\" ++_ \"sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22173959f0f1251c-02b23a99ebe2348-2a7e1928-250125-173959f0f13306%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22173959f0f1251c-02b23a99ebe2348-2a7e1928-250125-173959f0f13306%22%7D; smidV2=20200625063318234e9ebbec4db70c61e0c84399f9a38c00d9d359da46b6430\" ++_ \"-\" ++_ \"crc%3D1235087727\"";
//        String str = "\"**************, **************\" ++_ \"1608451090.983\" ++_ \"POST\" ++_ \"-\" ++_ \"-\" ++_ \"-\" ++_ \"-\" ++_ \"SensorsAnalytics iOS SDK\" ++_ \"production\" ++_ \"smidV2=20200917205651d831e7e688a6056e920d9ccb1876b95200a719ca4a6c02870; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%221765523d034954-0ee0c403c2e8a18-16a7d24-304704-1765523d035a48%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%221765523d034954-0ee0c403c2e8a18-16a7d24-304704-1765523d035a48%22%7D; UM_distinctid=176552387723ba-07ba20079c9aa4-16a7d24-4a640-176552387735fb; duToken=d41d8cd9|135328404|1607746150|57ddcdc730eccb95\" ++_ \"-\" ++_ \"-\"";
//        String str = "{\"@timestamp\":\"2021-01-04T12:11:42.928Z\",\"@metadata\":{\"beat\":\"filebeat\",\"type\":\"_doc\",\"version\":\"7.10.0\",\"pipeline\":\"filebeat-7.10.0-nginx-access-pipeline\"},\"message\":\"\\\"***************, **************\\\" ++_ \\\"1609762302.887\\\" ++_ \\\"POST\\\" ++_ \\\"-\\\" ++_ \\\"-\\\" ++_ \\\"-\\\" ++_ \\\"crc=930932242&gzip=1&data_list=H4sIAAAAAAAAE%2B2b224TRxiAX8Xa5jKxZmbPuSMJaSO1Kmpoe1FVq8nu2B6x3nX3kJAipECVqgEaQlEPoqDSKi1ctFICiADh8DCwdniL%2FrN2SOzYbmwHoyrri8g7szPzH7%2F5Pbv54pzk%2BkXuWdyRxiUsmxrBhoakUSniZSaNYw2ZukZkhAxTHZWo53uLZT8O6%2FdPnDTliUlkjmFNJ2OKYU6NmdMnpscMgqcUMj0tG1MKTMXmmRfB7VFAHWaFjAZ2yQpYGLuRxc5WYDoGd1nQbZ9JJyaYyArCsDQ0F9w4LFkt4hADy6NSJfArLIg4C6Xxc1LIwpD73uFFq8%2BGZUVRkA4SjIhFvvY9ZvmFQshAZBiIQGu36Ac8KpVB6K9iFkbWjFhCR0zTHVWjGMZj3RQz%2BKE1zwIhhjCnkiei0aZBwFkALa8e%2FZP8%2Bqx2Zzu5dBd65nnII6vsO6CaBNcOm%2Bc2s%2BK4F%2Bs6CxYYMEiHiNUak%2FQyBdjRie3I8qgwMujLzlLPYUFugXrF3KsnN5LVb5PNzWT9Tm31cq52Zym5%2BSRXYhx8GFY3Hrz%2BYXvn4XKycTW5ulVduVy9%2FCK5siztt9vuAg2HLVrz1I1hKTBuczhUfLBIw3pYaOPyOfjOP54VF2XqxQVqR3GQWvNEpeKKyAkrcSN%2BiaLpWIamOdeHWIoWK0IfXXjBpukt%2Bhwhpqwy4iDZnEPgPkQcjBy5YGPEVFVMV6YBeIV5jVnTlcFHrhDkVAniA5NRXPf2nmwg6D7XkzzOiywaCe2AMc9a4E5UksYVrIhbacSjeNfptFKx5mLuiqWUvIbyKE%2FSoaIjFcD2y%2FmQL8SU550YWqGTOwV6eP%2FuSgEuK5YgrA1Tg0aPRQt%2B8MZKyvv77gS%2FQc7ShitciIbQphWWKup7xX3ijyzwApfGC9QNWUPoPTPU9RHWj4MAJrQqtCjGyVqznW3fi0R3Q5Sm2LFL1POYW8856U3A7EaKFcSuGFPbfpqsfbfXf4YtWqCecyCehcw8tAo8gER26OIb2RsDQz8ObJhRPg%2B5xcOIexC4B%2FFYlzRlFlymYXquYxQ0RXEHE6Vjyywq%2BXWfg4HPnx%2Ftgc%2Fm0PisIFVFhGDB4I58VjI%2BD4fPn4s%2FH%2FpFPyfge3UN4JsDJNd%2Bu1%2B7sDE4hknPGNZNpOsZhDMIHzsIm0gZGoRlTZMVGWky6gJhNYPwW4Lwy6W110sXautPq6t3d75fe7l0DVqqt%2B4mjy8ma1eqfz%2BHlhZQn4gibtNGwby8ITi982Lr1bPbucEpLfdeLCMNKcTMOJ1x%2Bhhy2hgap2FVbMqGpildOK1nnB5Osdw4vGig%2BKeb1Surab28fW1n5d7gGFb6wLBKdBllGM4wfPwwDEE5tHLZUDSiGxjhLhg2Mgy%2FmzPl0xDAELe5ZP1Gsv442by4s%2Fxj8uivvQON5M%2F7UFPv1stHhWu1d1xjRVVlJcN1hutjiGtteI8AoV5WMQRzt0eAZobroZ1utACcpgV07d7PO5c2k2t%2FVLe2IDWS5W9qD243jjaOitFafyU1yRidMfoYMpqg4ZXUmJi6bKRPHjsxWoiTMfpdlNQfnJz55OTsbNv3M15ff1699XtK6esPj4TSeu%2BUJlBLG9n5c0bp40hpeXiUNmUDqVBOd6mkCc4o%2FU5fpjsV8DJ1qyu%2F1FYeCywfOPiA24HeR1ZUG%2F0U1RC4aobrDNfHDdfYMAZ%2B93kEEmrS5alC%2B%2BFMZFWWZaXLi3SEtLJ5f24MzuluqdAuxQ6f7B29ASnqHmSj6GEuK%2B8L1k9nJmgwEUeR781ErNwv1dvHZHvwHMEO0HmPasuF1o2rJes77aOd0NaWR81YPMCZDqnVdgM%2BzK6xK0RjD5yKT9U3qdkUAx%2FBd5flm9qmWES5%2BxlnC5PAq8B3XZiwEzMrfgApxKMeYd0vdJxUtv71eC%2F9dLFz7wijceSfTkXtiWPmwM%2FbOnCMYF2TkW6oWgayDGQZyDKQvU2QEUXFRwEyod1s6pdmmhnIhA%2FSjC4wO%2FDvZ%2F8XmPUHnk7O7AFIfRGo7Q%2BpQ58BdGJOM5laSRkH9RyBuPRCYALoPBGH3AOnNmfKJI0Y%2FJRaPJDtAzEtYAUW1DE0AG%2BGTsb%2BrfXfP4yHDMc%2BVXnLePzyXzay55StOwAA\\\" ++_ \\\"SensorsAnalytics iOS SDK\\\" ++_ \\\"production\\\" ++_ \\\"sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22176cd42f0b0afa-04d1da58853c858-183d1933-370944-176cd42f0b1107d%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22176cd42f0b0afa-04d1da58853c858-183d1933-370944-176cd42f0b1107d%22%7D; duToken=d41d8cd9|139621860|1607156994|48b627af1b412a14\\\" ++_ \\\"-\\\" ++_ \\\"-\\\"\",\"input\":{\"type\":\"log\"},\"fileset\":{\"name\":\"access\"},\"service\":{\"type\":\"nginx\"},\"log\":{\"offset\":1673709848,\"file\":{\"path\":\"/data/sa_cluster/nginx/access_log.2021010420\"}},\"event\":{\"module\":\"nginx\",\"dataset\":\"nginx.access\",\"timezone\":\"+08:00\"},\"host\":{\"name\":\"dw-prd-bigdata-sensors_data_log-02\"},\"fields\":{\"ip\":\"************\"}}";
//        String str = "\"**************, *************\" ++_ \"1610175600.005\" ++_ \"POST\" ++_ \"-\" ++_ \"-\" ++_ \"-\" ++_ \"crc=1041104505&gzip=1&data_list=H4sIAAAAAAAAE%2B2ZzW7TQBCA32XpMbW8P%2Fbu5hb1R%2BqJihY4IGSZeNOs6uxa63WjUvUFeAUeAC7ckRBvQ8VrMHYobSWapgirPmwOSTwez87Mzjf2JG8uUGlPtMl0gcYIYykkZjxBI%2BT1QqExTnGMeZIIwRkZodxYc76wTb3Sl3KysyPJ3jbDHMMb3t%2Be0D22vS8Ym%2ByJlHC6C6bUmTIe1LcmVfVKq%2BXR1Cll4ETmXT497WyRVCaExZhSEM%2FKpp5ndz2QUnI4VzlbKee1qtH4Am1Z%2BED6%2BREY26o7s9lSF36OxgwzkOVVlZ0pV2trQJFFSRzFra6us5l2tc%2BK%2FByNZ3lZK5AubKHK1uDh3BqFyQi3uoU601P1qIi3jPJL604zf15BDOj1wf5BK57mzmnlQPLj65erj99%2Ffvp29eFze6YN9j0smtnZrFaQrW0mYpAv9UyjsXdN61%2FjWu92X%2B7YxaIx2p9Huy%2BPnTLFrvK5Ltvc7ljjnS1LWOQmJXOlT%2BZgU8gUhGXutW%2BK1i8aR2nCBKWCEAG7j9P2KqdmyrnOzTtrNS%2FUFI5gvcP8RN1dqbTm5NoqjlkU8xRjQZggKWGd1VK%2Fu7UVJMIRacW2viXFNLrWvdnXRW6aWT71zcolKKJS3QrO5G2Z%2FENWLGyF8ZCMbunKOqhG7dHvqum2G8KNar1sch0VDUjR5QgVuvbaTP1fkFltdlfUcNgFcbEKvOhc2dzNZ91rTdruZOi%2BKm%2BvXSg%2Ft62neePtcefa5eVoU%2BwlxbJP7CnjMqVSUBawfxT2q6JpCybXRrm%2B0V8P1MDg3zQ3A2gAa10dSBOQlCes1yaQUMwSlkoSmsCjmsCf773j%2FyBSA2sBD2dmAPDf4%2BSAsE%2F7xJ5BhWDJUx6ofwz1657C%2FzP1aygaFu8b5OTpeb%2FPyaHwnmCS9Mk7LE1BX1AcgA8jfhjxh4J9KkWv2HOoDkziNAnYhxE%2FjPiDbAI8oXGvP%2B9zQgmPaegBYcIPE%2F6AqO%2F1Tz3MpaBSpiw88YcRP4z4Tw%2B8lKTX2zwFQGMY8IkIwIcRP4z4%2FWL%2F9hdj2PZbviMAAA%3D%3D\" ++_ \"SensorsAnalytics iOS SDK\" ++_ \"production\" ++_ \"UM_distinctid=173bc47a4f656-0560f3bc1264508-2d7b1f29-5a900-173bc47a4f7285; duToken=d41d8cd9|119891475|1596374491|d91a919671da461e\" ++_ \"-\" ++_ \"-\"";
        new LibraPerformanceTest02().eval(str);
    }

}
