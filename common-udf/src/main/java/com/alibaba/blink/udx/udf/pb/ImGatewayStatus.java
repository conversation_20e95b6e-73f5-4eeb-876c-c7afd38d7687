// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: protofile.proto

package com.alibaba.blink.udx.udf.pb;

public final class ImGatewayStatus {
  private ImGatewayStatus() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface StatusOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Status)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * The status event name (online, offline).
     * </pre>
     *
     * <code>string event = 1;</code>
     * @return The event.
     */
    String getEvent();
    /**
     * <pre>
     * The status event name (online, offline).
     * </pre>
     *
     * <code>string event = 1;</code>
     * @return The bytes for event.
     */
    com.google.protobuf.ByteString
        getEventBytes();

    /**
     * <pre>
     * The connection session id.
     * </pre>
     *
     * <code>string sid = 2;</code>
     * @return The sid.
     */
    String getSid();
    /**
     * <pre>
     * The connection session id.
     * </pre>
     *
     * <code>string sid = 2;</code>
     * @return The bytes for sid.
     */
    com.google.protobuf.ByteString
        getSidBytes();

    /**
     * <pre>
     * The user id.
     * </pre>
     *
     * <code>string uid = 3;</code>
     * @return The uid.
     */
    String getUid();
    /**
     * <pre>
     * The user id.
     * </pre>
     *
     * <code>string uid = 3;</code>
     * @return The bytes for uid.
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <pre>
     * The application identifier.
     * </pre>
     *
     * <code>string scheme = 4;</code>
     * @return The scheme.
     */
    String getScheme();
    /**
     * <pre>
     * The application identifier.
     * </pre>
     *
     * <code>string scheme = 4;</code>
     * @return The bytes for scheme.
     */
    com.google.protobuf.ByteString
        getSchemeBytes();

    /**
     * <pre>
     * The application key.
     * </pre>
     *
     * <code>string appKey = 5;</code>
     * @return The appKey.
     */
    String getAppKey();
    /**
     * <pre>
     * The application key.
     * </pre>
     *
     * <code>string appKey = 5;</code>
     * @return The bytes for appKey.
     */
    com.google.protobuf.ByteString
        getAppKeyBytes();

    /**
     * <pre>
     * The application version (4.99.1, ...).
     * </pre>
     *
     * <code>string appVersion = 6;</code>
     * @return The appVersion.
     */
    String getAppVersion();
    /**
     * <pre>
     * The application version (4.99.1, ...).
     * </pre>
     *
     * <code>string appVersion = 6;</code>
     * @return The bytes for appVersion.
     */
    com.google.protobuf.ByteString
        getAppVersionBytes();

    /**
     * <pre>
     * The user access agent string.
     * </pre>
     *
     * <code>string agent = 7;</code>
     * @return The agent.
     */
    String getAgent();
    /**
     * <pre>
     * The user access agent string.
     * </pre>
     *
     * <code>string agent = 7;</code>
     * @return The bytes for agent.
     */
    com.google.protobuf.ByteString
        getAgentBytes();

    /**
     * <pre>
     * The name of the client platform (ios, android, web, ...).
     * </pre>
     *
     * <code>string platform = 8;</code>
     * @return The platform.
     */
    String getPlatform();
    /**
     * <pre>
     * The name of the client platform (ios, android, web, ...).
     * </pre>
     *
     * <code>string platform = 8;</code>
     * @return The bytes for platform.
     */
    com.google.protobuf.ByteString
        getPlatformBytes();

    /**
     * <pre>
     * The user language identifier (zh_cn, ...).
     * </pre>
     *
     * <code>string lang = 9;</code>
     * @return The lang.
     */
    String getLang();
    /**
     * <pre>
     * The user language identifier (zh_cn, ...).
     * </pre>
     *
     * <code>string lang = 9;</code>
     * @return The bytes for lang.
     */
    com.google.protobuf.ByteString
        getLangBytes();

    /**
     * <pre>
     * The client sdk version (1.0.0, ...).
     * </pre>
     *
     * <code>string sdkVersion = 10;</code>
     * @return The sdkVersion.
     */
    String getSdkVersion();
    /**
     * <pre>
     * The client sdk version (1.0.0, ...).
     * </pre>
     *
     * <code>string sdkVersion = 10;</code>
     * @return The bytes for sdkVersion.
     */
    com.google.protobuf.ByteString
        getSdkVersionBytes();

    /**
     * <pre>
     * The user device identifier.
     * </pre>
     *
     * <code>string deviceId = 11;</code>
     * @return The deviceId.
     */
    String getDeviceId();
    /**
     * <pre>
     * The user device identifier.
     * </pre>
     *
     * <code>string deviceId = 11;</code>
     * @return The bytes for deviceId.
     */
    com.google.protobuf.ByteString
        getDeviceIdBytes();

    /**
     * <pre>
     * The user device brand (huawei, xiaomi, ...).
     * </pre>
     *
     * <code>string deviceBrand = 12;</code>
     * @return The deviceBrand.
     */
    String getDeviceBrand();
    /**
     * <pre>
     * The user device brand (huawei, xiaomi, ...).
     * </pre>
     *
     * <code>string deviceBrand = 12;</code>
     * @return The bytes for deviceBrand.
     */
    com.google.protobuf.ByteString
        getDeviceBrandBytes();

    /**
     * <pre>
     * The client os name (HarmonyOS, Android, ...).
     * </pre>
     *
     * <code>string osName = 13;</code>
     * @return The osName.
     */
    String getOsName();
    /**
     * <pre>
     * The client os name (HarmonyOS, Android, ...).
     * </pre>
     *
     * <code>string osName = 13;</code>
     * @return The bytes for osName.
     */
    com.google.protobuf.ByteString
        getOsNameBytes();

    /**
     * <pre>
     * The client os version (12.0.5, ...).
     * </pre>
     *
     * <code>string osVersion = 14;</code>
     * @return The osVersion.
     */
    String getOsVersion();
    /**
     * <pre>
     * The client os version (12.0.5, ...).
     * </pre>
     *
     * <code>string osVersion = 14;</code>
     * @return The bytes for osVersion.
     */
    com.google.protobuf.ByteString
        getOsVersionBytes();

    /**
     * <pre>
     * The status release timestamp (ms).
     * </pre>
     *
     * <code>fixed64 timestamp = 15;</code>
     * @return The timestamp.
     */
    long getTimestamp();

    /**
     * <pre>
     * The chat service tinode uid.
     * </pre>
     *
     * <code>int64 cid = 16;</code>
     * @return The cid.
     */
    long getCid();

    /**
     * <pre>
     * The connection group name.
     * </pre>
     *
     * <code>string group = 17;</code>
     * @return The group.
     */
    String getGroup();
    /**
     * <pre>
     * The connection group name.
     * </pre>
     *
     * <code>string group = 17;</code>
     * @return The bytes for group.
     */
    com.google.protobuf.ByteString
        getGroupBytes();
  }
  /**
   * Protobuf type {@code Status}
   */
  public static final class Status extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Status)
      StatusOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Status.newBuilder() to construct.
    private Status(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Status() {
      event_ = "";
      sid_ = "";
      uid_ = "";
      scheme_ = "";
      appKey_ = "";
      appVersion_ = "";
      agent_ = "";
      platform_ = "";
      lang_ = "";
      sdkVersion_ = "";
      deviceId_ = "";
      deviceBrand_ = "";
      osName_ = "";
      osVersion_ = "";
      group_ = "";
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new Status();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Status(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              String s = input.readStringRequireUtf8();

              event_ = s;
              break;
            }
            case 18: {
              String s = input.readStringRequireUtf8();

              sid_ = s;
              break;
            }
            case 26: {
              String s = input.readStringRequireUtf8();

              uid_ = s;
              break;
            }
            case 34: {
              String s = input.readStringRequireUtf8();

              scheme_ = s;
              break;
            }
            case 42: {
              String s = input.readStringRequireUtf8();

              appKey_ = s;
              break;
            }
            case 50: {
              String s = input.readStringRequireUtf8();

              appVersion_ = s;
              break;
            }
            case 58: {
              String s = input.readStringRequireUtf8();

              agent_ = s;
              break;
            }
            case 66: {
              String s = input.readStringRequireUtf8();

              platform_ = s;
              break;
            }
            case 74: {
              String s = input.readStringRequireUtf8();

              lang_ = s;
              break;
            }
            case 82: {
              String s = input.readStringRequireUtf8();

              sdkVersion_ = s;
              break;
            }
            case 90: {
              String s = input.readStringRequireUtf8();

              deviceId_ = s;
              break;
            }
            case 98: {
              String s = input.readStringRequireUtf8();

              deviceBrand_ = s;
              break;
            }
            case 106: {
              String s = input.readStringRequireUtf8();

              osName_ = s;
              break;
            }
            case 114: {
              String s = input.readStringRequireUtf8();

              osVersion_ = s;
              break;
            }
            case 121: {

              timestamp_ = input.readFixed64();
              break;
            }
            case 128: {

              cid_ = input.readInt64();
              break;
            }
            case 138: {
              String s = input.readStringRequireUtf8();

              group_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return internal_static_Status_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return internal_static_Status_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status.class, com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status.Builder.class);
    }

    public static final int EVENT_FIELD_NUMBER = 1;
    private volatile Object event_;
    /**
     * <pre>
     * The status event name (online, offline).
     * </pre>
     *
     * <code>string event = 1;</code>
     * @return The event.
     */
    @Override
    public String getEvent() {
      Object ref = event_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        event_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The status event name (online, offline).
     * </pre>
     *
     * <code>string event = 1;</code>
     * @return The bytes for event.
     */
    @Override
    public com.google.protobuf.ByteString
        getEventBytes() {
      Object ref = event_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        event_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SID_FIELD_NUMBER = 2;
    private volatile Object sid_;
    /**
     * <pre>
     * The connection session id.
     * </pre>
     *
     * <code>string sid = 2;</code>
     * @return The sid.
     */
    @Override
    public String getSid() {
      Object ref = sid_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        sid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The connection session id.
     * </pre>
     *
     * <code>string sid = 2;</code>
     * @return The bytes for sid.
     */
    @Override
    public com.google.protobuf.ByteString
        getSidBytes() {
      Object ref = sid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UID_FIELD_NUMBER = 3;
    private volatile Object uid_;
    /**
     * <pre>
     * The user id.
     * </pre>
     *
     * <code>string uid = 3;</code>
     * @return The uid.
     */
    @Override
    public String getUid() {
      Object ref = uid_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The user id.
     * </pre>
     *
     * <code>string uid = 3;</code>
     * @return The bytes for uid.
     */
    @Override
    public com.google.protobuf.ByteString
        getUidBytes() {
      Object ref = uid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SCHEME_FIELD_NUMBER = 4;
    private volatile Object scheme_;
    /**
     * <pre>
     * The application identifier.
     * </pre>
     *
     * <code>string scheme = 4;</code>
     * @return The scheme.
     */
    @Override
    public String getScheme() {
      Object ref = scheme_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        scheme_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The application identifier.
     * </pre>
     *
     * <code>string scheme = 4;</code>
     * @return The bytes for scheme.
     */
    @Override
    public com.google.protobuf.ByteString
        getSchemeBytes() {
      Object ref = scheme_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        scheme_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APPKEY_FIELD_NUMBER = 5;
    private volatile Object appKey_;
    /**
     * <pre>
     * The application key.
     * </pre>
     *
     * <code>string appKey = 5;</code>
     * @return The appKey.
     */
    @Override
    public String getAppKey() {
      Object ref = appKey_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        appKey_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The application key.
     * </pre>
     *
     * <code>string appKey = 5;</code>
     * @return The bytes for appKey.
     */
    @Override
    public com.google.protobuf.ByteString
        getAppKeyBytes() {
      Object ref = appKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        appKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APPVERSION_FIELD_NUMBER = 6;
    private volatile Object appVersion_;
    /**
     * <pre>
     * The application version (4.99.1, ...).
     * </pre>
     *
     * <code>string appVersion = 6;</code>
     * @return The appVersion.
     */
    @Override
    public String getAppVersion() {
      Object ref = appVersion_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        appVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The application version (4.99.1, ...).
     * </pre>
     *
     * <code>string appVersion = 6;</code>
     * @return The bytes for appVersion.
     */
    @Override
    public com.google.protobuf.ByteString
        getAppVersionBytes() {
      Object ref = appVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        appVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AGENT_FIELD_NUMBER = 7;
    private volatile Object agent_;
    /**
     * <pre>
     * The user access agent string.
     * </pre>
     *
     * <code>string agent = 7;</code>
     * @return The agent.
     */
    @Override
    public String getAgent() {
      Object ref = agent_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        agent_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The user access agent string.
     * </pre>
     *
     * <code>string agent = 7;</code>
     * @return The bytes for agent.
     */
    @Override
    public com.google.protobuf.ByteString
        getAgentBytes() {
      Object ref = agent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        agent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLATFORM_FIELD_NUMBER = 8;
    private volatile Object platform_;
    /**
     * <pre>
     * The name of the client platform (ios, android, web, ...).
     * </pre>
     *
     * <code>string platform = 8;</code>
     * @return The platform.
     */
    @Override
    public String getPlatform() {
      Object ref = platform_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        platform_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The name of the client platform (ios, android, web, ...).
     * </pre>
     *
     * <code>string platform = 8;</code>
     * @return The bytes for platform.
     */
    @Override
    public com.google.protobuf.ByteString
        getPlatformBytes() {
      Object ref = platform_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        platform_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LANG_FIELD_NUMBER = 9;
    private volatile Object lang_;
    /**
     * <pre>
     * The user language identifier (zh_cn, ...).
     * </pre>
     *
     * <code>string lang = 9;</code>
     * @return The lang.
     */
    @Override
    public String getLang() {
      Object ref = lang_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        lang_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The user language identifier (zh_cn, ...).
     * </pre>
     *
     * <code>string lang = 9;</code>
     * @return The bytes for lang.
     */
    @Override
    public com.google.protobuf.ByteString
        getLangBytes() {
      Object ref = lang_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        lang_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SDKVERSION_FIELD_NUMBER = 10;
    private volatile Object sdkVersion_;
    /**
     * <pre>
     * The client sdk version (1.0.0, ...).
     * </pre>
     *
     * <code>string sdkVersion = 10;</code>
     * @return The sdkVersion.
     */
    @Override
    public String getSdkVersion() {
      Object ref = sdkVersion_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        sdkVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The client sdk version (1.0.0, ...).
     * </pre>
     *
     * <code>string sdkVersion = 10;</code>
     * @return The bytes for sdkVersion.
     */
    @Override
    public com.google.protobuf.ByteString
        getSdkVersionBytes() {
      Object ref = sdkVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sdkVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICEID_FIELD_NUMBER = 11;
    private volatile Object deviceId_;
    /**
     * <pre>
     * The user device identifier.
     * </pre>
     *
     * <code>string deviceId = 11;</code>
     * @return The deviceId.
     */
    @Override
    public String getDeviceId() {
      Object ref = deviceId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        deviceId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The user device identifier.
     * </pre>
     *
     * <code>string deviceId = 11;</code>
     * @return The bytes for deviceId.
     */
    @Override
    public com.google.protobuf.ByteString
        getDeviceIdBytes() {
      Object ref = deviceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        deviceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DEVICEBRAND_FIELD_NUMBER = 12;
    private volatile Object deviceBrand_;
    /**
     * <pre>
     * The user device brand (huawei, xiaomi, ...).
     * </pre>
     *
     * <code>string deviceBrand = 12;</code>
     * @return The deviceBrand.
     */
    @Override
    public String getDeviceBrand() {
      Object ref = deviceBrand_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        deviceBrand_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The user device brand (huawei, xiaomi, ...).
     * </pre>
     *
     * <code>string deviceBrand = 12;</code>
     * @return The bytes for deviceBrand.
     */
    @Override
    public com.google.protobuf.ByteString
        getDeviceBrandBytes() {
      Object ref = deviceBrand_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        deviceBrand_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OSNAME_FIELD_NUMBER = 13;
    private volatile Object osName_;
    /**
     * <pre>
     * The client os name (HarmonyOS, Android, ...).
     * </pre>
     *
     * <code>string osName = 13;</code>
     * @return The osName.
     */
    @Override
    public String getOsName() {
      Object ref = osName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        osName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The client os name (HarmonyOS, Android, ...).
     * </pre>
     *
     * <code>string osName = 13;</code>
     * @return The bytes for osName.
     */
    @Override
    public com.google.protobuf.ByteString
        getOsNameBytes() {
      Object ref = osName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        osName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OSVERSION_FIELD_NUMBER = 14;
    private volatile Object osVersion_;
    /**
     * <pre>
     * The client os version (12.0.5, ...).
     * </pre>
     *
     * <code>string osVersion = 14;</code>
     * @return The osVersion.
     */
    @Override
    public String getOsVersion() {
      Object ref = osVersion_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        osVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The client os version (12.0.5, ...).
     * </pre>
     *
     * <code>string osVersion = 14;</code>
     * @return The bytes for osVersion.
     */
    @Override
    public com.google.protobuf.ByteString
        getOsVersionBytes() {
      Object ref = osVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        osVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 15;
    private long timestamp_;
    /**
     * <pre>
     * The status release timestamp (ms).
     * </pre>
     *
     * <code>fixed64 timestamp = 15;</code>
     * @return The timestamp.
     */
    @Override
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int CID_FIELD_NUMBER = 16;
    private long cid_;
    /**
     * <pre>
     * The chat service tinode uid.
     * </pre>
     *
     * <code>int64 cid = 16;</code>
     * @return The cid.
     */
    @Override
    public long getCid() {
      return cid_;
    }

    public static final int GROUP_FIELD_NUMBER = 17;
    private volatile Object group_;
    /**
     * <pre>
     * The connection group name.
     * </pre>
     *
     * <code>string group = 17;</code>
     * @return The group.
     */
    @Override
    public String getGroup() {
      Object ref = group_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        group_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * The connection group name.
     * </pre>
     *
     * <code>string group = 17;</code>
     * @return The bytes for group.
     */
    @Override
    public com.google.protobuf.ByteString
        getGroupBytes() {
      Object ref = group_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        group_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(event_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, event_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, sid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(uid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, uid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheme_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, scheme_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appKey_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, appKey_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, appVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(agent_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, agent_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(platform_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, platform_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(lang_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, lang_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sdkVersion_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, sdkVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, deviceId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceBrand_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, deviceBrand_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, osName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osVersion_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, osVersion_);
      }
      if (timestamp_ != 0L) {
        output.writeFixed64(15, timestamp_);
      }
      if (cid_ != 0L) {
        output.writeInt64(16, cid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(group_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 17, group_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(event_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, event_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, sid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(uid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, uid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(scheme_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, scheme_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appKey_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, appKey_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(appVersion_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, appVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(agent_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, agent_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(platform_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, platform_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(lang_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, lang_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sdkVersion_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, sdkVersion_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, deviceId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(deviceBrand_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, deviceBrand_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, osName_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(osVersion_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, osVersion_);
      }
      if (timestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeFixed64Size(15, timestamp_);
      }
      if (cid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(16, cid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(group_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, group_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status)) {
        return super.equals(obj);
      }
      com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status other = (com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status) obj;

      if (!getEvent()
          .equals(other.getEvent())) return false;
      if (!getSid()
          .equals(other.getSid())) return false;
      if (!getUid()
          .equals(other.getUid())) return false;
      if (!getScheme()
          .equals(other.getScheme())) return false;
      if (!getAppKey()
          .equals(other.getAppKey())) return false;
      if (!getAppVersion()
          .equals(other.getAppVersion())) return false;
      if (!getAgent()
          .equals(other.getAgent())) return false;
      if (!getPlatform()
          .equals(other.getPlatform())) return false;
      if (!getLang()
          .equals(other.getLang())) return false;
      if (!getSdkVersion()
          .equals(other.getSdkVersion())) return false;
      if (!getDeviceId()
          .equals(other.getDeviceId())) return false;
      if (!getDeviceBrand()
          .equals(other.getDeviceBrand())) return false;
      if (!getOsName()
          .equals(other.getOsName())) return false;
      if (!getOsVersion()
          .equals(other.getOsVersion())) return false;
      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (getCid()
          != other.getCid()) return false;
      if (!getGroup()
          .equals(other.getGroup())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + EVENT_FIELD_NUMBER;
      hash = (53 * hash) + getEvent().hashCode();
      hash = (37 * hash) + SID_FIELD_NUMBER;
      hash = (53 * hash) + getSid().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + SCHEME_FIELD_NUMBER;
      hash = (53 * hash) + getScheme().hashCode();
      hash = (37 * hash) + APPKEY_FIELD_NUMBER;
      hash = (53 * hash) + getAppKey().hashCode();
      hash = (37 * hash) + APPVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getAppVersion().hashCode();
      hash = (37 * hash) + AGENT_FIELD_NUMBER;
      hash = (53 * hash) + getAgent().hashCode();
      hash = (37 * hash) + PLATFORM_FIELD_NUMBER;
      hash = (53 * hash) + getPlatform().hashCode();
      hash = (37 * hash) + LANG_FIELD_NUMBER;
      hash = (53 * hash) + getLang().hashCode();
      hash = (37 * hash) + SDKVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getSdkVersion().hashCode();
      hash = (37 * hash) + DEVICEID_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceId().hashCode();
      hash = (37 * hash) + DEVICEBRAND_FIELD_NUMBER;
      hash = (53 * hash) + getDeviceBrand().hashCode();
      hash = (37 * hash) + OSNAME_FIELD_NUMBER;
      hash = (53 * hash) + getOsName().hashCode();
      hash = (37 * hash) + OSVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getOsVersion().hashCode();
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestamp());
      hash = (37 * hash) + CID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCid());
      hash = (37 * hash) + GROUP_FIELD_NUMBER;
      hash = (53 * hash) + getGroup().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Status}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Status)
        com.alibaba.blink.udx.udf.pb.ImGatewayStatus.StatusOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return internal_static_Status_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return internal_static_Status_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status.class, com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status.Builder.class);
      }

      // Construct using com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        event_ = "";

        sid_ = "";

        uid_ = "";

        scheme_ = "";

        appKey_ = "";

        appVersion_ = "";

        agent_ = "";

        platform_ = "";

        lang_ = "";

        sdkVersion_ = "";

        deviceId_ = "";

        deviceBrand_ = "";

        osName_ = "";

        osVersion_ = "";

        timestamp_ = 0L;

        cid_ = 0L;

        group_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return internal_static_Status_descriptor;
      }

      @Override
      public com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status getDefaultInstanceForType() {
        return getDefaultInstance();
      }

      @Override
      public com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status build() {
        com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status buildPartial() {
        com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status result = new com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status(this);
        result.event_ = event_;
        result.sid_ = sid_;
        result.uid_ = uid_;
        result.scheme_ = scheme_;
        result.appKey_ = appKey_;
        result.appVersion_ = appVersion_;
        result.agent_ = agent_;
        result.platform_ = platform_;
        result.lang_ = lang_;
        result.sdkVersion_ = sdkVersion_;
        result.deviceId_ = deviceId_;
        result.deviceBrand_ = deviceBrand_;
        result.osName_ = osName_;
        result.osVersion_ = osVersion_;
        result.timestamp_ = timestamp_;
        result.cid_ = cid_;
        result.group_ = group_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status) {
          return mergeFrom((com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status other) {
        if (other == getDefaultInstance()) return this;
        if (!other.getEvent().isEmpty()) {
          event_ = other.event_;
          onChanged();
        }
        if (!other.getSid().isEmpty()) {
          sid_ = other.sid_;
          onChanged();
        }
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          onChanged();
        }
        if (!other.getScheme().isEmpty()) {
          scheme_ = other.scheme_;
          onChanged();
        }
        if (!other.getAppKey().isEmpty()) {
          appKey_ = other.appKey_;
          onChanged();
        }
        if (!other.getAppVersion().isEmpty()) {
          appVersion_ = other.appVersion_;
          onChanged();
        }
        if (!other.getAgent().isEmpty()) {
          agent_ = other.agent_;
          onChanged();
        }
        if (!other.getPlatform().isEmpty()) {
          platform_ = other.platform_;
          onChanged();
        }
        if (!other.getLang().isEmpty()) {
          lang_ = other.lang_;
          onChanged();
        }
        if (!other.getSdkVersion().isEmpty()) {
          sdkVersion_ = other.sdkVersion_;
          onChanged();
        }
        if (!other.getDeviceId().isEmpty()) {
          deviceId_ = other.deviceId_;
          onChanged();
        }
        if (!other.getDeviceBrand().isEmpty()) {
          deviceBrand_ = other.deviceBrand_;
          onChanged();
        }
        if (!other.getOsName().isEmpty()) {
          osName_ = other.osName_;
          onChanged();
        }
        if (!other.getOsVersion().isEmpty()) {
          osVersion_ = other.osVersion_;
          onChanged();
        }
        if (other.getTimestamp() != 0L) {
          setTimestamp(other.getTimestamp());
        }
        if (other.getCid() != 0L) {
          setCid(other.getCid());
        }
        if (!other.getGroup().isEmpty()) {
          group_ = other.group_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private Object event_ = "";
      /**
       * <pre>
       * The status event name (online, offline).
       * </pre>
       *
       * <code>string event = 1;</code>
       * @return The event.
       */
      public String getEvent() {
        Object ref = event_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          event_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The status event name (online, offline).
       * </pre>
       *
       * <code>string event = 1;</code>
       * @return The bytes for event.
       */
      public com.google.protobuf.ByteString
          getEventBytes() {
        Object ref = event_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          event_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The status event name (online, offline).
       * </pre>
       *
       * <code>string event = 1;</code>
       * @param value The event to set.
       * @return This builder for chaining.
       */
      public Builder setEvent(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        event_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status event name (online, offline).
       * </pre>
       *
       * <code>string event = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEvent() {

        event_ = getDefaultInstance().getEvent();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status event name (online, offline).
       * </pre>
       *
       * <code>string event = 1;</code>
       * @param value The bytes for event to set.
       * @return This builder for chaining.
       */
      public Builder setEventBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        event_ = value;
        onChanged();
        return this;
      }

      private Object sid_ = "";
      /**
       * <pre>
       * The connection session id.
       * </pre>
       *
       * <code>string sid = 2;</code>
       * @return The sid.
       */
      public String getSid() {
        Object ref = sid_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          sid_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The connection session id.
       * </pre>
       *
       * <code>string sid = 2;</code>
       * @return The bytes for sid.
       */
      public com.google.protobuf.ByteString
          getSidBytes() {
        Object ref = sid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          sid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The connection session id.
       * </pre>
       *
       * <code>string sid = 2;</code>
       * @param value The sid to set.
       * @return This builder for chaining.
       */
      public Builder setSid(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        sid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The connection session id.
       * </pre>
       *
       * <code>string sid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSid() {

        sid_ = getDefaultInstance().getSid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The connection session id.
       * </pre>
       *
       * <code>string sid = 2;</code>
       * @param value The bytes for sid to set.
       * @return This builder for chaining.
       */
      public Builder setSidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        sid_ = value;
        onChanged();
        return this;
      }

      private Object uid_ = "";
      /**
       * <pre>
       * The user id.
       * </pre>
       *
       * <code>string uid = 3;</code>
       * @return The uid.
       */
      public String getUid() {
        Object ref = uid_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The user id.
       * </pre>
       *
       * <code>string uid = 3;</code>
       * @return The bytes for uid.
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The user id.
       * </pre>
       *
       * <code>string uid = 3;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user id.
       * </pre>
       *
       * <code>string uid = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {

        uid_ = getDefaultInstance().getUid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user id.
       * </pre>
       *
       * <code>string uid = 3;</code>
       * @param value The bytes for uid to set.
       * @return This builder for chaining.
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        uid_ = value;
        onChanged();
        return this;
      }

      private Object scheme_ = "";
      /**
       * <pre>
       * The application identifier.
       * </pre>
       *
       * <code>string scheme = 4;</code>
       * @return The scheme.
       */
      public String getScheme() {
        Object ref = scheme_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          scheme_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The application identifier.
       * </pre>
       *
       * <code>string scheme = 4;</code>
       * @return The bytes for scheme.
       */
      public com.google.protobuf.ByteString
          getSchemeBytes() {
        Object ref = scheme_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          scheme_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The application identifier.
       * </pre>
       *
       * <code>string scheme = 4;</code>
       * @param value The scheme to set.
       * @return This builder for chaining.
       */
      public Builder setScheme(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        scheme_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The application identifier.
       * </pre>
       *
       * <code>string scheme = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearScheme() {

        scheme_ = getDefaultInstance().getScheme();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The application identifier.
       * </pre>
       *
       * <code>string scheme = 4;</code>
       * @param value The bytes for scheme to set.
       * @return This builder for chaining.
       */
      public Builder setSchemeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        scheme_ = value;
        onChanged();
        return this;
      }

      private Object appKey_ = "";
      /**
       * <pre>
       * The application key.
       * </pre>
       *
       * <code>string appKey = 5;</code>
       * @return The appKey.
       */
      public String getAppKey() {
        Object ref = appKey_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          appKey_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The application key.
       * </pre>
       *
       * <code>string appKey = 5;</code>
       * @return The bytes for appKey.
       */
      public com.google.protobuf.ByteString
          getAppKeyBytes() {
        Object ref = appKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          appKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The application key.
       * </pre>
       *
       * <code>string appKey = 5;</code>
       * @param value The appKey to set.
       * @return This builder for chaining.
       */
      public Builder setAppKey(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        appKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The application key.
       * </pre>
       *
       * <code>string appKey = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppKey() {

        appKey_ = getDefaultInstance().getAppKey();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The application key.
       * </pre>
       *
       * <code>string appKey = 5;</code>
       * @param value The bytes for appKey to set.
       * @return This builder for chaining.
       */
      public Builder setAppKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        appKey_ = value;
        onChanged();
        return this;
      }

      private Object appVersion_ = "";
      /**
       * <pre>
       * The application version (4.99.1, ...).
       * </pre>
       *
       * <code>string appVersion = 6;</code>
       * @return The appVersion.
       */
      public String getAppVersion() {
        Object ref = appVersion_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          appVersion_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The application version (4.99.1, ...).
       * </pre>
       *
       * <code>string appVersion = 6;</code>
       * @return The bytes for appVersion.
       */
      public com.google.protobuf.ByteString
          getAppVersionBytes() {
        Object ref = appVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          appVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The application version (4.99.1, ...).
       * </pre>
       *
       * <code>string appVersion = 6;</code>
       * @param value The appVersion to set.
       * @return This builder for chaining.
       */
      public Builder setAppVersion(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        appVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The application version (4.99.1, ...).
       * </pre>
       *
       * <code>string appVersion = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppVersion() {

        appVersion_ = getDefaultInstance().getAppVersion();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The application version (4.99.1, ...).
       * </pre>
       *
       * <code>string appVersion = 6;</code>
       * @param value The bytes for appVersion to set.
       * @return This builder for chaining.
       */
      public Builder setAppVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        appVersion_ = value;
        onChanged();
        return this;
      }

      private Object agent_ = "";
      /**
       * <pre>
       * The user access agent string.
       * </pre>
       *
       * <code>string agent = 7;</code>
       * @return The agent.
       */
      public String getAgent() {
        Object ref = agent_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          agent_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The user access agent string.
       * </pre>
       *
       * <code>string agent = 7;</code>
       * @return The bytes for agent.
       */
      public com.google.protobuf.ByteString
          getAgentBytes() {
        Object ref = agent_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          agent_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The user access agent string.
       * </pre>
       *
       * <code>string agent = 7;</code>
       * @param value The agent to set.
       * @return This builder for chaining.
       */
      public Builder setAgent(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        agent_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user access agent string.
       * </pre>
       *
       * <code>string agent = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearAgent() {

        agent_ = getDefaultInstance().getAgent();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user access agent string.
       * </pre>
       *
       * <code>string agent = 7;</code>
       * @param value The bytes for agent to set.
       * @return This builder for chaining.
       */
      public Builder setAgentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        agent_ = value;
        onChanged();
        return this;
      }

      private Object platform_ = "";
      /**
       * <pre>
       * The name of the client platform (ios, android, web, ...).
       * </pre>
       *
       * <code>string platform = 8;</code>
       * @return The platform.
       */
      public String getPlatform() {
        Object ref = platform_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          platform_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The name of the client platform (ios, android, web, ...).
       * </pre>
       *
       * <code>string platform = 8;</code>
       * @return The bytes for platform.
       */
      public com.google.protobuf.ByteString
          getPlatformBytes() {
        Object ref = platform_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          platform_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The name of the client platform (ios, android, web, ...).
       * </pre>
       *
       * <code>string platform = 8;</code>
       * @param value The platform to set.
       * @return This builder for chaining.
       */
      public Builder setPlatform(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        platform_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The name of the client platform (ios, android, web, ...).
       * </pre>
       *
       * <code>string platform = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatform() {

        platform_ = getDefaultInstance().getPlatform();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The name of the client platform (ios, android, web, ...).
       * </pre>
       *
       * <code>string platform = 8;</code>
       * @param value The bytes for platform to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        platform_ = value;
        onChanged();
        return this;
      }

      private Object lang_ = "";
      /**
       * <pre>
       * The user language identifier (zh_cn, ...).
       * </pre>
       *
       * <code>string lang = 9;</code>
       * @return The lang.
       */
      public String getLang() {
        Object ref = lang_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          lang_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The user language identifier (zh_cn, ...).
       * </pre>
       *
       * <code>string lang = 9;</code>
       * @return The bytes for lang.
       */
      public com.google.protobuf.ByteString
          getLangBytes() {
        Object ref = lang_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          lang_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The user language identifier (zh_cn, ...).
       * </pre>
       *
       * <code>string lang = 9;</code>
       * @param value The lang to set.
       * @return This builder for chaining.
       */
      public Builder setLang(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        lang_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user language identifier (zh_cn, ...).
       * </pre>
       *
       * <code>string lang = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearLang() {

        lang_ = getDefaultInstance().getLang();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user language identifier (zh_cn, ...).
       * </pre>
       *
       * <code>string lang = 9;</code>
       * @param value The bytes for lang to set.
       * @return This builder for chaining.
       */
      public Builder setLangBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        lang_ = value;
        onChanged();
        return this;
      }

      private Object sdkVersion_ = "";
      /**
       * <pre>
       * The client sdk version (1.0.0, ...).
       * </pre>
       *
       * <code>string sdkVersion = 10;</code>
       * @return The sdkVersion.
       */
      public String getSdkVersion() {
        Object ref = sdkVersion_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          sdkVersion_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The client sdk version (1.0.0, ...).
       * </pre>
       *
       * <code>string sdkVersion = 10;</code>
       * @return The bytes for sdkVersion.
       */
      public com.google.protobuf.ByteString
          getSdkVersionBytes() {
        Object ref = sdkVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          sdkVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The client sdk version (1.0.0, ...).
       * </pre>
       *
       * <code>string sdkVersion = 10;</code>
       * @param value The sdkVersion to set.
       * @return This builder for chaining.
       */
      public Builder setSdkVersion(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        sdkVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The client sdk version (1.0.0, ...).
       * </pre>
       *
       * <code>string sdkVersion = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearSdkVersion() {

        sdkVersion_ = getDefaultInstance().getSdkVersion();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The client sdk version (1.0.0, ...).
       * </pre>
       *
       * <code>string sdkVersion = 10;</code>
       * @param value The bytes for sdkVersion to set.
       * @return This builder for chaining.
       */
      public Builder setSdkVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        sdkVersion_ = value;
        onChanged();
        return this;
      }

      private Object deviceId_ = "";
      /**
       * <pre>
       * The user device identifier.
       * </pre>
       *
       * <code>string deviceId = 11;</code>
       * @return The deviceId.
       */
      public String getDeviceId() {
        Object ref = deviceId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          deviceId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The user device identifier.
       * </pre>
       *
       * <code>string deviceId = 11;</code>
       * @return The bytes for deviceId.
       */
      public com.google.protobuf.ByteString
          getDeviceIdBytes() {
        Object ref = deviceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          deviceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The user device identifier.
       * </pre>
       *
       * <code>string deviceId = 11;</code>
       * @param value The deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceId(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        deviceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user device identifier.
       * </pre>
       *
       * <code>string deviceId = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceId() {

        deviceId_ = getDefaultInstance().getDeviceId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user device identifier.
       * </pre>
       *
       * <code>string deviceId = 11;</code>
       * @param value The bytes for deviceId to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        deviceId_ = value;
        onChanged();
        return this;
      }

      private Object deviceBrand_ = "";
      /**
       * <pre>
       * The user device brand (huawei, xiaomi, ...).
       * </pre>
       *
       * <code>string deviceBrand = 12;</code>
       * @return The deviceBrand.
       */
      public String getDeviceBrand() {
        Object ref = deviceBrand_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          deviceBrand_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The user device brand (huawei, xiaomi, ...).
       * </pre>
       *
       * <code>string deviceBrand = 12;</code>
       * @return The bytes for deviceBrand.
       */
      public com.google.protobuf.ByteString
          getDeviceBrandBytes() {
        Object ref = deviceBrand_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          deviceBrand_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The user device brand (huawei, xiaomi, ...).
       * </pre>
       *
       * <code>string deviceBrand = 12;</code>
       * @param value The deviceBrand to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceBrand(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        deviceBrand_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user device brand (huawei, xiaomi, ...).
       * </pre>
       *
       * <code>string deviceBrand = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeviceBrand() {

        deviceBrand_ = getDefaultInstance().getDeviceBrand();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The user device brand (huawei, xiaomi, ...).
       * </pre>
       *
       * <code>string deviceBrand = 12;</code>
       * @param value The bytes for deviceBrand to set.
       * @return This builder for chaining.
       */
      public Builder setDeviceBrandBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        deviceBrand_ = value;
        onChanged();
        return this;
      }

      private Object osName_ = "";
      /**
       * <pre>
       * The client os name (HarmonyOS, Android, ...).
       * </pre>
       *
       * <code>string osName = 13;</code>
       * @return The osName.
       */
      public String getOsName() {
        Object ref = osName_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          osName_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The client os name (HarmonyOS, Android, ...).
       * </pre>
       *
       * <code>string osName = 13;</code>
       * @return The bytes for osName.
       */
      public com.google.protobuf.ByteString
          getOsNameBytes() {
        Object ref = osName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          osName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The client os name (HarmonyOS, Android, ...).
       * </pre>
       *
       * <code>string osName = 13;</code>
       * @param value The osName to set.
       * @return This builder for chaining.
       */
      public Builder setOsName(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        osName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The client os name (HarmonyOS, Android, ...).
       * </pre>
       *
       * <code>string osName = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsName() {

        osName_ = getDefaultInstance().getOsName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The client os name (HarmonyOS, Android, ...).
       * </pre>
       *
       * <code>string osName = 13;</code>
       * @param value The bytes for osName to set.
       * @return This builder for chaining.
       */
      public Builder setOsNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        osName_ = value;
        onChanged();
        return this;
      }

      private Object osVersion_ = "";
      /**
       * <pre>
       * The client os version (12.0.5, ...).
       * </pre>
       *
       * <code>string osVersion = 14;</code>
       * @return The osVersion.
       */
      public String getOsVersion() {
        Object ref = osVersion_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          osVersion_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The client os version (12.0.5, ...).
       * </pre>
       *
       * <code>string osVersion = 14;</code>
       * @return The bytes for osVersion.
       */
      public com.google.protobuf.ByteString
          getOsVersionBytes() {
        Object ref = osVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          osVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The client os version (12.0.5, ...).
       * </pre>
       *
       * <code>string osVersion = 14;</code>
       * @param value The osVersion to set.
       * @return This builder for chaining.
       */
      public Builder setOsVersion(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        osVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The client os version (12.0.5, ...).
       * </pre>
       *
       * <code>string osVersion = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsVersion() {

        osVersion_ = getDefaultInstance().getOsVersion();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The client os version (12.0.5, ...).
       * </pre>
       *
       * <code>string osVersion = 14;</code>
       * @param value The bytes for osVersion to set.
       * @return This builder for chaining.
       */
      public Builder setOsVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        osVersion_ = value;
        onChanged();
        return this;
      }

      private long timestamp_ ;
      /**
       * <pre>
       * The status release timestamp (ms).
       * </pre>
       *
       * <code>fixed64 timestamp = 15;</code>
       * @return The timestamp.
       */
      @Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <pre>
       * The status release timestamp (ms).
       * </pre>
       *
       * <code>fixed64 timestamp = 15;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {

        timestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The status release timestamp (ms).
       * </pre>
       *
       * <code>fixed64 timestamp = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {

        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private long cid_ ;
      /**
       * <pre>
       * The chat service tinode uid.
       * </pre>
       *
       * <code>int64 cid = 16;</code>
       * @return The cid.
       */
      @Override
      public long getCid() {
        return cid_;
      }
      /**
       * <pre>
       * The chat service tinode uid.
       * </pre>
       *
       * <code>int64 cid = 16;</code>
       * @param value The cid to set.
       * @return This builder for chaining.
       */
      public Builder setCid(long value) {

        cid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The chat service tinode uid.
       * </pre>
       *
       * <code>int64 cid = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearCid() {

        cid_ = 0L;
        onChanged();
        return this;
      }

      private Object group_ = "";
      /**
       * <pre>
       * The connection group name.
       * </pre>
       *
       * <code>string group = 17;</code>
       * @return The group.
       */
      public String getGroup() {
        Object ref = group_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          group_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * The connection group name.
       * </pre>
       *
       * <code>string group = 17;</code>
       * @return The bytes for group.
       */
      public com.google.protobuf.ByteString
          getGroupBytes() {
        Object ref = group_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          group_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * The connection group name.
       * </pre>
       *
       * <code>string group = 17;</code>
       * @param value The group to set.
       * @return This builder for chaining.
       */
      public Builder setGroup(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        group_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The connection group name.
       * </pre>
       *
       * <code>string group = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearGroup() {

        group_ = getDefaultInstance().getGroup();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * The connection group name.
       * </pre>
       *
       * <code>string group = 17;</code>
       * @param value The bytes for group to set.
       * @return This builder for chaining.
       */
      public Builder setGroupBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        group_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Status)
    }

    // @@protoc_insertion_point(class_scope:Status)
    private static final com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status();
    }

    public static com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Status>
        PARSER = new com.google.protobuf.AbstractParser<Status>() {
      @Override
      public Status parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Status(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Status> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<Status> getParserForType() {
      return PARSER;
    }

    @Override
    public com.alibaba.blink.udx.udf.pb.ImGatewayStatus.Status getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Status_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Status_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\017protofile.proto\"\241\002\n\006Status\022\r\n\005event\030\001 " +
      "\001(\t\022\013\n\003sid\030\002 \001(\t\022\013\n\003uid\030\003 \001(\t\022\016\n\006scheme\030" +
      "\004 \001(\t\022\016\n\006appKey\030\005 \001(\t\022\022\n\nappVersion\030\006 \001(" +
      "\t\022\r\n\005agent\030\007 \001(\t\022\020\n\010platform\030\010 \001(\t\022\014\n\004la" +
      "ng\030\t \001(\t\022\022\n\nsdkVersion\030\n \001(\t\022\020\n\010deviceId" +
      "\030\013 \001(\t\022\023\n\013deviceBrand\030\014 \001(\t\022\016\n\006osName\030\r " +
      "\001(\t\022\021\n\tosVersion\030\016 \001(\t\022\021\n\ttimestamp\030\017 \001(" +
      "\006\022\013\n\003cid\030\020 \001(\003\022\r\n\005group\030\021 \001(\tB/\n\034com.ali" +
      "baba.blink.udx.udf.pbB\017ImGatewayStatusb\006" +
      "proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Status_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Status_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Status_descriptor,
        new String[] { "Event", "Sid", "Uid", "Scheme", "AppKey", "AppVersion", "Agent", "Platform", "Lang", "SdkVersion", "DeviceId", "DeviceBrand", "OsName", "OsVersion", "Timestamp", "Cid", "Group", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
