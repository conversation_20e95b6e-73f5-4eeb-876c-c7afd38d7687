package com.alibaba.blink.udx.rockmq.bean;

public class OrderMessage {

    private int openApiTerminalCode;

    private int orderStatus;

    private String platform;

    private String orderNo;

    private String createTime;

    private String payTime;

    private RequestInfo requestInfo;


//    Platform            string `json:"platform"`
//    OrderNo             string `json:"orderNo"`
//    RequestInfo         struct {
//        Imei      string `json:"imei"`
//        Oaid      string `json:"oaid"`
//        DeviceId  string `json:"deviceId"`
//        Uid       int64  `json:"uid"`
//        ClientIp  string `json:"clientIp"`
//        UserAgent string `json:"userAgent"`
//        Channel   string `json:"channel"`
//        ShumeiId  string `json:"shumeiId"`
//        Version   string `json:"version"`
//    } `json:"RequestInfo"`
//    CreateTime string `json:"createTime"`
//    PayTime    string `json:"PayTime"`


    public RequestInfo getRequestInfo() {
        return requestInfo;
    }

    public void setRequestInfo(RequestInfo requestInfo) {
        this.requestInfo = requestInfo;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public int getOpenApiTerminalCode() {
        return openApiTerminalCode;
    }

    public void setOpenApiTerminalCode(int openApiTerminalCode) {
        this.openApiTerminalCode = openApiTerminalCode;
    }

    public int getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(int orderStatus) {
        this.orderStatus = orderStatus;
    }
}
