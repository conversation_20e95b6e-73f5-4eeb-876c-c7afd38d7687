package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Author: WangLin
 * Date: 2023/8/15 上午9:20
 * Description: 解析实时归因里面的venue_level_info信息，将level0Info~level3Info都解析出来如果存在的话，这是多级会场归因需要的
 * <p>
 * 数据格式如下：
 * {
 * "level0Info":{
 * "eventName":"venue_pageview_61",
 * "eventTime":"1691801936984",
 * "properties":{
 * "fcuuid":"UUID9d73683ac4b14855a3a31bcac6a31f2f",
 * "skid":"SIfY5mBaWajmOgVtNuwDnSt",
 * "venue_page_title":"得物七夕主会场",
 * "venue_page_url":"https://cdn-fast.dewu.com/nezha-plus/detail/64bf69f6146ef80108077c22?isAllowVideoAutoPlay=1&nezhaChannel=20",
 * "societytab":0,
 * "venue_title":"七夕主会场",
 * "is_background_tracking":0,
 * "ipvx":"*************",
 * "sk":"9JmwKurfQajMMtomm124rz47CrqMJ84mUR01fYBrFRPSgpwUz9OkFEHzsW2uoiUSAlas2o6LqbByTkSFddVuF5Vb3o1x",
 * "event_name":"venue_pageview_61",
 * "venue_level":"promotion_level_mainVenue",
 * "venue_id":"64bf69f6146ef80108077c22",
 * "current_page":"61",
 * "current_page_title":"会场搭建页",
 * "event_time":"1691801936984"
 * },
 * "venueId":"64bf69f6146ef80108077c22"
 * },
 * "level1Info":{
 * "eventName":"venue_component_content_click_61_3718",
 * "eventTime":"1691802750383",
 * "properties":{
 * "fcuuid":"UUID9d73683ac4b14855a3a31bcac6a31f2f",
 * "block_type_title":"极简_商品流组件",
 * "component_content_type":"0",
 * "block_type":"3718",
 * "skid":"SIfY5mBaWajmOgVtNuwDnSt",
 * "block_content_layout":"一排二默认",
 * "tab_title":"猜她喜欢",
 * "acm":"1.rcmd.product_1550612403.ec_tstream.17990-17992-21725-17996-21722-18000-21234.cn_MIND_ONLINE-rid_45ad7d128a9bbc0c-pos_5-cid_214941-bn_实验103-qcid_214941-cstpl_",
 * "component_type":"simpleProductFlow",
 * "ipvx":"*************",
 * "sk":"9JmwKurfQajMMtomm124rz47CrqMJ84mUR01fYBrFRPSgpwUz9OkFEHzsW2uoiUSAlas2o6LqbByTkSFddVuF5Vb3o1x",
 * "button_title":"阿玛尼 丝绒哑光红管唇釉405 6.5ml +热门色号口红正装 素颜黄皮显白 复古轻奢珍珠小香风礼盒",
 * "venue_id":"64cfb25e41ebe5c66c78f4aa",
 * "current_page":"61",
 * "component_content_position":"117",
 * "venue_page_title":"得物",
 * "venue_page_url":"https://cdn-fast.dewu.com/nezha-plus/detail/64cfb25e41ebe5c66c78f4aa?isAllowVideoAutoPlay=1&navControl=1&nezhaChannel=64bf69f6146ef80108077c22",
 * "venue_title":"七夕送女生",
 * "is_background_tracking":0,
 * "component_uid":"wFG1DudA-41759de",
 * "component_content_type_title":"商品",
 * "event_name":"venue_component_content_click_61_3718",
 * "position":"117",
 * "venue_level":"promotion_level_subVenue",
 * "current_page_title":"会场搭建页",
 * "event_time":"1691802750383"
 * },
 * "venueId":"64cfb25e41ebe5c66c78f4aa"
 * }
 * }
 */
@FunctionHint(output = @DataTypeHint("ROW<f0 STRING>"))
public class VenueLevelInfoParser extends TableFunction<Row> {

    private static final Logger LOG = LoggerFactory.getLogger(VenueLevelInfoParser.class);

    public void eval(String message) {

        try {
            JSONObject jsonObject = JSON.parseObject(message);

            String level0Info = jsonObject.getString("level0Info");
            String level1Info = jsonObject.getString("level1Info");
            String level2Info = jsonObject.getString("level2Info");
            String level3Info = jsonObject.getString("level3Info");

            if (level0Info != null) {
                Row row = new Row(1);
                row.setField(0, level0Info);

                collect(row);
            }
            if (level1Info != null) {
                Row row = new Row(1);
                row.setField(0, level1Info);

                collect(row);
            }
            if (level2Info != null) {
                Row row = new Row(1);
                row.setField(0, level2Info);

                collect(row);
            }
            if (level3Info != null) {
                Row row = new Row(1);
                row.setField(0, level3Info);

                collect(row);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void test1() {
        String s = "{\"level0Info\":{\"eventName\":\"venue_pageview_61\",\"eventTime\":\"1691801936984\",\"properties\":{\"fcuuid\":\"UUID9d73683ac4b14855a3a31bcac6a31f2f\",\"skid\":\"SIfY5mBaWajmOgVtNuwDnSt\",\"venue_page_title\":\"得物七夕主会场\",\"venue_page_url\":\"https://cdn-fast.dewu.com/nezha-plus/detail/64bf69f6146ef80108077c22?isAllowVideoAutoPlay=1&nezhaChannel=20\",\"societytab\":0,\"venue_title\":\"七夕主会场\",\"is_background_tracking\":0,\"ipvx\":\"*************\",\"sk\":\"9JmwKurfQajMMtomm124rz47CrqMJ84mUR01fYBrFRPSgpwUz9OkFEHzsW2uoiUSAlas2o6LqbByTkSFddVuF5Vb3o1x\",\"event_name\":\"venue_pageview_61\",\"venue_level\":\"promotion_level_mainVenue\",\"venue_id\":\"64bf69f6146ef80108077c22\",\"current_page\":\"61\",\"current_page_title\":\"会场搭建页\",\"event_time\":\"1691801936984\"},\"venueId\":\"64bf69f6146ef80108077c22\"},\"level1Info\":{\"eventName\":\"venue_component_content_click_61_3718\",\"eventTime\":\"1691802750383\",\"properties\":{\"fcuuid\":\"UUID9d73683ac4b14855a3a31bcac6a31f2f\",\"block_type_title\":\"极简_商品流组件\",\"component_content_type\":\"0\",\"block_type\":\"3718\",\"skid\":\"SIfY5mBaWajmOgVtNuwDnSt\",\"block_content_layout\":\"一排二默认\",\"tab_title\":\"猜她喜欢\",\"acm\":\"1.rcmd.product_1550612403.ec_tstream.17990-17992-21725-17996-21722-18000-21234.cn_MIND_ONLINE-rid_45ad7d128a9bbc0c-pos_5-cid_214941-bn_实验103-qcid_214941-cstpl_\",\"component_type\":\"simpleProductFlow\",\"ipvx\":\"*************\",\"sk\":\"9JmwKurfQajMMtomm124rz47CrqMJ84mUR01fYBrFRPSgpwUz9OkFEHzsW2uoiUSAlas2o6LqbByTkSFddVuF5Vb3o1x\",\"button_title\":\"阿玛尼 丝绒哑光红管唇釉405 6.5ml +热门色号口红正装 素颜黄皮显白 复古轻奢珍珠小香风礼盒\",\"venue_id\":\"64cfb25e41ebe5c66c78f4aa\",\"current_page\":\"61\",\"component_content_position\":\"117\",\"venue_page_title\":\"得物\",\"venue_page_url\":\"https://cdn-fast.dewu.com/nezha-plus/detail/64cfb25e41ebe5c66c78f4aa?isAllowVideoAutoPlay=1&navControl=1&nezhaChannel=64bf69f6146ef80108077c22\",\"venue_title\":\"七夕送女生\",\"is_background_tracking\":0,\"component_uid\":\"wFG1DudA-41759de\",\"component_content_type_title\":\"商品\",\"event_name\":\"venue_component_content_click_61_3718\",\"position\":\"117\",\"venue_level\":\"promotion_level_subVenue\",\"current_page_title\":\"会场搭建页\",\"event_time\":\"1691802750383\"},\"venueId\":\"64cfb25e41ebe5c66c78f4aa\"}}";

        new VenueLevelInfoParser().eval(s);

        JSONObject jsonObject = JSON.parseObject(s);

        String level0Info = jsonObject.getString("level0Info");
        String level1Info = jsonObject.getString("level1Info");
        String level2Info = jsonObject.getString("level2Info");
        String level3Info = jsonObject.getString("level3Info");

        System.out.println(level3Info);
    }

    public static void main(String[] args) {

        test1();
    }

}
