package com.alibaba.blink.udx.rockmq.service;

import com.alibaba.blink.udx.rockmq.bean.*;
import com.alibaba.blink.udx.rockmq.consts.Consts;
import com.alibaba.blink.udx.rockmq.utils.BaseUtils;
import com.alibaba.blink.udx.rockmq.consts.Consts;
import com.alibaba.blink.udx.rockmq.utils.BaseUtils;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class RocketMqAttributeService {

    public static Logger LOG = LoggerFactory.getLogger(RocketMqAttributeService.class);

    public RocketMqAttributeService() {
    }

    AttributeServiceBackUp attributeService;

    String url;

    public static final Map<String, String> OsMap;

    static {
        OsMap = new HashMap<String, String>();
        OsMap.put(Consts.Android, "1");
        OsMap.put(Consts.Ios, "2");
    }


    public RocketMqAttributeService(String ip, int port) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).tagIp(ip).tagPort(port).
                build();
    }

    public RocketMqAttributeService(String ip, int port,int db,String password) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).clickDB(db).clickPassword(password)
                .tagIp(ip).tagPort(port).tagDB(db).tagPassword(password).
                        build();
    }

    public RocketMqAttributeService(String ip, int port,int db) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).clickDB(db).
                build();
    }

    public RocketMqAttributeService(String ip, int port,int db,String password,String url) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).clickDB(db).clickPassword(password)
                .tagIp(ip).tagPort(port).tagDB(db).tagPassword(password).
                        build();
        this.url=url;
    }

    public RocketMqAttributeService(String clickIp, int clickPort, String clickPassword, int clickDB,
                          String tagIp, int tagPort, String tagPassword, int tagDB,
                          String retainIp, int retainPort, String retainPassword, int retainDB
            ,String url) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(clickIp).clickPort(clickPort).clickPassword(clickPassword).clickDB(clickDB)
                .tagIp(tagIp).tagPort(tagPort).tagPassword(tagPassword).tagDB(tagDB)
                .retainIp(retainIp).retainPort(retainPort).retainPassword(retainPassword).retainDB(retainDB).
                        build();
        this.url=url;
    }


    public RocketMqAttributeService(String hbaseUserName,String hbasePassword,String hbaseZookeeperQuorum,
                                    String tagIp, int tagPort, String tagPassword, int tagDB,String url) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder()
                        .hbaseUsername(hbaseUserName).hbasePassword(hbasePassword).hbaseZookeeperQuorum(hbaseZookeeperQuorum)
                        .tagIp(tagIp).tagPort(tagPort).tagPassword(tagPassword).tagDB(tagDB).
                        build();
        this.url=url;
    }


    public AttributeServiceBackUp getAttributeService() {
        return attributeService;
    }

    public void setAttributeService(AttributeServiceBackUp attributeService) {
        this.attributeService = attributeService;
    }

    /**
     * 功能描述 http调风控接口获取riskLevel
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/16
     */
    public List<SinkMessage> riskActivate(Message message) throws Exception {
        Gson gson = new Gson();
        List<SinkMessage> sinkMessageList = new ArrayList<>();
        DelayMessage delayMsg = new DelayMessage();
        try {
            delayMsg = gson.fromJson(new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET), DelayMessage.class);
        } catch (Exception e) {
            //解析失败则调用http接口获取不到正确数据，则riskLevel = Consts.RiskNo组装发送到Kafka
            LOG.error( "[rocketMq][risk]Unmarshal {}",new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET),e);
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "riskActivate解析异常", "",-1L);
            sinkMessageList.add(temp);
            delayMsg.setActive(new QueryReq());
            delayMsg.setClick(new HashMap<String,String>());
        }

        QueryReq params = delayMsg.getActive();
        Map<String, String> deliveryClick = delayMsg.getClick();
        Map<String, String> retainClickFlag = delayMsg.getRetainClick();

        String log="【延迟调风控接口获取riskLevel再处理数据】"+gson.toJson(delayMsg);
        int riskLevel = getHttpRiskLevel(message, gson, params);

        // 回传媒体
        QueryReqBack logBean= BaseUtils.copyQueryReq(params);
        logBean.setType("风控延迟激活归因");

        SinkMessage temp = new SinkMessage(Consts.DEBUG, "风控延迟激活归因", gson.toJson(logBean));
        temp.setDeliverTime(82L);
        sinkMessageList.add(temp);
        //延迟2分钟后，点击数据可能被更新
        log=log+"【实时侧点击数据】"+gson.toJson(retainClickFlag);
        attributeService.callbackActivate(params, deliveryClick, riskLevel, sinkMessageList,log,logBean,retainClickFlag,0,0,"delay");
        return sinkMessageList;
    }

    public List<SinkMessage> riskShowActivate(Message message,String hbaseTableName) throws Exception {
        Gson gson = new Gson();
        List<SinkMessage> sinkMessageList = new ArrayList<>();
        ShowDelayMessage delayMsg = new ShowDelayMessage();
        try {
            delayMsg = gson.fromJson(new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET), ShowDelayMessage.class);
        } catch (Exception e) {
            LOG.error( "[rocketMq][risk]Unmarshal {}",new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET),e);
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "riskActivate解析异常", "",-1L);
            sinkMessageList.add(temp);
            delayMsg.setActive(new QueryReq());
            delayMsg.setShowResult(new SearchShowResult());
        }

        QueryReq params = delayMsg.getActive();
        String log="【延迟调风控接口获取riskLevel再处理数据】"+gson.toJson(delayMsg);
        int riskLevel = getHttpRiskLevel(message, gson, params);

        // 回传媒体
        QueryReqBack logBean=BaseUtils.copyQueryReq(params);
        logBean.setType("风控延迟激活归因");
        SinkMessage temp = new SinkMessage(Consts.DEBUG, "风控延迟激活归因", gson.toJson(logBean));
        temp.setDeliverTime(82L);
        sinkMessageList.add(temp);
        attributeService.callbackShowActivate(params, delayMsg.getShowResult(), riskLevel, sinkMessageList,log,logBean,hbaseTableName);
        return sinkMessageList;
    }

    private int getHttpRiskLevel(Message message, Gson gson, QueryReq params) {
        String genericDeviceId = "";
        String os = params.getOs();
        if(os!=null) {
            switch (os) {
                case Consts.Ios: {
                    // iOS查找顺序：idfa -> ipua
                    if (StringUtils.isNotBlank(params.getIdfa())) {
                        genericDeviceId = params.getIdfa();
                    } else {
                        genericDeviceId = params.getIpua();
                    }
                    break;
                }
                case Consts.Android: {
                    // Android查找顺序：oaid -> imei -> android_id
                    if (StringUtils.isNotBlank(params.getAndroidId())) {
                        genericDeviceId = params.getAndroidId();
                    }
                    if (StringUtils.isNotBlank(params.getImei())) {
                        genericDeviceId = params.getImei();
                    }
                    if (StringUtils.isNotBlank(params.getOaid())) {
                        genericDeviceId = params.getOaid();
                    }
                    break;
                }
                default:
                    break;
            }
        }

        // duUuid，安卓设备去android_id，ios设备优先取idfa，取不到取fcuuid,对应激活消息的uuid
        Map<String, String> riskParam = new HashMap<>();
        riskParam.put("seqNo", message.getKeys());
        riskParam.put("duUuid", params.getUuid());
        riskParam.put("genericDeviceId", genericDeviceId);
        riskParam.put("action", "E7_01");

        String deviceType = OsMap.get(params.getOs());
        if (deviceType != null) {
            riskParam.put("deviceType", deviceType);
        }

        int riskLevel = Consts.RiskNo;
        String body = HttpService.getRiskLevel(url,riskParam);
        if (StringUtils.isBlank(body)) {
//            logs.Error(err, "[active][risk] req :%+v resp: %+v", riskParam, resp)
        } else {
            HashMap map = gson.fromJson(body, HashMap.class);
            Object code = map.get("code");
            if(code instanceof Double)
            {
                double e1= (double) code;
                riskLevel=new Double(e1).intValue();
            }else if(code instanceof Integer)
            {
                riskLevel = (int) code;
            }
        }
        return riskLevel;
    }

    /**
     * 功能描述 http调QueryLtv接口获取ltv  值未进行
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/16
     */
    @Deprecated
    public List<SinkMessage> ltvQuery(Message message)  {
        return null;
    }

    /**
     *功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息,
     * rocketmq消息(tag MsgTagActiveRisk)
     * @date 2022/2/23
     * @param
     * @return
     */
    public List<SinkMessage> userAction(Message message) throws Exception {
        Gson gson = new Gson();
        List<SinkMessage> sinkMessageList = new ArrayList<>();
        UserActionMessage delayMsg = new UserActionMessage();
        try {
            delayMsg = gson.fromJson(new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET), UserActionMessage.class);
        } catch (Exception e) {
            LOG.error( "[rocketMq][userAction]Unmarshal {}",new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET),e);
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "userAction解析异常", "",-1L);
            temp.setMessageLog(new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET));
            sinkMessageList.add(temp);
            delayMsg.setRequest(new QueryReq());
        }

        String log=gson.toJson(delayMsg);

        QueryReq request = delayMsg.getRequest();
        String eventType = delayMsg.getEventType();
        switch (eventType) {
            case Consts.TypeRegister:
            case Consts.TypeOrder:
            case Consts.TypeStartOrder:
            {
                attributeService.callbackTodayAction(request, eventType, sinkMessageList,log);
                break;
            }
            case Consts.TypeRetain: {
                attributeService.callbackRetain(request, sinkMessageList);
                break;
            }
            case Consts.TypeActivate: {
                //-------debug
                attributeService.formatParams(delayMsg.getRequest());
                QueryReqBack logBean = BaseUtils.copyQueryReq(delayMsg.getRequest());
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "延迟消费数据记录", gson.toJson(logBean));
                temp.setDeliverTime(82L);
                sinkMessageList.add(temp);
                //-------debug
                attributeService.recall(request, sinkMessageList,0,1,log);
                break;
            }
            default:
                //do nothing
                break;
        }
        return sinkMessageList;
    }


    public List<SinkMessage> showUserAction(Message message,String hbaseTableName) throws Exception {
        Gson gson = new Gson();
        List<SinkMessage> sinkMessageList = new ArrayList<>();
        UserActionMessage delayMsg = new UserActionMessage();
        try {
            delayMsg = gson.fromJson(new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET), UserActionMessage.class);
        } catch (Exception e) {
            LOG.error( "[rocketMq][userAction]Unmarshal {}",new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET),e);
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "userAction解析异常", "",-1L);
            temp.setMessageLog(new String(message.getBody(),RemotingHelper.DEFAULT_CHARSET));
            sinkMessageList.add(temp);
            delayMsg.setRequest(new QueryReq());
        }

        String log=gson.toJson(delayMsg);

        QueryReq request = delayMsg.getRequest();
        String eventType = delayMsg.getEventType();
        switch (eventType) {
            case Consts.TypeActivate: {
                attributeService.formatParams(request);
                SearchShowResult showResult = attributeService.searchShowData(request,hbaseTableName);
                if(showResult.getNew())
                {
                    //新设备，不处理
                    return sinkMessageList;
                }
                attributeService.showRecall(showResult,request, sinkMessageList,log,hbaseTableName);
                break;
            }
            default:
                break;
        }
        return sinkMessageList;
    }


    public String active(QueryReq params, long sleep, int count, String log) throws Exception
    {
        return attributeService.active(params,0,1,log);
    }


    public String showActive(QueryReq params, String log,String hbaseTableName) throws Exception
    {
        return attributeService.showActive(params,0,1,log,hbaseTableName);
    }

}
