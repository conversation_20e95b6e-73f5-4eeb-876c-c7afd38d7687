package com.alibaba.blink.udx.udf;

import com.google.common.base.Strings;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashSet;
import java.util.Set;

/**
 * 高性能字符串列表并集去重计数UDF
 * 
 * 功能：计算两个以逗号分隔的字符串列表的并集去重后的元素数量
 * 
 * 性能优化：
 * 1. 使用HashSet实现O(n+m)时间复杂度
 * 2. 预先计算容量避免扩容
 * 3. 快速路径处理空值情况
 * 4. 避免不必要的字符串操作
 * 
 * 时间复杂度：O(n + m)，其中n和m分别是两个列表的元素数量
 * 空间复杂度：O(n + m)
 */
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class StringListUnionCountUdf extends ScalarFunction {

    /**
     * 计算两个字符串列表并集去重后的数量
     * 
     * @param list1 第一个列表，以逗号分隔的字符串，可以为空
     * @param list2 第二个列表，以逗号分隔的字符串，可以为空
     * @return 并集去重后的元素数量
     */
    public int eval(String list1, String list2) {
        // 快速路径：两个列表都为空
        if (Strings.isNullOrEmpty(list1) && Strings.isNullOrEmpty(list2)) {
            return 0;
        }
        
        // 快速路径：只有一个列表为空
        if (Strings.isNullOrEmpty(list1)) {
            return countUniqueElements(list2);
        }
        if (Strings.isNullOrEmpty(list2)) {
            return countUniqueElements(list1);
        }
        
        // 两个列表都不为空，计算并集
        return countUnionElements(list1, list2);
    }
    
    /**
     * 计算单个列表的去重元素数量
     * 
     * @param list 以逗号分隔的字符串
     * @return 去重后的元素数量
     */
    private int countUniqueElements(String list) {
        if (Strings.isNullOrEmpty(list)) {
            return 0;
        }
        
        // 估算初始容量，避免HashSet扩容
        int estimatedSize = estimateElementCount(list);
        Set<String> uniqueElements = new HashSet<>(estimatedSize);
        
        // 使用indexOf和substring避免split的数组创建开销
        int start = 0;
        int commaIndex;
        
        while ((commaIndex = list.indexOf(',', start)) != -1) {
            String element = list.substring(start, commaIndex).trim();
            if (!element.isEmpty()) {
                uniqueElements.add(element);
            }
            start = commaIndex + 1;
        }
        
        // 处理最后一个元素
        if (start < list.length()) {
            String element = list.substring(start).trim();
            if (!element.isEmpty()) {
                uniqueElements.add(element);
            }
        }
        
        return uniqueElements.size();
    }
    
    /**
     * 计算两个列表并集的去重元素数量
     * 终极优化版本：真正的O(M+N)时间复杂度，单次遍历
     *
     * @param list1 第一个列表
     * @param list2 第二个列表
     * @return 并集去重后的元素数量
     */
    private int countUnionElements(String list1, String list2) {
        // 估算总容量
        int estimatedSize = estimateElementCount(list1) + estimateElementCount(list2);
        Set<String> unionSet = new HashSet<>(estimatedSize);

        // 同时解析两个字符串，真正的单次遍历
        int start1 = 0, start2 = 0;
        int pos1 = 0, pos2 = 0;
        int len1 = list1.length(), len2 = list2.length();

        while (pos1 <= len1 || pos2 <= len2) {
            // 处理第一个列表
            if (pos1 <= len1) {
                if (pos1 == len1 || list1.charAt(pos1) == ',') {
                    // 到达分隔符或字符串末尾
                    if (pos1 > start1) {
                        String element = list1.substring(start1, pos1).trim();
                        if (!element.isEmpty()) {
                            unionSet.add(element);
                        }
                    }
                    start1 = pos1 + 1;
                }
                if (pos1 < len1) pos1++;
                else pos1++; // 确保pos1能超过len1退出循环
            }

            // 处理第二个列表
            if (pos2 <= len2) {
                if (pos2 == len2 || list2.charAt(pos2) == ',') {
                    // 到达分隔符或字符串末尾
                    if (pos2 > start2) {
                        String element = list2.substring(start2, pos2).trim();
                        if (!element.isEmpty()) {
                            unionSet.add(element);
                        }
                    }
                    start2 = pos2 + 1;
                }
                if (pos2 < len2) pos2++;
                else pos2++; // 确保pos2能超过len2退出循环
            }
        }

        return unionSet.size();
    }
    
    /**
     * 将字符串列表中的元素添加到Set中
     * 
     * @param list 以逗号分隔的字符串
     * @param set 目标Set
     */
    private void addElementsToSet(String list, Set<String> set) {
        if (Strings.isNullOrEmpty(list)) {
            return;
        }
        
        int start = 0;
        int commaIndex;
        
        while ((commaIndex = list.indexOf(',', start)) != -1) {
            String element = list.substring(start, commaIndex).trim();
            if (!element.isEmpty()) {
                set.add(element);
            }
            start = commaIndex + 1;
        }
        
        // 处理最后一个元素
        if (start < list.length()) {
            String element = list.substring(start).trim();
            if (!element.isEmpty()) {
                set.add(element);
            }
        }
    }
    
    /**
     * 估算字符串中元素的数量，用于优化HashSet初始容量
     * 
     * @param list 以逗号分隔的字符串
     * @return 估算的元素数量
     */
    private int estimateElementCount(String list) {
        if (Strings.isNullOrEmpty(list)) {
            return 0;
        }
        
        // 计算逗号数量 + 1，作为元素数量的估算
        int commaCount = 0;
        for (int i = 0; i < list.length(); i++) {
            if (list.charAt(i) == ',') {
                commaCount++;
            }
        }
        
        // 返回估算值，并设置合理的负载因子
        int estimatedCount = commaCount + 1;
        return (int) (estimatedCount / 0.75) + 1;
    }
}
