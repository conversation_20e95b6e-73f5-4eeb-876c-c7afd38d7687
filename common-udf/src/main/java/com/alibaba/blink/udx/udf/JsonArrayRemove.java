package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 根据指定的key 删除json数组中指定元素
 *
 * */
public class JsonArrayRemove extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    /**
     *
     * jsonArray,keyName,keyValues ...
     * @param args
     * @return
     */
    public String eval(Object... args) {

        if (Objects.isNull(args) || args.length==0 || Objects.isNull(args[0])){
            return null;
        }
        JSONArray originJson;

        if (args[0] instanceof JSONArray)
        {
            originJson = (JSONArray) args[0];
        } else {
            originJson = JSON.parseArray((String)args[0]);
        }

        if (args.length > 2 ) {
            return remove(originJson, args);
        } else {
            return JSONArray.toJSONString(originJson,SerializerFeature.WriteMapNullValue);
        }
    }

    private String remove(JSONArray originJson, Object[] args) {
        String keyName = (String) args[1];

        for (int i = originJson.size() - 1; i >= 0; i--) {
            JSONObject jsonObject;
            Object o = originJson.get(i);

            if ( originJson.get(i) instanceof JSONObject ){
                jsonObject = (JSONObject) o;
            }else
                jsonObject=JSON.parseObject((String) o);

            if (jsonObject.containsKey(keyName)){
                String keyValue = jsonObject.getString(keyName);
                for (int j = 2; j < args.length; j++) {
                    if ((ofString(args[j])).equals(keyValue)) {
                        originJson.remove(i);
                        break;
                    }
                }
            }
        }
        return JSONArray.toJSONString(originJson,SerializerFeature.WriteMapNullValue);
    }

    private String ofString(Object o){
        if (o instanceof String){
            return (String) o;
        }else
            return String.valueOf(o);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

    public static void main(String[] args) {
        JsonArrayRemove jsonArrayRemove = new JsonArrayRemove();
        System.out.println(jsonArrayRemove.eval("[{\"expenseType\":1,\"originalExpense\":2540,\"originalPercent\":500,\"extendJson\":\"{\\\"activityInfo\\\":{\\\"activityDimension\\\":7,\\\"activityId\\\":7292,\\\"activityName\\\":\\\"9月护肤降价0%\\\",\\\"activityType\\\":1,\\\"channelId\\\":15,\\\"endTime\\\":1664553599,\\\"poundagePercent\\\":0,\\\"startTime\\\":1661961600,\\\"typeId\\\":1},\\\"currentPercent\\\":0,\\\"currentexpense\\\":0,\\\"expenseLimit\\\":{\\\"max\\\":9223372036854775807,\\\"min\\\":0},\\\"originalTechnicalFeeLimit\\\":{\\\"expenseLimit\\\":{\\\"max\\\":9223372036854775807,\\\"min\\\":0},\\\"originalExpense\\\":2540,\\\"originalPercent\\\":500,\\\"speical\\\":{},\\\"standard\\\":{}},\\\"salaInfoInnerVo\\\":{\\\"salaPercent\\\":5650,\\\"salaType\\\":2}}\",\"currentPercent\":0,\"activityPercent\":0,\"expenseName\":\"技术服务费\",\"currentExpense\":0},{\"expenseType\":3,\"extendJson\":\"{\\\"currentExpense\\\":899,\\\"currentPercent\\\":100}\",\"currentPercent\":100,\"expenseName\":\"转账手续费\",\"currentExpense\":899},{\"expenseType\":13,\"originalExpense\":2000,\"extendJson\":\"{\\\"currentExpense\\\":2000,\\\"originalExpense\\\":2000,\\\"packingMaterialsFee\\\":{\\\"fee\\\":500,\\\"id\\\":1,\\\"matchWhiteMerchantIds\\\":false},\\\"special\\\":{},\\\"standard\\\":{}}\",\"expenseName\":\"操作服务费\",\"currentExpense\":2000},{\"expenseType\":8,\"originalExpense\":0,\"expenseName\":\"跨境税费\",\"currentExpense\":0},{\"expenseType\":7,\"originalExpense\":87001,\"expenseName\":\"预计收入\",\"currentExpense\":87001},{\"expenseType\":6,\"originalExpense\":2899,\"expenseName\":\"总费用\",\"currentExpense\":2899},{\"expenseType\":8,\"originalExpense\":8381,\"expenseName\":\"跨境税费\",\"currentExpense\":8381}]",
                "expenseType", "13", 8));
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() < 2) {
                        throw new ValidationException(String.format(
                                "function %s argument should be (STRING json, STRING jsonPath ...)",
                                functionName));
                    }

                    //校验jsonpath 字段类型为STRING
                    List<DataType> jsonPathValues = argumentDataTypes.subList(
                            1,
                            argumentDataTypes.size());
                    for (DataType dataType : jsonPathValues) {
                        if (!dataType
                                .getLogicalType()
                                .getTypeRoot()
                                .getFamilies()
                                .contains(
                                        LogicalTypeFamily.CHARACTER_STRING)) {
                            throw new ValidationException(String.format(
                                    "function %s argument should be (STRING json, STRING jsonPath ...)",
                                    functionName));
                        }
                    }

                    return Optional.of(DataTypes.STRING());
                })
                .build();
    }
}
