package com.alibaba.blink.udx.udf;

import com.alibaba.blink.udx.dimjoin.GetDuTradeCommodityDim;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Objects;
import java.util.stream.Collectors;

public class GetCatesBySpuIds extends ScalarFunction {


    private static Logger LOG = LoggerFactory.getLogger(GetDuTradeCommodityDim.class);
    //<tableName,<pk,value>>
    private HashMap<String, HashMap<Long, String>> cacheAll;
    private HashMap<String, Long> lastCacheAllTime;
    private String driverClassName;
    private String url;
    private String username;
    private String password;

    private String delimiter;
    private long tableTtl;

    /**
     * 数据库连接参数
     * @param context
     * @throws SQLException
     */
    @Override
    public void open(FunctionContext context) throws SQLException {

        driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        url = context.getJobParameter("jdbc.url", "******************************************************************************************************************************************************************************************************************************************************************************");
        username = context.getJobParameter("jdbc.username", "dw_algo");
        password = context.getJobParameter("jdbc.password", "w1iRMRGeBdEUCn8e");
        cacheAll = new HashMap<>(32);
        lastCacheAllTime = new HashMap<>(32);

        delimiter = context.getJobParameter("values.delimiter", ",");
        String table_ttl = context.getJobParameter("table.ttl", "3600000");
        tableTtl = Long.parseLong(table_ttl);

        LOG.info("GetCatesBySpuIds opened : driverClassName : {},url : {},username : {},password : {},delimiter : {},tableTtl : {}",driverClassName,url,username,password,delimiter,tableTtl);

    }


    /**
     *
     * @param tableName
     * @param pkName
     * @param valueName
     * @param pkValues
     * @return
     * @throws SQLException
     */
    public String eval(String tableName, String pkName, String valueName, String pkValues ) throws SQLException {
        if (!cacheAll.containsKey(tableName) || lastCacheAllTime.getOrDefault(tableName, 0L) == 0 || (System.currentTimeMillis() - lastCacheAllTime.getOrDefault(tableName, 0L) > tableTtl)) {//默认 cache 1小时
            cacheAll(tableName, pkName, valueName);
        }

        HashMap<Long, String> tableCache = cacheAll.get(tableName);

        String res = null;
        if (null != pkValues) {
            if (  pkValues.contains(delimiter)){

                res = Arrays.stream(pkValues.split(delimiter)).map(pkValue -> getValue(tableCache, pkValue))
                        .filter(Objects::nonNull).distinct().collect(Collectors.joining(delimiter));

            } else  {

                res =  getValue(tableCache, pkValues);
            }
        }

        return Objects.isNull(res) ? "" : res;
    }

    private String getValue(HashMap<Long, String> tableCache, String pkValue) {
        String res = null;

        try {
            res = tableCache.get(Long.parseLong(pkValue));
        } catch (NumberFormatException e) {
            LOG.warn("pkValue is parseLong Exception");
        }
        return res;
    }

    @Override
    public void close() {
    }

    /**
     * 获取数据库连接，缓存查数据库后的数据
     * @param tableName
     * @param pkName
     * @param valueName
     * @throws SQLException
     */
    private void cacheAll(String tableName, String pkName, String valueName) throws SQLException {
        HashMap<Long, String> tableCache = initTableCache(tableName);
        Connection conn = getConnection(driverClassName,url,username,password);
        String sql = "select " + pkName + "," + valueName + " from " + tableName;
        PreparedStatement pstmt = conn.prepareStatement(sql);
        ResultSet resultSet = pstmt.executeQuery();
        while (resultSet.next()) {//只能存在唯一关系
            long pk = resultSet.getLong(pkName);
            String value = resultSet.getString(valueName);
            tableCache.put(pk, value);
        }
        lastCacheAllTime.put(tableName, System.currentTimeMillis());
        if (conn != null){
            conn.close();
        }
    }


    /**
     * 初始化表
     * @param tableName
     * @return
     */
    private HashMap<Long, String> initTableCache(String tableName) {
        if (cacheAll.containsKey(tableName)) {
            HashMap<Long, String> tableCache = cacheAll.get(tableName);
            tableCache.clear();
            return tableCache;
        } else {
            HashMap<Long, String> tableCache = new HashMap<>(2048);
            cacheAll.put(tableName, tableCache);
            return tableCache;
        }
    }



    /**
     * 数据库连接
     * @param driverClassName
     * @param url
     * @param username
     * @param password
     * @return
     * @throws SQLException
     */
    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        //数据库连接
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        Connection conn = DriverManager.getConnection(url, username,password);
        return conn;
    }
}
