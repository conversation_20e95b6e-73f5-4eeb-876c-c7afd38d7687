package com.alibaba.blink.udx.rockmq.utils;



import com.alibaba.blink.udx.rockmq.bean.SearchShowResult;
import com.alibaba.blink.udx.rockmq.consts.Consts;
import com.alibaba.blink.udx.rockmq.consts.RedisConsts;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;


public class HbaseUtil implements Serializable {

    private static Logger logger = LoggerFactory.getLogger(HbaseUtil.class);

    private Configuration configuration;

    private Connection connection;

    private String nameSpaceTableName;
    public static final String DATA_CELL_NAME="data";

    public HbaseUtil(String username, String password, String zookeeperQuorum) throws IOException {
        this.configuration = HBaseConfiguration.create();
        this.configuration.set("hbase.zookeeper.quorum", zookeeperQuorum);
        if (StringUtils.isNotBlank(username)) {
            this.configuration.set("hbase.client.username", username);
        }
        if (StringUtils.isNotBlank(password)) {
            this.configuration.set("hbase.client.password", password);
        }

        configuration.set("hbase.client.ipc.pool.size","10");
        connection = ConnectionFactory.createConnection(configuration);
    }


    public Connection getConnection() {
        return connection;
    }

    public Result singleGet(Get get) throws IOException {
        return this.singleGet(nameSpaceTableName, get);
    }

    /**
     * 获取某个rowKey的所有列簇所有列值
     *
     * @param tableName hbase表名
     * @param get       只指定了rowKey的get
     * @return 返回result
     */
    public Result singleGet(String tableName, Get get) throws IOException {
        Result result = null;
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            result = table.get(get);
        } catch (IOException e) {
            logger.error("singleGet rowKey:{} get failed", new String(get.getRow()), e);
            connection.close();
            throw e;
        }
        return result;
    }


    public Result[] batchGet(List<Get> gets) throws IOException {
        return this.batchGet(nameSpaceTableName, gets);
    }

    /**
     * 批量获取
     *
     * @param tableName 表名
     * @param gets      get列表
     * @return
     */
    public Result[] batchGet(String tableName, List<Get> gets) throws IOException {
        Result[] results = null;
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            results = table.get(gets);
        } catch (IOException e) {
            logger.error("batchGets get failed", e);
            connection.close();
            throw e;
        }
        return results;
    }


    public void putData(Put put) throws IOException {
        this.putData(nameSpaceTableName, put);
    }


    /**
     * 向hbase表插入数据
     *
     * @param tableName hbase表名
     * @param put       要插入的put，需指定列簇和列
     */
    public void putData(String tableName, Put put) throws IOException {
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            table.put(put);
            table.close();
        } catch (IOException e) {
            logger.error("rowKey:{} put failed", new String(put.getRow()), e);
            connection.close();
            throw e;
        }
    }


    public void putBatchData(List<Put> puts) throws IOException {
        this.putBatchData(nameSpaceTableName, puts);
    }

    /**
     * 向hbase表批量插入数据
     *
     * @param tableName hbase表名
     * @param puts      要插入的puts，需指定列簇和列
     */
    public void putBatchData(String tableName, List<Put> puts) throws IOException {
        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            table.put(puts);
        } catch (IOException e) {
            logger.error("put batch data failed", e);
            throw e;
        }
    }


    public SearchShowResult getShowData(List<String> keys, String hbaseTableName) throws Exception {
        SearchShowResult searchShowResult = new SearchShowResult();
        try {
            for (String k : keys) {
                if (StringUtils.isBlank(k)) {
                    continue;
                }
                Get get = new Get((RedisConsts.HbaseShow+k).getBytes());
//                Result result = singleGet("realtime_dim:realtime_delivery_hard_show_data",get);
                Result result = singleGet(hbaseTableName,get);
                List<Cell> cells = result.listCells();
                if(cells==null) {
                    continue;
                }
                String data = null;
                for (int i = 0; i < cells.size(); i++) {
                    Cell cell = cells.get(i);
                    String cellName = Bytes.toString(CellUtil.cloneQualifier(cell));
                    if (cellName.equals(DATA_CELL_NAME)) {
                        data = Bytes.toString(CellUtil.cloneValue(cell));
                        break;
                    }
                }
                if (data != null) {
                    searchShowResult = new SearchShowResult(data,k);
                    return searchShowResult;
                }
            }
            return searchShowResult;
        } catch (Exception e) {
            throw e;
        } finally {

        }
    }


    public void setShowUsed(String key, Map<String, String> data,String hbaseTableName) throws IOException {
        try {
            Put put = new Put(Bytes.toBytes(key));
            data.put(Consts.Used,Consts.USE_FLAG);
            put.addColumn(Bytes.toBytes("f1"), Bytes.toBytes(DATA_CELL_NAME), Bytes.toBytes(new Gson().toJson(data)));
//            put.addColumn(Bytes.toBytes("f1"), Bytes.toBytes(Consts.Used), Bytes.toBytes(Consts.USE_FLAG));
//            putData("realtime_dim:realtime_delivery_hard_show_data", put);
            putData(hbaseTableName, put);
        } catch (Exception e) {
            throw e;
        } finally {
        }
    }


    public void setActivateData(String[] keys, Map<String, String> data,String hbaseTableName) throws IOException {
        String recall = data.get(Consts.Recall);
        String prefix = RedisConsts.getShowRetainKey(recall);
        Gson gson = new Gson();
        for (String k : keys) {
            if (StringUtils.isNotBlank(k)) {
                try {
                    Put put = new Put(Bytes.toBytes(prefix + k));
                    put.addColumn(Bytes.toBytes("f1"), Bytes.toBytes(DATA_CELL_NAME), Bytes.toBytes(gson.toJson(data)));
//                    put.addColumn(Bytes.toBytes("f1"), Bytes.toBytes(Consts.Used), Bytes.toBytes(Consts.NO_USE_FLAG));
//                    putData("realtime_dim:realtime_delivery_hard_show_data", put);
                    putData(hbaseTableName, put);
                } catch (Exception e) {
                    throw e;
                }
            }
        }
    }

    public SearchShowResult getActivateData(String[] keys, String recall,String hbaseTableName) throws IOException {
        SearchShowResult searchShowResult = new SearchShowResult();
        String prefix = RedisConsts.getShowRetainKey(recall);
        for (int i = 0; i < keys.length; i++) {
            String k = keys[i];
            if (StringUtils.isBlank(k)) {
                continue;
            }

            try {
                Get get = new Get((prefix + k).getBytes());
//                Result result = singleGet("realtime_dim:realtime_delivery_hard_show_data", get);
                Result result = singleGet(hbaseTableName, get);
                List<Cell> cells = result.listCells();
                if(cells==null) {
                    continue;
                }
                String data = null;
                for (int j = 0; j < cells.size(); j++) {
                    Cell cell = cells.get(j);
                    String cellName = Bytes.toString(CellUtil.cloneQualifier(cell));
                    if (cellName.equals(DATA_CELL_NAME)) {
                        data = Bytes.toString(CellUtil.cloneValue(cell));
                        break;
                    }
                }
                if (data != null) {
                    searchShowResult = new SearchShowResult(data);
                }
                return searchShowResult;
            } catch (Exception e) {
                throw e;
            } finally {
            }
        }
        return searchShowResult;
    }

}
