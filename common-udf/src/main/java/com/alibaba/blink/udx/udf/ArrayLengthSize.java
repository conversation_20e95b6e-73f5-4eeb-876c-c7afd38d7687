package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/4/16 16:22
 */
@FunctionHint(
        output = @DataTypeHint("INT")
)
public class ArrayLengthSize extends ScalarFunction {
    public Integer eval(@DataTypeHint("ARRAY<STRING>") String[] ay) {

        if (ay == null) {
            return 0;
        }
        return ay.length;
    }
}
