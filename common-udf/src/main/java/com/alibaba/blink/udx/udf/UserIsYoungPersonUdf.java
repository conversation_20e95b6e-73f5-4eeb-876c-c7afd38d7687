package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 青少年模式判定UDF
 * 判定逻辑：
 * 1. 设备维度判定：item_key 是 sys_young_person_ 开头，item_value 中的值结构为 ["uuid1","uuid2"]，
 *    如果在某个 item_value 中包含当前用户的设备id，判定为：开启青少年模式
 * 2. App设置维度判定：item_key 是 young_person_password，item_value 中存储的是用户在得物开启青少年模式的秘钥，
 *    如果 item_value 中有值且不是空字符串的，判定为：开启青少年模式
 * 3. 其他情况判定为：未开启青少年模式
 */
@Slf4j
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class UserIsYoungPersonUdf extends ScalarFunction {

    Logger log = LoggerFactory.getLogger(UserIsYoungPersonUdf.class);
    /**
     * 青少年模式判定
     * @param itemKey 配置项的key
     * @param itemValue 配置项的value
     * @param uuidList 当前用户的设备id列表，多个用逗号分隔
     * @return 1-开启青少年模式，0-未开启青少年模式
     */
    public int eval(String itemKey, String itemValue, String uuidList) {
        try {
            // 参数校验
            if (Strings.isNullOrEmpty(itemKey) || Strings.isNullOrEmpty(itemValue)) {
                return 0;
            }

            // 第一步：设备维度判定
            if (itemKey.startsWith("sys_young_person_")) {
                return checkDeviceDimension(itemValue, uuidList);
            }

            // 第二步：App设置维度判定
            if ("young_person_password".equals(itemKey)) {
                return checkAppSettingDimension(itemValue);
            }

            // 其他情况判定为未开启青少年模式
            return 0;

        } catch (Exception e) {
            log.error("UserIsYoungPersonUdf执行异常，参数为 [{}, {}, {}]", itemKey, itemValue, uuidList, e);
            return 0;
        }
    }

    /**
     * 设备维度判定
     * @param itemValue JSON数组格式的设备ID列表，如：["uuid1","uuid2"]
     * @param uuidList 当前用户的设备id列表，多个用逗号分隔
     * @return 1-开启青少年模式，0-未开启青少年模式
     */
    private int checkDeviceDimension(String itemValue, String uuidList) {
        try {
            // 快速判断：如果itemValue为空数组，直接返回0
            if ("[]".equals(itemValue)) {
                return 0;
            }

            // 如果uuidList为空，直接返回0
            if (Strings.isNullOrEmpty(uuidList)) {
                return 0;
            }

            // 解析itemValue中的设备ID列表
            List<String> deviceIds = JSON.parseArray(itemValue, String.class);
            if (deviceIds == null || deviceIds.isEmpty()) {
                return 0;
            }

            // 将用户设备ID列表按逗号分割
            String[] userDeviceIds = uuidList.split(",");

            // 检查是否有交集
            for (String userDeviceId : userDeviceIds) {
                if (!Strings.isNullOrEmpty(userDeviceId.trim()) && deviceIds.contains(userDeviceId.trim())) {
                    return 1; // 找到匹配的设备ID，开启青少年模式
                }
            }

            return 0;
        } catch (Exception e) {
            log.error("设备维度判定异常，itemValue: {}, uuidList: {}", itemValue, uuidList, e);
            return 0;
        }
    }

    /**
     * App设置维度判定
     * @param itemValue 青少年模式密码
     * @return 1-开启青少年模式，0-未开启青少年模式
     */
    private int checkAppSettingDimension(String itemValue) {
        // 如果itemValue有值且不是空字符串，判定为开启青少年模式
        return (!Strings.isNullOrEmpty(itemValue) && !itemValue.trim().isEmpty()) ? 1 : 0;
    }
}
