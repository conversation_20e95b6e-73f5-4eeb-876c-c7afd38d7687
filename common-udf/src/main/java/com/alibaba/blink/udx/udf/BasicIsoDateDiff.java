package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/8/9 21:34
 */
public class BasicIsoDateDiff extends ScalarFunction {

    public long eval(String date01, String date02) {
        return getDaysDifferenceBetween(date01, date02);
    }

    public long dateDiff(LocalDate strDate, LocalDate nowDate) {
        return ChronoUnit.DAYS.between(strDate, nowDate);
    }

    public long getDaysDifferenceBetween(String dateStr01, String dateStr02) {
        LocalDate localDate01 = LocalDate.parse(dateStr01, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate localDate02 = LocalDate.parse(dateStr02, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
        return dateDiff(localDate01, localDate02);
    }

}
