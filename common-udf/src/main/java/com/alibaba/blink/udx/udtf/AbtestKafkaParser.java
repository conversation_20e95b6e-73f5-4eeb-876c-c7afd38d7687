package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 解析ab老日志.
 */
@FunctionHint(output = @DataTypeHint("ROW<f0 STRING,f1 STRING,f2 STRING,f3 BIGINT,f4 INT,f5 STRING,f6 INT,f7 STRING,f8 INT,f9 INT,f10 STRING,f11 INT,f12 FLOAT,f13 STRING,f14 BOOLEAN>"))
public class AbtestKafkaParser extends TableFunction<Row> {

    public void eval(byte[] message) {

        try {

            //将kafka消息转化为字符串格式
            String msg = new String(message, StandardCharsets.UTF_8);


            try {
                //使用fastjson解析JSON数组
                List<KafkaEs> kafkaEs = JSON.parseArray(msg, KafkaEs.class);
                //临时去重用的
                ArrayList<KafkaEs> distinctKafkaEsList = kafkaEs.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(f -> f.getActivity().getName()))), ArrayList::new));
                for (KafkaEs ke : distinctKafkaEsList) {
                    //获取需要的字段
                    String deviceUUID = ke.getRequest().getDevice_uuid();
                    String userId = ke.getRequest().getUserId();
                    String requestParams = ke.getRequest().getParams();
                    Boolean useUserId = ke.getRequest().getUseUserId();
                    Long requestTime = ke.getTime();
                    Integer activityId = ke.getActivity().getId();
                    String activityName = ke.getActivity().getName();
                    Integer activityVersion = ke.getActivity().getVersion();
                    String activitySalt = ke.getActivity().getSalt();
                    Integer activityLayerId = ke.getActivity().getLayerId();
                    Integer groupId = ke.getGroups().getId();
                    String groupName = ke.getGroups().getName();
                    Integer groupDistributionType = ke.getGroups().getDistributionType();
                    Float groupPercentage = ke.getGroups().getPercentage();
                    String groupResultSdk = ke.getGroups().getResultsdk();

                    //创建Row，参数个数为14
                    Row row = new Row(15);
                    row.setField(0, deviceUUID);
                    row.setField(1, userId);
                    row.setField(2, requestParams);
                    row.setField(3, requestTime);
                    row.setField(4, activityId);
                    row.setField(5, activityName);
                    row.setField(6, activityVersion);
                    row.setField(7, activitySalt);
                    row.setField(8, activityLayerId);
                    row.setField(9, groupId);
                    row.setField(10, groupName);
                    row.setField(11, groupDistributionType);
                    row.setField(12, groupPercentage);
                    row.setField(13, groupResultSdk);
                    row.setField(14,useUserId);

                    //输出一行
                    collect(row);
                }

            } catch (ClassCastException e) {
                System.out.println("Input data format error. Input data " + msg + "is not json string");
            }

        }catch (Exception e){
         e.printStackTrace();
        }

    }

    /**
     * abtest kafka消息格式
     */
    public static class KafkaEs {

        /**
         * request : {"params":"","userId":"72317771","device_uuid":"3da6a55f88b31e00"}
         * activity : {"salt":"f82a","layerId":1,"layersort":1,"name":"user_test","id":13,"version":1,"status":2}
         * groups : {"activityId":13,"distributionType":2,"percentage":40,"name":"实验组1","id":11,"jsonResult":{"myparam1":"10"}}
         * time : 1575342839650
         */

        private RequestBean request;
        private ActivityBean activity;
        private GroupsBean groups;
        private Long time;

        public RequestBean getRequest() {
            return request;
        }

        public void setRequest(RequestBean request) {
            this.request = request;
        }

        public ActivityBean getActivity() {
            return activity;
        }

        public void setActivity(ActivityBean activity) {
            this.activity = activity;
        }

        public GroupsBean getGroups() {
            return groups;
        }

        public void setGroups(GroupsBean groups) {
            this.groups = groups;
        }

        public Long getTime() {
            return time;
        }

        public void setTime(Long time) {
            this.time = time;
        }

        public static class RequestBean {
            /**
             * params :
             * userId : 72317771
             * device_uuid : 3da6a55f88b31e00
             */

            private String params;
            private String userId;
            private String device_uuid;
            private Boolean useUserId;

            public Boolean getUseUserId() {
                return useUserId;
            }

            public void setUseUserId(Boolean useUserId) {
                this.useUserId = useUserId;
            }

            public String getParams() {
                return params;
            }

            public void setParams(String params) {
                this.params = params;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public String getDevice_uuid() {
                return device_uuid;
            }

            public void setDevice_uuid(String device_uuid) {
                this.device_uuid = device_uuid;
            }

            @Override
            public String toString() {
                return "RequestBean{" +
                        "params='" + params + '\'' +
                        ", userId='" + userId + '\'' +
                        ", device_uuid='" + device_uuid + '\'' +
                        ", useUserId=" + useUserId +
                        '}';
            }
        }

        public static class ActivityBean {
            /**
             * salt : f82a
             * layerId : 1
             * layersort : 1
             * name : user_test
             * id : 13
             * version : 1
             * status : 2
             */

            private String salt;
            private Integer layerId;
            private Integer layersort;
            private String name;
            private Integer id;
            private Integer version;
            private Integer status;

            public String getSalt() {
                return salt;
            }

            public void setSalt(String salt) {
                this.salt = salt;
            }

            public Integer getLayerId() {
                return layerId;
            }

            public void setLayerId(Integer layerId) {
                this.layerId = layerId;
            }

            public Integer getLayersort() {
                return layersort;
            }

            public void setLayersort(Integer layersort) {
                this.layersort = layersort;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public Integer getId() {
                return id;
            }

            public void setId(Integer id) {
                this.id = id;
            }

            public Integer getVersion() {
                return version;
            }

            public void setVersion(Integer version) {
                this.version = version;
            }

            public Integer getStatus() {
                return status;
            }

            public void setStatus(Integer status) {
                this.status = status;
            }

            @Override
            public String toString() {
                return "ActivityBean{" +
                        "salt='" + salt + '\'' +
                        ", layerId=" + layerId +
                        ", layersort=" + layersort +
                        ", name='" + name + '\'' +
                        ", id=" + id +
                        ", version=" + version +
                        ", status=" + status +
                        '}';
            }
        }

        public static class GroupsBean {
            /**
             * activityId : 13
             * distributionType : 2
             * percentage : 40
             * name : 实验组1
             * id : 11
             * jsonResult : {"myparam1":"10"}
             */

            private Integer activityId;
            private Integer distributionType;
            private Float percentage;
            private String name;
            private Integer id;
            private String resultsdk;

            public void setResultsdk(String resultsdk) {
                this.resultsdk = resultsdk;
            }

            public String getResultsdk() {
                return resultsdk;
            }

            public Integer getActivityId() {
                return activityId;
            }

            public void setActivityId(Integer activityId) {
                this.activityId = activityId;
            }

            public Integer getDistributionType() {
                return distributionType;
            }

            public void setDistributionType(Integer distributionType) {
                this.distributionType = distributionType;
            }

            public Float getPercentage() {
                return percentage;
            }

            public void setPercentage(Float percentage) {
                this.percentage = percentage;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public Integer getId() {
                return id;
            }

            public void setId(Integer id) {
                this.id = id;
            }


            @Override
            public String toString() {
                return "GroupsBean{" +
                        "activityId=" + activityId +
                        ", distributionType=" + distributionType +
                        ", percentage=" + percentage +
                        ", name='" + name + '\'' +
                        ", id=" + id +
                        ", resultsdk='" + resultsdk + '\'' +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "KafkaEs{" +
                    "request=" + request +
                    ", activity=" + activity +
                    ", groups=" + groups +
                    ", time=" + time +
                    '}';
        }
    }

}
