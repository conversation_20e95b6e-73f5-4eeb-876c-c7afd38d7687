package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.Random;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class SampleData100 extends TableFunction<Row> {
    public void eval(String str){
        Row row = new Row(1);
        row.setField(0, str);
        try {
            JSONObject jsonObject = JSON.parseObject(str);
            String ts = (String) jsonObject.getOrDefault("timestamp",System.currentTimeMillis()+"");
            if(Long.parseLong(ts) % 100 == 1){
                collect(row);
            }
        }catch (Exception e){
            collect(row);
        }

    }

    public static void main(String[] args) {
        Random r = new Random();
        for (int i = 0; i< 100; i++) {
            System.err.println(r.nextInt(3));
        }
    }
}
