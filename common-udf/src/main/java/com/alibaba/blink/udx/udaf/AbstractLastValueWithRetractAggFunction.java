package com.alibaba.blink.udx.udaf;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.binary.BinaryStringData;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.table.types.utils.DataTypeUtils;

import java.util.*;

/**
 * @description: /支持mini batch 的 LastValue
 * accumulate( value) 的撤回是相对随机的
 * 建议使用 accumulate( value ，order )
 * @Author: lmh
 * @date: 2022/12/2 14:45
 */
@Slf4j
public abstract class AbstractLastValueWithRetractAggFunction extends AggregateFunction<Object, AbstractLastValueWithRetractAggFunction.LastValueWithRetractAccumulator> {
    private final boolean isNeedFilterNull;
    public boolean isTwoPhase;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        isTwoPhase = context.getJobParameter("table.exec.mini-batch.enabled", "").equalsIgnoreCase("TRUE");
        log.info("isTwoPhase:{}", isTwoPhase);
    }

    public AbstractLastValueWithRetractAggFunction(boolean isNeedFilterNull) {
        this.isNeedFilterNull = isNeedFilterNull;
    }

    @Override
    public Object getValue(LastValueWithRetractAccumulator accumulator) {
        return accumulator.lastValue;

    }

    @Override
    public LastValueWithRetractAccumulator createAccumulator() {
        return new LastValueWithRetractAccumulator();
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {

                    List<DataType> dataTypes = callContext.getArgumentDataTypes();

                    DataType argDataType;

                    if (dataTypes.get(0)
                            .getLogicalType()
                            .getTypeRoot()
                            .getFamilies()
                            .contains(LogicalTypeFamily.CHARACTER_STRING)) {
                        argDataType = DataTypes.STRING();
                    } else
                        argDataType = DataTypeUtils.toInternalDataType(dataTypes.get(0));

                    return Optional.of(argDataType);
                })
                .accumulatorTypeStrategy(callContext -> {

                    List<DataType> dataTypes = callContext.getArgumentDataTypes();

                    DataType argDataType;
                    if (dataTypes.get(0)
                            .getLogicalType()
                            .getTypeRoot()
                            .getFamilies()
                            .contains(LogicalTypeFamily.CHARACTER_STRING)) {
                        argDataType = DataTypes.STRING();
                    } else
                        argDataType = DataTypeUtils.toInternalDataType(dataTypes.get(0));


                    DataType accDataType = DataTypes.STRUCTURED(
                            LastValueWithRetractAccumulator.class,
                            DataTypes.FIELD("lastValue", argDataType.nullable()),
                            DataTypes.FIELD("lastOrder", DataTypes.BIGINT()),
                            DataTypes.FIELD("retractList", DataTypes.ARRAY(
                                    DataTypes.STRUCTURED(
                                            Tuple2.class,
                                            DataTypes.FIELD("f0", argDataType.nullable()),
                                            DataTypes.FIELD("f1", DataTypes.BIGINT())
                                    )).bridgedTo(List.class)),
                            DataTypes.FIELD(
                                    "valueToOrderMap",
                                    MapView.newMapViewDataType(
                                            argDataType.nullable(),
                                            DataTypes.ARRAY(DataTypes.BIGINT()).bridgedTo(List.class))),
                            //todo:blink 使用SortedMapView 优化性能，开源未实现；使用MapView Retract需要遍历所有key
                            DataTypes.FIELD(
                                    "orderToValueMap",
                                    MapView.newMapViewDataType(
                                            DataTypes.BIGINT(),
                                            DataTypes.ARRAY(argDataType.nullable()).bridgedTo(List.class)))
                    );

                    return Optional.of(accDataType);
                })
                .build()
                ;
    }

    /**
     * @param acc   累加器
     * @param value 值
     * @param order 值在流中的所在位置 不允许为null 存储使用 Map < order , list< Object > >
     * @throws Exception
     */
    public void accumulate(LastValueWithRetractAccumulator acc, Object value, Long order) throws Exception {

        if (isNeedFilterNull) {
            if (Objects.isNull(value)) {
                return;
            }
        }

        Long prevOrder = acc.lastOrder;
        if (prevOrder == null || prevOrder <= order) {
            acc.lastValue = value;
            acc.lastOrder = order;
        }

        //local 阶段
        List<Object> valueList = acc.orderToValueMap.get(order);
        if (valueList == null) {
            valueList = new ArrayList<>();
        }
        valueList.add(value);
        acc.orderToValueMap.put(order, valueList);


    }

    public void retract(LastValueWithRetractAccumulator acc, Object value, Long order) throws Exception {

        retract(acc, value, order, Boolean.TRUE);
    }


    public void retract(LastValueWithRetractAccumulator acc, Object value, Long order, Boolean isLocal) throws Exception {

        if (isNeedFilterNull) {
            if (Objects.isNull(value)) {
                return;
            }
        }

        List<Object> valueList = acc.orderToValueMap.get(order);

        if (valueList == null || valueList.size() == 0) {

            //global 阶段不做处理
            if (isTwoPhase && isLocal) {
                //local阶段的缓存未命中 下发到global
                acc.retractList.add(Tuple2.of(value, order));
            }

            return;
        }

        int index = valueList.indexOf(value);

        if (index >= 0) {
            valueList.remove(index);
            if (valueList.isEmpty()) {
                acc.orderToValueMap.remove(order);
            } else {
                acc.orderToValueMap.put(order, valueList);
            }
        }

        if (Objects.equals(value, acc.lastValue)) {
            Long startKey = acc.lastOrder;
            Iterator<Long> iter = acc.orderToValueMap.keys().iterator();

            Long nextKey = Long.MIN_VALUE;
            while (iter.hasNext()) {
                Long key = iter.next();
                if (key <= startKey && key > nextKey) {
                    nextKey = key;
                }
            }

            if (nextKey != Long.MIN_VALUE) {
                List<Object> values = acc.orderToValueMap.get(nextKey);
                acc.lastValue = values.get(values.size() - 1);
                acc.lastOrder = nextKey;
            } else {
                acc.lastValue = null;
                acc.lastOrder = null;
            }
        }
    }

    /**
     * @param acc   累加器
     * @param value 值
     *              值在流中的所在位置使用System.currentTimeMillis() 是可能重复的
     *              使用 Map < Object , list< Order > >  +  Map < order , list< Object > >
     * @throws Exception
     */
    public void accumulate(LastValueWithRetractAccumulator acc, Object value) throws Exception {

        if (isNeedFilterNull) {
            if (Objects.isNull(value)) {
                return;
            }
        }

        Long order = System.currentTimeMillis();
        List<Long> orderList = acc.valueToOrderMap.get(value);
        if (orderList == null) {
            orderList = new ArrayList<>();
        }
        orderList.add(order);
        acc.valueToOrderMap.put(value, orderList);

        accumulate(acc, value, order);

    }

    public void retract(LastValueWithRetractAccumulator acc, Object value, Boolean isLocal) throws Exception {

        if (isNeedFilterNull) {
            if (Objects.isNull(value)) {
                return;
            }
        }

        List<Long> orderList = acc.valueToOrderMap.get(value);


        if (orderList != null && orderList.size() > 0) {

            Long order = orderList.get(0);
            orderList.remove(0);
            if (orderList.isEmpty()) {
                acc.valueToOrderMap.remove(value);
            } else {
                acc.valueToOrderMap.put(value, orderList);
            }

            retract(acc, value, order, isLocal);
        } else {

            if (isTwoPhase && isLocal) {

                acc.retractList.add(Tuple2.of(value, null));

            }
        }

    }

    public void retract(LastValueWithRetractAccumulator acc, Object value) throws Exception {

        retract(acc, value, Boolean.TRUE);
    }


    public void merge(LastValueWithRetractAccumulator acc, Iterable<LastValueWithRetractAccumulator> iterables) throws Exception {

        if (null == iterables) {
            return;
        }

        for (LastValueWithRetractAccumulator other : iterables) {

            if (Objects.isNull(other)) {
                return;
            }

            if (!other.retractList.isEmpty()) {
                for (Tuple2<Object, Long> data : other.retractList) {

                    Object order = data.getField(1);

                    if (order == null) {
                        retract(acc, data.getField(0), Boolean.FALSE);
                    } else
                        retract(acc, data.getField(0), (Long) order, Boolean.FALSE);
                }
            }

            if (other.lastOrder == null) {
                return;
            }

            for (Map.Entry<Object, List<Long>> entry : other.valueToOrderMap.entries()) {

                if (acc.valueToOrderMap.contains(entry.getKey())) {
                    List<Long> orderList = acc.valueToOrderMap.get(entry.getKey());
                    orderList.addAll(entry.getValue());
                    acc.valueToOrderMap.put(entry.getKey(), orderList);
                } else {
                    acc.valueToOrderMap.put(entry.getKey(), entry.getValue());
                }

            }

            for (Map.Entry<Long, List<Object>> entry : other.orderToValueMap.entries()) {

                if (acc.orderToValueMap.contains(entry.getKey())) {
                    List<Object> orderList = acc.orderToValueMap.get(entry.getKey());
                    orderList.addAll(entry.getValue());
                    acc.orderToValueMap.put(entry.getKey(), orderList);
                } else {
                    acc.orderToValueMap.put(entry.getKey(), entry.getValue());
                }

            }

            if (acc.lastOrder == null || acc.lastOrder <= other.lastOrder) {
                acc.lastValue = other.lastValue;
                acc.lastOrder = other.lastOrder;
            }

        }


    }

    public static class LastValueWithRetractAccumulator {
        public Object lastValue = null;
        public Long lastOrder = null;
        public List<Tuple2<Object, Long>> retractList = new ArrayList<>();
        public MapView<Object, List<Long>> valueToOrderMap = new MapView<>();
        public MapView<Long, List<Object>> orderToValueMap = new MapView<>();

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof LastValueWithRetractAccumulator)) {
                return false;
            }
            LastValueWithRetractAccumulator that = (LastValueWithRetractAccumulator) o;
            return Objects.equals(lastValue, that.lastValue)
                    && Objects.equals(lastOrder, that.lastOrder)
                    && Objects.equals(retractList, that.retractList)
                    && valueToOrderMap.equals(that.valueToOrderMap)
                    && orderToValueMap.equals(that.orderToValueMap)
                    ;
        }

        @Override
        public int hashCode() {
            return Objects.hash(lastValue, lastOrder, valueToOrderMap, orderToValueMap, retractList);
        }

    }

    public void resetAccumulator(AbstractLastValueWithRetractAggFunction.LastValueWithRetractAccumulator acc) {
        acc.lastValue = null;
        acc.lastOrder = null;
        acc.retractList.clear();
        acc.valueToOrderMap.clear();
        acc.orderToValueMap.clear();
    }

    public static class DWLastValueAggFunctionNotNUll extends AbstractLastValueWithRetractAggFunction {

        public DWLastValueAggFunctionNotNUll() {
            super(Boolean.TRUE);
        }
    }

    public static class DWLastValueAggFunction extends AbstractLastValueWithRetractAggFunction {

        public DWLastValueAggFunction() {
            super(Boolean.FALSE);
        }
    }

    public static void main(String[] args) throws Exception {
        DWLastValueAggFunctionNotNUll func = new DWLastValueAggFunctionNotNUll();
        func.isTwoPhase=true;
        LastValueWithRetractAccumulator localAcc01 = new LastValueWithRetractAccumulator();
        LastValueWithRetractAccumulator localAcc02 = new LastValueWithRetractAccumulator();
        LastValueWithRetractAccumulator aggAcc = new LastValueWithRetractAccumulator();

        func.accumulate(localAcc01,"a1");
        Thread.sleep(1);
        func.accumulate(localAcc01,"a2");
        Thread.sleep(1);
        func.retract(localAcc01,"a1");
        func.accumulate(localAcc01,"a0");
        Thread.sleep(1);
        func.accumulate(localAcc01,"a0");

        func.accumulate(localAcc02,"b1");
        Thread.sleep(1);
        func.retract(localAcc02,"b1");
        func.retract(localAcc02,"a0");
        func.retract(localAcc02,"a0");
        Thread.sleep(1);

        func.merge(aggAcc,Collections.singleton(localAcc01));
        func.merge(aggAcc,Collections.singleton(localAcc02));
        System.out.println(func.getValue(aggAcc));

        BinaryStringData a = new BinaryStringData("11001030178");
        BinaryStringData b = new BinaryStringData("020010301");

        System.out.println(a.compareTo(b));

    }
}
