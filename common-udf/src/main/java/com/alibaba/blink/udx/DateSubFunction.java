package com.alibaba.blink.udx;

import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.utils.DateTimeUtils;

import java.sql.Timestamp;

public class DateSubFunction  extends ScalarFunction {
    public String eval(String startdate, Integer days) {
        String result = "";
        try {
            result = DateTimeUtils.dateSub(startdate, days);
        } catch (Exception e) {
            result = null;
        }
        return result;
    }

    public String eval(Timestamp startdate, Integer days) {
        String result = "";
        try {
            result = DateTimeUtils.dateSub(startdate.getTime(), days);
        } catch (Exception e) {
            result = null;
        }
        return result;
    }
}
