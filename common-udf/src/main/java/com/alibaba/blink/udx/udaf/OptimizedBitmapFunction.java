package com.alibaba.blink.udx.udaf;

import com.google.common.hash.Hashing;
import com.udf.types.BitmapValue;
import com.udf.types.OptimizedBitmapTypeSerializer;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;

/**
 * @description: 一个优化的bitmap 实现
 * @Author: lmh
 * @date: 2024/2/26 14:37
 */
public abstract class OptimizedBitmapFunction<T> extends AggregateFunction<T, BitmapValue> {

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> {
                    DataType type = DataTypes.RAW(
                            BitmapValue.class,
                            OptimizedBitmapTypeSerializer.INSTANCE
                    );
                    return Optional.of(type);
                })
                .outputTypeStrategy(callContext -> outPutOptional())
                .build()
                ;
    }

    @Override
    public BitmapValue createAccumulator() {
        return new BitmapValue();
    }

    public void accumulate(BitmapValue accumulator, Long value) {
        if (null != value) {
            accumulator.add(value);
        }
    }

    public void accumulate(BitmapValue accumulator, Integer value) {
        if (null != value) {
            accumulator.add(value);
        }
    }

    public void accumulate(BitmapValue accumulator, String value) {
        if (null != value) {
            accumulate(accumulator, Hashing.farmHashFingerprint64().hashString(value, StandardCharsets.UTF_8).asLong());
        }
    }

    public void merge(BitmapValue accumulator, Iterable<BitmapValue> its) {
        for (BitmapValue acc : its) {
            accumulator.or(acc);
        }
    }

    protected abstract Optional<DataType> outPutOptional();

    /**
     * 返回序列化后的bitmap 可以与SR中的 bitmap 直接合并
     */
    public static class BitmapBase64Function extends OptimizedBitmapFunction<String> {

        @Override
        public String getValue(BitmapValue accumulator) {
            try {
                return Base64.getEncoder().encodeToString(BitmapValue.bitmapToBytes(accumulator));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        protected Optional<DataType> outPutOptional() {
            return Optional.of(DataTypes.STRING());
        }
    }

    public static class BitmapDistinctFunction extends OptimizedBitmapFunction<Long> {

        @Override
        protected Optional<DataType> outPutOptional() {
            return Optional.of(DataTypes.BIGINT());
        }

        @Override
        public Long getValue(BitmapValue accumulator) {
            return accumulator.cardinality();
        }
    }

}
