package com.alibaba.blink.udx.udaf.acc;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;

public class InvQtyAccumulator {
    public long sum = 0;

    private static final Logger logger = LoggerFactory.getLogger(InvQtyAccumulator.class);


    @DataTypeHint("RAW")
    public HashMap<String,Long> daySumHash;

    public  InvQtyAccumulator() {
        daySumHash = new HashMap<>();
    }

    public void add(String formatDay,Long dayChangedQty) {
        daySumHash.put(formatDay,dayChangedQty);
    }

    public String getValue() {
        // 这里要处理 近两日的结果
        try{
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            Date yestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24), "yyyyMMdd"));
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            calendar.setTime(yestDate);
            String yestday = dateFormat.format(calendar.getTime());
            Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyyMMdd"));
            calendar.setTime(date);
            String today = dateFormat.format(calendar.getTime());

            Long yestdayQty = daySumHash.getOrDefault(yestday, 0L);
            Long todayQty = daySumHash.getOrDefault(today, 0L);

            return String.valueOf(yestdayQty+todayQty) ;
        } catch (Exception e) {
            logger.error("get value failed.", e);
        }

        return String.valueOf(sum) ;
    }

}
