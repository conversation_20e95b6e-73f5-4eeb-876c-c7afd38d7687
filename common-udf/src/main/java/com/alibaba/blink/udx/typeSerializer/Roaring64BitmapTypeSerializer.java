package com.alibaba.blink.udx.typeSerializer;

import org.apache.flink.api.common.typeutils.SimpleTypeSerializerSnapshot;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.TypeSerializerSnapshot;
import org.apache.flink.core.memory.DataInputView;
import org.apache.flink.core.memory.DataOutputView;
import org.roaringbitmap.longlong.Roaring64Bitmap;

import java.io.IOException;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/24 10:54
 */
public class Roaring64BitmapTypeSerializer extends TypeSerializer<Roaring64Bitmap> {
    /**
     * Sharable instance of the Roaring64BitmapTypeSerializer.
     */
    public static final Roaring64BitmapTypeSerializer INSTANCE = new Roaring64BitmapTypeSerializer();
    private static final long serialVersionUID = -8544079063839253971L;

    @Override
    public boolean isImmutableType() {
        return false;
    }

    @Override
    public TypeSerializer<Roaring64Bitmap> duplicate() {
        return this;
    }

    @Override
    public Roaring64Bitmap createInstance() {
        return new Roaring64Bitmap();
    }

    @Override
    public Roaring64Bitmap copy(Roaring64Bitmap from) {
        return from.clone();
    }

    @Override
    public Roaring64Bitmap copy(Roaring64Bitmap from, Roaring64Bitmap reuse) {
        return copy(from);
    }

    @Override
    public int getLength() {
        return -1;
    }

    @Override
    public void serialize(Roaring64Bitmap record, DataOutputView target) throws IOException {
        record.serialize(target);
    }

    @Override
    public Roaring64Bitmap deserialize(DataInputView source) throws IOException {
        Roaring64Bitmap navigableMap = new Roaring64Bitmap();
        navigableMap.deserialize(source);
        return navigableMap;
    }

    @Override
    public Roaring64Bitmap deserialize(Roaring64Bitmap reuse, DataInputView source) throws IOException {
        reuse.deserialize(source);
        return reuse;
    }

    @Override
    public void copy(DataInputView source, DataOutputView target) throws IOException {
        Roaring64Bitmap deserialize = this.deserialize(source);
        copy(deserialize);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        } else if (obj != null && obj.getClass() == Roaring64BitmapTypeSerializer.class) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return this.getClass().hashCode();
    }

    @Override
    public TypeSerializerSnapshot<Roaring64Bitmap> snapshotConfiguration() {
        return new Roaring64BitmapTypeSerializer.Roaring64BitmapSerializerSnapshot();
    }

    public static final class Roaring64BitmapSerializerSnapshot
            extends SimpleTypeSerializerSnapshot<Roaring64Bitmap> {

        public Roaring64BitmapSerializerSnapshot() {
            super(() -> Roaring64BitmapTypeSerializer.INSTANCE);
        }
    }
}
