package com.alibaba.blink.udx.log;

import com.StringTableFunction;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * V2版本，Json批量解析请切换到此类修改
 */
public class JsonParseFieldV3 extends StringTableFunction {

    private static final Logger logger = LoggerFactory.getLogger(JsonParseFieldV3.class);

    @Override
    public void open(FunctionContext context) {
    }

    public void eval(String message, String... args) {
        collect(getRow(message, args));
    }

    /**
     * 此udtf核心逻辑
     *
     * @param message
     * @param args
     * @return Row
     */
    public static Row getRow(String message, String... args) {
        if (args == null || args.length == 0) {
            throw new IllegalArgumentException("Parse field array cannot be empty!");
        }
        Row row = new Row(args.length);
        if (Objects.nonNull(message) && !"{}".equals(message) && message.length() != 0) {
            try {
                JSONObject messageFields = JSON.parseObject(message);
                for (int i = 0; i < args.length; i++) {
                    String field = args[i];
                    if (field.contains(".")) {
                        JSONObject temp = messageFields;
                        String[] filedArray = field.split("\\.");
                        for (int j = 0; j < filedArray.length - 1; j++) {
                            try {
                                temp = temp.getJSONObject(filedArray[j]);
                            } catch (com.alibaba.fastjson.JSONException e) {
                                //内部json解析异常时候跳过当前字段解析
                                logger.error("Parse json {} encounter an error :{}", temp.getString(filedArray[j]), e.getMessage());
                                temp = null;
                            }
                            if (temp == null) break;
                        }
                        if (temp == null) {
                            row.setField(i, null);
                        } else {
                            row.setField(i, temp.getString(filedArray[filedArray.length - 1]));
                        }
                    } else {
                        row.setField(i, messageFields.getString(args[i]));
                    }
                }
            } catch (Exception e) {
                logger.error("Parse json encounter an error : {}", e.getMessage());
            }
        }
        return row;
    }


    public static void main(String[] args) {
        String message = "{\"appVersion\":\"4.65.2\",\"blockType\":\"9\",\"currentPage\":\"300000\",\"deviceId\":\"D197DCE2-53CD-40C6-A03B-218CDF6D863B\",\"dwUserId\":\"42547099\",\"event\":\"trade_recommend_feed_exposure\",\"level1Event\":\"trade_recommend_feed_click\",\"level1EventProperties\":\"{\\\"$screen_orientation\\\":\\\"portrait\\\",\\\"block_type_title\\\":\\\"首页瀑布流\\\",\\\"block_type\\\":\\\"9\\\",\\\"recommend_content_title\\\":\\\"Air Jordan Courtside 23 黑白 Concord\\\",\\\"algorithm_product_property_value\\\":\\\"0\\\",\\\"$os\\\":\\\"iOS\\\",\\\"$longitude\\\":\\\"\\\",\\\"algorithm_request_Id\\\":\\\"1375328068451368960\\\",\\\"$network_type\\\":\\\"WIFI\\\",\\\"$wifi\\\":true,\\\"$ip\\\":\\\"**************\\\",\\\"$screen_height\\\":812,\\\"device_uuid\\\":\\\"UUIDe7792e2420f6422b82b7e3a9b1a634ad\\\",\\\"trade_tab_title\\\":\\\"0\\\",\\\"dw_userid\\\":\\\"42547099\\\",\\\"$device_id\\\":\\\"D197DCE2-53CD-40C6-A03B-218CDF6D863B\\\",\\\"caid\\\":\\\"85ebdf62dd9bdbae2143826a89f6a5da\\\",\\\"$app_id\\\":\\\"com.siwuai.duapp\\\",\\\"$latitude\\\":\\\"\\\",\\\"recommend_content_type\\\":\\\"0\\\",\\\"recommend_content_info_list\\\":\\\"[{\\\\\\\"contentType\\\\\\\":0,\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Air Jordan Courtside 23 黑白 Concord\\\\\\\",\\\\\\\"contentID\\\\\\\":69785,\\\\\\\"position\\\\\\\":5,\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"channel\\\\\\\":\\\\\\\"VEC\\\\\\\"}]\\\",\\\"user_agent\\\":\\\"Mozilla/5.0 (iPhone; CPU iPhone OS 14_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148/duapp/4.65.2\\\",\\\"current_page\\\":\\\"300000\\\",\\\"$carrier\\\":\\\"中国联通\\\",\\\"algorithm_channel_Id\\\":\\\"VEC\\\",\\\"$os_version\\\":\\\"14.4.1\\\",\\\"trade_tab_id\\\":\\\"0\\\",\\\"$is_first_day\\\":false,\\\"$model\\\":\\\"iPhone10,3\\\",\\\"receive_time\\\":1616738951074,\\\"session_id\\\":\\\"UUIDe7792e2420f6422b82b7e3a9b1a634ad1616738644948\\\",\\\"$screen_width\\\":375,\\\"$app_version\\\":\\\"4.65.2\\\",\\\"$lib\\\":\\\"iOS\\\",\\\"recommend_content_url\\\":\\\"\\\",\\\"app_build\\\":\\\"*********\\\",\\\"visit_mode\\\":\\\"1\\\",\\\"$timezone_offset\\\":-480,\\\"$lib_version\\\":\\\"2.1.0\\\",\\\"shumei_id\\\":\\\"201907092138528e286a96d0dcdeca8edc936522bd354a01c005e3c0cd7a4b\\\",\\\"recommend_content_position\\\":\\\"5\\\",\\\"recommend_content_id\\\":\\\"69785\\\",\\\"$manufacturer\\\":\\\"Apple\\\",\\\"recommend_content_type_title\\\":\\\"商品\\\",\\\"current_page_title\\\":\\\"购买首页\\\"}\",\"level1Ts\":\"1616738950309\",\"lib\":\"iOS\",\"properties\":\"{\\\"$screen_orientation\\\":\\\"portrait\\\",\\\"block_type_title\\\":\\\"首页瀑布流\\\",\\\"block_type\\\":\\\"9\\\",\\\"recommend_content_title\\\":\\\"Nike Air Max Fusion 蓝白黑\\\",\\\"algorithm_product_property_value\\\":\\\"0\\\",\\\"is_back\\\":\\\"1\\\",\\\"$os\\\":\\\"iOS\\\",\\\"$longitude\\\":\\\"\\\",\\\"algorithm_request_Id\\\":\\\"1375328068451368960\\\",\\\"$network_type\\\":\\\"WIFI\\\",\\\"$wifi\\\":true,\\\"$ip\\\":\\\"**************\\\",\\\"$screen_height\\\":812,\\\"device_uuid\\\":\\\"UUIDe7792e2420f6422b82b7e3a9b1a634ad\\\",\\\"trade_tab_title\\\":\\\"0\\\",\\\"dw_userid\\\":\\\"42547099\\\",\\\"$device_id\\\":\\\"D197DCE2-53CD-40C6-A03B-218CDF6D863B\\\",\\\"caid\\\":\\\"85ebdf62dd9bdbae2143826a89f6a5da\\\",\\\"$app_id\\\":\\\"com.siwuai.duapp\\\",\\\"$latitude\\\":\\\"\\\",\\\"recommend_content_type\\\":\\\"0\\\",\\\"recommend_content_info_list\\\":\\\"[{\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"contentID\\\\\\\":43621,\\\\\\\"contentType\\\\\\\":0,\\\\\\\"position\\\\\\\":4,\\\\\\\"channel\\\\\\\":\\\\\\\"VEC\\\\\\\",\\\\\\\"contentTitle\\\\\\\":\\\\\\\"匹克 帕克7代 态极科技 黑白\\\\\\\"},{\\\\\\\"contentType\\\\\\\":0,\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"channel\\\\\\\":\\\\\\\"VEC\\\\\\\",\\\\\\\"position\\\\\\\":9,\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Air Jordan Super Fly MVP PF 白\\\\\\\",\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"contentID\\\\\\\":18192},{\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"position\\\\\\\":2,\\\\\\\"channel\\\\\\\":\\\\\\\"CLICK\\\\\\\",\\\\\\\"contentID\\\\\\\":1119372,\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Nike Court Borough Mid 2 (GS) 黑红\\\\\\\",\\\\\\\"contentType\\\\\\\":0,\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\"},{\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Anta安踏 神盾1代篮球鞋 黑绿\\\\\\\",\\\\\\\"contentType\\\\\\\":0,\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"channel\\\\\\\":\\\\\\\"FSICF\\\\\\\",\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"contentID\\\\\\\":1052085,\\\\\\\"position\\\\\\\":7},{\\\\\\\"contentType\\\\\\\":0,\\\\\\\"contentID\\\\\\\":69785,\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"position\\\\\\\":5,\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"channel\\\\\\\":\\\\\\\"VEC\\\\\\\",\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Air Jordan Courtside 23 黑白 Concord\\\\\\\",\\\\\\\"propertyValueId\\\\\\\":0},{\\\\\\\"channel\\\\\\\":\\\\\\\"CLICK\\\\\\\",\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Nike Air Max Fusion 蓝白黑\\\\\\\",\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"contentType\\\\\\\":0,\\\\\\\"contentID\\\\\\\":1267268,\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"position\\\\\\\":10},{\\\\\\\"contentTitle\\\\\\\":\\\\\\\"国潮鞋款\\\\\\\",\\\\\\\"contentType\\\\\\\":2,\\\\\\\"requestID\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"position\\\\\\\":8,\\\\\\\"channel\\\\\\\":\\\\\\\"FIACLICK\\\\\\\",\\\\\\\"jumpUrl\\\\\\\":\\\\\\\"https:\\\\\\\\/\\\\\\\\/m.dewu.com\\\\\\\\/router\\\\\\\\/product\\\\\\\\/BoutiqueRecommendDetailPage?recommendId=1016379&spuIds=1224499,60535,1123510,1271711,65491,37360,1091076,46465,34750,1052085,1057908,1126191,1146707,1255820,63493,1139095,1143899,1250031,43621,1253747\\\\\\\",\\\\\\\"contentID\\\\\\\":1016379},{\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"contentID\\\\\\\":34750,\\\\\\\"position\\\\\\\":3,\\\\\\\"contentTitle\\\\\\\":\\\\\\\"LiNing李宁 驭帅11 男子减震耐磨防滑高帮篮球鞋 水蜜桃\\\\\\\",\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"channel\\\\\\\":\\\\\\\"CLICK\\\\\\\",\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"contentType\\\\\\\":0},{\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"contentType\\\\\\\":4,\\\\\\\"channel\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"position\\\\\\\":1},{\\\\\\\"channel\\\\\\\":\\\\\\\"CLICK\\\\\\\",\\\\\\\"acm\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"requestID\\\\\\\":\\\\\\\"1375328068451368960\\\\\\\",\\\\\\\"contentType\\\\\\\":0,\\\\\\\"contentTitle\\\\\\\":\\\\\\\"Nike Court Borough Mid 2 (GS) 蓝白 小黑曜石\\\\\\\",\\\\\\\"propertyValueId\\\\\\\":0,\\\\\\\"contentID\\\\\\\":1267547,\\\\\\\"position\\\\\\\":6}]\\\",\\\"user_agent\\\":\\\"Mozilla/5.0 (iPhone; CPU iPhone OS 14_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148/duapp/4.65.2\\\",\\\"current_page\\\":\\\"300000\\\",\\\"$carrier\\\":\\\"中国联通\\\",\\\"algorithm_channel_Id\\\":\\\"CLICK\\\",\\\"$os_version\\\":\\\"14.4.1\\\",\\\"trade_tab_id\\\":\\\"0\\\",\\\"$is_first_day\\\":false,\\\"$model\\\":\\\"iPhone10,3\\\",\\\"receive_time\\\":1616739066651,\\\"session_id\\\":\\\"UUIDe7792e2420f6422b82b7e3a9b1a634ad1616738644948\\\",\\\"$screen_width\\\":375,\\\"is_up\\\":\\\"0\\\",\\\"$app_version\\\":\\\"4.65.2\\\",\\\"$lib\\\":\\\"iOS\\\",\\\"recommend_content_url\\\":\\\"\\\",\\\"app_build\\\":\\\"*********\\\",\\\"visit_mode\\\":\\\"1\\\",\\\"$timezone_offset\\\":-480,\\\"$lib_version\\\":\\\"2.1.0\\\",\\\"shumei_id\\\":\\\"201907092138528e286a96d0dcdeca8edc936522bd354a01c005e3c0cd7a4b\\\",\\\"recommend_content_position\\\":\\\"10\\\",\\\"recommend_content_id\\\":\\\"1267268\\\",\\\"$manufacturer\\\":\\\"Apple\\\",\\\"recommend_content_type_title\\\":\\\"商品\\\",\\\"current_page_title\\\":\\\"购买首页\\\"}\",\"time\":\"1616739066593\",\"visitSourceLogId\":\"D197DCE2-53CD-40C6-A03B-218CDF6D863B1616739066593b6c9e0527c8e4b41b9a1a7b22d1dd957\"}";
//        String message = "abc";
        JsonParseFieldV3 f = new JsonParseFieldV3();
        f.eval(message, "level1EventProperties.block_type", "block_type");

    }
}
