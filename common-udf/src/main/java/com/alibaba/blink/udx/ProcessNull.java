package com.alibaba.blink.udx;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * Created on 2022/4/13.
 *
 * <AUTHOR>
 */
public class ProcessNull extends ScalarFunction {
    // 可选， open方法可以不写,若写的话需要import org.apache.flink.table.functions.FunctionContext;
    @Override
    public void open(FunctionContext context) {
    }
    public String eval(String a) {
        return a == null ? "" : a;
    }
    public String eval(String a, String b) {
        if (b == null) {
            b = "";
        }
        return a == null ? b : a;
    }
    public Long eval(Long a, Long b) {
        if (b == null) {
            b = 0L;
        }
        return a == null ? b : a;
    }
    //可选，close方法可以不写
    @Override
    public void close() {
    }
}