package com.alibaba.blink.udx.udaf;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.data.GenericRowData;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.data.StringData;
import org.apache.flink.table.data.binary.BinaryStringData;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.utils.DataTypeUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 支持mini batch 的 LastValue 不支持撤回
 * @Author: lmh
 * @date: 2022/11/30 14:45
 */
@Slf4j
abstract class AbstractLastValueAggFunction extends AggregateFunction<Object, RowData> {

    private final boolean isNeedFilterNull;

    @Override
    public Object getValue(RowData accumulator) {
        GenericRowData genericRowData = (GenericRowData) accumulator;
        return genericRowData.getField(0);
    }

    public AbstractLastValueAggFunction(Boolean isNeedFilterNull) {
        this.isNeedFilterNull = isNeedFilterNull;
    }

    @Override
    public boolean isDeterministic() {
        return false;
    }

    @Override
    public RowData createAccumulator() {
        GenericRowData acc = new GenericRowData(2);
        acc.setField(0, null);
        acc.setField(1, Long.MIN_VALUE);
        return acc;
    }

    public void resetAccumulator(RowData accumulator) {
        GenericRowData acc = (GenericRowData) accumulator;
        acc.setField(0, null);
        acc.setField(1, Long.MIN_VALUE);
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {

                    List<DataType> dataTypes = callContext.getArgumentDataTypes();

                    DataType argDataType = DataTypeUtils.toInternalDataType(dataTypes.get(0));

                    return Optional.of(argDataType);
                })
                .accumulatorTypeStrategy(callContext -> {

                    List<DataType> dataTypes = callContext.getArgumentDataTypes();

                    DataType dataType = DataTypeUtils.toInternalDataType(dataTypes.get(0));

                    DataType accDataType = DataTypes.ROW(new DataTypes.Field[]{
                                    DataTypes.FIELD("lastValue", dataType.nullable()),
                                    DataTypes.FIELD("lastOrder", DataTypes.BIGINT())})
                            .bridgedTo(RowData.class);

                    return Optional.of(accDataType);
                })
                .build()
                ;
    }

    /**
     * @param accumulator 累加器
     * @param value       arg
     * @param order       offset
     */
    public void accumulate(RowData accumulator, Object value, Long order) {

        if (isNeedFilterNull) {
            if (Objects.isNull(value)) {
                return;
            }
        }

        if (value instanceof String) {

            accumulate(accumulator, StringData.fromString((String) value), order);

        } else {

            GenericRowData acc = (GenericRowData) accumulator;

            if (acc.getLong(1) <= order) {
                acc.setField(0, value);
                acc.setField(1, order);
            }

        }

    }

    /**
     * 默认使用 System.currentTimeMillis() 标记数据的位置
     */
    public void accumulate(RowData accumulator, Object value) {
        accumulate(accumulator, value, System.currentTimeMillis());
    }

    public void retract(RowData acc, Object value) {

    }

    public void retract(RowData acc, Object value, Long order) {

    }

    public void accumulate(GenericRowData acc, StringData value, Long order) {
        accumulate(acc, (Object) ((BinaryStringData) value).copy(), order);
    }

    /**
     * 根据order merger acc.order <= otherAcc.order 就会替换值
     */
    public void merge(RowData acc, Iterable<RowData> iterables) {
        for (RowData otherAcc : iterables) {
            GenericRowData otherGAcc = (GenericRowData) otherAcc;
            accumulate(acc, otherGAcc.getField(0), otherGAcc.getLong(1));
        }
    }

    /**
     * 过滤null FLink 原生是过滤null 的
     */
    public static class DWLastValueAggFunctionNotNUll extends AbstractLastValueAggFunction{

        public DWLastValueAggFunctionNotNUll() {
            super(Boolean.TRUE);
        }
    }

    /**
     * 不过滤null
     */
    public static class DWLastValueAggFunction extends AbstractLastValueAggFunction{

        public DWLastValueAggFunction() {
            super(Boolean.FALSE);
        }
    }

}
