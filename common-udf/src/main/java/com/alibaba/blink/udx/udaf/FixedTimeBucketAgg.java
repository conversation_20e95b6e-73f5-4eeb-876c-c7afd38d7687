package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.FixedTimeBucketAcc;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

/**
 * Created on 2022/4/13.
 *
 * <AUTHOR>
 */
public class FixedTimeBucketAgg  extends AggregateFunction<String, FixedTimeBucketAcc> {
    private static final Logger logger = LoggerFactory.getLogger(FixedTimeBucketAgg.class);

    @Override
    public FixedTimeBucketAcc createAccumulator() {
        return new FixedTimeBucketAcc(300);
    }

    @Override
    public String getValue(FixedTimeBucketAcc fixedTimeBucketAcc) {
        if (null == fixedTimeBucketAcc) {
            logger.error(String.format("meet null acc here.\n%s", Arrays.toString(Thread.currentThread().getStackTrace())));
            return "{}";
        }
        return fixedTimeBucketAcc.getSnapshot();
    }

    public void accumulate(FixedTimeBucketAcc accumulator, String timeLabel, Long value) {
        if (null == value) {
            value = 0L;
        }
        innerUpdate(accumulator, timeLabel, value);
    }

    public void retract(FixedTimeBucketAcc accumulator, String timeLabel, Long value) {
        if (null == value) {
            value = 0L;
        }
        innerUpdate(accumulator, timeLabel, -value);
    }

    private void innerUpdate(FixedTimeBucketAcc accumulator, String timeLabel, Long value) {
        if (!StringUtils.isNullOrWhitespaceOnly(timeLabel)) {
            if (!accumulator.update(timeLabel, value)) {
                logger.error(String.format("fail to deal with [%s.%s]", timeLabel, value));
            }
        } else {
            logger.error(String.format("found an empty timeLabel [%s.%s], ignore.", timeLabel, value));
        }
    }

    public void merge(FixedTimeBucketAcc accumulator, Iterable<FixedTimeBucketAcc> its) {
        // window merge should be careful.
        for (FixedTimeBucketAcc acc : its) {
            accumulator.merge(acc);
        }
    }
}
