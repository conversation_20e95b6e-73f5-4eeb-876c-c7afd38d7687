package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(
        input = {@org.apache.flink.table.annotation.DataTypeHint("STRING"), @org.apache.flink.table.annotation.DataTypeHint("STRING")},
        output = @DataTypeHint("ARRAY<STRING>")
)
public class StringSplitUdf extends ScalarFunction {

    public String[] eval(String input, String delimiter) {
        if (null == input || null == delimiter) {
            return new String[0];
        }

        return input.split(delimiter);
    }
}
