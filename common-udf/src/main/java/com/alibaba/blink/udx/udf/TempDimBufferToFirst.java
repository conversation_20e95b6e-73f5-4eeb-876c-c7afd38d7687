package com.alibaba.blink.udx.udf;

import com.util.MD5Utils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 将关联的维表数据，临时存在缓存并判断自己是否是第一次来；
 * 适用场景 需要利用维表做第一次 判断 且 会实时更新维表的场景
 */
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("BIGINT")
)
public class TempDimBufferToFirst extends ScalarFunction {
    private static final Logger logger = LoggerFactory.getLogger(TempDimBufferToFirst.class);
    private  ConcurrentHashMap<String, Tuple2<String,Date>> buff= new ConcurrentHashMap<>();
    private  Integer liveTime=10;
    private  Long clearTime;
    private  int buffMaxsize=200000;
    private  int buffSize=0;
    private  int autoDilatation =-1;
    private  int autoDilatationCount =0;
    {
        updateClearTime();
    }
    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        String liveTimeStr = context.getJobParameter("tempDimBufferToFirst.liveTime.minute", "10");
        String innitSize = context.getJobParameter("tempDimBufferToFirst.buff.innit.size", "2000");
        String maxSize = context.getJobParameter("tempDimBufferToFirst.buff.max.size", "200000");
        String autoDilatationStr = context.getJobParameter("tempDimBufferToFirst.buff.auto.dilatation.count", "2");
        autoDilatation = Integer.parseInt(autoDilatationStr);
        liveTime=Integer.parseInt(liveTimeStr);
        buffMaxsize=Integer.parseInt(maxSize);
        updateClearTime();
        buff=new ConcurrentHashMap<>(Integer.parseInt(innitSize));
    }

    public  Long eval(String key,String value) {

        checkNull(key);

        isClearBuff();

        if (buff.containsKey(key)){

           if (Objects.equals(buff.get(key).f0, value)){
               return 1L;
           }
            return 0L;

        }else{
            putBuff(key,value);
            return 1L;
        }

    }




    public void putBuff(String key,String value){
        Calendar nowTime = Calendar.getInstance();
        Date nowTimeTime = nowTime.getTime();
        buff.put(key,Tuple2.of(value,nowTimeTime));
        buffSize++;

        if (buffSize>buffMaxsize && autoDilatation>0 ){
            clearBuff();
            logger.warn("time:{};buff size exceed the quota ！！！try clear,size{}",System.currentTimeMillis(),buffSize);
            if (autoDilatationCount<=autoDilatation){
                buffMaxsize+=buffMaxsize;
                autoDilatationCount++;
            }else {
                logger.error("time:{};buff auto dilatation Count:{} ！！！size{}",System.currentTimeMillis(),autoDilatationCount,buffSize);
                throw new RuntimeException("Try automatic expansion; The buff size still exceeds the quota !!!!");
            }
        }else if (buffSize>buffMaxsize && autoDilatation<0 ){
            logger.error("buff size exceed the quota !!!!");
            throw new RuntimeException("Automatic expansion is not allowed; Buff size exceeds quota !!!!");
        }

    }
    public void removeBuff(String key){
        buff.remove(key);
        buffSize--;
    }

    public void updateClearTime(){
        clearTime=System.currentTimeMillis() +  liveTime * 60 * 1000;
    }

    public void isClearBuff(){
        if (!buff.isEmpty() && clearTime<=System.currentTimeMillis()){
            clearBuff();
            updateClearTime();
        }
    }
    public void checkNull(String key){
        if (key==null ){
            throw new IllegalArgumentException("args cannot be null!");
        }
    }

    public  void clearBuff(){
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE,-liveTime);
        Date time = nowTime.getTime();
        for(String key : buff.keySet()){
            Date dateTime = buff.get(key).f1;
            if(dateTime == null || time.after(dateTime)){
                removeBuff(key);
            }
        }
    }

    public static void main(String[] args) {
//        TempDimBufferToFirst tempDimBufferToFirst = new TempDimBufferToFirst();
//        System.out.println(tempDimBufferToFirst.eval("88",null));
//        System.out.println(tempDimBufferToFirst.eval("88","9999"));
//        System.out.println(tempDimBufferToFirst.eval("88","9998"));
//        tempDimBufferToFirst.clearBuff();
//        System.out.println(tempDimBufferToFirst.buff.size());
//        System.out.println(Integer.parseInt("10"));
        System.out.println(MD5Utils.md5("188240896", StandardCharsets.UTF_8.name())+"_188240896");
    }
}
