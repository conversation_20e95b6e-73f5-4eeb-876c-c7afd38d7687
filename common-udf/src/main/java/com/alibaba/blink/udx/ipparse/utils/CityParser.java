package com.alibaba.blink.udx.ipparse.utils;

import com.alibaba.blink.udx.ipparse.exception.IPFormatException;
import com.alibaba.blink.udx.ipparse.exception.InvalidDatabaseException;
import com.alibaba.blink.udx.ipparse.pojo.CityInfo;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class CityParser {
    private DBReader reader;

    public CityParser(String name) throws IOException, InvalidDatabaseException {
        this.reader = new DBReader(name);
    }

    public CityParser(InputStream in) throws IOException, InvalidDatabaseException {
        this.reader = new DBReader(in);
    }

    public boolean reload(String name) {
        try {
            DBReader r = new DBReader(name);
            this.reader = r;
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public String[] find(String addr, String language) throws IPFormatException, InvalidDatabaseException {
        return this.reader.find(addr, language);
    }

    public Map<String, String> findMap(String addr, String language) throws IPFormatException, InvalidDatabaseException {
        String[] data = this.reader.find(addr, language);
        if (data == null)
            return null;
        String[] fields = this.reader.getSupportFields();
        Map<String, String> m = new HashMap<>();
        for (int i = 0, l = data.length; i < l; i++)
            m.put(fields[i], data[i]);
        return m;
    }

    public CityInfo findInfo(String addr, String language) throws IPFormatException, InvalidDatabaseException {
        String[] data = this.reader.find(addr, language);
        if (data == null)
            return null;
        return new CityInfo(data);
    }

    public int buildTime() {
        return this.reader.getBuildUTCTime();
    }

    public boolean isIPv4() {
        return this.reader.isIPv4();
    }

    public boolean isIPv6() {
        return this.reader.isIPv6();
    }

    public String fields() {
        return Arrays.toString((Object[])this.reader.getSupportFields());
    }

    public String languages() {
        return this.reader.getSupportLanguages();
    }
}

