package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.*;

/**
 * @ClassName: GetValFromArray
 * @Description:
 * @author: scl
 * @date: 2022/4/19  12:39 下午
 */

public class MultiValueMax extends ScalarFunction {
	public Long eval(Long val1, Long... others) {
		long max = val1;
		if (others.length > 1) {
			for (long l : others) {
				if (l > max) {
					max = l;
				}
			}
		}
		return max;
	}

	public static void main(String[] args) {
		System.out.println(new MultiValueMax().eval(1l,2l,4l,3l));
	}
}
