/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.hyperloglog.HllBuffer;
import com.alibaba.blink.udx.hyperloglog.HyperLogLogPlusPlus;
import com.alibaba.blink.udx.hyperloglog.XXH64;
import org.apache.flink.core.memory.MemorySegment;
import org.apache.flink.core.memory.MemorySegmentFactory;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.data.binary.BinaryStringData;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;

import java.text.DecimalFormat;
import java.util.Optional;

import static com.alibaba.blink.udx.hyperloglog.XXH64.DEFAULT_SEED;

public class ApproximateCountDistinctFunction extends AggregateFunction<Long, HllBuffer> {

    private static final Double RELATIVE_SD = 0.001;
    private HyperLogLogPlusPlus hyperLogLogPlusPlus;

    @Override
    public void open(FunctionContext context) throws Exception {
        hyperLogLogPlusPlus = new HyperLogLogPlusPlus(RELATIVE_SD);
        super.open(context);
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> {
                    DataType dataType = DataTypes.STRUCTURED(
                            HllBuffer.class,
                            DataTypes.FIELD("array", DataTypes.ARRAY(DataTypes.BIGINT()))
                    );
                    return Optional.of(dataType);
                })
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.BIGINT()))
                .build();
    }

    @Override
    public HllBuffer createAccumulator() {
        HllBuffer buffer = new HllBuffer();
        buffer.array = new Long[hyperLogLogPlusPlus.getNumWords()];
        resetAccumulator(buffer);
        return buffer;
    }

    public void accumulate(HllBuffer buffer, Long input) throws Exception {

        if (input != null) {
            hyperLogLogPlusPlus.updateByHashcode(buffer, XXH64.hashLong(input, DEFAULT_SEED));
        }
    }

    public void accumulate(HllBuffer buffer, String input) throws Exception {

        if (input != null) {
            BinaryStringData s = BinaryStringData.fromString(input);

            MemorySegment[] segments = s.getSegments();

            Long l = null;
            if (segments.length == 1) {
                l = XXH64.hashUnsafeBytes(
                        segments[0], s.getOffset(), s.getSizeInBytes(), DEFAULT_SEED);
            } else {
                l = XXH64.hashUnsafeBytes(
                        MemorySegmentFactory.wrap(s.toBytes()),
                        0,
                        s.getSizeInBytes(),
                        DEFAULT_SEED);
            }

            hyperLogLogPlusPlus.updateByHashcode(buffer, l);

        }
    }

    public void merge(HllBuffer buffer, Iterable<HllBuffer> it) throws Exception {
        for (HllBuffer tmpBuffer : it) {
            hyperLogLogPlusPlus.merge(buffer, tmpBuffer);
        }
    }

    public void resetAccumulator(HllBuffer buffer) {
        int word = 0;
        while (word < hyperLogLogPlusPlus.getNumWords()) {
            buffer.array[word] = 0L;
            word++;
        }
    }

    @Override
    public Long getValue(HllBuffer buffer) {
        return hyperLogLogPlusPlus.query(buffer);
    }

    public static void main(String[] args) {
        HyperLogLogPlusPlus hyperLogLogPlusPlus1 = new HyperLogLogPlusPlus(RELATIVE_SD);

        ApproximateCountDistinctFunction approximate = new ApproximateCountDistinctFunction();
        approximate.hyperLogLogPlusPlus = hyperLogLogPlusPlus1;
        HllBuffer accumulator = new HllBuffer();
        accumulator.array = new Long[hyperLogLogPlusPlus1.getNumWords()];
        approximate.resetAccumulator(accumulator);

        try {
            for (long i = 0; i < 1000000L; i++) {
                approximate.accumulate(accumulator, i);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        System.out.println("uv：" + approximate.getValue(accumulator));

        System.out.println("桶大小：" + hyperLogLogPlusPlus1.getNumWords());
        DecimalFormat decimalFormat = new DecimalFormat("#.######");
        String formattedNumber = decimalFormat.format(hyperLogLogPlusPlus1.trueRsd());

        System.out.println("标准误差：" + formattedNumber);
        System.out.println("准确率：" + decimalFormat.format((1 - (Math.abs(approximate.getValue(accumulator)-1000000L) / 1000000D)) * 100) + "%");
    }
}
