package com.alibaba.blink.udx.typeSerializer;

import org.apache.flink.api.common.typeutils.SimpleTypeSerializerSnapshot;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.TypeSerializerSnapshot;
import org.apache.flink.core.memory.DataInputView;
import org.apache.flink.core.memory.DataOutputView;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.io.IOException;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/23 20:45
 */
public class Roaring64NavigableMapTypeSerializer extends TypeSerializer<Roaring64NavigableMap> {
    /** Sharable instance of the Roaring64NavigableMapTypeSerializer. */
    public static final Roaring64NavigableMapTypeSerializer INSTANCE = new Roaring64NavigableMapTypeSerializer();
    private static final long serialVersionUID = 3538487939444101755L;

    @Override
    public boolean isImmutableType() {
        return false;
    }

    @Override
    public TypeSerializer<Roaring64NavigableMap> duplicate() {
        return this;
    }

    @Override
    public Roaring64NavigableMap createInstance() {
        return new Roaring64NavigableMap();
    }

    @Override
    public Roaring64NavigableMap copy(Roaring64NavigableMap from) {
        Roaring64NavigableMap copiedMap = new Roaring64NavigableMap();
        from.forEach(copiedMap::addLong);
        return copiedMap;
    }

    @Override
    public Roaring64NavigableMap copy(Roaring64NavigableMap from, Roaring64NavigableMap reuse) {
        from.forEach(reuse::addLong);
        return reuse;
    }

    @Override
    public int getLength() {
        return -1;
    }

    @Override
    public void serialize(Roaring64NavigableMap record, DataOutputView target) throws IOException {
        record.serialize(target);
    }

    @Override
    public Roaring64NavigableMap deserialize(DataInputView source) throws IOException {
        Roaring64NavigableMap navigableMap = new Roaring64NavigableMap();
        navigableMap.deserialize(source);
        return navigableMap;
    }

    @Override
    public Roaring64NavigableMap deserialize(Roaring64NavigableMap reuse, DataInputView source) throws IOException {
        reuse.deserialize(source);
        return reuse;
    }

    @Override
    public void copy(DataInputView source, DataOutputView target) throws IOException {
        Roaring64NavigableMap deserialize = this.deserialize(source);
        copy(deserialize);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        } else if (obj != null && obj.getClass() == Roaring64NavigableMapTypeSerializer.class) {
//            final Roaring64NavigableMapTypeSerializer that = (Roaring64NavigableMapTypeSerializer) obj;
            return true;
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return this.getClass().hashCode();
    }

    @Override
    public TypeSerializerSnapshot<Roaring64NavigableMap> snapshotConfiguration() {
        return new Roaring64NavigableMapSerializerSnapshot();
    }

    public static final class Roaring64NavigableMapSerializerSnapshot
            extends SimpleTypeSerializerSnapshot<Roaring64NavigableMap> {

        public Roaring64NavigableMapSerializerSnapshot() {
            super(() -> Roaring64NavigableMapTypeSerializer.INSTANCE);
        }
    }
}
