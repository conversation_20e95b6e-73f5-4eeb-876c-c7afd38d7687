package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

/**
 * Author: WangLin
 * Date: 2022/9/26 下午6:05
 * Description: 生成13位时间戳udf
 */
public class GenerateTimestamp13 extends ScalarFunction {

	public Long eval() {
		try {
			return System.currentTimeMillis();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	@Override
	public boolean isDeterministic() {
		return false;
	}
}
