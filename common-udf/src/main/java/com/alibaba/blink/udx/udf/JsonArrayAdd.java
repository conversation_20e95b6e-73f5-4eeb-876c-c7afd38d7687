package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;

import java.util.List;
import java.util.Optional;

public class JsonArrayAdd extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    /**
     *
     *
     * @param args
     * @return
     */
    public String eval(Object... args) {
        JSONArray originJson;

        if (args[0] == null){
            originJson=new JSONArray();
        }else if (args[0] instanceof JSONArray)
        {
            originJson = (JSONArray) args[0];
        } else {
            originJson = JSON.parseArray((String)args[0]);
        }

        if (args.length > 1 ) {
            return deal(originJson, args);
        } else {
            return JSONArray.toJSONString(originJson, SerializerFeature.WriteMapNullValue);
        }
    }

    private String deal(JSONArray originJson, Object[] args) {
        for (int i = 1; i < args.length ; i++) {
            if (args[i] instanceof JSONObject){
                originJson.add((JSONObject)args[i]);
            }else {
                originJson.add(JSON.parseObject((String) args[i]));
            }

        }
        return JSONArray.toJSONString(originJson,SerializerFeature.WriteMapNullValue);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() < 2) {
                        throw new ValidationException(String.format(
                                "function %s argument should be (STRING json, STRING jsonPath ...)",
                                functionName));
                    }

                    //校验jsonpath 字段类型为STRING
                    List<DataType> jsonPathValues = argumentDataTypes.subList(
                            1,
                            argumentDataTypes.size());
                    for (DataType dataType : jsonPathValues) {
                        if (!dataType
                                .getLogicalType()
                                .getTypeRoot()
                                .getFamilies()
                                .contains(
                                        LogicalTypeFamily.CHARACTER_STRING)) {
                            throw new ValidationException(String.format(
                                    "function %s argument should be (STRING json, STRING jsonPath ...)",
                                    functionName));
                        }
                    }

                    return Optional.of(DataTypes.STRING());
                })
                .build();
    }

}
