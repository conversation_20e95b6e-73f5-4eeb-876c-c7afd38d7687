package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class JsonParseArrayv2  extends TableFunction<Row> {

    private static final Logger log = LoggerFactory.getLogger(JsonParseArrayv2.class);

    @Override
    public void open(FunctionContext context) throws Exception {

    }
    /**
     * @param message      array类型json
     * @param keyColumn    能够区分每个json的主键
     * @param returnColumn 返回值的字段
     * @param keys         指定需要返回json的key
     */
    public void eval(String message, String keyColumn, String returnColumn, String... keys) {
        if (keyColumn ==null || returnColumn ==null || keys ==null){
            throw new IllegalArgumentException("Parse array args cannot be empty!");
        }
        Row row = new Row(keys.length);
        if (Objects.nonNull(message) && !"{}".equals(message) && message.length()!=0) {
            try {
                List<JSONObject> jsonObjectList = JSONObject.parseArray(message, JSONObject.class);
                Iterator<JSONObject> iterator = jsonObjectList.iterator();
                HashMap<String, String> map = new HashMap<>();
                List<String> keysList = Arrays.asList(keys);
                while (iterator.hasNext()) {
                    JSONObject jsonObject = iterator.next();
                    String key = jsonObject.getString(keyColumn);
                    if (keysList.contains(key)) {
                        String returnValue = jsonObject.getString(returnColumn);
                        map.put(key, returnValue);
                    }
                }
                for (int i = 0; i < keys.length; i++) {
                    row.setField(i, map.get(keys[i]));
                }
            } catch (Exception e) {
                log.error("error JsonParseArrayv2:{}",message);
                e.printStackTrace();
                try {
                    List<String> keysList = Arrays.asList(keys);
                    HashMap<String, String> map = new HashMap<>();
                    JSONObject jsonObject = JSONObject.parseObject(message);
                    String key = jsonObject.getString(keyColumn);
                    if (keysList.contains(key)) {
                        String returnValue = jsonObject.getString(returnColumn);
                        map.put(key, returnValue);
                    }
                    for (int i = 0; i < keys.length; i++) {
                        row.setField(i, map.get(keys[i]));
                    }
                }catch (Exception e1)
                {
                    log.error("======error2 JsonParseArrayv2:{}",message);
                }
            }
//            System.out.println(row);
            collect(row);
        } else {
//            System.out.println("1"+row);
            collect(row);
        }
    }

    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 3];
                    for (int i = 0; i < argumentDataTypes.size() - 3; i++) {
                        outputDataTypes[i] = DataTypes.STRING();
                    }
                    return Optional.of(DataTypes.ROW(outputDataTypes));
                })
                .build();
    }

    @Override
    public void close() throws Exception {
    }

    public static void main(String[] args) {
//        String str="[{\"expenseType\":1,\"expenseName\":\"技术服务费\",\"originalExpense\":12495,\"currentExpense\":12495,\"originalPercent\":500,\"currentPercent\":500,\"extendJson\":\"{\\\"currentPercent\\\":500,\\\"currentexpense\\\":12495,\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalTechnicalFeeLimit\\\":{\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalExpense\\\":12495,\\\"originalPercent\\\":500},\\\"salaInfoInnerVo\\\":{\\\"salaPercent\\\":10000,\\\"salaType\\\":5}}\",\"salePercent\":10000},{\"expenseType\":3,\"expenseName\":\"转账手续费\",\"originalExpense\":2499,\"currentExpense\":2499,\"originalPercent\":100,\"currentPercent\":100,\"extendJson\":\"{\\\"currentExpense\\\":2499,\\\"currentPercent\\\":100,\\\"originalExpense\\\":2499,\\\"originalPercent\\\":100}\"},{\"expenseType\":5,\"expenseName\":\"查验费\",\"originalExpense\":800,\"currentExpense\":800,\"extendJson\":\"{\\\"currentExpense\\\":800,\\\"originalExpense\\\":800}\"},{\"expenseType\":2,\"expenseName\":\"鉴别费\",\"originalExpense\":1500,\"currentExpense\":1500,\"extendJson\":\"{\\\"currentExpense\\\":1500,\\\"originalExpense\\\":1500}\"},{\"expenseType\":4,\"expenseName\":\"包装服务费\",\"originalExpense\":1000,\"currentExpense\":1000,\"extendJson\":\"{\\\"currentExpense\\\":1000,\\\"originalExpense\\\":1000}\"},{\"expenseType\":7,\"expenseName\":\"预计收入\",\"originalExpense\":231606,\"currentExpense\":231606},{\"expenseType\":6,\"expenseName\":\"总费用\",\"originalExpense\":18294,\"currentExpense\":18294}]";
       String str="{\"funderType\":1,\"funderNo\":\"\",\"subsidyAmount\":0}";
        JsonParseArrayv2 jsonParseArray = new JsonParseArrayv2();
        jsonParseArray.eval(str,"funderType","subsidyAmount","1","2");
    }

}
