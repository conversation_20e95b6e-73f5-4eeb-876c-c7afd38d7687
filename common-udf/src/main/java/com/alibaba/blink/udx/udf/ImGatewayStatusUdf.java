package com.alibaba.blink.udx.udf;

import com.alibaba.blink.udx.udf.pb.ImGatewayStatus;
import com.alibaba.fastjson.JSON;
import org.apache.flink.table.functions.ScalarFunction;
import org.jcodings.util.Hash;

import java.util.HashMap;
import java.util.Map;

/**
 * Author: WangLin
 * Date: 2022/11/9 下午3:29
 * Description: 用户上下线心跳通知
 */
public class ImGatewayStatusUdf extends ScalarFunction {

	public String eval(byte[] body) {

		try {
			ImGatewayStatus.Status status = ImGatewayStatus.Status.parseFrom(body);
			if ((status.getScheme().equals("user") && status.getGroup().equals("default")) && (status.getEvent().equals("offline") || status.getEvent().equals("online"))) {
				Map<String, String> map = new HashMap<>();
				map.put("userId", status.getUid());
				map.put("timestamp", String.valueOf(status.getTimestamp()));
				map.put("event", status.getEvent());
				return JSON.toJSONString(map);
			} else {
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

}
