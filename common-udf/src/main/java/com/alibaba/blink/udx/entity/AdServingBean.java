package com.alibaba.blink.udx.entity;

import java.util.Arrays;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 4/14/22 8:26 下午
 */
public class AdServingBean {

  private String category_lv1_id[];
  private String category_lv2_id[];
  private String category_lv3_id[];
  private String brand_id[];
  //    private String sex[];
  private String user_id;

  private String is_new;

  public String[] getCategory_lv1_id() {
    return category_lv1_id;
  }

  public void setCategory_lv1_id(String[] category_lv1_id) {
    this.category_lv1_id = category_lv1_id;
  }

  public String[] getCategory_lv2_id() {
    return category_lv2_id;
  }

  public void setCategory_lv2_id(String[] category_lv2_id) {
    this.category_lv2_id = category_lv2_id;
  }

  public String[] getCategory_lv3_id() {
    return category_lv3_id;
  }

  public void setCategory_lv3_id(String[] category_lv3_id) {
    this.category_lv3_id = category_lv3_id;
  }

  public String[] getBrand_id() {
    return brand_id;
  }

  public void setBrand_id(String[] brand_id) {
    this.brand_id = brand_id;
  }

//    public String[] getSex() {
//        return sex;
//    }
//
//    public void setSex(String[] sex) {
//        this.sex = sex;
//    }

  public String getUser_id() {
    return user_id;
  }

  public void setUser_id(String user_id) {
    this.user_id = user_id;
  }

  public String getIs_new() {
    return is_new;
  }

  public void setIs_new(String is_new) {
    this.is_new = is_new;
  }

  @Override
  public String toString() {
    return "AdServingBean{" +
        "category_lv1_id=" + Arrays.toString(category_lv1_id) +
        ", category_lv2_id=" + Arrays.toString(category_lv2_id) +
        ", category_lv3_id=" + Arrays.toString(category_lv3_id) +
        ", brand_id=" + Arrays.toString(brand_id) +
//                ", sex=" + Arrays.toString(sex) +
        ", user_id='" + user_id + '\'' +
        ", is_new='" + is_new + '\'' +
        '}';
  }
}
