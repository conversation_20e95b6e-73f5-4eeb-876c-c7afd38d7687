package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.util.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Map;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2023/6/1
 */

public class NewbieUserCouponExpireTimeParser extends ScalarFunction {

	private final static String END_TIME = "endTime";

	public Long eval(String couponInfo) {
		long maxEndTime = 0L;
		if (!StringUtils.isBlank(couponInfo)) {
			JSONArray couponInfos = JSON.parseArray(couponInfo);
			for (Object o : couponInfos) {
				if (o instanceof Map) {
					if (((Map<?, ?>) o).containsKey(END_TIME)) {
						long endTime = Long.parseLong(((Map) o).get(END_TIME).toString());
						if (endTime > maxEndTime) {
							maxEndTime = endTime;
						}
					}
				} else {
					throw new RuntimeException("not a valid newbie user coupon info : " + couponInfo);
				}
			}
		}
		return maxEndTime;
	}

	public static void main(String[] args) {
		String couponInfo = "[{\"algoCategoryIds\":[\"0\"],\"amount\":2000,\"categoryId\":0,\"couponName\":\"新用户专属券\",\"createTime\":1685471578907,\"detailsNo\":\"109783936325\",\"endTime\":1686076378,\"limitAmount\":40000,\"ruleDesc\":\"满400可用\",\"startTime\":1685471578},{\"algoCategoryIds\":[\"0\"],\"amount\":2700,\"categoryId\":0,\"couponName\":\"新用户专属券\",\"createTime\":1685471578907,\"detailsNo\":\"109783936326\",\"endTime\":1686076378,\"limitAmount\":60000,\"ruleDesc\":\"满600可用\",\"startTime\":1685471578},{\"algoCategoryIds\":[\"0\"],\"amount\":3300,\"categoryId\":0,\"couponName\":\"新用户专属券\",\"createTime\":1685471578907,\"detailsNo\":\"109783936327\",\"endTime\":1686076378,\"limitAmount\":90000,\"ruleDesc\":\"满900可用\",\"startTime\":1685471578},{\"algoCategoryIds\":[\"0\"],\"amount\":4100,\"categoryId\":0,\"couponName\":\"新用户专属券\",\"createTime\":1685471578907,\"detailsNo\":\"109783936328\",\"endTime\":1686076378,\"limitAmount\":120000,\"ruleDesc\":\"满1200可用\",\"startTime\":1685471578},{\"algoCategoryIds\":[\"0\"],\"amount\":4800,\"categoryId\":0,\"couponName\":\"新用户专属券\",\"createTime\":1685471578907,\"detailsNo\":\"109783936329\",\"endTime\":1686076378,\"limitAmount\":160000,\"ruleDesc\":\"满1600可用\",\"startTime\":1685471578},{\"algoCategoryIds\":[\"0\"],\"amount\":6000,\"categoryId\":0,\"couponName\":\"新用户专属券\",\"createTime\":1685471578907,\"detailsNo\":\"109783936330\",\"endTime\":1686076378,\"limitAmount\":200000,\"ruleDesc\":\"满2000可用\",\"startTime\":1685471578}]";
		System.out.println(new NewbieUserCouponExpireTimeParser().eval(couponInfo));
	}

}
