package com.alibaba.blink.udx.udaf.acc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.api.dataview.MapView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Union uniqueKey join
 * <tabname,map<uniquekeyObject,messageJsonStr>>
 */
public class SettlementOperationAccumulator {

    private static final Logger logger = LoggerFactory.getLogger(SettlementOperationAccumulator.class);
    public MapView<String, Map<String, String>> resViewMap;
    public Map<String, List<String>> updatedKeyMap;

    public SettlementOperationAccumulator() {
        this.resViewMap = new MapView<>();
        this.updatedKeyMap = new HashMap<>();
    }

    public void add(String tableName, String uniqueKey, String row) throws Exception {
        if (!this.resViewMap.contains(tableName)) {
            HashMap<String, String> newHash = new HashMap<String, String>();
            newHash.put(uniqueKey, row);
            this.resViewMap.put(tableName, newHash);
        } else {
            try {
                Map<String, String> keysHash = this.resViewMap.get(tableName);
                if ( keysHash== null ||  keysHash.isEmpty() ) keysHash =  new HashMap<String, String>();
                keysHash.put(uniqueKey, row);
                this.resViewMap.put(tableName, keysHash);
            } catch (Exception e) {
                logger.error(" find an error :table name is :{}, key is  {} ,and message is  {}",tableName,uniqueKey,row);
                throw new RuntimeException(e);
            }
        }

        // updatedKey
        if (!this.updatedKeyMap.containsKey(tableName)) {
            List<String> modifySet = new ArrayList<>();
            modifySet.add(uniqueKey);
            this.updatedKeyMap.put(tableName, modifySet);
        } else {
            List<String> updatedSet = updatedKeyMap.get(tableName);
            if (!updatedSet.contains(uniqueKey)) {
                updatedSet.add(uniqueKey);
                this.updatedKeyMap.put(tableName, updatedSet);
            }
        }
    }

    public void merge(SettlementOperationAccumulator other) throws Exception {
        if (null != other) {
            for (String key : other.resViewMap.keys()) {
                Map<String, String> otherKeysHash = other.resViewMap.get(key);
                if (!this.resViewMap.contains(key)) {
                    this.resViewMap.put(key, otherKeysHash);
                } else {
                    Map<String, String> thisTableMap = this.resViewMap.get(key);
                    thisTableMap.putAll(otherKeysHash);
                    this.resViewMap.put(key, thisTableMap);
                }
            }

        } else {
            logger.error("merge with null.");
        }
    }

    //  jsonarr
    public String getSnapshot() throws Exception {
        if (!this.updatedKeyMap.isEmpty() && !this.resViewMap.isEmpty()) {
            Map<String, Map<String, String>> allShapshotMap = this.resViewMap.getMap();
//            List<List<String>> mergeListData = new ArrayList<>();
            // 变更量下发
            if (updatedKeyMap.size() == 1 && allShapshotMap.size() != 1) {
                HashMap<String, String> tem = new HashMap<>();
                String table = updatedKeyMap.keySet().stream().findFirst().get();
                List<String> upadtedUinqueKeySet = updatedKeyMap.get(table);
                if (upadtedUinqueKeySet.size() > 0) {
                    upadtedUinqueKeySet.stream().forEach(k -> {
                        try {
                            tem.put(k, allShapshotMap.get(table).get(k));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                }


                // 更新变更的集合数量
                allShapshotMap.replace(table, tem);
            }

            List<List<String>> listTableRecordsHash = allShapshotMap.values().stream().map(r ->
                    new ArrayList<>(r.values())
            ).collect(Collectors.toList());
            JSONArray lisReturn = getDescartes(listTableRecordsHash);
            allShapshotMap.clear();
            listTableRecordsHash.clear();
            updatedKeyMap.clear();
            return JSONObject.toJSONString(lisReturn);
        }
        return new JSONArray().toJSONString();
    }


    private static <T> JSONArray getDescartes(List<List<T>> list) throws IOException {
        JSONArray returnList = new JSONArray();
        descartesRecursive(list, 0, returnList, new ArrayList<String>());
        return returnList;
    }

    /**
     * 递归实现
     * 原理：从原始list的0开始依次遍历到最后
     *
     * @param originalList 原始list
     * @param position     当前递归在原始list的position
     * @param returnList   返回结果
     * @param cacheList    临时保存的list
     */
    private static <T> void descartesRecursive(List<List<T>> originalList, int position, JSONArray returnList, List<String> cacheList) {
        List<T> originalItemList = originalList.get(position);
        try {
            for (int i = 0; i < originalItemList.size(); i++) {
                //最后一个复用cacheList，节省内存
                List<String> childCacheList = (i == originalItemList.size() - 1) ? cacheList : new ArrayList<>(cacheList);
                childCacheList.add((String) originalItemList.get(i));
                if (position == originalList.size() - 1) {//遍历到最后退出递归
                    JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(childCacheList));
                    returnList.add(jsonArray);
                    continue;
                }
                descartesRecursive(originalList, position + 1, returnList, childCacheList);
                childCacheList.clear();
            }
        } catch (Exception e1) {
            throw new RuntimeException(e1);
        }

    }

}