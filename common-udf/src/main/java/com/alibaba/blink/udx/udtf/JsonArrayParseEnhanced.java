package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> 张天龙
 * @Project: du-tech-data-udf
 * @Package com.alibaba.blink.udx.udtf
 * @date Date : 2025年01月15日 18:00
 * 需求：增加一个改进版的JSONArray解析UDTF
 * 1. 可以全行解析，不传递参数时
 * 2. 可以逐个字段解析，当传递解析字段名称时
 * 3. 支持嵌套解析，当有嵌套字段时，支持通过'.'分隔符进行分割提取
 * 逻辑：
 * 当有多个字段，且有分隔符的情况下，会先构建提取规则。优化提取过程，保证每个json层级只遍历一次。优化普通嵌套提取时，对每个层级逐个遍历提取。
 * 对多字段的嵌套json提取，可以大幅提升解析性能
 */
@Slf4j
public class JsonArrayParseEnhanced extends TableFunction<Row> {

	private Row emptyRow = new Row(BASE_ROW_SIZE);

	private static final int BASE_ROW_SIZE = 1;
	private boolean COLLECT_FULL_FIELDS = false;

	private ConcurrentHashMap<String, Object> keyExtractConfig = null;


	public boolean validateJsonArray(String message) {
		if (StringUtils.isEmpty(message) || message.startsWith("{") || "{}".equals(message) || "[]".equals(message) || "[{}]".equals(message)) {
			return false;
		}
		return true;
	}

	public void eval(String msg, String... keySet) {
		if (keySet.length == 0) {
			COLLECT_FULL_FIELDS = true;
		} else {
			//对key进行分组排序。
			if (keyExtractConfig == null) {
				keyExtractConfig = buildKeyMap(keySet);
			}
		}
		if (!validateJsonArray(msg)) {
			handleInvalidJson(keySet.length);
			return;
		}
		final List<JSONObject> jsonObjects;
		try {
			jsonObjects = JSON.parseArray(msg, JSONObject.class);
		} catch (Exception e) {
			log.error("JSON parsing error. Ignoring parse and using empty record instead. Error data: {}. Exception: {}", msg, e.getMessage(), e);
			handleInvalidJson(keySet.length);
			return;
		}
		for (JSONObject jsonObject : jsonObjects) {
			if (COLLECT_FULL_FIELDS) {
				final Row row = new Row(BASE_ROW_SIZE);
				row.setField(0, jsonObject.toJSONString());
				collectFunction(row);
			} else {
				final Row row = new Row(keySet.length);
				fillRowData(keyExtractConfig, jsonObject, row);
				collectFunction(row);
			}
		}
	}

	private void handleInvalidJson(int length) {
		if (COLLECT_FULL_FIELDS) {
			collectFunction(emptyRow);
		} else {
			Row row = new Row(length);
			collectFunction(row);
		}
	}

	public void fillRowData(ConcurrentHashMap<String, Object> keyExtractConfig, JSONObject jsonObject, Row row) {
		final Set<Map.Entry<String, Object>> entries = keyExtractConfig.entrySet();
		for (Map.Entry<String, Object> entry : entries) {
			final Object keyPositionValue = entry.getValue();
			final String name = keyPositionValue.getClass().getSimpleName();
			final boolean isPosition = name.equals("Integer");
			if (isPosition) {
				String value;
				if (jsonObject == null) {
					value = null;
				} else {
					value = jsonObject.getString(entry.getKey());
				}
				row.setField((Integer) keyPositionValue, value);
			} else {
				JSONObject value;
				if (jsonObject == null) {
					value = null;
				} else {
					value = jsonObject.getJSONObject(entry.getKey());
				}
				fillRowData((ConcurrentHashMap<String, Object>) keyPositionValue, value, row);
			}
		}

	}

	public void collectFunction(Row row) {
		collect(row);
		// 将结果收集到列表中
//		collectedResults.add(row.toString());
	}

	public ConcurrentHashMap<String, Object> buildKeyMap(String... keySet) {
		ConcurrentHashMap<String, Object> keyMap = new ConcurrentHashMap<>();
		ConcurrentHashMap<String, Object> originMap = keyMap;
		for (int i = 0; i < keySet.length; i++) {
			final String key = keySet[i];
			final String[] splitKey = key.split("\\.");
			for (int j = 0; j < splitKey.length; j++) {
				String aPartOfKey = splitKey[j];
				Object o = keyMap.get(aPartOfKey);
				if (o == null) {
					if (j == splitKey.length - 1) {
						keyMap.put(aPartOfKey, Integer.valueOf(i));
					} else {
						o = new ConcurrentHashMap<>();
						keyMap.put(aPartOfKey, o);
						keyMap = (ConcurrentHashMap<String, Object>) o;
					}
				} else {
					try {
						keyMap = (ConcurrentHashMap<String, Object>) o;
					} catch (Exception e) {
						throw new UnsupportedOperationException(e);
					}
				}
			}
			keyMap = originMap;
		}
		return keyMap;
	}

	@Override
	public TypeInference getTypeInference(DataTypeFactory typeFactory) {
		return TypeInference.newBuilder()
				.outputTypeStrategy(callContext -> {
					//根据函数入参情况确定返回值
					List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();

					if (argumentDataTypes.size() == 1) {
						return Optional.of(DataTypes.ROW(DataTypes.STRING()));
					} else {
						DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1];
						for (int i = 0; i < argumentDataTypes.size() - 1; i++) {
							outputDataTypes[i] = DataTypes.STRING();
						}
						return Optional.of(DataTypes.ROW(outputDataTypes));
					}

				})
				.build();
	}

//	public static void main(String[] args) {
//		testCase1();
//		testCase2();
//		testCase3();
//		testCase4();
//		testCase5();
//		testCase6();
//		testCase7();
//		testCase8();
//		testCase9();
//		testCase10();
//		testCase11();
//		testCase12();
//		testCase14();
//		testCase14();
//		testCase15();
//	}
//
//	public static void testCase1() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1,\"b\":2,\"c\":3},{\"a\":9,\"b\":8}]";
//		System.out.println("------------- Test Case 1 -------------");
//		jsonArrayParseEnhanced.evel(msg);
//		// 预期输出：{"a":1,"b":2,"c":3} 以及 {"a":9,"b":8}
//
//		System.out.println("msg: " + msg);
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
////		Assert.assertEquals(expectedResults, results);
//	}
//
//	public static void testCase2() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1,\"b\":2,\"c\":3},{\"a\":9,\"b\":8}]";
//		System.out.println("------------- Test Case 2 -------------");
//		// 预期输出：{"a":1,"b":2,"c":3} 以及 {"a":9,"b":8}
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c");
//
//		System.out.println("msg: " + msg);
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase3() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1,\"b\":2,\"c\":3},{\"a\":9,\"b\":8}]";
//		System.out.println("------------- Test Case 3 -------------");
//		// 预期输出：{"a":1,"b":2,"c":3} 以及 {"a":9,"b":8}
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c");
//
//		System.out.println("msg: " + msg);
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase4() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1,\"b\":2,\"c\":3},{\"a\":null,\"b\":8}]"; // 测试包含 null 值的对象
//		System.out.println("------------- Test Case 4 -------------");
//		// 预期输出：{"a":1,"b":2,"c":3} 以及 {"a":null,"b":8}
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c");
//
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase5() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"x\":5,\"y\":10,\"z\":15},{\"x\":12,\"y\":22}]"; // 测试不同的键
//		System.out.println("------------- Test Case 5 -------------");
//		// 预期输出：{"x":5,"y":10,"z":15} 以及 {"x":12,"y":22}
//		jsonArrayParseEnhanced.evel(msg, "x", "y", "z");
//
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase6() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[]"; // 测试空数组
//		System.out.println("------------- Test Case 6 -------------");
//		// 预期没有输出
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase7() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "{\"a\":1,\"b\":2}"; // 测试非数组
//		System.out.println("------------- Test Case 7 -------------");
//		// 预期没有输出
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase8() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1},{\"a\":2},{\"a\":3}]"; // 测试数组中只有部分字段
//		System.out.println("------------- Test Case 8 -------------");
//		// 预期输出：{"a":1}，{"a":2}，{"a":3}
//		jsonArrayParseEnhanced.evel(msg, "a");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase9() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1,\"b\":2,\"c\":{\"d\":4}}, {\"a\":9,\"b\":null,\"c\":{\"d\":5}}]"; // 测试嵌套对象
//		System.out.println("------------- Test Case 9 -------------");
//		// 预期输出：{"a":1,"b":2,"c.d":4} 以及 {"a":9,"b":null,"c.d":5}
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c.d");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase10() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = null; // 测试 null 输入
//		System.out.println("------------- Test Case 10 -------------");
//		// 预期没有输出或处理错误
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase11() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1,\"b\":\"text\",\"c\":{\"d\":\"string\"}}]"; // 测试字符串值
//		System.out.println("------------- Test Case 11 -------------");
//		// 预期输出：{"a":1,"b":"text","c.d":"string"}
//		jsonArrayParseEnhanced.evel(msg, "a", "b", "c.d");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase12() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\": 1}, {\"a\": 2}, {\"a\": 3}]"; // 测试重复键
//		System.out.println("------------- Test Case 12 -------------");
//		// 预期输出：{"a":1}，{"a":2}，{"a":3}
//		jsonArrayParseEnhanced.evel(msg, "a", "b");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase13() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":{\"b\":{\"c\": 1}}}, {\"a\":{\"b\":{\"c\": 2}}}]"; // 测试深层嵌套对象
//		System.out.println("------------- Test Case 13 -------------");
//		// 预期输出：{"a.b.c":1} 以及 {"a.b.c":2}
//		jsonArrayParseEnhanced.evel(msg, "a.b.c");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}
//
//	public static void testCase14() {
//		final JsonArrayParseEnhanced jsonArrayParseEnhanced = new JsonArrayParseEnhanced();
//		String msg = "[{\"a\":1}, {\"a\":\"text\"}, {\"a\":{\"key\":\"value\"}}]"; // 测试不同类型的值
//		System.out.println("------------- Test Case 14 -------------");
//		// 预期输出：{"a":1}，{"a":"text"}，{"a.key":"value"}
//		jsonArrayParseEnhanced.evel(msg, "a");
//
//		System.out.println("msg: " + msg);
//
//		List<String> results = jsonArrayParseEnhanced.getCollectedResults();
//		System.out.println(results.toString());
//	}

//	String ABC = "{\n" +
//			"    \"name\": \"yichangshuju\",\n" +
//			"    \"address\": \"\"\n" +
//			"}";
//
//	public static void main(String[] args) {
//		String jsonArray = "\n" +
//				"[\n" +
//				"        {\n" +
//				"            \"addressName\": \"上海\",\n" +
//				"            \"addr\": {\n" +
//				"                \"phone\": \"aaa\",\n" +
//				"                \"secondPhone\": \"bbb\"\n" +
//				"            },\n" +
//				"            \"phone\": {\n" +
//				"                \"primaryPhone\": {\n" +
//				"                    \"name\": \"iphone\"\n" +
//				"                },\n" +
//				"                \"secondPhone\": {\n" +
//				"                    \"name\": \"huawei\"\n" +
//				"                }\n" +
//				"            }\n" +
//				"        },\n" +
//				"        {\n" +
//				"            \"addressName\": \"广州\",\n" +
//				"            \"addr\": {\n" +
//				"                \"phone\": \"ccc\",\n" +
//				"                \"secondPhone\": \"ddd\"\n" +
//				"            },\n" +
//				"            \"phone\": {\n" +
//				"                \"primaryPhone\": {\n" +
//				"                    \"name\": \"vivo\"\n" +
//				"                },\n" +
//				"                \"secondPhone\": {\n" +
//				"                    \"name\": \"huawei\"\n" +
//				"                }\n" +
//				"            }\n" +
//				"        }\n" +
//				"    ]";
//
//		String jsonString = "{\n" +
//				"    \"name\": \"yichangshuju\",\n" +
//				"    \"address\": \"\"\n" +
//				"}";
//
//		final JSONObject jsonObject = JSON.parseObject(jsonString);
//		jsonObject.put("address", JSON.parseArray(jsonArray).toJSONString());
//		System.out.println(jsonObject.toJSONString());
//	}
}
