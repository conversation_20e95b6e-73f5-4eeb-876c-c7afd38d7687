package com.alibaba.blink.udx.udaf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.util.StringUtils;

@FunctionHint(input = @DataTypeHint(inputGroup = InputGroup.ANY), output = @DataTypeHint("STRING"))
public class NonNullFunction extends AggregateFunction<String, NonNullFunction.StringAcc> {

    /**
     * final
     * @param stringAcc
     * @return
     */
    @Override
    public String getValue(StringAcc stringAcc) {
        return stringAcc.getCache();
    }

    @Override
    public StringAcc createAccumulator() {
        return new StringAcc();
    }

    /**
     * middle
     * @param accumulator
     * @param value
     */
    public void accumulate(StringAcc accumulator, Object value) {
        accumulator.acc(String.valueOf(value));
    }

    public void retract(StringAcc accumulator, Object value) {
    }

    public void merge(StringAcc accumulator, Iterable<StringAcc> iterables) {
        for (StringAcc acc : iterables) {
            accumulator.acc(acc.getCache());
        }
    }

    public static class StringAcc {

        String cache = null;

        public String getCache() {
            return cache;
        }

        public void setCache(String cache) {
            this.cache = cache;
        }

        public boolean isBlankOrNull() {
            //处理cache='null'
            return StringUtils.isNullOrWhitespaceOnly(cache) || cache.toLowerCase().equals("null");
        }

        public void acc(String value) {
            if (isBlankOrNull()) {
                setCache(value);
            }
        }
    }
}
