package com.alibaba.blink.udx.udtf.dynamic.express;


import com.googlecode.aviator.Expression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class AviatorExpression implements UDExpression {

    public static Logger LOG = LoggerFactory.getLogger(AviatorExpression.class);

    private Expression compile;

    private String calculatorExpression;

    public Expression getCompile() {
        return compile;
    }

    public void setCompile(Expression compile) {
        this.compile = compile;
    }

    @Override
    public Object execute(Map map) {
        return execute(map, false);
    }

    @Override
    public Object execute(Map map, boolean ignoreError) {
        try {
            Object execute = compile.execute(map);
            return execute;
        } catch (Exception e) {
            LOG.error("AviatorExpression calculatoExpressionr:{},map:{}", calculatorExpression, map, e);
            if (ignoreError) {
                return null;
            }
            throw e;
        }
    }

    @Override
    public Object execute() {
        Object execute = compile.execute();
        return execute;
    }

    @Override
    public String getCalculatorExpression() {
        return calculatorExpression;
    }

    public void setCalculatorExpression(String calculatorExpression) {
        this.calculatorExpression = calculatorExpression;
    }

    @Override
    public String toString() {
        return "AviatorExpression{" +
                "calculatoExpressionr='" + calculatorExpression + '\'' +
                '}';
    }
}

