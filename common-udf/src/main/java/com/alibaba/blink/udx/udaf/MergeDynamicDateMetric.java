package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.AggregateFunction;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/11/27 16:41
 */
@Slf4j
public class MergeDynamicDateMetric extends AggregateFunction<String, MergeDynamicDateMetric.MergeAcc> {

    @Override
    public String getValue(MergeAcc accumulator) {
        return accumulator.getValueJson();
    }

    public void accumulate(MergeAcc accumulator, String time, Integer offset, String... kvArgs) {
        try {
            accumulator.inputValue(time, offset, kvArgs);
        } catch (Exception e) {
            log.error("acc error",e);
        }
    }

    public void retract(MergeAcc accumulator, String time, Integer offset, String... kvArgs) {

    }

    public void merge(MergeAcc accumulator, Iterable<MergeAcc> its) {
        try {
            for (MergeAcc acc : its) {
                accumulator.merge(acc);
            }
        } catch (Exception e) {
            log.error("acc error",e);
        }
    }

    @Override
    public MergeAcc createAccumulator() {
        return new MergeAcc();
    }

    @Data
    public static class MergeAcc {
        public LocalDate lastTime;
        public LocalDate firstTime;
        public Map<LocalDate, Map<String, Long>> valueMap;
        public List<LocalDate> timeList;
        public long offset;
        public List<LocalDate> updateTime;

        public MergeAcc() {
            this.valueMap = new HashMap<>();
            this.timeList = new ArrayList<>();
            this.updateTime = new ArrayList<>();
        }

        public String getValueJson() {

            if (updateTime.isEmpty()) {
                return "";
            }

            timeList = timeList.stream().distinct().sorted().collect(Collectors.toList());

            if (dateDiff(firstTime, lastTime) > offset) {
                //需要删数据
                Iterator<LocalDate> iterator = timeList.iterator();

                while (iterator.hasNext()) {
                    LocalDate date = iterator.next();
                    if (dateDiff(date, lastTime) > offset) {
                        iterator.remove();
                        updateTime.removeAll(Collections.singletonList(date));
                        valueMap.remove(date);
                    }
                    if (dateDiff(date, lastTime) == offset) {
                        break;
                    }
                }

                firstTime = timeList.get(0);
            }

            List<LocalDate> upList = updateTime.stream().distinct().sorted().collect(Collectors.toList());

            HashMap<String, Object> acc = new HashMap<>();

            Iterator<LocalDate> iterator = timeList.iterator();

            StringBuilder sb = new StringBuilder();

            String historyDate = lastTime.minusDays(offset + 1).format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE);

            int tmp = 0;
            do {
                LocalDate date = iterator.next();

                Map<String, Long> kvMap = valueMap.get(date);

                kvMap.forEach((k, v) -> {
                    Long buffValue = (Long) acc.getOrDefault(k, 0L);
                    acc.put(k, buffValue + v);

                });

                if (upList.contains(date) || tmp != 0) {
                    if (tmp != 0)
                        sb.append('*');
                    tmp++;
                    HashMap<String, Object> res = new HashMap<>(acc);
                    res.put("date", date.format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE));
                    res.put("pt", historyDate);
                    sb.append(JSON.toJSONString(res));
                }

            } while (iterator.hasNext());

            this.updateTime.clear();
            return sb.toString();

        }

        public void inputValue(String time, int offset, String... args) {

            LocalDate inputDate = dateStringFormatLocalDate(time);
            if (inputDate == null){
                log.warn("time is null,time:{}",time);
            }
            this.timeList.add(inputDate);
            this.updateTime.add(inputDate);
            this.offset = offset;

            Map<String, Long> tmpValue = valueMap.getOrDefault(inputDate, new HashMap<>());

            for (int i = 0; i < args.length; i = i + 2) {

                tmpValue.put((String) args[i], args[i + 1] == null ? 0L : Long.parseLong(String.valueOf(args[i + 1])));

            }

            valueMap.put(inputDate, tmpValue);

            if (lastTime == null) {
                lastTime = inputDate;
                firstTime = inputDate;
                return;
            }

            if (dateDiff(lastTime, inputDate) > 0) {
                lastTime = inputDate;
            }

            if (dateDiff(firstTime, inputDate) < 0) {
                firstTime = inputDate;
            }

        }


        public void merge(MergeAcc other) {
            if (other.firstTime == null ||  other.lastTime== null){
                log.warn("time is null,time:{},{}",firstTime,lastTime);
            }
            this.offset = other.offset;
            this.firstTime = this.firstTime == null || dateDiff(this.firstTime, other.firstTime) < 0 ? other.firstTime : this.firstTime;
            this.lastTime = this.lastTime == null || dateDiff(this.lastTime, other.lastTime) > 0 ? other.lastTime : this.lastTime;
            this.timeList.addAll(other.timeList);
            this.updateTime.addAll(other.updateTime);

            other.valueMap.forEach((key, value) -> {
                Map<String, Long> tmpValue = this.valueMap.getOrDefault(key, new HashMap<>());
                tmpValue.putAll(value);
                this.valueMap.put(key, tmpValue);
            });
        }

        public long dateDiff(LocalDate strDate, LocalDate nowDate) {
            return ChronoUnit.DAYS.between(strDate, nowDate);
        }

        public LocalDate dateStringFormatLocalDate(String strDate) {
            return LocalDate.parse(strDate, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
        }

    }

    public static void main(String[] args) {

        MergeDynamicDateMetric function = new MergeDynamicDateMetric();

        MergeAcc acc1 = new MergeAcc();

        function.accumulate(acc1, "20231126", 3, "active", "1", "cost", "1");
        function.accumulate(acc1, "20231128", 3, "active", "2", "cost", "1");
        function.accumulate(acc1, "20231125", 3, "active", "3", "cost", "1");
        function.accumulate(acc1, "20231127", 3, "active", "4", "cost", "1");

        acc1.updateTime.clear();

        MergeAcc acc2 = new MergeAcc();

        function.accumulate(acc2, "20231128", 3, "active", "1", "cost", "1");
        function.accumulate(acc2, "20231126", 3, "active", "2", "cost", "1");

        function.merge(acc1, Collections.singletonList(acc2));


        String value = function.getValue(acc1);

        System.out.println(value);
    }

}
