package com.alibaba.blink.udx.udtf.dynamic.express;


import com.googlecode.aviator.AviatorEvaluator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class AviatorFactoryExpression implements FactoryExpression {


    public static Logger LOG = LoggerFactory.getLogger(AviatorFactoryExpression.class);

    static {
        LOG.info("init aviator functions ------------");
    }

    public AviatorFactoryExpression() {
        LOG.info("new AviatorFactoryExpression ------------");
    }

    @Override
    public AviatorExpression compile(final String expression) {
        AviatorExpression aviatorExpress = new AviatorExpression();
        com.googlecode.aviator.Expression compile = AviatorEvaluator.compile(expression);
        aviatorExpress.setCompile(compile);
        aviatorExpress.setCalculatorExpression(expression);
        return aviatorExpress;
    }


}
