package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.AggregateFunction;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 张天龙
 * @Project: du-tech-data-udf
 * @Package com.alibaba.blink.udx.udf
 * @date Date : 2023年02月22日 14:42
 * 需求：
 * 逻辑：
 */
public class ALGO_COLLECT_LIST extends AggregateFunction<Map<String, String>[], List<Map<String, String>>> {

	public void retract(List<Map<String, String>> acc, Map<String, String> conlum) {
		acc.remove(conlum);
	}

	public void accumulate(List<Map<String, String>> acc, Map<String, String> conlum) {
		acc.add(conlum);
	}

	public void resetAccumulator(List list) {
		list.clear();
	}

	@Override
	public Map<String, String>[] getValue(List<Map<String, String>> maps) {
		return maps.toArray(new Map[0]);
	}

	@Override
	public List<Map<String, String>> createAccumulator() {
		return new ArrayList<>();
	}


}
