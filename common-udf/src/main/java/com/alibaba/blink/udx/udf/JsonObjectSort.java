package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.LoggerFactory;

/**
 * Author: WangLin
 * Date: 2022/9/26 下午6:38
 * Description: ads层指标unique生成udf
 */
public class JsonObjectSort extends ScalarFunction {

	private static org.slf4j.Logger logger = LoggerFactory.getLogger(JsonObjectSort.class);

	public String eval(String dimValue, String metricName) {

		try {
			JSONObject jsonObject = JSON.parseObject(dimValue);
			String sortJson = JSON.toJSONString(jsonObject, SerializerFeature.MapSortField);
			return DigestUtils.md5Hex(sortJson + "_" + metricName);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
