package com.alibaba.blink.udx.udaf.acc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.table.annotation.DataTypeHint;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class HashSetAcc {

	@DataTypeHint("RAW")
	public HashSet<String> arrayDistinct;

	public HashSetAcc() {
		arrayDistinct = new HashSet<>();
	}

	public void add(String element) {
		try {
			if (element.startsWith("contentId:")) {
				arrayDistinct.add(element.substring(10));
			} else {
				List<String> elementList = JSONArray.parseArray(element, String.class);
				arrayDistinct.addAll(elementList);
			}
		} catch (JSONException e) {
			//不是数组格式
			System.out.println("json array parse error : " + e);
			arrayDistinct.add(element);
		}
	}

	public String getValue() {
		return arrayDistinct.toString();
	}

	public static void main(String[] args) {
		HashSetAcc hashSetAcc = new HashSetAcc();
		hashSetAcc.add("[\"SITE_SET_KANDIAN\",\"SITE_SET_MOBILE_UNION\",\"SITE_SET_QQ_MUSIC_GAME\",\"SITE_SET_TENCENT_NEWS\",\"SITE_SET_TENCENT_VIDEO\"]");
		System.out.println(hashSetAcc.getValue());
	}

}
