package com.alibaba.blink.udx.udaf.acc;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.api.common.typeinfo.Types;

/**
 * Created on 2022/8/19.
 *
 * <AUTHOR>
 */
public class WideJsonAcc {
    public MapView<String,String> viewMap;
    public WideJsonAcc() {
        this.viewMap = new MapView<>(Types.STRING,Types.STRING);
    }
    public void add(String metric_name,String metric_value)  {
        try {
            viewMap.put(metric_name,metric_value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String toString() {
        try {
            JSONObject jsonObject = new JSONObject();
            viewMap.entries().forEach(x->{
                jsonObject.put((String) x.getKey(),x.getValue());
            });
            return jsonObject.toJSONString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void merge(WideJsonAcc othAcc){
        try {
            othAcc.viewMap.entries().forEach(x->{
                add((String) x.getKey(),(String) x.getValue());
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
