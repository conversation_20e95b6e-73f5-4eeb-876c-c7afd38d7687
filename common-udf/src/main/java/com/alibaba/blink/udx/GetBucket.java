package com.alibaba.blink.udx;


import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 * @Date 2021/4/15 1:54 下午
 */
public class GetBucket extends ScalarFunction {

    public Long eval(Long userId){
        String md5Hex = DigestUtils.md5DigestAsHex(userId.toString().getBytes());
        return Math.abs(Long.parseUnsignedLong(md5Hex.substring(md5Hex.length() - 16, md5Hex.length() - 1), 16)) % 100;

    }


}
