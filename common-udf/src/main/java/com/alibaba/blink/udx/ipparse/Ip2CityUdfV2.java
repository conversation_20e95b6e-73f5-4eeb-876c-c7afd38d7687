package com.alibaba.blink.udx.ipparse;

import java.io.IOException;
import java.net.InetAddress;
import java.util.regex.Pattern;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.blink.udx.ipparse.exception.IPFormatException;
import com.alibaba.blink.udx.ipparse.exception.IpTypeException;
import com.alibaba.blink.udx.ipparse.utils.AWReader;
import com.alibaba.blink.udx.ipparse.utils.CityParser;

@FunctionHint(
		input = {@DataTypeHint("STRING"), @DataTypeHint("INT")},
        output = @DataTypeHint("STRING")
)
public class Ip2CityUdfV2 extends ScalarFunction {
    final Logger Log = LoggerFactory.getLogger(getClass());
    private static AWReader awReader = null;

    private static CityParser cityParser;
    private static String regex = "^(((\\d{1,2})|(1\\d{2})|(2[0-4]\\d)|(25[0-5]))\\.){3}((\\d{1,2})|(1\\d{2})|(2[0-4]\\d)|(25[0-5]))$";
    private static Pattern pattern = Pattern.compile(regex);

    @Override
    public void open(FunctionContext context) throws Exception {
        try{
//            // 因为jar无法读取文件,复制创建临时文件
//            // /dump/1/nm-local-dir/usercache/admin/appcache/application_1623921146842_2185/container_e03_1623921146842_2185_01_000003/temp/ip2region.db
//            String tmpDir = System.getProperty("user.dir") + File.separator + "temp";
//            String dbPath1 = tmpDir + File.separator + "IP_city_single_BD09_WGS84_ipv6.awdb";
//            System.out.println(dbPath1);
//            Log.info("init ip region db path [{}]", dbPath1);
//            File file1 = new File(dbPath1);
//            String dbPath2 = tmpDir + File.separator + "IP_city_single_BD09_WGS84_ipv6.awdb";
//            System.out.println(dbPath1);
//            Log.info("init ip region db path [{}]", dbPath2);
//            File file2 = new File(dbPath2);
//
//
//            InputStream inputStream = Ip2CityUdfV2.class.getClassLoader().getResourceAsStream("IP_city_single_BD09_WGS84_ipv6.awdb");
//            FileUtils.copyInputStreamToFile(inputStream, file1);
//            InputStream inputStream2 = Ip2CityUdfV2.class.getClassLoader().getResourceAsStream("mydata4vipday2_cn.ipdb");
//            FileUtils.copyInputStreamToFile(inputStream2, file2);
//
//            awReader = new AWReader(file1);
//            cityParser = new CityParser("");
            awReader = new AWReader(Ip2CityUdfV2.class.getClassLoader().getResourceAsStream("IP_city_single_BD09_WGS84_ipv6.awdb"));
            cityParser = new CityParser(Ip2CityUdfV2.class.getClassLoader().getResourceAsStream("mydata4vipday2_cn.ipdb"));

        }catch (Exception e){
            e.printStackTrace();
        }
        super.open(context);
    }

    public static boolean matchIPV4(String sourceText) {
        if (sourceText == null)
            return false;
        return pattern.matcher(sourceText).find();
    }

    public static String[] parseIPV4(String ip) {
        String[] arr = null;
        try {
            arr = cityParser.find(ip, "CN");
        } catch (IOException | IPFormatException ex) {
            ex.printStackTrace();
        }
        return arr;
    }

    public static String[] parseIPV6(String ip){
        String ret[] = {"","",""};
        try{
            InetAddress address = InetAddress.getByName(ip);
            JsonNode record = awReader.get(address);
            if(record==null){
                return null;
            }else{

                JsonNode country = record.get("country");
                if (country != null) {
                    ret[0] = country.asText();
                }

                JsonNode province = record.get("province");
                if (province != null) {
                    ret[1] = province.asText().replaceFirst("(省|市)$","");
                }

                JsonNode city = record.get("city");
                if (city != null) {
                    ret[2] = city.asText().replaceFirst("市$","");
                }
            }
        } catch (IpTypeException exception) {
            exception.printStackTrace();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return ret;
    }

    public String eval(String ip, Integer level) {
        String ret = null;
        if (matchIPV4(ip)) {
            String[] ipv4 = parseIPV4(ip);
            ret = ipv4[level-1];
        }else{
            String[] ipv6 = parseIPV6(ip);
            ret = ipv6[level-1];
        }
        return ret;
    }
}
