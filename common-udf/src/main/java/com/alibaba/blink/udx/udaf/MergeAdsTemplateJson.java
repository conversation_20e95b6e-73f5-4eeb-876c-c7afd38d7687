package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;

import java.util.*;
import java.util.function.Consumer;

/**
 *   <p>合并ads dim_value一样的数据
 *
 *    <p>1.每个指标根据ts 大小 取last_value
 *    <p>2.默认透出ext dim_value metric_value 中的数据 todo:key 不能重复
 *    <p>3.要求 ts 不为null
 *
 * <p>metric_name 不同的case:
 * <pre>{@code
 * case 1:
 *    {
 *     "dim_name": "account_id,date,device_type,media_id,operator,plan_id",
 *     "ext": {
 *         "dim_type": "planDim"
 *     },
 *     "metric_name": "test_01",
 *     "unique_key": "a7653d73f0f0690c385c1eebe1c88569",
 *     "track_id": "f6fbc649-1b0e-430f-9a70-b5d9f325f688",
 *     "metric_value": {
 *         "pay_ue_24h": null,
 *         "cost": 7173.0
 *     },
 *     "job": "test_01",
 *     "dim_value": {
 *         "date": "********",
 *         "account_id": "****************",
 *         "operator": "刘浩南"
 *     },
 *     "ts": *************
 *
 *   case 2:
 *      {
 *      "dim_name": "account_id,date,device_type,media_id,operator,plan_id",
 *      "ext": {
 *          "dim_type": "planDim"
 *      },
 *      "metric_name": "test_02",
 *      "unique_key": "a7653d73f0f0690c385c1eebe1c88569",
 *      "track_id": "f6fbc649-1b0e-430f-9a70-b5d9f325f688",
 *      "metric_value": {
 *          "ord_uv": 22
 *      },
 *      "job": "test_02",
 *      "dim_value": {
 *          "date": "********",
 *          "account_id": "****************",
 *          "operator": "刘浩南"
 *      },
 *      "ts": *************
 *
 *   MergeAdsTemplateJson(case 2)
 *   MergeAdsTemplateJson(case 1)
 *
 *   res:
 *      {
 *      "date": "********",
 *      "account_id": "****************",
 *      "operator": "刘浩南",
 *      "ord_uv": 22,
 *      "pay_ue_24h": null,
 *      "cost":9999.0,
 *      "dim_type":"planDim"
 *      }
 *
 * }
 * }</pre>
 * @Author: lmh
 * @date: 2022/10/18 11:47 上午
 */
@FunctionHint(input = @DataTypeHint("STRING"), output = @DataTypeHint("STRING"))
@Slf4j
public class MergeAdsTemplateJson extends AggregateFunction<String, MergeAdsTemplateJson.JsonAcc> {

    public static List<String> needKeys = new ArrayList<>();
    public static Boolean isFilter = false;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        isFilter = Boolean.valueOf(context.getJobParameter("MergeAdsTemplateJson.isFilter", "false"));
        needKeys = new ArrayList<String>(Arrays.asList(context.getJobParameter("MergeAdsTemplateJson.needKeys", "").split(",")));
        log.info("keys:{}",needKeys);
    }

    @Override
    public JsonAcc createAccumulator() {
        return new JsonAcc();
    }

    @Override
    public String getValue(JsonAcc accumulator) {
        return accumulator.getCache();
    }

    public void accumulate(MergeAdsTemplateJson.JsonAcc accumulator, Object value) {

        JSONObject source = object2Json(value);

        accumulator.acc(source);

    }


    public void retract(MergeAdsTemplateJson.JsonAcc accumulator, Object value) {
    }

    public void merge(MergeAdsTemplateJson.JsonAcc accumulator, Iterable<MergeAdsTemplateJson.JsonAcc> iterables) {
        for (MergeAdsTemplateJson.JsonAcc acc : iterables) {
            accumulator.merge(acc);
        }
    }

    private JSONObject object2Json(Object o) {
        //嵌套Json 避免转译
        if (o instanceof String && JSONValidator.from((String) o).validate()) {
            return JSON.parseObject((String) o);
        } else if (o instanceof JSONObject) {
            return (JSONObject) o;
        } else
            throw new RuntimeException("arg is not json. arg: " + o);
    }

    public static class JsonAcc {
        // TODO: 2022/11/10  map<metricKey ,ts>
        public MapView<String, Tuple2<String,Long>> viewMap;

        private static final String JOB = "job";
        private static final String METRIC_NAME = "metric_name";
        private static final String METRIC_VALUE = "metric_value";
        private static final String DIM_VALUE = "dim_value";
        private static final String DIM_NAME = "dim_name";
        private static final String EXT = "ext";
        private static final String TS = "ts";

        public JsonAcc() {
            this.viewMap = new MapView<>();
        }

        public String getCache() {
            JSONObject object = new JSONObject();

            try {
                viewMap.entries().forEach(stringTuple2Entry -> object.put(stringTuple2Entry.getKey(),stringTuple2Entry.getValue().f0));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return JSON.toJSONString(object,SerializerFeature.WriteMapNullValue);

        }

        /**
         * 默认将 metric_value ext dim_value 透出
         * */
        public void setCache(JSONObject cache) {

            JSONObject metrics = cache.getJSONObject(METRIC_VALUE);
            JSONObject ext = cache.getJSONObject(EXT);
            JSONObject dim = cache.getJSONObject(DIM_VALUE);

            Long ts = cache.getLong(TS);

            metrics.forEach((s, o) -> setValue(s, o, ts));

            ext.forEach((s, o) -> setValue(s, o, ts));

            dim.forEach((s, o) -> setValue(s, o, ts));

        }

        private void setValue(String s, Object o, Long ts) {

            if (!filter(s)){
                return;
            }

            try {
                if (viewMap.contains(s)){

                    Tuple2<String, Long> tuple2 = viewMap.get(s);
                    if (tuple2.f1 < ts || tuple2.f1.equals(ts)){
                        tuple2.f0 = String.valueOf(o);
                        tuple2.f1 = ts;
                    }

                    viewMap.put(s,tuple2);

                }else {
                    viewMap.put(s,Tuple2.of(String.valueOf(o),ts));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }


        }

        public boolean filter(String key){

            if (isFilter){
                return needKeys.contains(key);
            }
            return true;
        }

        public boolean isBlankOrNull(JSONObject value) {
            return Objects.isNull(value) || value.isEmpty();
        }

        public void acc(JSONObject value) {
            if (!isBlankOrNull(value)) {
                setCache(value);
            }
        }

        public void merge(JsonAcc otherAcc) {
            try {
                for (Map.Entry entry : otherAcc.viewMap.entries()) {
                    String key = (String) entry.getKey();

                    Tuple2<String, Long> otherMetric = (Tuple2<String, Long>) entry.getValue();

                    if (this.viewMap.contains(key)) {

                        Tuple2<String, Long> thisMetric = this.viewMap.get(key);
                        if (thisMetric.f1 < otherMetric.f1 || thisMetric.f1.equals(otherMetric.f1))
                        this.viewMap.put(key, otherMetric);

                    } else
                        this.viewMap.put(key, otherMetric);
                }

            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }

    }

    public static void main(String[] args) {
        MergeAdsTemplateJson mergeAdsTemplateJson = new MergeAdsTemplateJson();
        JsonAcc jsonAcc = new JsonAcc();

        String v1 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"be7e3e7c-3b38-4201-8c8b-259d3c108ab6\",\"metric_value\":{\"ord_uv\":null,\"pay_ue_24h\":null,\"cost\":1.0,\"remain_cnt_1d\":null,\"orderCAC\":null,\"productExposureRate\":null,\"pay_ue\":null,\"activationCAC\":null,\"click\":0.0,\"roi\":null,\"real_cost\":0.****************,\"ltv\":null,\"charge1\":null,\"gmv1\":null,\"view\":3.0,\"ad_site\":\"[]\",\"order_count1\":null,\"roi24\":null,\"first_day_spu_exposure_cnt\":null,\"activate\":null,\"retain2Rate\":null,\"order_count_24h\":null},\"job\":\"ads_dewu_increase_metrics_about_rule_engine\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        String v2 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"b06b8740-1dd4-48d0-bf8e-ac46671e12ec\",\"metric_value\":{\"agg5Day_activate\":0,\"agg7Day_activate\":0},\"job\":\"ads_dewu_interface_hard_ad_metric_about_agg7day\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        String v3 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"b050fffe-172f-4358-bad7-938a66d99f9b\",\"metric_value\":{\"agg5Day_activate\":1,\"agg7Day_activate\":1},\"job\":\"ads_dewu_interface_hard_ad_metric_about_agg7day\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        JsonAcc jsonAcc2 = new JsonAcc();
        mergeAdsTemplateJson.accumulate(jsonAcc, v1);
        mergeAdsTemplateJson.accumulate(jsonAcc, v3);
        mergeAdsTemplateJson.accumulate(jsonAcc2, v2);

        ArrayList<JsonAcc> jsonAccs = new ArrayList<>();
        jsonAccs.add(jsonAcc);
        jsonAccs.add(jsonAcc2);
        JsonAcc jsonAcc1 = new JsonAcc();

        mergeAdsTemplateJson.merge(jsonAcc1, jsonAccs);
        System.out.println(mergeAdsTemplateJson.getValue(jsonAcc1));
    }

}
