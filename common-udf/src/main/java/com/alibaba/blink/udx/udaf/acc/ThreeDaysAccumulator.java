package com.alibaba.blink.udx.udaf.acc;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;

public class ThreeDaysAccumulator {
    private static final Logger logger = LoggerFactory.getLogger(ThreeDaysAccumulator.class);

    @DataTypeHint("RAW")
    public HashMap<String,Long> daySumHash;

    public ThreeDaysAccumulator() {
        daySumHash = new HashMap<>();
    }

    public void add(String formatDay,Long dayChangedQty) {
        daySumHash.put(formatDay,dayChangedQty);
    }

    public String getValue() {
        try{
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

            Date beforeYestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 86400 * 2 ),  "yyyyMMdd"));
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            calendar.setTime(beforeYestDate);
            String beforeYestday = dateFormat.format(calendar.getTime());

            Date yestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 86400), "yyyyMMdd"));
            calendar.setTime(yestDate);
            String yestday = dateFormat.format(calendar.getTime());

            Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyyMMdd"));
            calendar.setTime(date);
            String today = dateFormat.format(calendar.getTime());

            Long beforeYestdayQty = daySumHash.getOrDefault(beforeYestday, 0L);
            Long yestdayQty = daySumHash.getOrDefault(yestday, 0L);
            Long todayQty = daySumHash.getOrDefault(today, 0L);

            JSONObject json = new JSONObject();
            json.put("sum_one_day",todayQty);
            json.put("sum_two_days",todayQty+yestdayQty);
            json.put("sum_three_days",todayQty+yestdayQty+beforeYestdayQty);

            return JSONObject.toJSONString(json);
        } catch (Exception e) {
            logger.error("get value failed.", e);
            throw new RuntimeException("get Json error:");
        }

    }

}