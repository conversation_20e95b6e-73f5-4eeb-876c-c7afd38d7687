package com.alibaba.blink.udx.udaf.acc;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Created on 2022/4/13.
 *
 * <AUTHOR>
 */

public class FixedTimeBucketAcc {
    private static final Logger logger = LoggerFactory.getLogger(FixedTimeBucketAcc.class);

    @DataTypeHint("RAW")
    public  TreeMap<String, AtomicLong> bucket = new TreeMap<>();

    public FixedTimeBucketAcc() {
    }

    public FixedTimeBucketAcc(long timeStep) {
        if (timeStep < 0 || 86400 % timeStep != 0) {
            throw new RuntimeException("Error with illegal timeStep[%s], it should gt 0 and also 86400 should be divided evenly");
        }
        initBucket(timeStep);
    }

    private void initBucket(long timeStep) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
            calendar.setTime(date);
            int size = (int) (86400 / timeStep);
            dateFormat.applyPattern("HH:mm:ss");
            while (bucket.size() < size) {
                bucket.put(dateFormat.format(calendar.getTime()), new AtomicLong(0L));
                calendar.add(Calendar.SECOND, (int) timeStep);
            }
            // logger.warn(bucket.toString());
        } catch (Exception e) {
            logger.error("timeBucket init failed.", e);
        }
    }

    public boolean update(String key, long value) {
        if (bucket.containsKey(key)) {
            SortedMap<String, AtomicLong> subMap = bucket.tailMap(key);
            for (AtomicLong lastValue : subMap.values()) {
                lastValue.addAndGet(value);
            }
            return true;
        }
        logger.error(String.format("found %s,\nbut need %s", bucket.keySet(), key));
        return false;
    }

    public void merge(FixedTimeBucketAcc other) {
        if (null != other) {
            if (bucket.size() != other.getSize() || !bucket.keySet().containsAll(other.bucket.keySet())) {
                throw new RuntimeException(String.format("FixedTimeBucketAcc bucket keys not satisfied when merge with \n%s\n%s", bucket, other.bucket));
            }
            for (String key : bucket.keySet()) {
                bucket.get(key).addAndGet(other.bucket.get(key).get());
            }
        } else {
            logger.error("merge with null.");
        }
    }

    public int getSize() {
        return bucket.size();
    }

    public String getSnapshot() {
        return JSON.toJSONString(bucket);
    }
}