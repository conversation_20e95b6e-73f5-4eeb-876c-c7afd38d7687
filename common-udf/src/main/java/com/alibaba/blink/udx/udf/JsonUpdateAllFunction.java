package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.guava30.com.google.common.cache.Cache;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/8/7 23:08
 */

@Slf4j
public class JsonUpdateAllFunction extends ScalarFunction {

    private static final JSONObject EMPTY_JSON_OBJECT = new JSONObject();

    public String eval(String originJson, String addJson, String... needKeys) {
        if (originJson == null) {
            originJson = "{}";
        }
        if (addJson == null) {
            addJson = "{}";
        }

        JSONObject originObject = getJsonObject(originJson), addObject = getJsonObject(addJson);
        Set<String> keys = new HashSet<>(Arrays.asList(needKeys));

        for (Map.Entry<String, Object> entry : addObject.entrySet()) {
            String key = entry.getKey();
            if (keys.contains(key)) {
                originObject.put(key, entry.getValue());
            }
        }

        return originObject.toString();
    }

    public String eval(String originJson, String addJson) {
        if (originJson == null) {
            originJson = "{}";
        }
        if (addJson == null) {
            addJson = "{}";
        }

        JSONObject originObject = getJsonObject(originJson), addObject = getJsonObject(addJson);
        originObject.putAll(addObject);

        return originObject.toString();
    }


    // 初始化 Guava Cache
    private static final Cache<String, JSONObject> CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000) // 最大缓存条数
            .expireAfterWrite(10, TimeUnit.MINUTES) // 缓存过期时间
            .build();

    // 返回指定JSON字符串解析后的JSONObject对象，第一次解析后会进行缓存
    private JSONObject getJsonObject(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            return EMPTY_JSON_OBJECT;
        }

        try {
            return CACHE.get(jsonString, () -> JSON.parseObject(jsonString));
        } catch (ExecutionException e) {
            log.error("getJsonObject error: {}", e.getMessage());
            return EMPTY_JSON_OBJECT;
        }
    }



}
