package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s STRING>")
)
/**
 * 将内jsonarr合并成单条json
 */
public class MergeConvertSingle extends TableFunction<Row> {

    private static final Logger logger = LoggerFactory.getLogger(MergeConvertSingle.class);
    private static final int BASE_ROW_SIZE = 1;
    private static final Gson gson = new Gson();

    public void eval(String message) {
        Row row = new Row(BASE_ROW_SIZE);
        Row emptyRow = new Row(BASE_ROW_SIZE);
        try {
            if (Objects.nonNull(message)) {
                if (isValidObject(message)) {
                    row.setField(0, message);
                    collect(row);
                } else if (isValidArray(message)) {
                    JSONArray outJsonArr = JSONArray.parseArray(message);
                    if (outJsonArr.size() > 0) {
                        outJsonArr.forEach(r -> {
                            JSONArray inJsonarr = (JSONArray) r;
                            ObjectMapper mapper = new ObjectMapper();
                            Map<String, Object> jsonMap = new HashMap<>();
                            inJsonarr.forEach(j -> {
                                try {
                                    Map<String, Object>  jsonContent = mapper.readValue(j.toString(), Map.class);
                                    jsonContent.forEach((k, v) -> {
                                        if (jsonMap.containsKey(k)) {
                                            // 处理重复字段，优先报错提示用户不能使用重复字段
                                            throw new RuntimeException(String.format("Find an Duplicate definition error,please check table fields alias name : %s", k));
                                        } else {
                                            jsonMap.put(k, v);
                                        }
                                    });
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }

                            });
                            row.setField(0, JSONObject.toJSON(jsonMap).toString());
                            collect(row);
                        });
                    } else {
                        collect(emptyRow);
                    }

                } else {
                    System.out.println("message is not object and array :" + message);
                    collect(emptyRow);
                }
            } else {
                collect(emptyRow);
            }
        } catch (JSONException e) {
            throw new RuntimeException(String.format("message is  %s, parse error:  %s !", message, e.getCause()));
        }
    }

    private boolean isValidObject(String value) {
        if (value.trim().startsWith("[")) {
            return false;
        }
        try {
            gson.fromJson(value, JSONObject.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidArray(String value) {
        try {
            gson.fromJson(value, JSONArray.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {
        String s = "[[\"{\\\"uniquekey\\\":1,\\\"tablea\\\":\\\"m\\\",\\\"a_1\\\":\\\"b_1\\\"}\",\"{\\\"uniquekeyb\\\":2,\\\"tableb\\\":\\\"m\\\",\\\"b\\\":\\\"1\\\"}\",\"{\\\"uniquekeyc\\\":5,\\\"tablec\\\":\\\"m\\\",\\\"c\\\":\\\"hh\\\"}\"],[\"{\\\"uniquekey\\\":1,\\\"tablea\\\":\\\"m\\\",\\\"a_1\\\":\\\"b_1\\\"}\",\"{\\\"uniquekeyb\\\":3,\\\"tableb\\\":\\\"m\\\",\\\"b\\\":\\\"3\\\"}\",\"{\\\"uniquekeyc\\\":5,\\\"tablec\\\":\\\"m\\\",\\\"c\\\":\\\"hh\\\"}\"]]";
//        JSONArray outJsonArr = JSONArray.parseArray(s);
//        System.out.println( outJsonArr.size());

        new MergeConvertSingle().eval(s);


    }
}