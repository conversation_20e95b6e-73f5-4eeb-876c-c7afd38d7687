package com.alibaba.blink.udx.udtf;

import com.alibaba.blink.udx.entity.AdServingBean;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;


/**
 * 功能描述
 *
 * @param
 * <AUTHOR>
 * @date 2021/8/15
 * @return
 */
@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s0 STRING, s1 STRING, s2 STRING, s3 STRING>")
)
@Slf4j
public class SLSParserFunctionV1 extends TableFunction<Row> {

    private static final Logger logger = LoggerFactory.getLogger(SLSParserFunctionV1.class);

    private static int num=0;

    /**
     * 功能描述
     * [algo_tags_false]tags:{CategoryLv1Id:[] CategoryLv2Id:[] CategoryLv3Id:[] BrandId:[] Sex:[]},request:map[ad_name:adq+社区投放+穿搭+空+代理自产+安利+横版大图+空+空+代理选品+0726+包3+得物2+sl+0726-3 ad_site:25 adgroup_id:3730747438 adgroup_name:adq+社区投放+穿搭+空+代理自产+安利+横版大图+空+空+代理选品+0726+包3+得物2+sl+0726-3 advertiser_id:19182370 agent_id:19498993 aid:19182370
     * algo_tags: android_id:ef1107128fde07c69aa207f93d37ab48 app_id:duapp app_type:android appid:1104972716 campaign_id:3729958325 campaign_name:adq-社区-横版大图+穿搭-包3-常规多-20210726-1 channel_package_id:000116083935************ cid:492940164 click_id:iptrqyi7aaafmhzvalma click_sku_id:2306237453218746709 click_time:1629022050 content_id: creative_id:492940164 creative_name:动态创意_202107261433211 deeplink_url: dest_url:http://www.myapp.com device_os_type:android device_os_version:11 event_time:1629022050 four_channel:adq gid:3729958325 imei:6c4650d19278b1116b5cea6994710c37 impression_id:gmdspnqo7hjvk01
     * ip:************* ipua:2f6fb6cd2dce96aead0ada5778c813b8 ipv6: jump_tag: media:guangdiantong module_type:0 muid:6c4650d19278b1116b5cea6994710c37 oaid:939D825EE2C41EBBA3D41AE534F42D774C1D4C5B8BEF7DF3EE37A6D9843D6B62 os:android package_name:3730747438 pid:3730747438 process_time:1629022019 product_type:12 request_id:gmdspnqo7hjvk rid:iptrqyi7aaafmhzvalma site_set:25 site_set_name:SITE_SET_QQ_MUSIC_GAME tags: tid:9641ef2fddf28a81165cd088cf22de9d ua:Dalvik/2.1.0 (Linux; U; Android 11; PD2068B Build/RP1A.200720.012) user_agent:Dalvik/2.1.0 (Linux; U; Android 11; PD2068B Build/RP1A.200720.012)]
     *
     *   String message = "[algo-tags]value:map[algo:{\"category_lv1_id\":[\"29\"],\"category_lv2_id\":null,\"category_lv3_id\":[\"31\"],\"brand_id\":null,\"sex\":\"\"} content: spu: tags:],request:&{UserId:1678273399 AppId: Os:android PkgChannel:huawei Imei: Oaid:*************-4f7b-b1a7-bffa68b19b55 AndroidId:58d7fa084a7d0551 Idfa: Fcuuid: Uuid:58d7fa084a7d0551 Clipboard: Ua: Ip:***************},isNew:false";
     *   Sex的消息格式不统一，有的为数组，有的为字符串，然后需求不需要提取sex标签，该标签就不冗余进来了。
      *功能描述
      * <AUTHOR>
      * @date 2021/8/18
      * @param
      * @return
      */
    public void eval(String message) throws Exception {
        try {
            Gson gson = new Gson();
            AdServingBean adServingBean = gson.fromJson(message, AdServingBean.class);
            Row row = new Row(4);
            row.setField(0, ArraysToString(adServingBean.getCategory_lv1_id()));
            row.setField(1, ArraysToString(adServingBean.getCategory_lv2_id()));
            row.setField(2, ArraysToString(adServingBean.getCategory_lv3_id()));
            row.setField(3, ArraysToString(adServingBean.getBrand_id()));
            collect(row);
        } catch (Exception e) {
//            num++;
//            e.printStackTrace();
//            ByteArrayOutputStream stream = new ByteArrayOutputStream();
//            e.printStackTrace(new PrintStream(stream));
//            String exception = stream.toString();
//            logger.error("message:{}",message,e);
//            logger.warn("-------------------------------err message num:{}",num);
//            logger.warn("-------------------------------err message num:{}",num);
//            logger.warn("-------------------------------err message num:{}",num);
//            throw new Exception(exception + " message:" + message);
        }
    }

    public static String ArraysToString(Object[] a) {
        if (a == null)
            return null;

        int iMax = a.length - 1;
        if (iMax == -1)
            return null;

        StringBuilder b = new StringBuilder();
        for (int i = 0; ; i++) {
            b.append(String.valueOf(a[i]));
            if (i == iMax)
                return b.toString();
            b.append(",");
        }
    }

    //{"os":"android","device_uuid":"aafd689ac53f3dbb","category_lv1_id":["2"],"category_lv2_id":null,"category_lv3_id":null,"brand_id":null,"sex":"","is_new":true,"cspu_id":null,"material_strategy_laoyue_id":""}
    public static void main(String[] args) throws Exception {
        String message = "[algo-tags]value:map[algo:{\"category_lv1_id\":[],\"category_lv2_id\":null,\"category_lv3_id\":[\"31\",\"33\"],\"brand_id\":[\"33\"],\"sex\":\"\"} content: cspu: tags:],request:&{UserId:6184357 AppId:duapp Os:ios PkgChannel: Imei: Oaid: AndroidId:null Idfa:BA221151-495B-4030-BB85-D669D0583BEC Fcuuid:32c5bdad1cef48c899a2150a4ffdfd18 Uuid:BA221151-495B-4030-BB85-D669D0583BEC Clipboard: Ua:Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Ip:*************** Source:activate EventTime:1630917196 ShumeiId: OrderNo:},isNew:false";
        message = "{\"content_id\":null,\"content_name\":null,\"category_lv1_id\":[\"92\"],\"category_lv2_id\":[\"421\"],\"category_lv3_id\":[\"117\"],\"category_lv1_name\":[\"配件\"],\"category_lv2_name\":[\"首饰\"],\"category_lv3_name\":[\"项链\"],\"brand_id\":[\"1009252\"],\"brand_name\":[\"BRILLPIECE\"],\"ad_site\":[\"INVENTORY_UNION_SLOT\"],\"ad_type\":[\"CREATIVE_IMAGE_MODE_LARGE\"],\"content_tag_id\":null,\"material_strategy_laoyue_id\":null,\"cspu_id\":null}";
//        message="[algo-tags]value:map[algo:{\"rid\":\"543310a27ec344ffb4b5fa771b9c4b89u7335\",\"media\":\"toutiao\",\"ad_site\":[\"INVENTORY_UNION_SLOT\"],\"ad_type\":[\"CREATIVE_IMAGE_MODE_VIDEO_VERTICAL\"],\"aid\":\"1718028506633229\",\"gid\":\"1721904684714019\",\"pid\":\"1721929362077764\",\"cid\":\"1721929851175016\",\"ad_name\":\"唤醒投放+服装+优惠券+常规+服饰会场+混剪+通用+通用+20211205+CDN+1\",\"mac\":\"\",\"os\":\"android\",\"ua\":\"Mozilla/5.0 (Linux; Android 11; M2006J10C Build/RP1A.200720.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36 hap/1.9/xiaomi com.miui.hybrid/******* com.dianzhong.hmxs/5.5.2.586 ({\\\"packageName\\\":\\\"com.tencent.submarine\\\",\\\"type\\\":\\\"url\\\",\\\"extra\\\":{}})\",\"ip\":\"***************\",\"app_id\":\"duapp\",\"module_type\":\"\",\"package_name\":\"\",\"four_channel\":\"\",\"model\":\"\",\"device_uuid\":\"8c8b0380e06122b7\",\"category_lv1_id\":[\"2\"],\"category_lv2_id\":null,\"category_lv3_id\":null,\"brand_id\":null,\"sex\":\"\",\"is_new\":false,\"cspu_id\":null,\"material_strategy_laoyue_id\":\"\"} content: cspu: tags:],request:&{UserId:1515035782 AppId:duapp AppVersion: Os:android PkgChannel:xiaomi Imei: Oaid:99c9279d24d7741d AndroidId:8c8b0380e06122b7 Idfa: Fcuuid: Uuid:8c8b0380e06122b7 Clipboard: Ua:/duapp/4.82.0(android;11) Ip:*************** Ipvx:*************** Source:activate_app EventTime:1642748563 ShumeiId: OrderNo: Ipua:079442fbf48977e37feb2dba4ba90eda LastActiveTime:1640239721000 DayFirstActiveTime:1642748563},isNew:false,media:toutiao,response:&{UserId:1515035782 Os:Android DeviceType:oaid ModuleType: ProjectId: ContentId: ContentTagId: Tags: IsNew:false},os-Android,module-,type-oaid";
        SLSParserFunctionV1 slsParserFunction = new SLSParserFunctionV1();

        slsParserFunction.eval(message);

    }

}
