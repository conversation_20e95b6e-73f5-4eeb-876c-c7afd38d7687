package com.alibaba.blink.udx.rockmq.utils;

import com.alibaba.blink.udx.rockmq.bean.*;
import com.alibaba.blink.udx.rockmq.consts.Consts;
import com.google.gson.Gson;

import java.util.HashMap;
import java.util.Map;

public class SinkMessageUtils {

    private static Gson gson = new Gson();

    public static SinkMessage sendCallbackMediaKafka(QueryReq params, Map<String, String> data, String eventType) {
        Map<String, String> sendMess=new HashMap<>();
        sendMess.put("data",gson.toJson(data));
        sendMess.put("params",gson.toJson(params));
        sendMess.put("event_type",eventType);
        SinkMessage sinkMessage = new SinkMessage(Consts.CALL_BACK_KAFKA, Consts.CALL_BACK_KAFKA_TOPIC, gson.toJson(sendMess));
        sinkMessage.setMessageKey(params.getUuid());
        return sinkMessage;
    }


    public static SinkMessage sendKafka(QueryReq params, Map<String, String> data, String eventType) {
        data.put(Consts.Type, eventType);
        data.put(Consts.Imei, params.getImei());
        data.put(Consts.Oaid, params.getOaid());
        data.put(Consts.AndroidId, params.getAndroidId());
        data.put(Consts.Os, params.getOs());
        data.put(Consts.Idfa, params.getIdfa());
        data.put(Consts.Ua, params.getUa());

        data.put(Consts.Ip, params.getIp());
        data.put(Consts.IpUa, params.getIpua());
        data.put(Consts.EventTime, params.getEventTime() + "");
        data.put(Consts.LogTime, params.getLogTime() + "");
        return new SinkMessage(Consts.KAFKA,Consts.ATTRIBUTE_TOPIC,gson.toJson(data));
    }

    @Deprecated
    public static SinkMessage sendLtvKafka(LtvQueryDelayMessage delayMsg,double ltv) {
        Map data = new HashMap<String, Object>();
        data.put("device_uuid", delayMsg.getDeviceUuid());
        data.put("ltv", ltv);
        data.put("delayed_hour", delayMsg.getDelayedHour());
        data.put("time", System.currentTimeMillis());
        return new SinkMessage(Consts.KAFKA, Consts.LTV_TOPIC, gson.toJson(data));
    }


    /**
     *功能描述 发送到现有的 rocketmq topic中（投放目前使用的topic）
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    @Deprecated
    public static SinkMessage sendLtvActiveDelayMsg(String deviceUuid){
        Long messageTime = (System.currentTimeMillis()/1000/3600) * 3600 + 3600 * 2 + 60 * 10;
        LtvQueryDelayMessage message = new LtvQueryDelayMessage(deviceUuid,1);
        SinkMessage sinkMessage = new SinkMessage(Consts.ROCKETMQ,"",gson.toJson(message));
        sinkMessage.setDeliverTime(messageTime);
        sinkMessage.setMessageKey(deviceUuid);
        sinkMessage.setMessageTag(Consts.MsgTagLtvQuery);
        return sinkMessage;
    }

    /**
     *功能描述 发送到active_useraction_delay_message rocketmq（实时独立topic）
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    public static SinkMessage sendUserActionDelayMsg(QueryReq params, String eventType, Long second) {
        UserActionMessage userActionMessage = new UserActionMessage(params, eventType);
        SinkMessage sinkMessage = new SinkMessage(Consts.ROCKETMQ,eventType,gson.toJson(userActionMessage));
        sinkMessage.setDeliverTime(second*1000);
        sinkMessage.setMessageKey(params.getUuid()+System.nanoTime());
        sinkMessage.setMessageTag(Consts.MsgTagUserAction);
        return sinkMessage;
    }


    public static SinkMessage sendActiveDelayMsg(QueryReq params,Map<String,String> data){
        DelayMessage delayMessage = new DelayMessage(params,data);
        SinkMessage sinkMessage = new SinkMessage(Consts.ROCKETMQ,"AntiSpam",gson.toJson(delayMessage));
        sinkMessage.setDeliverTime((long) (6 * 60*1000));
        sinkMessage.setMessageKey(data.get(Consts.Uuid));
        sinkMessage.setMessageTag(Consts.MsgTagActiveRisk);
        return sinkMessage;
    }

    public static SinkMessage sendActiveDelayMsgDebug(QueryReq params,Map<String,String> clickData,
                                                      Map<String,String> debug,long ttl){
        DelayMessage delayMessage = new DelayMessage(params,clickData,debug,ttl);
        SinkMessage sinkMessage = new SinkMessage(Consts.ROCKETMQ,"AntiSpam",gson.toJson(delayMessage));
        sinkMessage.setDeliverTime((long) (6 * 60*1000));
        sinkMessage.setMessageKey(clickData.get(Consts.Uuid));
        sinkMessage.setMessageTag(Consts.MsgTagActiveRisk);
        return sinkMessage;
    }

    public static SinkMessage sendActiveDelayMsgShow(QueryReq params,SearchShowResult showResult,
                                                      Map<String,String> debug,long ttl){
        ShowDelayMessage delayMessage = new ShowDelayMessage(params,showResult,debug,ttl);
        SinkMessage sinkMessage = new SinkMessage(Consts.ROCKETMQ,"AntiSpam",gson.toJson(delayMessage));
        sinkMessage.setDeliverTime((long) (6 * 60*1000));
        sinkMessage.setMessageKey(showResult.getData().get(Consts.Uuid));
        sinkMessage.setMessageTag(Consts.MsgTagActiveRisk);
        return sinkMessage;
    }


    public static Map<String, String> castKafkaBeanDebug(QueryReq params, Map<String, String> data, String eventType) {
        data.put(Consts.Type, eventType);
        data.put(Consts.Imei, params.getImei());
        data.put(Consts.Oaid, params.getOaid());
        data.put(Consts.AndroidId, params.getAndroidId());
        data.put(Consts.Os, params.getOs());
        data.put(Consts.Idfa, params.getIdfa());
        data.put(Consts.Ua, params.getUa());

        data.put(Consts.Ip, params.getIp());
        data.put(Consts.IpUa, params.getIpua());
        data.put(Consts.EventTime, params.getEventTime() + "");
        data.put(Consts.LogTime, params.getLogTime() + "");
        return data;
    }
}
