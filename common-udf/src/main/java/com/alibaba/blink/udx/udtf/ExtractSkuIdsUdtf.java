package com.alibaba.blink.udx.udtf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 更严格的JSON数组验证版本
 */
@FunctionHint(output = @DataTypeHint("ROW<sku_id STRING>"))
public class ExtractSkuIdsUdtf extends TableFunction<Row> {

    private static final Pattern SKU_ID_PATTERN = Pattern.compile("\"sku_id\":(\\d+)");
    // JSON数组基本格式验证模式
    private static final Pattern JSON_ARRAY_PATTERN = Pattern.compile("^\\s*\\[.*\\]\\s*$");

    public void eval(String skuIdList) {
        if (!isValidJsonArray(skuIdList)) {
            return;
        }

        // 提取所有sku_id
        Matcher matcher = SKU_ID_PATTERN.matcher(skuIdList);
        while (matcher.find()) {
            try {
                String skuId = matcher.group(1);
                collect(Row.of(skuId));
            } catch (NumberFormatException e) {
                continue;
            }
        }
    }

    /**
     * 验证是否为有效的JSON数组格式
     */
    private boolean isValidJsonArray(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false;
        }

        String trimmed = input.trim();

        // 基本格式检查：必须以[开头且以]结尾
        if (!trimmed.startsWith("[") || !trimmed.endsWith("]")) {
            return false;
        }

        // 空数组检查
        if (trimmed.equals("[]")) {
            return false;
        }

        // 使用正则表达式进行更严格的验证
        if (!JSON_ARRAY_PATTERN.matcher(trimmed).matches()) {
            return false;
        }

        return true;
    }
}