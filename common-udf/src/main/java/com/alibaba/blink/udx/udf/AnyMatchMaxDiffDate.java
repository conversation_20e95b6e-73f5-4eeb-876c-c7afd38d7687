package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/7/31 18:19
 */
public class AnyMatchMaxDiffDate extends ScalarFunction {

    public Boolean eval(Long diff, String stringArr, String date) {

        List<String> elementList = Optional.ofNullable(stringArr)
                .map(str -> Arrays.stream(str.split(",")).collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        elementList.add(date);

        LocalDate nowDate = LocalDate.parse(date, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);

        return elementList.stream()
                .anyMatch(arg -> dateDiff(arg, nowDate) > 0 && dateDiff(arg, nowDate) <= diff);

    }

    public long dateDiff(String strDate, LocalDate nowDate) {
        LocalDate date = LocalDate.parse(strDate, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);
        return ChronoUnit.DAYS.between(date, nowDate);
    }

}
