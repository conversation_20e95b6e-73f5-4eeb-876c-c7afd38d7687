package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSONValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/3/2 17:19
 */
@Slf4j
public class JsonMergeArray extends AggregateFunction<String, List<String>> {

    public static ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public String getValue(List<String> accumulator) {
        return parseJsonStr(accumulator);
    }

    @SuppressWarnings({"all"})
    public void accumulate(List<String> accumulator, String value) throws JsonProcessingException {

        if (JSONValidator.from(value).validate()) {
            accumulator.add(value);
        } else
            log.warn(value + "is not json !");

    }

    @Override
    public List<String> createAccumulator() {
        return new ArrayList<>();
    }

    public void retract(List<String> accumulator, String value) {

    }

    public String parseJsonStr(List<String> acc) {

        if (acc.isEmpty()) {
            return "[]";
        }
        try {
            return mapper.writeValueAsString(acc);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }

    public static void main(String[] args) throws JsonProcessingException {
        JsonMergeArray a = new JsonMergeArray();
        ArrayList<String> acc = new ArrayList<>();
        String v1 = "{\"data\":[{\"id\":\"28480\",\"account_id\":\"********\",\"media_id\":\"3\",\"account_mode\":\"1\",\"system\":\"2\",\"delivery_goal\":\"1\",\"delivery_mode\":\"1\",\"operation_mode\":\"1\",\"channel\":\"快手\",\"agent_id\":\"7\",\"status\":\"1\",\"modify_time\":\"2023-03-01 14:21:40\",\"create_time\":\"2022-08-08 09:55:12\",\"delivery_type\":\"1\",\"account_remark\":\"\"}],\"database\":\"dw_delivery_marketing_api\",\"es\":*************,\"id\":********,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint(20) unsigned\",\"account_id\":\"varchar(128)\",\"media_id\":\"bigint(20) unsigned\",\"account_mode\":\"tinyint(4)\",\"system\":\"tinyint(4)\",\"delivery_goal\":\"tinyint(4)\",\"delivery_mode\":\"varchar(16)\",\"operation_mode\":\"tinyint(4)\",\"channel\":\"varchar(64)\",\"agent_id\":\"bigint(20) unsigned\",\"status\":\"tinyint(4)\",\"modify_time\":\"datetime\",\"create_time\":\"datetime\",\"delivery_type\":\"tinyint(4)\",\"account_remark\":\"varchar(256)\"},\"old\":[{\"system\":\"1\",\"modify_time\":\"2023-02-28 13:53:58\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":12,\"media_id\":-5,\"account_mode\":-6,\"system\":-6,\"delivery_goal\":-6,\"delivery_mode\":12,\"operation_mode\":-6,\"channel\":12,\"agent_id\":-5,\"status\":-6,\"modify_time\":93,\"create_time\":93,\"delivery_type\":-6,\"account_remark\":12},\"table\":\"dp_mg_account\",\"ts\":*************,\"type\":\"UPDATE\"}";
        String v2 = "{\"data\":[{\"id\":\"28480\",\"account_id\":\"********\",\"media_id\":\"3\",\"account_mode\":\"1\",\"system\":\"2\",\"delivery_goal\":\"1\",\"delivery_mode\":\"1\",\"operation_mode\":\"1\",\"channel\":\"快手\",\"agent_id\":\"7\",\"status\":\"1\",\"modify_time\":\"2023-03-01 14:21:40\",\"create_time\":\"2022-08-08 09:55:12\",\"delivery_type\":\"1\",\"account_remark\":\"\"}],\"database\":\"dw_delivery_marketing_api\",\"es\":*************,\"id\":********,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint(20) unsigned\",\"account_id\":\"varchar(128)\",\"media_id\":\"bigint(20) unsigned\",\"account_mode\":\"tinyint(4)\",\"system\":\"tinyint(4)\",\"delivery_goal\":\"tinyint(4)\",\"delivery_mode\":\"varchar(16)\",\"operation_mode\":\"tinyint(4)\",\"channel\":\"varchar(64)\",\"agent_id\":\"bigint(20) unsigned\",\"status\":\"tinyint(4)\",\"modify_time\":\"datetime\",\"create_time\":\"datetime\",\"delivery_type\":\"tinyint(4)\",\"account_remark\":\"varchar(256)\"},\"old\":[{\"system\":\"1\",\"modify_time\":\"2023-02-28 13:53:58\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":12,\"media_id\":-5,\"account_mode\":-6,\"system\":-6,\"delivery_goal\":-6,\"delivery_mode\":12,\"operation_mode\":-6,\"channel\":12,\"agent_id\":-5,\"status\":-6,\"modify_time\":93,\"create_time\":93,\"delivery_type\":-6,\"account_remark\":12},\"table\":\"dp_mg_account\",\"ts\":*************,\"type\":\"UPDATE\"}";
        a.accumulate(acc, v1);
        a.accumulate(acc, v2);

        System.out.println(a.getValue(acc));

    }

}
