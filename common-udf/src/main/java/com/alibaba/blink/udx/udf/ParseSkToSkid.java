package com.alibaba.blink.udx.udf;

import com.shizhuang.Sk2Skid;
import com.shizhuang.sk.SkCipher;
import org.apache.flink.table.functions.ScalarFunction;


/**
 * 将埋点中的SK解析成SID
 * 使用风控SDK
 */
public class ParseSkToSkid extends ScalarFunction {
    private static final long serialVersionUID = 7115580260935582054L;

    private static final String ANDROID="Android";
    private static final String IOS="iOS";
    private static final String JS="js";
    private static final String MINI_PROGRAM="MiniProgram";
    private static final String BYTEDANCE_MINI="BytedanceMini";
    private static final String QQ_MINI="QQMini";

    /**
     *
     * @param event 判断是否是海外埋点
     * @param sk    入参
     * @param lib   平台
     * @return skid sk或者lib为空直接返回null, 解析未成功会返回""，与之区别，方便定位问题；
     */
    public String eval(String event,String sk,String lib){

       // event sk 为null直接返回null
        if (checkNull(event) || checkNull(sk) ){
           return null;
        }

       //lib为null 无法判断平台 先按非法sk处理；skDecode方法解析失败，会轮询密钥解析；
        if (checkNull(lib) ){
            return Sk2Skid.skDecode(sk,SkCipher.PLATFORM_SE);
        }

        //根据平台直接来命中解析
        String password;
        //国内
        if (!event.contains("poizon")){
           switch (lib){
               case ANDROID:
                   password= SkCipher.PLATFORM_ANDROID_DEWU;
                   break;
               case IOS:
                   password= SkCipher.PLATFORM_IOS_DEWU;
                   break;
               case JS:
                   password= SkCipher.PLATFORM_H5_DEWU;
                   break;
               case MINI_PROGRAM:
                   password= SkCipher.PLATFORM_WX_DEWU;
                   break;
               case BYTEDANCE_MINI:
                   password= SkCipher.PLATFORM_DY_DEWU;
                   break;
               case QQ_MINI:
                   password= SkCipher.PLATFORM_QQ_DEWU;
                   break;
               default:
                   password=SkCipher.PLATFORM_SE;
           }
           //海外
        }else {
           switch (lib){
               case ANDROID:
                   password= SkCipher.PLATFORM_ANDROID_POIZON;
                   break;
               case IOS:
                   password= SkCipher.PLATFORM_IOS_POIZON;
                   break;
               default:
                   password=SkCipher.PLATFORM_SE;
           }
        }
        return Sk2Skid.skDecode(sk, password);

   }

   private Boolean checkNull(String input){
       return input == null || input.length() == 0;
   }

    public static void main(String[] args) {
        //h5
//        String sk = "4B2CYsdM4RnxLESvXkxBqPEJtG%2B4%2BN85%2Bqscuo9zg%2F5NCwL5veMRN2Jyh0QHhVjz";
        //抖音
        String sk="hArPEKkM3h1a85ODRMmbcq2C3XjF77uQs5Dac3f1vPp%2FV%2BSEDHnMlH8Hce9JJcWK";
        //根据参数，自主判断用哪个密钥解密
        String password = SkCipher.PLATFORM_IOS_POIZON;
        String s = new ParseSkToSkid().eval("trade_product_step_pageview", sk,"MiniProgram" );
        //转换成SID
        String skid = Sk2Skid.skDecode(sk, password);
        System.out.println(s);
    }
}


