package com.alibaba.blink.udx.udf.algo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.Meter;
import org.apache.flink.metrics.MeterView;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import pemja.core.PythonInterpreter;
import pemja.core.PythonInterpreterConfig;
import pemja.core.object.PyObject;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

import static java.util.concurrent.ThreadLocalRandom.current;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/2/3 16:37
 */
@Slf4j
public class JCPUdfFunction extends ScalarFunction {

    private final AtomicBoolean firstInit = new AtomicBoolean(false);
    private final AtomicBoolean needInit = new AtomicBoolean(true);
    private final ReentrantLock lock = new ReentrantLock();
    private transient PythonInterpreter pythonInterpreter;
    private transient PyObject pyUDFObject;
    private static final String PY_PROCESS_METHOD = "process";
    private static final String PY_OPEN_METHOD = "open";
    private static final String PY_CLOSE_METHOD = "close";
    private transient String udfUUid;
    private transient Meter meter;
    private transient Counter numOfDate;
    private transient Counter numOfRt;
    private transient long maxRt;
    private transient String JobName;
    private transient String JobTmName;
    private transient String kubernetesImage;

    @Override
    public void open(FunctionContext context) throws Exception {
        udfUUid = "o_" + UUID.randomUUID().toString().replaceAll("-", "_");

        //总处理数据
        this.numOfDate = context.getMetricGroup()
                .counter("numOfDate");
        //处理数率
        this.meter = context.getMetricGroup()
                .meter("rps/s", new MeterView(numOfDate, 1));
        //最大响应时间
        context.getMetricGroup()
                .gauge("max-rt/ms", (Gauge<Long>) () -> maxRt);
        //总响应时间
        this.numOfRt = context.getMetricGroup()
                .counter("numOfRt");
        //平均响应时间
        context.getMetricGroup()
                .gauge("avg-rt/ms", (Gauge<Long>) () -> numOfRt.getCount() / numOfDate.getCount());

        JobName = Optional.ofNullable(System.getenv("TASK_NAME")).orElse("unknown");

        JobTmName = Optional.ofNullable(System.getenv("HOSTNAME")).orElse("unknown");

        kubernetesImage = Optional.ofNullable(System.getenv("kubernetes.container.image")).orElse("unknown");

        super.open(context);
    }

    public String eval(String value, String execPath, String pythonUDFDir, String pyUDFClassName, String modelType, String pythonEnv, String... otherPath) throws Exception {

        this.tryInnitExec(execPath, pythonUDFDir, pyUDFClassName, modelType, pythonEnv, otherPath);


        this.numOfDate.inc(1L);
        long timeStart = Calendar.getInstance().getTimeInMillis();
        HashMap<String, Object> resultMap = new HashMap<>();

        resultMap.put("input", value);
        resultMap.put("task_name", JobName);
        resultMap.put("tm_name", JobTmName);
        resultMap.put("start_time", timeStart);
        resultMap.put("kubernetes_image", kubernetesImage);
        resultMap.put("track_id", UUID.randomUUID());

        try {
            Object result = this.pyUDFObject.invokeMethod(PY_PROCESS_METHOD, value);
            if (result instanceof HashMap) {
                Object o = ((HashMap<?, ?>) result).get("code");
                resultMap.put("model_code", o);
            }
            resultMap.put("code", 0);
            resultMap.put("output", result);
        } catch (Exception e) {
            resultMap.put("code", -1);
            resultMap.put("error_msg", getExceptionStackTrace(e));
            log.error("JCPUdfFunction eval Exception.input:{}", value, e);
        }

        long timeEnd = Calendar.getInstance().getTimeInMillis();
        long cost = timeEnd - timeStart;

        resultMap.put("cost_millis", cost);
        resultMap.put("ts", timeEnd);
        resultMap.put("udf_uuid", this.udfUUid);

        this.numOfRt.inc(cost);
        this.maxRt = Math.max(cost, this.maxRt);

        return JSON.toJSONString(resultMap);
    }

    public void tryInnitExec(String execPath, String pythonUDFDir, String pyUDFClassName, String modelType, String newPythonEnv, String... otherPath) throws InterruptedException {
        while (needInit.get()) {
            if (firstInit.compareAndSet(false, true)) {
                try {
                    lock.lockInterruptibly();
                    String sitePackagesPath;
                    try {
                        String out =
                                tryExecute(new String[]{execPath, "-c", "import sysconfig; print(sysconfig.get_paths()[\"purelib\"])"});
                        sitePackagesPath = String.join(File.pathSeparator, out.trim().split("\n"));
                        log.info("sitePackagesPath:{}", sitePackagesPath);
                    } catch (IOException e) {
                        throw new RuntimeException(
                                "Failed to get pemja path. You need to `pip install pemja` firstly.", e);
                    }

                    File libFile = new File(sitePackagesPath);

                    log.info("listFiles:{}", Arrays.toString(libFile.listFiles()));

                    PythonInterpreterConfig config = PythonInterpreterConfig
                            .newBuilder()
                            .setPythonExec(execPath) // specify python exec
                            .addPythonPaths(pythonUDFDir)
                            .addPythonPaths(otherPath)
                            .setExcType(modelTypeOf(modelType))
                            .build();

                    log.info("PythonInterpreter is {}", modelTypeOf(modelType).name());

                    long starInnitPythonTime = System.currentTimeMillis();
                    pythonInterpreter = new PythonInterpreter(config);
                    long endInnitPythonTime = System.currentTimeMillis();

                    log.info("Python interpreter 初始化完成,耗时:{}毫秒。appDir:{},execPath：{}.otherPath:{}", endInnitPythonTime - starInnitPythonTime, pythonUDFDir, execPath, otherPath);

                    if (StringUtils.isNotEmpty(newPythonEnv)) {
                        JSONObject cfgEnv = JSONObject.parseObject(newPythonEnv);
                        StringBuilder strCode = new StringBuilder("import os;");
                        cfgEnv.forEach((s, o) -> {
                            strCode.append("os.environ['");
                            strCode.append(s)
                                    .append("'] = '")
                                    .append(o).append("';")
                            ;
                        });
                        log.info("环境变量新增执行脚本：{}", strCode.toString());
                        pythonInterpreter.exec(strCode.toString());
                    }

                    String pythonEnv =
                            tryExecute(new String[]{execPath, "-c", "import os\n" +
                                    "env_vars = os.environ\n" +
                                    "for key, value in env_vars.items():\n" +
                                    "    print(f\"{key}: {value}\")"});

                    log.info("PythonEnv：{}", pythonEnv);

                    pythonInterpreter.exec("import " + pyUDFClassName.split("\\.")[0]);
                    pythonInterpreter.exec(udfUUid + "_jcp_udf = " + pyUDFClassName);

                    pyUDFObject = (PyObject) pythonInterpreter.get(udfUUid + "_jcp_udf");

                    long starOpenModelTime = System.currentTimeMillis();

                    boolean ready = (boolean) pyUDFObject.invokeMethod(PY_OPEN_METHOD);

                    long endOpenModelTime = System.currentTimeMillis();

                    log.info("PyUDF对象 初始化{}。耗时{}毫秒", ready, endOpenModelTime - starOpenModelTime);

                    // get current directory.
                    pythonInterpreter.exec("import os;cwd = os.getcwd();");
                    String dirAndNums = pythonInterpreter.get("cwd", String.class);
                    log.info("当前工作路径：{}", dirAndNums);

                    needInit.compareAndSet(true, false);
                } catch (Exception e) {
                    log.info("Python 初始化失败。execPath：{}.otherPath:{}", execPath, otherPath);
                    throw new RuntimeException(e);
                } finally {
                    lock.unlock();
                }
            } else {
                TimeUnit.MILLISECONDS.sleep(current().nextInt(10));
            }
        }
    }

    @Override
    public void close() throws Exception {
        try {
            pyUDFObject.invokeMethod(PY_CLOSE_METHOD);
        } catch (Exception e) {
            log.error("Close py-udf-close error. UDF-UUID:{}.", udfUUid, e);
        }
        try {
            pyUDFObject.close();
        } catch (Exception e) {
            log.error("Close py-udf error. UDF-UUID:{}.", udfUUid, e);
        }
        try {
            pythonInterpreter.close();
        } catch (Exception e) {
            log.error("Close PythonInterpreter error. UDF-UUID:{}.", udfUUid, e);
        }

        super.close();
    }

    public String tryExecute(String[] commands) throws IOException {
        ProcessBuilder pb = new ProcessBuilder(commands);
        pb.redirectErrorStream(true);
        Process p = pb.start();
        InputStream in = new BufferedInputStream(p.getInputStream());
        StringBuilder out = new StringBuilder();
        String s;
        try (BufferedReader br = new BufferedReader(new InputStreamReader(in))) {
            while ((s = br.readLine()) != null) {
                out.append(s).append("\n");
            }
        }
        try {
            if (p.waitFor() != 0) {
                throw new IOException(
                        String.format(
                                "Failed to execute the command: %s\noutput: %s",
                                String.join(" ", commands), out));
            }
        } catch (InterruptedException e) {
            // Ignored. The subprocess is dead after "br.readLine()" returns null, so the call of
            // "waitFor" should return intermediately.
        }
        return out.toString();
    }

    private PythonInterpreterConfig.ExecType modelTypeOf(String modelType) {

        if ("multi_thread".equals(modelType)) {
            return PythonInterpreterConfig.ExecType.MULTI_THREAD;
        }

        if ("sub_interpreter".equals(modelType)) {
            return PythonInterpreterConfig.ExecType.SUB_INTERPRETER;
        }

        throw new UnsupportedOperationException("未实现的model");

    }

    public static String getExceptionStackTrace(Throwable throwable) {
        try (StringWriter sw = new StringWriter()) {
            try (PrintWriter pw = new PrintWriter(sw)) {
                throwable.printStackTrace(pw);
                return sw.toString();
            }
        } catch (Exception e) {
            log.error("getExceptionStackTrace error.", e);
        }
        return throwable.toString();
    }

    public static void main(String[] args) {
        PythonInterpreterConfig config = PythonInterpreterConfig
                .newBuilder()
                .setPythonExec("/usr/local/bin/python3.9")
                .addPythonPaths("/Users/<USER>/Library/Python/3.9/lib/python/site-packages")
                .addPythonPaths("/Users/<USER>/Desktop/code/du-tech-data-udf/common-udf/src/main/python")
                .setExcType(PythonInterpreterConfig.ExecType.MULTI_THREAD)
                .build();
        PythonInterpreter pythonInterpreter = new PythonInterpreter(config);
        pythonInterpreter.exec("import test");
        pythonInterpreter.exec("jcp_udf = test.FlinkUdf()");
        PyObject pyObject = (PyObject) pythonInterpreter.get("jcp_udf");
        String value = "{\"11\":\"1\",\"22\":\"1\"}";
        long timeStart = Calendar.getInstance().getTimeInMillis();
        Object result = null;
        HashMap<String, Object> resultMap = new HashMap();
        resultMap.put("input", value);

        try {
            result = pyObject.invokeMethod("process", new Object[]{value});
            resultMap.put("code", 0);
            resultMap.put("output", result);
        } catch (Exception var13) {
            resultMap.put("code", -1);
            resultMap.put("error_msg", getExceptionStackTrace(var13));
            log.error("JCPUdfFunction Eval Exception.input:{}", value, var13);
        }

        long timeEnd = Calendar.getInstance().getTimeInMillis();
        long cost = timeEnd - timeStart;
        resultMap.put("cost_millis", cost);
        resultMap.put("ts", timeEnd);
        System.out.println(JSON.toJSONString(resultMap));
    }

}
