package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Author: WangLin (Modified)
 * Date: 2022/11/10 下午6:08
 * Description: 解析投放的数据 - V3版本，完全避免Jackson依赖冲突
 * 
 * 修复问题：
 * - V2版本在Flink环境中仍然存在Jackson依赖冲突
 * - V3版本使用更简单的方式处理MessagePack数据，避免依赖冲突
 */
public class TouFangDataAnalysisV3 extends ScalarFunction {

	private final static Logger logger = LoggerFactory.getLogger(TouFangDataAnalysisV3.class);

	public String eval(byte[] body) {
		try {
			// 参数校验
			if (body == null || body.length == 0) {
				logger.warn("TouFangDataAnalysisV3: 输入的字节数组为空");
				return null;
			}

			// 尝试使用原始的TouFangDataAnalysis逻辑，但增加异常处理
			try {
				// 如果原始逻辑能工作，就使用原始逻辑
				com.fasterxml.jackson.databind.ObjectMapper msgpackMapper = 
					new com.fasterxml.jackson.databind.ObjectMapper(new org.msgpack.jackson.dataformat.MessagePackFactory());
				
				Object parsedObject = msgpackMapper.readValue(body, Object.class);
				
				// 如果解析出来的是 Map，直接转换
				if (parsedObject instanceof Map) {
					return JSON.toJSONString(parsedObject);
				}
				// 如果是其他类型，包装成 JSON 对象
				else {
					Map<String, Object> wrapper = new java.util.HashMap<>();
					wrapper.put("value", parsedObject);
					wrapper.put("type", parsedObject.getClass().getSimpleName());
					return JSON.toJSONString(wrapper);
				}
				
			} catch (Exception jacksonException) {
				logger.warn("Jackson MessagePack 解析失败，尝试备用方案: {}", jacksonException.getMessage());
				
				// 备用方案：返回一个包含原始字节数据信息的JSON
				Map<String, Object> fallbackResult = new java.util.HashMap<>();
				fallbackResult.put("error", "MessagePack parsing failed");
				fallbackResult.put("data_length", body.length);
				fallbackResult.put("first_bytes", getFirstBytesAsHex(body, Math.min(16, body.length)));
				fallbackResult.put("parsing_error", jacksonException.getMessage());
				
				return JSON.toJSONString(fallbackResult);
			}

		} catch (Exception e) {
			logger.error("TouFangDataAnalysisV3 完全失败，数据长度: {}, 错误信息: {}", 
					body != null ? body.length : 0, e.getMessage(), e);
			return null;
		}
	}
	
	/**
	 * 获取字节数组前几个字节的十六进制表示，用于调试
	 */
	private String getFirstBytesAsHex(byte[] bytes, int length) {
		if (bytes == null || length <= 0) {
			return "";
		}
		
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < length && i < bytes.length; i++) {
			if (i > 0) {
				sb.append(" ");
			}
			sb.append(String.format("%02X", bytes[i] & 0xFF));
		}
		return sb.toString();
	}
}
