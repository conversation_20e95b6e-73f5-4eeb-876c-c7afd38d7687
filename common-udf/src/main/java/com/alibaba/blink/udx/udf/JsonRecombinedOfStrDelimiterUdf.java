package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.stream.Collectors;

/**
 * 将Json的字段拼接在一起形成一个prop
 */
public class JsonRecombinedOfStrDelimiterUdf extends ScalarFunction {

    public String eval(String jsonStr, String delimiter) {

        if (StringUtils.isBlank(jsonStr)) {
            return jsonStr;
        }

        if (JSONObject.isValid(jsonStr)) {
            return JSON.parseObject(jsonStr).getInnerMap().values().stream().map(r -> String.valueOf(r)).collect(Collectors.joining(delimiter));
        }

        return jsonStr;
    }


//    public static void main(String[] args) {
//        System.out.println(new JsonFields2StrUdf().eval("{\"1\":\"浪漫蝴蝶结手链\",\"6\":\"均码\"}"," "));
//
//    }

}
