package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 处理算法社区实时样本曝光日志和服务端日志join的结果.
 * Author: Stephen_Lin
 */
@FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING"))
public class AlgoSampleRealtimeJoinDataDeal extends ScalarFunction {

    Map<String, String> map = new HashMap<>();

    /**
     * UDF的真正执行的函数.
     *
     * @param contentId   客户端日志的内容id
     * @param contentType 客户端日志的内容类型
     * @param items       服务端日志的items【du-alg-service-log-prd.recsys-community-trance】
     * @return 匹配到的结果
     */
    public String eval(String contentId, String contentType, String items) {

        try {
            map.clear();

            String realContentType = typeChange(contentType);
            List<String> itemList = JSONArray.parseArray(items, String.class);
            for (String item : itemList) {
                JSONObject jsonObject = JSON.parseObject(item);
                String ii = jsonObject.get("ii").toString();
                String it = jsonObject.get("it").toString();
                Object ncid = jsonObject.get("ncid");
                Object cn = jsonObject.get("cn");
                Object sc = jsonObject.get("sc");
                Object ps = jsonObject.get("ps");
                if (contentId.equals(ii) && realContentType.equals(it)) {
                    map.put("ncid", ncid == null ? null : ncid.toString());
                    map.put("cn", cn == null ? null : cn.toString());
                    map.put("sc", sc == null ? null : sc.toString());
                    map.put("ps", ps == null ? null : ps.toString());
                    map.put("it", it);
                    return JSON.toJSONString(map);
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 客户端内容类型想服务端内容类型的转换.
     *
     * @param clientContentType 客户端内容类型
     * @return 服务端内容类型
     */
    public static String typeChange(String clientContentType) {
        switch (clientContentType) {
            case "1":
            case "4":
                return "trend";
            case "2":
                return "video";
            case "3":
                return "post";
            case "5":
            case "10":
                return "live";
            case "11":
                return "adv";
            default:
                return null;
        }
    }

    public static void main(String[] args) {
//		System.out.println(typeChange("1"));
//		System.out.println(typeChange("4"));
//		System.out.println(typeChange("2"));
//		System.out.println(typeChange("3"));
//		System.out.println(typeChange("5"));
//		System.out.println(typeChange("10"));
//		System.out.println(typeChange("11"));
//		System.out.println(typeChange("12"));

        AlgoSampleRealtimeJoinDataDeal algo = new AlgoSampleRealtimeJoinDataDeal();

        String s = "[{\"reason\":\"L1-indi-FIT:0.10248\",\"tag\":6466,\"ii\":\"48411182\",\"it\":\"trend\",\"cn\":\"FIT\",\"sc\":0.29221710562705996,\"pid\":3601862,\"ncid\":\"020300\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"1\",\"sd\":\"2021-04-30\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":9.0},{\"reason\":\"L1-other-WORDCF:0.025005\",\"tag\":704,\"ii\":\"47441100\",\"it\":\"trend\",\"cn\":\"WORDCF\",\"sc\":0.17748752236366273,\"pid\":43360,\"ncid\":\"020100\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":7.0},{\"reason\":\"L1-indi-TIT3:0.12816\",\"tag\":306130,\"ii\":\"46196727\",\"it\":\"trend\",\"cn\":\"TIT3\",\"sc\":0.15317785739898683,\"pid\":3379193,\"ncid\":\"010100\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":6.0},{\"reason\":\"L1-other-WORDCF:0.0526\",\"tag\":6466,\"ii\":\"46635988\",\"it\":\"trend\",\"cn\":\"WORDCF\",\"sc\":0.239615797996521,\"pid\":25986,\"ncid\":\"020300\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"1\",\"sd\":\"2021-04-30\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":9.0},{\"reason\":\"MOP:0.088\",\"tag\":253883,\"ii\":\"49467027\",\"it\":\"trend\",\"cn\":\"MOP\",\"sc\":0.088,\"pid\":3625818,\"ncid\":\"020300\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ps\":\"0\",\"fid\":\"12458\"},{\"reason\":\"L1-indi-PIT:0.06541\",\"tag\":6208,\"ii\":\"46782406\",\"it\":\"trend\",\"cn\":\"PIT\",\"sc\":0.17072364687919618,\"pid\":3293741,\"ncid\":\"020100\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":7.0},{\"reason\":\"L1-other-WORDCF:0.034575\",\"tag\":7314,\"ii\":\"48390725\",\"it\":\"trend\",\"cn\":\"WORDCF\",\"sc\":0.11512920260429383,\"pid\":18499,\"ncid\":\"010100\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":4.0},{\"reason\":\"L1-indi-PIT:0.08455\",\"tag\":6466,\"ii\":\"46804799\",\"it\":\"trend\",\"cn\":\"PIT\",\"sc\":0.23442581295967106,\"pid\":3626093,\"ncid\":\"020300\",\"sex\":1,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":9.0},{\"reason\":\"L1-indi-PIT:0.06514\",\"tag\":6466,\"ii\":\"45977331\",\"it\":\"trend\",\"cn\":\"PIT\",\"sc\":0.15357398986816407,\"pid\":3503553,\"ncid\":\"020100\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":5.0},{\"reason\":\"L1-indi-PIT:0.13881\",\"tag\":237011,\"ii\":\"45538411\",\"it\":\"trend\",\"cn\":\"PIT\",\"sc\":0.10043567419052124,\"pid\":9971,\"ncid\":\"010100\",\"sex\":1,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"1\",\"sd\":\"2021-04-30\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":8.0},{\"reason\":\"L1-other-TFT:0.007395\",\"tag\":4733,\"ii\":\"47039989\",\"it\":\"trend\",\"cn\":\"TFT\",\"sc\":0.1795119047164917,\"pid\":83056,\"ncid\":\"020300\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":4.0},{\"reason\":\"L1-other-TFT:0.00242\",\"tag\":4731,\"ii\":\"48825673\",\"it\":\"trend\",\"cn\":\"TFT\",\"sc\":0.12617549300193788,\"pid\":3692873,\"ncid\":\"020100\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":5.0},{\"reason\":\"L1-video-TIV3:0.014386372560000001\",\"tag\":3379,\"ii\":\"49174907\",\"it\":\"video\",\"cn\":\"TIV3\",\"sc\":0.07893499732017517,\"pid\":44505,\"ncid\":\"020100\",\"sex\":1,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":0.0},{\"reason\":\"L1-other-TFT:0.01591\",\"tag\":6466,\"ii\":\"48668204\",\"it\":\"trend\",\"cn\":\"TFT\",\"sc\":0.1582264006137848,\"pid\":45331,\"ncid\":\"020300\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":5.0},{\"reason\":\"STARV3:0.0\",\"tag\":4733,\"ii\":\"49582939\",\"it\":\"trend\",\"cn\":\"STARV3\",\"sc\":0.0,\"ncid\":\"020500\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ps\":\"0\"},{\"reason\":\"L1-other-TFT:0.00896\",\"tag\":6466,\"ii\":\"46342244\",\"it\":\"trend\",\"cn\":\"TFT\",\"sc\":0.1310066282749176,\"pid\":83056,\"ncid\":\"020100\",\"sex\":1,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":8.0},{\"reason\":\"L1-indi-PIV:0.05086725501\",\"tag\":649,\"ii\":\"48100382\",\"it\":\"video\",\"cn\":\"PIV\",\"sc\":0.07625007629394531,\"pid\":54332,\"ncid\":\"020403\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":0.0},{\"reason\":\"L1-kol-KTP:0.02419\",\"tag\":4733,\"ii\":\"45957751\",\"it\":\"trend\",\"cn\":\"KTP\",\"sc\":0.03501838445663452,\"pid\":56162,\"ncid\":\"020300\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":6.0},{\"reason\":\"L1-video-CIV:0.12683365032\",\"tag\":752,\"ii\":\"45790098\",\"it\":\"video\",\"cn\":\"CIV\",\"sc\":0.07388806343078613,\"ncid\":\"010100\",\"sex\":2,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":0.0},{\"reason\":\"L2-indi-PIV:0.017897411414999998\",\"tag\":2063,\"ii\":\"46099778\",\"it\":\"video\",\"cn\":\"PIV\",\"sc\":0.06668424606323242,\"pid\":10903,\"ncid\":\"010100\",\"sex\":0,\"psc\":0.0,\"vr\":0.0,\"showsize\":0,\"ct\":\"0\",\"sd\":\"\",\"sexy\":\"0\",\"ps\":\"1\",\"initial_score\":0.0}]";

        String res = algo.eval("48411182", "1", s);

        System.out.println(res);


    }

}
