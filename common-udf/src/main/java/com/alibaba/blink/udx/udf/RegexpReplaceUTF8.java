package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class RegexpReplaceUTF8 extends ScalarFunction {

    public String eval(String str,String regex,String isEncode,String lFlag,String rFlag){

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
                matcher.appendReplacement(
                        sb,
                        getReplacement(matcher, isEncode, lFlag, rFlag));
        }
        matcher.appendTail(sb);
        return sb.toString();

    }

    public String eval(String str,String regex,String isEncode,String flag){
       return eval(str,regex,isEncode,flag,flag);

    }

    public String eval(String str,String regex,String isEncode){
        return eval(str,regex,isEncode,"");
    }

    public String eval(String str,String regex){
        return eval(str,regex,"ENCODE","");
    }

    /**
     *
     * @param str 字符串
     * @return 默认将enmoji 编码成utf8并替换
     */
    public String eval(String str){
        return eval(str,"([\\x{10000}-\\x{10ffff}\ud800-\udfff])","ENCODE","{{","}}");
    }

    public String getReplacement(Matcher matcher,String isEncode,String lFlag,String rFlag){

        try {
            if ("ENCODE".equals(isEncode)){

                return lFlag + URLEncoder.encode(matcher.group(1), "UTF-8") + rFlag;
            }else if(("DECODE".equals(isEncode))){

                return URLDecoder.decode(matcher.group(1), "UTF-8");
            }else throw new RuntimeException("arg2 only in ENCODE/DECODE!!!!!!!");
        } catch (UnsupportedEncodingException e) {

            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String str="\uD83D\uDCB0580 支持放店\uD83D\uDE80\n" +
                "【独家优势】\n" +
                "全新G版出货‼️                       A                            Ma Maniere x Air Jordan 1 High OG \"Sail and Burgundy\" Amm联名 蛇纹";
        String str2="@你的小太阳啊☀️：三球的\uD83D\uDC5F！Puma MB.01 x Rick and Morty 红绿鸳鸯";
        System.out.println(new RegexpReplaceUTF8().eval(str));
    }
}
