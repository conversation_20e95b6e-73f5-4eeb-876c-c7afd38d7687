package com.alibaba.blink.udx.udf;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 4/14/22 8:23 下午
 */

@FunctionHint(
    input = {@DataTypeHint("STRING")},
    output = @DataTypeHint("STRING")
)
public class UrlDecode extends ScalarFunction {

  public String eval(String url) throws UnsupportedEncodingException {
    if (url == null) {
      return "";
    } else {
      // 修复 https://blog.csdn.net/zz_xyz/article/details/84920187
      return URLDecoder.decode(url.replaceAll("%(?![0-9a-fA-F]{2})", "%25"), "UTF-8");
    }
  }
}
