package com.alibaba.blink.udx.hyperloglog;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/29 11:06
 */

import org.apache.flink.table.api.dataview.MapView;

import java.util.Optional;

/**
 * Buffer used by HyperLogLogPlusPlus and ApproximateCountDistinct.
 */
public class HllMapViewBuffer {
    public double zInverse = 0.0d;
    public double v = 0.0d;
    public MapView<Integer, Long> mapView;

    public HllMapViewBuffer() {
    }

    public long getViewValue(Integer wordsOffset) {
        try {
            return Optional.ofNullable(mapView.get(wordsOffset)).orElse(0L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void putViewValue(Integer wordsOffset, long value) {
        try {
            if (0L == value) {
                mapView.remove(wordsOffset);
            } else
                mapView.put(wordsOffset, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public boolean isEmpty() {
        try {
            return mapView.isEmpty();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static class Variable {
        public double zInverse = 0.0d;
        public double v = 0.0d;

        public Variable() {
        }
    }
}
