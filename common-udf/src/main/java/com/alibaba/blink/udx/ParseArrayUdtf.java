package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;

import java.util.List;

/**
 * <AUTHOR>
 */
@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class ParseArrayUdtf extends TableFunction<String> {
    public void eval(String str) {
        List<String> list = JSON.parseArray(str, String.class);
        if (list == null || list.size() == 0){
            collect("");
        } else {
            for (String s : list){
                collect(s);
        }
        }
    }

    public static void main(String[] args) {
        String str = "[\"1\",\"2\"]";
        new ParseArrayUdtf().eval(str);
    }
}

