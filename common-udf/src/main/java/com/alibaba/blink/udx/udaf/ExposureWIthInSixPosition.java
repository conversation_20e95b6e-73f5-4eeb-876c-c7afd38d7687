package com.alibaba.blink.udx.udaf;

import org.apache.flink.table.api.dataview.ListView;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * Author: WangLin
 * Date: 2022/9/29 下13:36
 * Description: 统计是社区首页品类搬运的曝光的动态，在产品指定的user_tag_id之间是否曝光position间隔有小于6
 */
public class ExposureWIthInSixPosition extends AggregateFunction<String, ExposureWIthInSixPosition.CollectAccumulator> {

	@Override
	public CollectAccumulator createAccumulator() {
		final CollectAccumulator acc = new CollectAccumulator();
		acc.listView = new ListView<>();
		return acc;
	}

	@Override
	public String getValue(ExposureWIthInSixPosition.CollectAccumulator accumulator) {
		if (accumulator.listView.getList().isEmpty()) {
			return "0";
		} else {
			List<Integer> list = accumulator.listView.getList();
			for (int i = 0; i < list.size(); i++) {
				for (int j = 0; j < list.size(); j++) {
					if (list.get(j) != list.get(i)) {
						if (Math.abs(list.get(i) - list.get(j)) < 6) {
							return "1";
						}
					}
				}
			}
			return "0";
		}
	}

	public void accumulate(ExposureWIthInSixPosition.CollectAccumulator accumulate, Integer value) throws Exception {
		accumulate.listView.add(value);
	}

	/**
	 * Accumulator for COLLECT.
	 */
	public static class CollectAccumulator {
		public ListView<Integer> listView;

		@Override
		public boolean equals(Object o) {
			if (this == o) {
				return true;
			}
			if (o == null || getClass() != o.getClass()) {
				return false;
			}

			CollectAccumulator that = (CollectAccumulator) o;
			return Objects.equals(listView, that.listView);
		}
	}


	public static void main(String[] args) {

		List<Integer> list = new ArrayList<>();
		list.add(4);
		list.add(4);
		list.add(8);

		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list.size(); j++) {
				if (list.get(j) != list.get(i)) {
					if (Math.abs(list.get(i) - list.get(j)) < 6) {
						System.out.println(list.get(i)+"==="+list.get(j));
					}
				}
			}
		}
	}
}
