package com.alibaba.blink.udx.udf;


import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * Author: WangLin
 * Date: 2022/9/28 下14:52
 * Description: 计算两个整数相除，保留n位，然后乘以10的n倍.例如 a/b保留4位然后乘以10000，表示一万次b里面有几个a
 */
public class DivideKeepIntegerMultiZero extends ScalarFunction {

	private static org.slf4j.Logger logger = LoggerFactory.getLogger(DivideKeepIntegerMultiZero.class);

	public Integer eval(Long a, Long b, Integer num) {

		try {

			long m = 1L;
			for (int i = 0; i < num; i++) {
				m = m * 10;
			}
			return BigDecimal.valueOf(Double.parseDouble(String.format("%." + num + "f", ((a.doubleValue() / b.doubleValue()))))).multiply(BigDecimal.valueOf(m)).intValue();

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public Integer eval(Integer a, Integer b, Integer num) {
		try {
			long m = 1L;
			for (int i = 0; i < num; i++) {
				m = m * 10;
			}
			return BigDecimal.valueOf(Double.parseDouble(String.format("%." + num + "f", ((a.doubleValue() / b.doubleValue()))))).multiply(BigDecimal.valueOf(m)).intValue();

		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
