package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 解析人群标签数据格式
 * 入参：label_info 【json】
 * 出参：label_info里面的各个key
 */
@FunctionHint(output = @DataTypeHint("ROW<f0 STRING>"))
public class CrowdLabelDataFormat extends TableFunction<Row> {

	private static final Logger log = LoggerFactory.getLogger(CrowdLabelDataFormat.class);

	public void eval(String labelInfo) {

		try {
			JSONObject jsonObject = JSON.parseObject(labelInfo);

			for (Map.Entry<String, Object> stringObjectEntry : jsonObject.entrySet()) {
				Row row = new Row(1);
				row.setField(0, stringObjectEntry.getKey());
				collect(row);
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.info(e.getMessage());
		}

	}

}
