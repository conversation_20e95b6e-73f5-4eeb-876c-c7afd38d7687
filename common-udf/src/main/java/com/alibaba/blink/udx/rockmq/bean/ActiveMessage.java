package com.alibaba.blink.udx.rockmq.bean;

public class ActiveMessage {

    private String device_id;
    private long user_id;
    private String adv_user_agent;
    private String advUserAgent;
    private String ip;
    private String caid;
    private String imei;
    private String version;
    private String platform;
    private long event_time;
    private String oaid;
    private Headers headers;

    public Headers getHeaders() {
        return headers;
    }

    public void setHeaders(Headers headers) {
        this.headers = headers;
    }
    //    Headers struct {
//        Fcuuid        string `json:"fcuuid"`
//        Idfa          string `json:"idfa"`
//        Shumeiid      string `json:"shumeiid"`
//        Appid         string `json:"appid"`
//        Duuuid        string `json:"duuuid"`
//        Dudevicetrait string `json:"dudevicetrait"`
//        Dudevicebrand string `json:"dudevicebrand"`
//        Brand         string `json:"brand"`
//    } `json:"headers"`
//    DeviceId     string `json:"device_id"`
//    UserId       int64  `json:"user_id"`
//    AdvUserAgent string `json:"adv_user_agent"`
//    Ip           string `json:"ip"`
//    Caid         string `json:"caid"`
//    Imei         string `json:"imei"`
//    Version      string `json:"version"`
//    Platform     string `json:"platform"`
//    EventTime    int64  `json:"event_time"`
//    Oaid         string `json:"oaid"`


    public String getAdvUserAgent() {
        return advUserAgent;
    }

    public void setAdvUserAgent(String advUserAgent) {
        this.advUserAgent = advUserAgent;
    }

    public String getDevice_id() {
        return device_id;
    }

    public void setDevice_id(String device_id) {
        this.device_id = device_id;
    }

    public long getUser_id() {
        return user_id;
    }

    public void setUser_id(long user_id) {
        this.user_id = user_id;
    }

    public String getAdv_user_agent() {
        return adv_user_agent;
    }

    public void setAdv_user_agent(String adv_user_agent) {
        this.adv_user_agent = adv_user_agent;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public long getEvent_time() {
        return event_time;
    }

    public void setEvent_time(long event_time) {
        this.event_time = event_time;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }
}
