package com.alibaba.blink.udx.udtf.dynamic.bean;

import java.io.Serializable;

public class AppLogSubscribes implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 应用名
     */
    private String application;

    /**
     * 环境
     */
    private String environment;

    /**
     * 订阅规则
     */
    private String avRules;

    /**
     * 集群类型
     */
    private String clusterName;

    private String clusterIp;

    /**
     * topic名称
     */
    private String topicName;

//    private LogSubscriber logSubscriber;

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }
    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

//    public LogSubscriber getLogSubscriber() {
//        return logSubscriber;
//    }
//
//    public void setLogSubscriber(LogSubscriber logSubscriber) {
//        this.logSubscriber = logSubscriber;
//    }

    public String getAvRules() {
        return avRules;
    }

    public void setAvRules(String avRules) {
        this.avRules = avRules;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getClusterIp() {
        return clusterIp;
    }

    public void setClusterIp(String clusterIp) {
        this.clusterIp = clusterIp;
    }
}

