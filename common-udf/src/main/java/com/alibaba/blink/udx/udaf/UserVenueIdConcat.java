package com.alibaba.blink.udx.udaf;

import org.apache.flink.table.api.dataview.ListView;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.Objects;

/**
 * 拼接用户参加的活动id.
 */
public class UserVenueIdConcat extends AggregateFunction<String, UserVenueIdConcat.CollectAccumulator> {

	@Override
	public CollectAccumulator createAccumulator() {
		final CollectAccumulator acc = new CollectAccumulator();
		acc.listView = new ListView<>();
		return acc;
	}

	@Override
	public String getValue(UserVenueIdConcat.CollectAccumulator accumulator) {
		if (accumulator.listView.getList().isEmpty()) {
			return "";
		} else {
			return String.join(",", accumulator.listView.getList()) + ",";
		}
	}

	public void accumulate(UserVenueIdConcat.CollectAccumulator accumulate, String value) throws Exception {
		accumulate.listView.add(value);
	}

	/** Accumulator for COLLECT. */
	public static class CollectAccumulator {
		public ListView<String> listView;

		@Override
		public boolean equals(Object o) {
			if (this == o) {
				return true;
			}
			if (o == null || getClass() != o.getClass()) {
				return false;
			}

			CollectAccumulator that = (CollectAccumulator) o;
			return Objects.equals(listView, that.listView);
		}
	}
}
