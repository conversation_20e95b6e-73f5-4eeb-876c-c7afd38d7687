package com.alibaba.blink.udx.udaf;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static java.util.concurrent.ThreadLocalRandom.current;

@Slf4j
public class MergeJsonForLastValue extends AggregateFunction<String, MergeJsonForLastValue.JsonAcc> {

    public static Set<String> needKeys = new HashSet<>();
    public static Boolean isFilter = false;
    private final AtomicBoolean firstInit = new AtomicBoolean(false);
    private final AtomicBoolean needInit = new AtomicBoolean(true);
    public static ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    public static transient String META_DATA_INFO_KEY = "msg_metadata_info";
    public static transient String PROCESS_TIME_KEY = "msg_product_time";

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
    }

    @Override
    public JsonAcc createAccumulator() {
        return new JsonAcc();
    }

    @Override
    public String getValue(JsonAcc accumulator) {
        try {
            return accumulator.getCache();
        } catch (Exception e) {
            log.error("get value err! acc:{}", accumulator);
            throw new RuntimeException(e);
        }
    }

    public void accumulate(MergeJsonForLastValue.JsonAcc accumulator, String value, String streamFlag, Integer partition, Long offset, Long order) throws JsonProcessingException {

        accumulate(accumulator, value, streamFlag, partition, offset, order, null);
    }

    public void accumulate(MergeJsonForLastValue.JsonAcc accumulator, String value, String streamFlag, Integer partition, Long offset, Long order, String... keys) throws JsonProcessingException {

        initNeedKeys(keys);

        if (outOfOrder(accumulator, streamFlag, partition, offset)) {
            return;
        }

        accumulator.acc(parseObject(value), order);

    }

    public boolean outOfOrder(MergeJsonForLastValue.JsonAcc accumulator, String streamFlag, Integer partition, Long offset) {

        if (streamFlag == null && offset == null && partition == null) {
            return false;
        }

        MetaDataInfo metaDataInfo = accumulator.offsetToStream.getOrDefault(streamFlag, null);

        if (metaDataInfo == null || (metaDataInfo.partition == partition && metaDataInfo.offset < offset)) {
            accumulator.offsetToStream.put(streamFlag, MetaDataInfo.of(partition, offset));
            return false;
        } else if (metaDataInfo.partition != partition) {
            log.warn("partition change ! streamFlag:{}. partition:{} -> {}.", streamFlag, metaDataInfo.partition, partition);
            return false;
        } else
            log.warn("local-out-Of-order ! streamFlag:{}.partition:{}. offset:{} -> {}.", streamFlag, metaDataInfo.partition, metaDataInfo.offset, offset);
        return true;

    }

    public void initNeedKeys(String... keys) {

        if (null == keys || keys.length == 0) {
            return;
        }

        while (needInit.get()) {
            if (firstInit.compareAndSet(false, true)) {
                MergeJsonForLastValue.needKeys.clear();
                MergeJsonForLastValue.needKeys.addAll(Arrays.asList(keys));
                isFilter = true;
                log.info("accumulate innit. size:{}; keys:{}; source:{}", needKeys.size(), needKeys, Arrays.toString(keys));
                needInit.compareAndSet(true, false);
            } else {
                try {
                    TimeUnit.MILLISECONDS.sleep(current().nextInt(10));
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    public void merge(MergeJsonForLastValue.JsonAcc accumulator, Iterable<MergeJsonForLastValue.JsonAcc> iterables) {
        for (MergeJsonForLastValue.JsonAcc acc : iterables) {
            accumulator.merge(acc);
        }
    }

    @SuppressWarnings({"all"})
    private Map<String, Object> parseObject(String jsonStr) {
        try {
            return mapper.readValue(jsonStr, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("json parse error, msg:" + jsonStr, e);
        }
    }

    public static class JsonAcc {

        public Map<String, Tuple3<byte[], Long, Byte>> viewMap;

        public Map<String, MetaDataInfo> offsetToStream;

        public JsonAcc() {
            this.viewMap = new HashMap<>();
            this.offsetToStream = new HashMap<>();
        }

        public String getCache() throws IOException {

            if (viewMap.isEmpty()) {
                return "{}";
            }

            Map<String, Object> rowMap = new HashMap<>(viewMap.size() + 2);
            for (Map.Entry<String, Tuple3<byte[], Long, Byte>> entry : viewMap.entrySet()) {
                String k = entry.getKey();
                Tuple3<byte[], Long, Byte> v = entry.getValue();
                rowMap.put(k, v.f0 == null ? null : mapper.readValue(v.f0, genericDataType(v.f2)));
            }
            rowMap.put(META_DATA_INFO_KEY, offsetToStream);
            rowMap.put(PROCESS_TIME_KEY, System.currentTimeMillis());
            return mapper.writeValueAsString(rowMap);
        }

        public void acc(Map<String, Object> value, Long order) throws JsonProcessingException {

            if (value == null) {
                return;
            }

            for (Map.Entry<String, Object> entry : value.entrySet()) {
                String k = entry.getKey();
                Object v = entry.getValue();
                setValue(k, v, order);
            }

        }

        private void setValue(String s, Object o, Long ts) throws JsonProcessingException {

            if (filter(s)) {
                return;
            }

            if (viewMap.containsKey(s)) {

                if (viewMap.get(s).f1 <= ts) {
                    viewMap.put(s, convert(o, ts));
                }

            } else {
                viewMap.put(s, convert(o, ts));
            }

        }

        public Tuple3<byte[], Long, Byte> convert(Object value, Long ts) throws JsonProcessingException {

            return Tuple3.of(value == null ? null : mapper.writeValueAsBytes(value), ts, dataTypeMapping(value));

        }

        public Byte dataTypeMapping(Object value) {

            if (value instanceof Map) {
                return 1;
            }
            if (value instanceof Double) {
                return 2;
            }
            if (value instanceof Float) {
                return 3;
            }
            if (value instanceof Long) {
                return 4;
            }
            if (value instanceof Integer) {
                return 5;
            }
            if (value instanceof String) {
                return 6;
            }

            return null;

        }

        public Class<?> genericDataType(Byte type) {

            switch (type) {
                case 1:
                    return Map.class;
                case 2:
                    return Double.class;
                case 3:
                    return Float.class;
                case 4:
                    return Long.class;
                case 5:
                    return Integer.class;
                default:
                    return String.class;
            }
        }

        public boolean filter(String key) {

            if (isFilter) {
                return !needKeys.contains(key);
            }
            return false;
        }

        public void merge(JsonAcc otherAcc) {

            Map<String, MetaDataInfo> otherMetaData = otherAcc.offsetToStream;
            if (otherMetaData != null) {
                for (Map.Entry<String, MetaDataInfo> entry : otherMetaData.entrySet()) {
                    String key = entry.getKey();
                    MetaDataInfo value = entry.getValue();
                    if (this.offsetToStream.containsKey(key)) {
                        MetaDataInfo metaData = this.offsetToStream.get(key);
                        if (value.partition != metaData.partition) {
                            log.warn("global-partition change ! streamFlag:{}. partition:{} -> {}.", key, metaData.partition, value.partition);
                            metaData.partition = value.partition;
                            metaData.offset = value.offset;
                        } else if (value.offset < metaData.offset) {
                            log.warn("global-out-Of-order ! streamFlag:{}. partition:{}. offset:{} -> {}.", key, metaData.partition, metaData.offset, value.offset);
                        } else {
                            metaData.offset = value.offset;
                        }
                    } else
                        this.offsetToStream.put(key, MetaDataInfo.of(value.partition, value.offset));
                }
            }

            for (Map.Entry<String, Tuple3<byte[], Long, Byte>> entry : otherAcc.viewMap.entrySet()) {
                String key = entry.getKey();

                Tuple3<byte[], Long, Byte> otherMetric = entry.getValue();

                if (this.viewMap.containsKey(key)) {

                    Tuple3<byte[], Long, Byte> thisMetric = this.viewMap.get(key);
                    if (thisMetric.f1 <= otherMetric.f1)
                        this.viewMap.put(key, otherMetric);

                } else
                    this.viewMap.put(key, otherMetric);
            }

        }

    }

    public static class MetaDataInfo {
        public Integer partition;
        public Long offset;

        public MetaDataInfo() {
        }

        public MetaDataInfo(Integer partition, Long offset) {
            this.partition = partition;
            this.offset = offset;
        }

        public static MetaDataInfo of(Integer partition, Long offset) {
            return new MetaDataInfo(partition, offset);
        }
    }

    public static void main(String[] args) throws IOException {
        MergeJsonForLastValue mergeAdsTemplateJson = new MergeJsonForLastValue();
        JsonAcc jsonAcc = new JsonAcc();

        String v1 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"be7e3e7c-3b38-4201-8c8b-259d3c108ab6\",\"metric_value\":{\"ord_uv\":null,\"pay_ue_24h\":null,\"cost\":1.0,\"remain_cnt_1d\":null,\"orderCAC\":null,\"productExposureRate\":null,\"pay_ue\":null,\"activationCAC\":null,\"click\":0.0,\"roi\":null,\"real_cost\":0.****************,\"ltv\":null,\"charge1\":null,\"gmv1\":null,\"view\":3.0,\"ad_site\":\"[]\",\"order_count1\":null,\"roi24\":null,\"first_day_spu_exposure_cnt\":null,\"activate\":null,\"retain2Rate\":null,\"order_count_24h\":null},\"job\":\"ads_dewu_increase_metrics_about_rule_engine\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        String v2 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"b06b8740-1dd4-48d0-bf8e-ac46671e12ec\",\"metric_value\":{\"agg5Day_activate\":0,\"agg7Day_activate\":0},\"job\":\"ads_dewu_interface_hard_ad_metric_about_agg7day\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        String v3 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"b050fffe-172f-4358-bad7-938a66d99f9b\",\"metric_value\":{\"agg5Day_activate\":1,\"agg7Day_activate\":1},\"job\":\"ads_dewu_interface_hard_ad_metric_about_agg7day\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        JsonAcc jsonAcc2 = new JsonAcc();
        mergeAdsTemplateJson.accumulate(jsonAcc, v1, "1", 1, 3L, 3L);
        mergeAdsTemplateJson.accumulate(jsonAcc, v3, "2", 1, 2L, 2L);
        mergeAdsTemplateJson.accumulate(jsonAcc2, v2, "1", 1, 1L, 1L);

        ArrayList<JsonAcc> jsonAccs = new ArrayList<>();
        jsonAccs.add(jsonAcc);
        jsonAccs.add(jsonAcc2);
        JsonAcc jsonAcc1 = new JsonAcc();

        mergeAdsTemplateJson.merge(jsonAcc1, jsonAccs);

        System.out.println(mergeAdsTemplateJson.getValue(jsonAcc1));


    }

}
