/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.hyperloglog.HllMapViewBuffer;
import com.alibaba.blink.udx.hyperloglog.HyperLogLogPlusPlus;
import com.alibaba.blink.udx.hyperloglog.XXH64;
import org.apache.flink.core.memory.MemorySegment;
import org.apache.flink.core.memory.MemorySegmentFactory;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.data.binary.BinaryStringData;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;

import java.text.DecimalFormat;
import java.util.Optional;

import static com.alibaba.blink.udx.hyperloglog.XXH64.DEFAULT_SEED;

public class ApproximateCountDistinctV2Function extends AggregateFunction<Long, HllMapViewBuffer> {

    private static final Double DEFAULT_RELATIVE_SD = 0.01;
    private transient HyperLogLogPlusPlus hyperLogLogPlusPlus;

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> {
                    DataType dataType = DataTypes.STRUCTURED(
                            HllMapViewBuffer.class,
                            DataTypes.FIELD("zInverse", DataTypes.DOUBLE().bridgedTo(double.class)),
                            DataTypes.FIELD("v", DataTypes.DOUBLE().bridgedTo(double.class)),
                            DataTypes.FIELD("mapView", MapView.newMapViewDataType(
                                    DataTypes.INT(),
                                    DataTypes.BIGINT()
                            ))
                    );
                    return Optional.of(dataType);
                })
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.BIGINT()))
                .build();
    }

    @Override
    public HllMapViewBuffer createAccumulator() {
        HllMapViewBuffer buffer = new HllMapViewBuffer();
        resetAccumulator(buffer);
        return buffer;
    }

    public void resetAccumulator(HllMapViewBuffer buffer) {
        HllMapViewBuffer.Variable var = hyperLogLogPlusPlus.buildOneWordVar(0L);
        HllMapViewBuffer.Variable lastVar = hyperLogLogPlusPlus.buildLastWordVar(0L);
        buffer.zInverse = var.zInverse * (hyperLogLogPlusPlus.getNumWords() - 1) + lastVar.zInverse;
        buffer.v = var.v * (hyperLogLogPlusPlus.getNumWords() - 1) + lastVar.v;
        buffer.mapView = new MapView<>();
    }

    public void accumulate(HllMapViewBuffer buffer, Long input, Double relativeSd) throws Exception {

        if (hyperLogLogPlusPlus == null) {
            hyperLogLogPlusPlus = new HyperLogLogPlusPlus(relativeSd);
        }

        if (input != null) {
            hyperLogLogPlusPlus.updateByHashcode(buffer, XXH64.hashLong(input, DEFAULT_SEED));
        }
    }

    public void accumulate(HllMapViewBuffer buffer, Long input) throws Exception {
        accumulate(buffer, input, DEFAULT_RELATIVE_SD);
    }

    public void accumulate(HllMapViewBuffer buffer, String input, Double relativeSd) throws Exception {

        if (hyperLogLogPlusPlus == null) {
            hyperLogLogPlusPlus = new HyperLogLogPlusPlus(relativeSd);
        }

        if (input != null) {
            BinaryStringData s = BinaryStringData.fromString(input);

            MemorySegment[] segments = s.getSegments();

            Long l = null;
            if (segments.length == 1) {
                l = XXH64.hashUnsafeBytes(
                        segments[0], s.getOffset(), s.getSizeInBytes(), DEFAULT_SEED);
            } else {
                l = XXH64.hashUnsafeBytes(
                        MemorySegmentFactory.wrap(s.toBytes()),
                        0,
                        s.getSizeInBytes(),
                        DEFAULT_SEED);
            }

            hyperLogLogPlusPlus.updateByHashcode(buffer, l);

        }
    }

    public void accumulate(HllMapViewBuffer buffer, String input) throws Exception {
        accumulate(buffer, input, DEFAULT_RELATIVE_SD);
    }

    public void merge(HllMapViewBuffer buffer, Iterable<HllMapViewBuffer> it) throws Exception {
        for (HllMapViewBuffer tmpBuffer : it) {
            hyperLogLogPlusPlus.merge(buffer, tmpBuffer);
        }
    }

    @Override
    public Long getValue(HllMapViewBuffer buffer) {
        return hyperLogLogPlusPlus.query(buffer.zInverse, buffer.v);
    }

    public static void main(String[] args) {
        HyperLogLogPlusPlus hyperLogLogPlusPlus1 = new HyperLogLogPlusPlus(DEFAULT_RELATIVE_SD);
        ApproximateCountDistinctV2Function approximate = new ApproximateCountDistinctV2Function();
        approximate.hyperLogLogPlusPlus = hyperLogLogPlusPlus1;
        HllMapViewBuffer accumulator = new HllMapViewBuffer();
        approximate.resetAccumulator(accumulator);
        System.out.println("桶大小：" + hyperLogLogPlusPlus1.getNumWords());
        try {
            for (long i = 0; i < 100000L; i++) {
                approximate.accumulate(accumulator, i);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        try {
            for (long i = 0; i < 1000L; i++) {
                approximate.accumulate(accumulator, 51110L);
            }
            for (long i = 0; i < 1000L; i++) {
                approximate.accumulate(accumulator, 5110L);
            }
            for (long i = 0; i < 100100L; i++) {
                approximate.accumulate(accumulator, 5110L);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        System.out.println("UV：" + approximate.getValue(accumulator));

        DecimalFormat decimalFormat = new DecimalFormat("#.######");
        String formattedNumber = decimalFormat.format(hyperLogLogPlusPlus1.trueRsd());
        System.out.println("标准误差：" + formattedNumber);
        System.out.println("准确率：" + decimalFormat.format((1 - (Math.abs(approximate.getValue(accumulator) - 100003L) / 100003D)) * 100) + "%");

    }
}
