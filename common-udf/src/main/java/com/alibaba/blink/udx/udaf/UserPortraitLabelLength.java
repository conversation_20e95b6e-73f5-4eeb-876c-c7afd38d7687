package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import org.apache.flink.api.java.tuple.Tuple1;
import org.apache.flink.table.functions.AggregateFunction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

/**
 * zmx
 * 对于list标签 计算size
 * 对于map标签  计算分数总和
 */
public class UserPortraitLabelLength extends AggregateFunction<String , Tuple1<Float>> {
    @Override
    public Tuple1<Float> createAccumulator() {
        return Tuple1.of(0f);
    }

    @Override
    public String getValue(Tuple1<Float> accumulator) {
        return accumulator.f0.toString();
    }

//    {"newValue":[{"key":"a1212","time":"112211"},{"key":"a1213","time":"112211"},{"key":"a1214","time":"112211"},{"key":"a1215","time":"112211"},{"key":"a1216","time":"112211"},{"key":"a1211","time":"112211"}],"record":{"content_type":"","content_id":"","user_id":"18","calculate_field1":"a1211","calculate_field2":"112211","time":"1234556","event":"event","properties":"{\"fcuuid\":\"UUID6c455d08fd3146b0abf969d2b9d3803c\",\"$screen_orientation\":\"portrait\",\"community_search_id\":\"78cdb9d9ac7878e635cdcea5d5cf8c13\",\"$os\":\"iOS\",\"$longitude\":\"\",\"$network_type\":\"4G\",\"$wifi\":false,\"$ip\":\"*************\",\"$screen_height\":736,\"device_uuid\":\"UUID6c455d08fd3146b0abf969d2b9d3803c\",\"dw_userid\":\"39280232\",\"$device_id\":\"4D216E17-8D3A-4C16-A791-4D66C3BFDD8C\",\"$app_id\":\"com.siwuai.duapp\",\"$latitude\":\"\",\"_time\":\"1628851612162\",\"user_agent\":\"Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148/duapp/4.74.1\",\"current_page\":\"144\",\"$carrier\":\"中国联通\",\"$os_version\":\"14.6\",\"time_offset\":\"380\",\"community_search_key_word_type\":\"手动输入\",\"$is_first_day\":false,\"$model\":\"iPhone9,2\",\"receive_time\":1628851617621,\"session_id\":\"UUID6c455d08fd3146b0abf969d2b9d3803c1628851601251\",\"$screen_width\":414,\"$app_version\":\"4.74.1\",\"$lib\":\"iOS\",\"app_build\":\"*********\",\"$app_name\":\"得物(毒)\",\"visit_mode\":\"1\",\"$timezone_offset\":-480,\"$lib_version\":\"2.1.0\",\"shumei_id\":\"20210604010325ba9d11b6086039a1b79443ea9351d0eb01e12ff961a6152b\",\"search_key_word_position\":\"1\",\"$manufacturer\":\"Apple\",\"search_key_word\":\"詹姆斯8\"}","event_page_block":"community_search_key_word_click_144"},"time":"1234556","oldValue":[{"$ref":"$.newValue[0]"},{"$ref":"$.newValue[1]"},{"$ref":"$.newValue[2]"},{"$ref":"$.newValue[3]"},{"$ref":"$.newValue[4]"},{"$ref":"$.newValue[5]"}],"labelName":"csehkeytlist","userId":"18"}


    public void accumulate(Tuple1<Float> accumulator, String input) {
        Float f = 0f;
        if (Objects.nonNull(input)) {
            if (input.startsWith("[")){
                try {
                    ArrayList list = JSON.parseObject(input, ArrayList.class);
                    accumulator.f0 = accumulator.f0+list.size();
                }catch (JSONException e){
                    e.printStackTrace();
                }
            }else if (input.startsWith("{")){
                try{
//                    float 写出的数据 blink转成了BigDecimal
                    HashMap<String, BigDecimal> map = JSON.parseObject(input, HashMap.class);
                    for (BigDecimal value : map.values()) {
                        f += value.floatValue();
                    }
                    accumulator.f0 = accumulator.f0 + f;
                }catch (JSONException e){
                    e.printStackTrace();
                }
            }
        }
    }


    public void merge(Tuple1<Float> accumulator, Iterable<Tuple1<Float>> its) {
        for (Tuple1<Float> other : its) {
            accumulator.f0 = accumulator.f0 + other.f0;
        }
    }

    public static void main(String[] args) {
        String input = "[{\"key\":\"a1212\",\"time\":\"112211\"},{\"key\":\"a1213\",\"time\":\"112211\"},{\"key\":\"a1214\",\"time\":\"112211\"},{\"key\":\"a1215\",\"time\":\"112211\"},{\"key\":\"a1216\",\"time\":\"112211\"},{\"key\":\"a1211\",\"time\":\"112211\"}]";
        ArrayList list = JSON.parseObject(input, ArrayList.class);
        System.out.println(list);
        System.out.println(list.size());

        String mapStr = "{\"30\":85.5}";
        ArrayList map = JSON.parseObject(mapStr, ArrayList.class);
        System.out.println(map);
    }

}
