package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.text.DecimalFormat;

public class DoubleCut extends ScalarFunction {

    public String eval(Double input1,Integer input2){
        Long a = input1.longValue();
        double b = input1 - a;
        if(b== 0){
            return String.valueOf(input1);
        }
        String f = "#.";
        System.out.println(Math.log10(b));
        for(int i = 0 ; i < -Math.floor(Math.log10(b)) + input2 -1;i++){
            f = f + "#";
        }
        DecimalFormat df = new DecimalFormat(f);
        return df.format(a+Double.valueOf(df.format(b)));
    }



    public static void main(String[] args) {
        System.out.println(new DoubleCut().eval(5.0E-4,1));
    }

}

