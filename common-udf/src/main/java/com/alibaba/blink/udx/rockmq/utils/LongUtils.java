package com.alibaba.blink.udx.rockmq.utils;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LongUtils {

    public static Logger LOG = LoggerFactory.getLogger(LongUtils.class);


    public static Long valueOf(String s) throws NumberFormatException {
        try {
            if (StringUtils.isBlank(s)) {
                return 0L;
            }
            return Long.valueOf(s);
        } catch (Exception e) {
            LOG.error("转换失败，数据格式：{}",s);
            return 0L;
        }
    }

}
