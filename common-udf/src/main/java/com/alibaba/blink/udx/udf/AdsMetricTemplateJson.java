package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Objects;
import java.util.TreeSet;
import java.util.UUID;

/**
 * @description: 构建一个ads 数据结构
 * @Author: lmh
 * @date: 2022/10/17 3:36 下午
 *  支持的入参顺序
 * （String jobName String metricName,String metricValue,String dimValue,String ext）
 * （String jobName String metricName,String metricValue,String dimValue）
 */
public class AdsMetricTemplateJson extends ScalarFunction {


    public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object... args){

        checkArgs(args);

        JSONObject res = new JSONObject();

        //jobName
        res.put("job",args[0]);

        //metricName
        res.put("metric_name",args[1]);

        //metricValue
        res.put("metric_value",object2Json(args[2]));

        //dimValue
        JSONObject dimValue = object2Json(args[3]);
        res.put("dim_value",dimValue);

        res.put("dim_name",jsonKeys(dimValue));

        //MD5（dimValue 排序后 + "_" + metricName ）
        res.put("unique_key", DigestUtils.md5Hex(JSON.toJSONString(dimValue, SerializerFeature.MapSortField) + "_" + args[1]));

        if (args.length ==5 ){
            res.put("ext",object2Json(args[4]));
        }
        res.put("track_id", UUID.randomUUID());

        res.put("ts", System.currentTimeMillis());

        //默认将json null 写出
        return JSON.toJSONString(res,SerializerFeature.WriteMapNullValue);
    }

    private void checkArgs(Object... args){

        if (Objects.isNull(args) || (args.length != 5 && args.length != 4)){
            throw  new RuntimeException("arg num is error, must 4 or 5 (jobName, metricName, metricValue, dimValue, ext)");
        }

    }

    private JSONObject object2Json(Object o){
        //嵌套Json 避免转译
        if (o instanceof  String && JSON.isValidObject((String) o)){
            return JSON.parseObject((String) o);
        }else if (o instanceof  JSONObject){
            return (JSONObject) o;
        }else
            throw new RuntimeException("arg is not json. arg: " + o);
    }

    private String jsonKeys(JSONObject jsonObject){

        TreeSet<String> set = new TreeSet<>(jsonObject.keySet());
        StringBuilder builder = new StringBuilder();

        int off=0;
        for (String s : set) {

            if (off ==1){
                builder.append(",");
            }

            if (off == 0 ){
                off =1;
            }

            builder.append(s);
        }

        return builder.toString();
    }

    public static void main(String[] args) {
        AdsMetricTemplateJson json = new AdsMetricTemplateJson();

        System.out.println(json.eval("test_job",
                "test_metric_name",
                "{\"gmv\":\"100\",\"order_pv\":\"10\",\"order_uv\":\"8\"}",
                "{\"brand\":\"nike\",\"category\":\"鞋\",\"date\":\"20220916\"}",
                "{\"title\":\"xxx\"}"
        ));

        System.out.println(json.eval("test_job",
                "test_metric_name",
                "{\"gmv\":\"100\",\"order_pv\":\"10\",\"order_uv\":\"8\"}",
                "{\"brand\":\"nike\",\"category\":\"鞋\",\"date\":\"20220916\"}"
        ));

        System.out.println(json.eval("test_job",
                "test_metric_name",
                "{}",
                "{\"brand\":\"nike\",\"category\":\"鞋\",\"date\":\"20220916\"}"
        ));

        System.out.println(json.eval("test_job",
                "test_metric_name",
                null,
                "{\"brand\":\"nike\",\"category\":\"鞋\",\"date\":\"20220916\"}"
        ));
    }

}
