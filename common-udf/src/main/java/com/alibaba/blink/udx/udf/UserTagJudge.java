//package com.alibaba.blink.udx.udf;
//
//
//import com.util.CategoryTransportUserTagIds;
//import org.apache.flink.table.functions.ScalarFunction;
//import org.slf4j.LoggerFactory;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
///**
// * Author: WangLin
// * Date: 2022/9/28 下14:52
// * Description: 判断社区内容的user_tag_ids是否在产品指定的tag里面
// */
//public class UserTagJudge extends ScalarFunction {
//
//	private static org.slf4j.Logger logger = LoggerFactory.getLogger(UserTagJudge.class);
//
//	public String eval(String userTagIds) {
//
//		List<String> list;
//
//		try {
//			list = Arrays.asList(userTagIds.split(","));
//			return !Collections.disjoint(list, CategoryTransportUserTagIds.getUserTagIdsByProduct()) ? "1" : "0";
//		} catch (Exception e) {
//			e.printStackTrace();
//			return "0";
//		}
//	}
//
//}
