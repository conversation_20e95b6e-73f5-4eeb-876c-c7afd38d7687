package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.types.Row;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("BOOLEAN")
)
public class StringArrayContainsUdf extends ScalarFunction {

    public Boolean eval(String input, String delimiter, String targetElement) throws Exception {
        try {

            if (null == input) {
                return false;
            } else {
                String[] split = input.split(delimiter);
                for (int i = 0; i < split.length; i++) {
                    String s = split[i];
                    if (s.trim().equalsIgnoreCase(targetElement.trim())) {
                        return true;
                    }
                }
                return false;
            }

        } catch (Exception e) {
            throw new Exception("input:"+input+" targetElement:"+targetElement,e);
        }
    }

    public static void main(String[] args) {
        String[] split = "RE101003274991874111".split("_");
        for (int i = 0; i < split.length; i++) {
            String s1 = split[i];
            Row row = new Row(1);
            row.setField(0, s1.trim());
//            collect(row);
        }

    }
}
