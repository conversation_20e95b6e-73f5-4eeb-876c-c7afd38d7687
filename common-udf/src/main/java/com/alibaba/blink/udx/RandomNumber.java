package com.alibaba.blink.udx;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Random;

@FunctionHint(
        output = @DataTypeHint("INT")
)
public class RandomNumber extends ScalarFunction {
    Random r;

    // 可选， open方法可以不写,若写需要import org.apache.flink.table.functions.FunctionContext;
    @Override
    public void open(FunctionContext context) {
        r = new Random();
    }

    public int eval() {
        return r.nextInt(2147483646);
    }

    // 可选，close方法可以不写
    @Override
    public void close() {
        // ... ...
    }

    public static void main (String[] args) {
//        RandomNumber randomNumber = new RandomNumber();
//        randomNumber.r = new Random();
//        System.out.println(randomNumber.eval());
        String a = "   ".trim();
        System.out.println(a.length());
    }

}