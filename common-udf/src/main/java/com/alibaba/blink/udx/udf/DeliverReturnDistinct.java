package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.guava30.com.google.common.cache.Cache;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.QualifierFilter;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static java.util.concurrent.ThreadLocalRandom.current;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/10/13 18:12
 */
@Slf4j
public class DeliverReturnDistinct extends ScalarFunction {

    private final List<String> needColumns = new ArrayList<>();
    private final AtomicBoolean firstInit = new AtomicBoolean(false);
    private final AtomicBoolean needInit = new AtomicBoolean(true);
    private transient Connection connection;
    private transient Table table;
    private transient byte[] family;
    private transient FilterList filterList;

    public transient Cache<String, Row> cache;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            if (argumentDataTypes.size() - 5 >= 1) {
                DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 5];
                for (int i = 0; i < (argumentDataTypes.size() - 5); i++) {
                    outputDataTypes[i] = DataTypes.STRING();
                }
                return Optional.of(DataTypes.ROW(outputDataTypes));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();


    }

    public boolean eval(String tableName, String family, String hbaseConfig, String rowKey, Row insertValue, String... keys) {

        initConfig(tableName, family, hbaseConfig, keys);

        Row row = get(rowKey);

        if (row == null) {
            //put
            put(rowKey, insertValue);
            return true;
        }

        return false;

    }

    public void initConfig(String tableName, String familyName, String hbaseConfig, String... keys) {

        if (null == keys || keys.length == 0) {
            return;
        }
        while (needInit.get()) {
            try {
                if (firstInit.compareAndSet(false, true)) {
                    needColumns.clear();
                    needColumns.addAll(Arrays.asList(keys));
                    //初始化一个hbase client
                    Configuration configuration = HBaseConfiguration.create();

                    JSONObject cfg = JSONObject.parseObject(hbaseConfig);
                    Long cacheSize = Optional.ofNullable(cfg.getLong("cache.size")).orElse(1000L);
                    Long cacheTtl = Optional.ofNullable(cfg.getLong("cache.ttl")).orElse(1000L * 60 * 60);

                    cache = CacheBuilder.newBuilder()
                            .maximumSize(cacheSize)
                            .expireAfterWrite(cacheTtl, TimeUnit.MILLISECONDS)
                            .build();
                    cfg.remove("cache.size");
                    cfg.remove("cache.ttl");

                    cfg.forEach((x, y) -> configuration.set(x, String.valueOf(y)));

                    connection = ConnectionFactory.createConnection(configuration);

                    family = strToB(familyName);

                    table = connection.getTable(TableName.valueOf(tableName));

                    log.info("innit hbase clint:\n tableName:{} \n familyName:{} \n config:{} joinNeedColumns:{} \nconnection-config{}", tableName, Bytes.toString(family), cfg, needColumns, connection.getConfiguration());

                    filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);

                    for (String columns : needColumns) {
                        filterList.addFilter(buildQualifierFilter(columns));
                    }

                    needInit.compareAndSet(true, false);
                } else {
                    TimeUnit.MILLISECONDS.sleep(current().nextInt(10));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }
    }


    public Row get(String rowKey) {

        Row buffRow = cache.getIfPresent(rowKey);

        if (buffRow != null) {
            return buffRow;
        }

        try {
            Result result = table.get(new Get(strToB(rowKey)));

            if (result.isEmpty()) {
                return null;
            } else {
                Row row = new Row(needColumns.size());

                for (int i = 0; i < needColumns.size(); i++) {
                    row.setField(i, Bytes.toString(result.getValue(family, strToB(needColumns.get(i)))));
                }

                return row;

            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    public void put(String rowKey, Row insertValue) {
        try {
            Put put = new Put(Bytes.toBytes(rowKey));
            for (int i = 0; i < needColumns.size(); i++) {
                put.addColumn(family, strToB(needColumns.get(i)), strToB(insertValue.<String>getFieldAs(i)));
            }
            table.put(put);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public QualifierFilter buildQualifierFilter(String columns) {
        return new QualifierFilter(CompareOperator.EQUAL, new BinaryComparator(Bytes.toBytes(columns)));
    }

    public byte[] strToB(String str) {
        return Bytes.toBytes(str);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (table != null) {
            table.close();
        }
        if (connection != null) {
            connection.close();
        }
    }
}
