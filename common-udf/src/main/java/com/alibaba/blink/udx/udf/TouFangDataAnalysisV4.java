package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Author: WangLin (Modified)
 * Date: 2022/11/10 下午6:08
 * Description: 解析投放的数据 - V4版本，最简化版本避免所有依赖冲突
 * 
 * 这个版本完全避免了Jackson MessagePack的使用，
 * 仅在运行时动态尝试解析，如果失败则返回调试信息
 */
public class TouFangDataAnalysisV4 extends ScalarFunction {

	private final static Logger logger = LoggerFactory.getLogger(TouFangDataAnalysisV4.class);

	public String eval(byte[] body) {
		try {
			// 参数校验
			if (body == null || body.length == 0) {
				logger.warn("TouFangDataAnalysisV4: 输入的字节数组为空");
				return null;
			}

			// 动态尝试创建和使用MessagePack解析器
			return parseMessagePack(body);

		} catch (Exception e) {
			logger.error("TouFangDataAnalysisV4 解析失败，数据长度: {}, 错误信息: {}", 
					body != null ? body.length : 0, e.getMessage(), e);
			
			// 返回错误信息而不是null，便于调试
			Map<String, Object> errorResult = new java.util.HashMap<>();
			errorResult.put("error", "Parsing failed");
			errorResult.put("message", e.getMessage());
			errorResult.put("data_length", body.length);
			return JSON.toJSONString(errorResult);
		}
	}
	
	/**
	 * 动态解析MessagePack数据
	 */
	private String parseMessagePack(byte[] body) {
		try {
			// 使用反射动态创建ObjectMapper，避免静态依赖
			Class<?> objectMapperClass = Class.forName("com.fasterxml.jackson.databind.ObjectMapper");
			Class<?> messagePackFactoryClass = Class.forName("org.msgpack.jackson.dataformat.MessagePackFactory");
			
			Object messagePackFactory = messagePackFactoryClass.newInstance();
			Object objectMapper = objectMapperClass.getConstructor(
				Class.forName("com.fasterxml.jackson.core.JsonFactory")
			).newInstance(messagePackFactory);
			
			// 调用readValue方法
			Object parsedObject = objectMapperClass.getMethod("readValue", byte[].class, Class.class)
				.invoke(objectMapper, body, Object.class);
			
			// 如果解析出来的是 Map，直接转换
			if (parsedObject instanceof Map) {
				return JSON.toJSONString(parsedObject);
			}
			// 如果是其他类型，包装成 JSON 对象
			else {
				Map<String, Object> wrapper = new java.util.HashMap<>();
				wrapper.put("value", parsedObject);
				wrapper.put("type", parsedObject.getClass().getSimpleName());
				return JSON.toJSONString(wrapper);
			}
			
		} catch (ClassNotFoundException e) {
			logger.error("MessagePack相关类未找到: {}", e.getMessage());
			return createErrorResult("MessagePack classes not found", e.getMessage(), body.length);
		} catch (Exception e) {
			logger.error("动态解析MessagePack失败: {}", e.getMessage(), e);
			return createErrorResult("Dynamic parsing failed", e.getMessage(), body.length);
		}
	}
	
	/**
	 * 创建错误结果JSON
	 */
	private String createErrorResult(String error, String message, int dataLength) {
		Map<String, Object> errorResult = new java.util.HashMap<>();
		errorResult.put("error", error);
		errorResult.put("message", message);
		errorResult.put("data_length", dataLength);
		errorResult.put("timestamp", System.currentTimeMillis());
		return JSON.toJSONString(errorResult);
	}
}
