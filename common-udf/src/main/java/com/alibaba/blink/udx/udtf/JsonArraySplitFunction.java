package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/30 11:57
 */
@Slf4j
public class JsonArraySplitFunction extends TableFunction<Row> {

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    int size = argumentDataTypes.size();

                    if (size == 1) {
                        DataType[] types = new DataType[1];
                        types[0] = DataTypes.STRING();
                        return Optional.of(DataTypes.ROW(types));
                    }

                    DataType[] types = new DataType[argumentDataTypes.size() - 1];
                    for (int i = 0; i < argumentDataTypes.size() - 1; i++) {
                        types[i] = DataTypes.STRING();
                    }
                    return Optional.of(DataTypes.ROW(types));
                })
                .build();
    }

    public void eval(String message, String... keys) {

        if (Objects.nonNull(message) && !"[]".equals(message)) {

            List<JSONObject> jsonObjectList = null;
            try {
                jsonObjectList = JSONObject.parseArray(message, JSONObject.class);
            } catch (Exception e) {
                JSONObject messageFields = JSON.parseObject(message);
                jsonObjectList = Collections.singletonList(messageFields);
            }

            for (JSONObject messageFields : jsonObjectList) {
                if (keys != null) {
                    collect(parseJson(messageFields, keys));
                } else {
                    collect(new Row(1));
                    Row row = new Row(1);
                    row.setField(0, messageFields.toJSONString());
                    collect(row);
                }
            }
        } else {
            collect(new Row(keys.length));
        }
    }

    public Row parseJson(JSONObject messageFields, String... keys) {
        Row row = new Row(keys.length);
        if (!messageFields.isEmpty()) {
            try {
                for (int i = 0; i < keys.length; i++) {
                    String field = keys[i];
                    if (field.contains(".")) {
                        JSONObject temp = messageFields;
                        String[] filedArray = field.split("\\.");
                        for (int j = 0; j < filedArray.length - 1; j++) {
                            try {
                                temp = temp.getJSONObject(filedArray[j]);
                            } catch (com.alibaba.fastjson.JSONException | NumberFormatException e) {
                                //内部json解析异常时候跳过当前字段解析
                                log.error("Parse json {} encounter an error :{}", temp.getString(filedArray[j]), e.getMessage());
                                temp = null;
                            }
                            if (temp == null) break;
                        }
                        if (temp == null) {
                            row.setField(i, null);
                        } else {
                            row.setField(i, temp.getString(filedArray[filedArray.length - 1]));
                        }
                    } else {
                        row.setField(i, messageFields.getString(keys[i]));
                    }
                }
            } catch (com.alibaba.fastjson.JSONException | NumberFormatException e) {
                log.error("Parse json encounter an error : {}", e.getMessage());
            }
        }
        return row;
    }
}
