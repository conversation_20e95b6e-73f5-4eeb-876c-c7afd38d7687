package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.util.Optional;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

public class JsonStringUdfV2 extends ScalarFunction {
    // 可选， open方法可以不写,若写的话需要import org.apache.flink.table.functions.FunctionContext;
    @Override
    public void open(FunctionContext context) {
    }

    public String eval(Object... args) {
        if (args.length <= 1) {
            return "{}";
        }
        if (!(args.length % 2 == 0)) {
            return "{}";
        }
        JSONObject jsonObject = JSON.parseObject("{}");
        for (int i = 0; i < args.length/2; i++) {
            Object arg = args[2 * i + 1];
            //嵌套Json 避免转译
            if (arg instanceof  String && JSON.isValidObject((String) arg)){
                jsonObject.put(args[2*i].toString(),JSON.parseObject((String) args[2*i+1]));
            }else
                jsonObject.put(args[2*i].toString(),args[2*i+1]);
        }
        return JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
            .outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
            .build();
    }

    public static void main(String[] args) {
//        String eval = new JsonStringUdf().eval("f", null, "a", null, "f", null, "g", "");
//        System.out.println(eval);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sss",11);
        jsonObject.put("sss2",11);
        jsonObject.put("ssws",11);
        jsonObject.put("ssws3",11);
        String eval1 = new JsonStringUdfV2().eval("ke", 1,"keee", "2", "kee",jsonObject.toJSONString());
        System.out.println(eval1);

        JSONObject object = JSON.parseObject(jsonObject.toJSONString());

    }
}
