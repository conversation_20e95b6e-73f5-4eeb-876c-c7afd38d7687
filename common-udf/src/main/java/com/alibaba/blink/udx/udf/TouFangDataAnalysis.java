package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.functions.ScalarFunction;
import org.msgpack.jackson.dataformat.MessagePackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Author: WangLin
 * Date: 2022/11/10 下午6:08
 * Description: 解析投放的数据
 */
public class TouFangDataAnalysis extends ScalarFunction {

	private final static Logger logger = LoggerFactory.getLogger(TouFangDataAnalysis.class);

	private final static ObjectMapper msgpackMapper = new ObjectMapper(new MessagePackFactory());

	public String eval(byte[] body) {
		try {
			return JSON.toJSONString(msgpackMapper.readValue(body, Map.class));
		} catch (Exception e) {
			logger.info(e.getMessage(), e);
			return null;
		}
	}
}
