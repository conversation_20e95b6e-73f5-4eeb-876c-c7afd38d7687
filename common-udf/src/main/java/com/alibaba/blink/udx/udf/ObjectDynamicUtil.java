package com.alibaba.blink.udx.udf;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.ScalarFunction;
import org.codehaus.commons.compiler.CompileException;
import org.codehaus.janino.ClassBodyEvaluator;
import org.codehaus.janino.Scanner;

import java.io.IOException;
import java.io.StringReader;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/11/8 10:53
 */

@Slf4j
public class ObjectDynamicUtil extends ScalarFunction {
    public transient DynamicCodeIF dynamicCode;
    public String codePrx = "public Object execute(Object... args){ ";
    public String codeSux = "}";

    public interface DynamicCodeIF {
        Object execute(Object... args);
    }

    public String eval(String code, String resultType, @DataTypeHint(inputGroup = InputGroup.ANY) Object... args) throws IOException, CompileException {
        return (String) execute(code, args);
    }

    public Boolean eval(String code, Boolean resultType, @DataTypeHint(inputGroup = InputGroup.ANY) Object... args) throws IOException, CompileException {
        return (Boolean) execute(code, args);
    }

    public Long eval(String code, Long resultType, @DataTypeHint(inputGroup = InputGroup.ANY) Object... args) throws IOException, CompileException {
        return (Long) execute(code, args);
    }

    public Integer eval(String code, Integer resultType, @DataTypeHint(inputGroup = InputGroup.ANY) Object... args) throws IOException, CompileException {
        return (Integer) execute(code, args);
    }


    public Object execute(String code, @DataTypeHint(inputGroup = InputGroup.ANY) Object... args) throws IOException, CompileException {

        if (dynamicCode == null) {

            String newCode = codePrx + code + codeSux;

            log.info("code: \n "+newCode);

            dynamicCode = (DynamicCodeIF) ClassBodyEvaluator.createFastClassBodyEvaluator(
                    new Scanner(null, new StringReader(newCode)),
                    DynamicCodeIF.class, (ClassLoader) null);

            log.info("fun: \n " + dynamicCode);

        }
        return dynamicCode.execute(args);
    }


    public static void main(String[] args) throws CompileException, IOException {
        ObjectDynamicUtil dynamicUtil = new ObjectDynamicUtil();

        System.out.println(dynamicUtil.eval(" for (Object arg : args) {\n" +
                "            if (arg !=null){\n" +
                "                return true;\n" +
                "            }\n" +
                "        }\n" +
                "        return false;", false, 1, null));

        System.out.println(dynamicUtil.eval(null, false, 1, null));

        System.out.println(dynamicUtil.eval(null, false, 1, null));
        System.out.println(dynamicUtil.eval(null, false, 1, null));

    }
}
