//package com.alibaba.blink.udx.udtf;
//
//import com.util.CategoryTransportUserTagIds;
//import org.apache.flink.table.annotation.DataTypeHint;
//import org.apache.flink.table.annotation.FunctionHint;
//import org.apache.flink.table.functions.TableFunction;
//import org.apache.flink.types.Row;
//
//import java.util.List;
//
///**
// * Author: WangLin
// * Date: 2022/10/9 上午10:14
// * Description: 解析列表数据，一变多
// */
//@FunctionHint(output = @DataTypeHint("ROW<f0 STRING>"))
//public class ListAnalysis extends TableFunction<Row> {
//
//	public void eval(String s) {
//
//		try {
//			String[] list = s.split(",");
//			for (String s1 : list) {
//				if(isWithEnums(s1)){
//					Row row = new Row(1);
//					row.setField(0, s1);
//					collect(row);
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//	}
//
//	private static boolean isWithEnums(String userTagId) {
//		List<String> userTagIdsByProduct = CategoryTransportUserTagIds.getUserTagIdsByProduct();
//		int i = userTagIdsByProduct.indexOf(userTagId);
//		return i != -1;
//	}
//
//	public static void main(String[] args) {
//		String s = "555";
//		boolean withEnums = isWithEnums(s);
//		System.out.println(withEnums);
//	}
//}
