package com.alibaba.blink.udx.udf;


import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Author: WangLin
 * Date: 2022/9/28 下14:52
 * Description: 整数相除保留固定小数
 */
public class DivideKeepDecimals extends ScalarFunction {

	private static org.slf4j.Logger logger = LoggerFactory.getLogger(DivideKeepDecimals.class);

	public String eval(Long a, Long b, Integer decimal) {

		try {
			return String.format("%." + decimal + "f", ((a.doubleValue() / b.doubleValue()) * 100)) + "%";
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public String eval(Integer a, Integer b, Integer decimal) {

		try {
			return String.format("%." + decimal + "f", ((a.doubleValue() / b.doubleValue()) * 100)) + "%";
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
