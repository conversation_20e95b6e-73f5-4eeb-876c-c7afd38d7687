package com.alibaba.blink.udx.udf;

import com.google.common.util.concurrent.RateLimiter;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class StreamLimiter extends ScalarFunction {

    private static final Logger LOGGER = LoggerFactory.getLogger(StreamLimiter.class);

    private static final long serialVersionUID = 1L;

    RateLimiter rateLimiter = null;

    public String eval(String mess, int limit) {
        if (rateLimiter == null) {
            System.out.println(Thread.currentThread().getName() + " 创建限流器");
            rateLimiter = RateLimiter.create(limit);
            LOGGER.info(Thread.currentThread().getName() + " 创建限流器:{}", rateLimiter);
        }

        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_TIME);
        boolean b = rateLimiter.tryAcquire();
        while (!b) {
            b = rateLimiter.tryAcquire();
        }
        return mess;
    }


    public static void main(String[] args) throws InterruptedException {

        StreamLimiter streamLimiter = new StreamLimiter();
        for (int i = 0; i < 10000; i++) {
            streamLimiter.eval(i + "", 100);
        }

    }

}
