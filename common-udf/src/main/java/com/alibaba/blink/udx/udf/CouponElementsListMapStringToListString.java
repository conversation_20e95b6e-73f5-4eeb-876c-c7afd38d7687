package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR> 张天龙
 * @Project: udx
 * @Package com.poizon.dataware.udf
 * @date Date : 2023年03月20日 17:56
 * 需求：
 * 逻辑：
 */
@Slf4j
public class CouponElementsListMapStringToListString extends ScalarFunction {

	public String getName() {
		return "CouponElementsListMapStringToListString";
	}

	public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object list) {
		if (list == null || StringUtils.isBlank(list.toString())) {
			return null;
		}
		ArrayList<String> result = new ArrayList<>();
		final JSONArray jsonArray;
		try {
			jsonArray = JSON.parseArray(list.toString());
			for (Object o : jsonArray) {
				final JSONObject jsonObject = JSON.parseObject(o.toString());
				final String fd = jsonObject.getString("fd");
				if (StringUtils.isNotBlank(fd)) {
					result.add(fd);
				}
				final String sd = jsonObject.getString("sd");
				if (StringUtils.isNotBlank(sd)) {
					result.add(sd);
				}
				try {
					final JSONArray td = jsonObject.getJSONArray("td");
					if (td != null && td.size() > 0) {
						for (Object oi : td) {
							result.add(oi.toString());
						}
					}
				} catch (Exception e) {
					log.error("inner parse error {},{}", e, list);
				}
			}
			return JSON.toJSONString(result);
		} catch (Exception e) {
			log.error("parse error {},{}", e, list);
		}
		return null;
	}

	public static void main(String[] args) {
//		final Map<String, String> fd = new HashMap<>();
//		fd.put("fd", "111");
//		fd.put("td", "[]");
//		fd.put("sd", "222");
//		final ArrayList<Map<String, String>> hashMaps = new ArrayList<>();
//		hashMaps.add(fd);
		final CouponElementsListMapStringToListString couponElementsListMapStringToListString = new CouponElementsListMapStringToListString();
//		final String eval = listMapStringToListString.eval(JSON.toJSONString(hashMaps));
		final String eval = couponElementsListMapStringToListString.eval("[\"{\\\"fd\\\":\\\"125\\\"}\",\"{\\\"fd\\\":\\\"107\\\"}\",\"{\\\"fd\\\":\\\"29\\\"}\"]");
		System.out.println(eval);


		final HashMap<String, String> sd = new HashMap<>();
		final HashMap<String, String> td = new HashMap<>();

	}

}
