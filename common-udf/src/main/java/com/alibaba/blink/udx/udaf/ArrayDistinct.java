package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.HashSetAcc;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.util.StringUtils;

public class ArrayDistinct extends AggregateFunction<String, HashSetAcc> {

	@Override
	public HashSetAcc createAccumulator() {
		return new HashSetAcc();
	}

	@Override
	public String getValue(HashSetAcc accumulator) {
		return accumulator.getValue();
	}

	public void accumulate(HashSetAcc accumulator, String value) {
		boolean legalValue = isLegalValue(value);
		if (legalValue) {
			accumulator.add(value);
		}
	}

	private boolean isLegalValue(String value) {
		if (!StringUtils.isNullOrWhitespaceOnly(value)) {
			if (!"[\"\"]".equals(value) && !"[]".equals(value) && !"[,]".equals(value)) {
				return true;
			}
		}
		return false;
	}
}
