package com.alibaba.blink.udx.udf;

import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

public class MapRemoveUdf extends ScalarFunction {
    public String eval(String message, String... keys) {
        Gson gson = new Gson();
        TreeMap<String, Object> map = gson.<TreeMap<String, Object>>fromJson(message, (Class)TreeMap.class);

        for (int i = 0; i < keys.length; i++) {
            String key = keys[i];
            map.remove(key);
        }

        return gson.toJson(map);
    }

    public String eval(String message) {
        Gson gson = new Gson();
        TreeMap<String, Object> map = gson.<TreeMap<String, Object>>fromJson(message, (Class)TreeMap.class);

        Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Object> entry = iterator.next();
            String key = entry.getKey();
            Object value = entry.getValue();
            if ("null".equals(value.toString()) || StringUtils.isBlank(value.toString()))
            {
                map.put(key, "");
            }
        }
        return gson.toJson(map);
    }
}
