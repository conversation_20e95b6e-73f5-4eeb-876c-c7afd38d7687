package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.typeSerializer.Roaring64NavigableMapTypeSerializer;
import com.google.common.hash.Hashing;
import org.apache.flink.shaded.guava30.com.google.common.cache.Cache;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/23 20:24
 */
public class BitMapDistinctFunction extends AggregateFunction<Long, Roaring64NavigableMap> {


    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> {
                    DataType type = DataTypes.RAW(
                            Roaring64NavigableMap.class,
                            Roaring64NavigableMapTypeSerializer.INSTANCE
                    );
                    return Optional.of(type);
                })
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.BIGINT()))
                .build()
                ;
    }

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
    }

    public void accumulate(Roaring64NavigableMap accumulator, Long value) {
        if (null != value) {
            accumulator.addLong(value);
        }
    }

    public void accumulate(Roaring64NavigableMap accumulator, String value) {
        if (null != value) {
            accumulate(accumulator, Hashing.farmHashFingerprint64().hashString(value, StandardCharsets.UTF_8).asLong());
        }
    }

    public void retract(Roaring64NavigableMap accumulator, String value) {

    }

    public void merge(Roaring64NavigableMap accumulator, Iterable<Roaring64NavigableMap> its) {
        for (Roaring64NavigableMap acc : its) {
            accumulator.or(acc);
        }
    }

    @Override
    public Long getValue(Roaring64NavigableMap accumulator) {
        return accumulator.getLongCardinality();
    }

    @Override
    public Roaring64NavigableMap createAccumulator() {
        return new Roaring64NavigableMap();
    }

    public static void main(String[] args) {
        Roaring64NavigableMapTypeSerializer roaring64NavigableMapTypeSerializer = new Roaring64NavigableMapTypeSerializer();
        Roaring64NavigableMap roaring64NavigableMap = new Roaring64NavigableMap();
        Roaring64NavigableMap copy = roaring64NavigableMapTypeSerializer.copy(roaring64NavigableMap);
        System.out.println(copy.getLongCardinality());
        System.out.println(DataTypes.RAW(
                Roaring64NavigableMap.class,
                Roaring64NavigableMapTypeSerializer.INSTANCE
        ));

        BitMapDistinctFunction bitMapDistinctFunction = new BitMapDistinctFunction();

        bitMapDistinctFunction.accumulate(roaring64NavigableMap, "10000L");
        bitMapDistinctFunction.accumulate(roaring64NavigableMap, "20000L");
        bitMapDistinctFunction.accumulate(roaring64NavigableMap, "30000L");
        bitMapDistinctFunction.accumulate(roaring64NavigableMap, "40000L");


        bitMapDistinctFunction.accumulate(copy, "10000L");
        bitMapDistinctFunction.accumulate(copy, "20000L");
        bitMapDistinctFunction.accumulate(copy, "60000L");
        bitMapDistinctFunction.accumulate(copy, "50000L");

        bitMapDistinctFunction.merge(roaring64NavigableMap, Collections.singletonList(copy));

        System.out.println(bitMapDistinctFunction.getValue(roaring64NavigableMap));
    }

}
