package com.alibaba.blink.udx.udaf.acc;

import org.apache.flink.shaded.guava30.com.google.common.collect.Iterables;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.api.common.typeinfo.Types;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.StreamSupport;

/***
 * 支持retract-stream 的 通用 district acc
 */
public class DistinctAccWithRetract {
    public MapView<String, Long> distinctValueMap;
    public long mapCount;

    public DistinctAccWithRetract() {
        this.distinctValueMap = new MapView<>(Types.STRING, Types.LONG);
        this.mapCount = 0;
    }

    public void add(String params) {
        try {
            Long currentCnt = distinctValueMap.get(params);
            if (currentCnt != null) {
                distinctValueMap.put(params, currentCnt + 1L);
            } else {
                distinctValueMap.put(params, 1L);
                mapCount++;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public long getDistinctCnt() {
        return this.mapCount;
    }

    public String getArrayStr() {
        return Arrays.toString(getArray());
    }

    public String getArrayStr(String outputTYpe) throws Exception {

        if ("[]".equals(outputTYpe)) {
            return Arrays.toString(getArray());
        } else if ("![]".equals(outputTYpe)) {
            int flag = 0;
            StringBuilder sb = new StringBuilder();
            for (String key : this.distinctValueMap.keys()) {
                if (flag != 0) {
                    sb.append(",");
                } else
                    flag++;
                sb.append(key);
            }
            return sb.toString();
        } else
            throw new UnsupportedOperationException();
    }

    public String[] getArray() {
        try {
            return StreamSupport.stream(this.distinctValueMap.keys().spliterator(), false)
                    .toArray(String[]::new);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void remove(String params) {
        try {
            Long currentCnt = distinctValueMap.get(params);
            if (currentCnt != null) {
                if (currentCnt == 1) {
                    distinctValueMap.remove(params);
                    mapCount--;
                } else {
                    distinctValueMap.put(params, currentCnt - 1L);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void merge(DistinctAccWithRetract otherAcc) {
        try {
            for (Map.Entry<String, Long> element : otherAcc.elements()) {
                String key = element.getKey();
                long num = this.distinctValueMap.get(key) == null ? 0L : this.distinctValueMap.get(key);
                this.distinctValueMap.put(key, num + element.getValue());
                if (num == 0L) {
                    mapCount++;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Set<Map.Entry<String, Long>> elements() {
        return distinctValueMap.getMap().entrySet();
    }

    public void reset() {
        distinctValueMap.clear();
        mapCount = 0L;
    }

    public static void main(String[] args) throws Exception {
        DistinctAccWithRetract distinctAccWithRetract = new DistinctAccWithRetract();
        System.out.println(distinctAccWithRetract.getDistinctCnt());
        System.out.println(distinctAccWithRetract.getArrayStr());
    }
}
