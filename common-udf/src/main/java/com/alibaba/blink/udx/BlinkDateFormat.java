package com.alibaba.blink.udx;

import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.utils.DateTimeUtils;

public class BlinkDateFormat extends ScalarFunction {

    public String eval(String tsText, String oldDateFormat, String newDateFormat) {
        if (tsText == null) {
            return null;
        }
        TimestampData timestampData = DateTimeUtils.parseTimestampData(tsText, oldDateFormat);

        if (timestampData == null) {
            return null;
        }

        return DateTimeUtils.dateFormat(timestampData, newDateFormat);
    }
}
