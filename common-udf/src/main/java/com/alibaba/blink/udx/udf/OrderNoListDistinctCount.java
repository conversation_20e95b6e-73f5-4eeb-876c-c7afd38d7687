package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashSet;
import java.util.Set;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class OrderNoListDistinctCount extends ScalarFunction {

    public Integer eval(String orderNoList1, String orderNoList2) {
        Set<String> uniqueOrders = new HashSet<>();
        addToSet(uniqueOrders, orderNoList1);
        addToSet(uniqueOrders, orderNoList2);
        return uniqueOrders.size();
    }

    private void addToSet(Set<String> set, String list) {
        if (list == null || list.trim().isEmpty()) {
            return;
        }
        String[] parts = list.split(",");
        for (String part : parts) {
            String trimmed = part.trim();
            if (!trimmed.isEmpty()) {
                set.add(trimmed);
            }
        }
    }
}