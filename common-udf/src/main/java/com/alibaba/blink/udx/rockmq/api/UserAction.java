package com.alibaba.blink.udx.rockmq.api;

import com.alibaba.blink.udx.rockmq.bean.QueryReq;
import com.alibaba.blink.udx.rockmq.bean.QueryResult;
import com.alibaba.blink.udx.rockmq.bean.SinkMessage;
import com.alibaba.blink.udx.rockmq.bean.UserActionMsg;
import com.alibaba.blink.udx.rockmq.consts.Consts;
import com.alibaba.blink.udx.rockmq.service.AttributeServiceBackUp;
import com.alibaba.blink.udx.rockmq.utils.LongUtils;
import com.alibaba.blink.udx.rockmq.utils.TimeUtils;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


public class UserAction {

    public static Logger LOG = LoggerFactory.getLogger(UserAction.class);

    public AttributeServiceBackUp attributeService;

    public Gson gson = new Gson();

    public UserAction(String ip, int port) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).tagIp(ip).tagPort(port).
                build();
    }

    public UserAction(String ip, int port,int db,String password) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).clickDB(db).clickPassword(password)
                .tagIp(ip).tagPort(port).tagDB(db).tagPassword(password).
                build();
    }

    public UserAction(String ip, int port,int db) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(ip).clickPort(port).clickDB(db).
                build();
    }

    public UserAction(String clickIp, int clickPort, String clickPassword, int clickDB,
                      String tagIp, int tagPort, String tagPassword, int tagDB,
                      String retainIp, int retainPort, String retainPassword, int retainDB) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(clickIp).clickPort(clickPort).clickPassword(clickPassword).clickDB(clickDB)
                .tagIp(tagIp).tagPort(tagPort).tagPassword(tagPassword).tagDB(tagDB)
                .retainIp(retainIp).retainPort(retainPort).retainPassword(retainPassword).retainDB(retainDB).
                        build();
    }

    public UserAction(String hbaseUserName,String hbasePassword,String hbaseZookeeperQuorum,
                      String tagIp, int tagPort, String tagPassword, int tagDB) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder()
                .hbaseUsername(hbaseUserName)
                .hbasePassword(hbasePassword)
                .hbaseZookeeperQuorum(hbaseZookeeperQuorum)
                .tagIp(tagIp).tagPort(tagPort).tagPassword(tagPassword).tagDB(tagDB)
                .build();
    }

    private static volatile UserAction singleton;

    private UserAction() {
    }

    public static UserAction getInstance(String ip, int port) throws IOException {
        if (singleton == null) {
            synchronized (UserAction.class) {
                if (singleton == null) {
                    singleton = new UserAction(ip,port);
                }
            }
        }
        return singleton;
    }

    public static UserAction getInstance(String ip, int port,int db) throws IOException {
        if (singleton == null) {
            synchronized (UserAction.class) {
                if (singleton == null) {
                    singleton = new UserAction(ip,port,db);
                }
            }
        }
        return singleton;
    }

    public static UserAction getInstance(String ip, int port,int db,String password) throws IOException {
        if (singleton == null) {
            synchronized (UserAction.class) {
                if (singleton == null) {
                    singleton = new UserAction(ip,port,db,password);
                }
            }
        }
        return singleton;
    }

    public static UserAction getInstance(String clickIp, int clickPort, String clickPassword, int clickDB,
                                         String tagIp, int tagPort, String tagPassword, int tagDB,
                                         String retainIp, int retainPort, String retainPassword, int retainDB) throws IOException {
        if (singleton == null) {
            synchronized (UserAction.class) {
                if (singleton == null) {
                    singleton = new UserAction(clickIp, clickPort, clickPassword, clickDB,
                            tagIp, tagPort, tagPassword, tagDB,
                            retainIp, retainPort, retainPassword, retainDB);
                }
            }
        }
        return singleton;
    }

    public static UserAction getHbaseInstance(String hbaseUserName,String hbasePassword,String hbaseZookeeperQuorum,
                                              String tagIp, int tagPort, String tagPassword, int tagDB) throws IOException {
        if (singleton == null) {
            synchronized (UserAction.class) {
                if (singleton == null) {
                    singleton = new UserAction(hbaseUserName,hbasePassword,hbaseZookeeperQuorum, tagIp,tagPort,tagPassword,tagDB);
                }
            }
        }
        return singleton;
    }



    /**
     *功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    public String deal(String value) throws Exception {
        UserActionMsg data = null;
        String log = " 【原始数据】:" + value;
        try {
            data = gson.fromJson(value, UserActionMsg.class);
        } catch (Exception e) {
            LOG.error("[kafka]消费用户关键行为数据失败:{}", value, e);
            List<SinkMessage> sinkMessageList =new ArrayList<>();
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "消费用户关键行为数据失败，解析异常", "",-1L);
            temp.setMessageLog(value);
            sinkMessageList.add(temp);
            return gson.toJson(sinkMessageList);
        }

        QueryReq params = userActionFormatParam(data);

        String event = data.getEvent();
        switch (event) {
            case "activity_signin_is_sucessful_click": {
                if (registerFormatParam(data, params))
                {
                    return null;
                }
                return gson.toJson(register(params,log));
            }
            case "order_place": {
                params.setUserId(data.getUserid());
                return gson.toJson(startOrder(params,log));
            }
            case "trade_order_place":
            case "trade_order_pay":
            case "trade_box_block_click":
            case "trade_product_step_pageview": {
                setUserIdStrToLong(data, params);
                return gson.toJson(trade(params,log));
            }
            case "activate_app": {
                activateAppSet(data, params);
                return active(params, log);
            }
            default:
                //do nothing
                return "";
        }
    }

    /**
     *功能描述 data.getType() != 0则结束方法
     * <AUTHOR>
     * @date 2022/2/24
     * @param
     * @return
     */
    private boolean registerFormatParam(UserActionMsg data, QueryReq params) throws ParseException {
        if (data.getType() != 0) {
            return true;
        }
        params.setOs(data.getPlatform());
        params.setUuid(data.getUuid());
        params.setAppVersion(data.getVersion());
        params.setPkgChannel(data.getChannel());

        setUserIdDoubleToLong(data, params);

        // 兼容两种时间格式
        long t = LongUtils.valueOf(data.getCreate_time());
        if (t > 0) {
            params.setEventTime(t);
        } else {
            params.setEventTime(TimeUtils.getTimeStampSecond(
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(data.getCreate_time()).getTime())
            );
        }
        //               if t, _ :=strconv.ParseInt(data.CreateTime, 10, 64);
//                t > 0 {
//                    params.EventTime = t
//                } else{
        //输入2022-02-11 15:34:31，输出1644564871，对于的时间戳仍是2022-02-11 15:34:31，"2006-01-02 15:04:05"的作用仅表示日期格式
//                    et, _ :=time.ParseInLocation("2006-01-02 15:04:05", data.CreateTime, consts.Location)
//                    params.EventTime = et.Unix()
//                }
        return false;
    }

    private void setUserIdStrToLong(UserActionMsg data, QueryReq params) {
        try {
            if(data.getUser_id() instanceof String)
            {
                String userId = (String) data.getUser_id();
                params.setUserId(LongUtils.valueOf(userId));
            }
        } catch (Exception e) {
            LOG.error("setUserIdStrToLong，data.getUser_id():{}",data.getUser_id());
        }
    }

    /**
     *功能描述 待校验
     * <AUTHOR>
     * @date 2022/2/14
     * @param
     * @return
     */
    private void setUserIdDoubleToLong(UserActionMsg data, QueryReq params) {
        try {
            if(data.getUser_id() instanceof Double)
            {
                double userId = (double) data.getUser_id();
                params.setUserId((long) userId);
            }
        } catch (Exception e) {
            LOG.error("setUserIdDoubleToLong，data.getUser_id():{}",data.getUser_id());
        }
//                if userId, ok :=data.UserId.(float64);
//                ok {
//                    params.UserId = int64(userId)
//                }
    }

    /**
     *功能描述
     * startPay 则返回sinkMessageList集合为0
     * pay 根据是否uuid是否有值，返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    private List<SinkMessage> trade(QueryReq params, String value) throws InvocationTargetException, IllegalAccessException {
        return attributeService.order(params,value);
    }

    private List<SinkMessage> startOrder(QueryReq params,String value) throws  InvocationTargetException, IllegalAccessException {
        return attributeService.startOrder(params,value);
    }

    /**
     *功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    private List<SinkMessage> register(QueryReq params,String value) throws InvocationTargetException, IllegalAccessException {
        return attributeService.register(params,value);
    }


    /**
     *功能描述 日活处理
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     * @date 2022/2/23
     * @param
     * @return
     */
    public String active(QueryReq params, String log) throws Exception {
        return attributeService.active(params,0,1, log);
    }

    public String showActive(QueryReq params, String log, String hbaseTableName) throws Exception {
        return attributeService.showActive(params,0,1, log,hbaseTableName);
    }

    @Deprecated
    public QueryResult queryDebug(QueryReq params)throws Exception {
        //查询点击流数据
//        QueryResult queryResult = attributeService.queryDebug(params, 0L, 1);
        return null;
    }


    public String dealShowUserAction(String value,String hbaseTableName) throws Exception {
        UserActionMsg data;
        String log = " 【原始数据】:" + value;
        try {
            data = gson.fromJson(value, UserActionMsg.class);
        } catch (Exception e) {
            LOG.error("[kafka]消费用户关键行为数据失败:{}", value, e);
            List<SinkMessage> sinkMessageList =new ArrayList<>();
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "消费用户关键行为数据失败，解析异常", "",-1L);
            temp.setMessageLog(log);
            sinkMessageList.add(temp);
            return gson.toJson(sinkMessageList);
        }

        QueryReq params = userActionFormatParam(data);

        String event = data.getEvent();
        switch (event) {
            case "activate_app": {
                activateAppSet(data, params);
                return showActive(params, log,hbaseTableName);
            }
            default:
                return "";
        }
    }

    private void activateAppSet(UserActionMsg data, QueryReq params) {
        params.setUserId(data.getUserid());
        params.setLastActiveTime(LongUtils.valueOf(data.getLast_active_time()));
        params.setDayFirstActiveTime(LongUtils.valueOf(data.getDay_first_active_time()));
    }

    private QueryReq userActionFormatParam(UserActionMsg data) {
        QueryReq params = new QueryReq();
        params.setAppId(Consts.APP_DEWU);
        params.setPkgChannel(data.getAndroid_channel());
        params.setSource(data.getEvent());
        params.setUuid(data.getDevice_uuid());
        params.setUa(data.getUser_agent());
        params.setOs(data.getLib());
        if (StringUtils.isBlank(params.getOs())) {
            params.setOs(data.getOs());
        }

        params.setOaid(data.getOaid());
        params.setImei(data.getImei());
        params.setAndroidId(data.getAndroid_id());
        params.setIdfa(data.getIdfa());
        params.setIp(data.getIp());
        params.setIpvx(data.getIpvx());
        params.setFcuuid(data.getFcuuid());
        params.setShumeiId(data.getShumei_id());
        params.setAppVersion(data.getApp_version());

        Long t = LongUtils.valueOf(data.getReceive_time());
        if (t > 0) {
            params.setEventTime(t);
        } else {
            params.setEventTime(LongUtils.valueOf(data.getEvent_time()));
        }
        return params;
    }

}
