package com.alibaba.blink.udx;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("INT")},
        output = @DataTypeHint("STRING"))
public class TimeWindow extends TableFunction<String> {

    private SimpleDateFormat format ;

    @Override
    public void open(FunctionContext context){
        format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    }

    public void eval(String str,Integer n) throws ParseException {
        for (int i = 0 ; i < n ; i++){
            collect(format.format(new Date(format.parse(str).getTime() + i * 1000 * 60)));
        }
    }
}
