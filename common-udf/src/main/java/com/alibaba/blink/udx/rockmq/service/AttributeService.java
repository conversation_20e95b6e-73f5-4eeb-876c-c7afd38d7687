package com.alibaba.blink.udx.rockmq.service;//package com.alibaba.blink.udx.rockmq.service;
//
//import com.alibaba.blink.udx.rockmq.bean.*;
//import com.alibaba.blink.udx.rockmq.bean.QueryReq;
//import com.alibaba.blink.udx.rockmq.bean.SearchClickResult;
//import com.alibaba.blink.udx.rockmq.consts.Consts;
//import com.alibaba.blink.udx.rockmq.utils.*;
//import com.alibaba.blink.udx.rockmq.consts.RedisConsts;
//import com.google.gson.Gson;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import java.util.*;
//
//public class AttributeService {
//
//    public static Logger LOG = LoggerFactory.getLogger(AttributeService.class);
//    private Gson gson = new Gson();
//
//    public AttributeService() {
//
//    }
//
//    public static String[] types = {
//            "", Consts.TypeRecallDefault
//    };
//
//    public JedisSington clickJedisSington;
//    public JedisSington tagJedisSington;
//
//
//    private String clickIp;
//    private Integer clickPort;
//    private String clickPassword;
//    private Integer clickDB;
//    private String tagIp;
//    private Integer tagPort;
//    private String tagPassword;
//    private Integer tagDB;
//
//    private AttributeService(Builder builder) {
//        this.clickIp = builder.clickIp;
//        this.clickPort = builder.clickPort;
//        this.clickPassword = builder.clickPassword;
//        this.clickDB =  builder.clickDB;
//        this.tagIp = builder.tagIp;
//        this.tagPort = builder.tagPort;
//        this.tagPassword = builder.tagPassword;
//        this.tagDB = builder.tagDB;
//        if(clickDB == null && clickPassword == null&&clickIp!=null&&clickPort!=null){
//            this.clickJedisSington = new JedisSington(clickIp, clickPort);
//        }else if(clickDB != null && clickPassword != null&&clickIp!=null&&clickPort!=null){
//            this.clickJedisSington = new JedisSington(clickIp, clickPort,clickPassword,clickDB);
//        }
//
//        if(tagDB == null && tagPassword == null&&tagIp!=null&&tagPort!=null){
//            this.tagJedisSington = new JedisSington(tagIp, tagPort);
//        }else if(tagDB != null && tagPassword != null&&tagIp!=null&&tagPort!=null){
//            this.tagJedisSington = new JedisSington(tagIp, tagPort,tagPassword,tagDB);
//        }
//    }
//
//
//    public static class Builder {
//
//        private String clickIp;
//        private Integer clickPort;
//        private String clickPassword;
//        private Integer clickDB;
//
//        private String tagIp;
//        private Integer tagPort;
//        private String tagPassword;
//        private Integer tagDB;
//
//        public Builder clickIp(String clickIp) {
//            this.clickIp = clickIp;
//            return this;
//        }
//
//        public Builder clickPort(Integer clickPort) {
//            this.clickPort = clickPort;
//            return this;
//        }
//
//        public Builder clickPassword(String clickPassword) {
//            this.clickPassword = clickPassword;
//            return this;
//        }
//
//        public Builder clickDB(Integer clickDB) {
//            this.clickDB = clickDB;
//            return this;
//        }
//
//        public Builder tagIp(String tagIp) {
//            this.tagIp = tagIp;
//            return this;
//        }
//
//        public Builder tagPort(Integer tagPort) {
//            this.tagPort = tagPort;
//            return this;
//        }
//
//        public Builder tagPassword(String tagPassword) {
//            this.tagPassword = tagPassword;
//            return this;
//        }
//
//        public Builder tagDB(Integer tagDB) {
//            this.tagDB = tagDB;
//            return this;
//        }
//
//        public AttributeService build() {
//            return new AttributeService(this);
//        }
//    }
//
//
//    // 下单回传
//    /**
//     *功能描述
//     * startPay 则返回sinkMessageList集合为0
//     * pay 根据是否uuid是否有值，返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
//     * <AUTHOR>
//     * @date 2022/2/23
//     * @param
//     * @return
//     */
//    public List<SinkMessage> order(QueryReq params) throws InterruptedException {
//        List<SinkMessage> sinkMessageList = new ArrayList();
//
//        //该if逻辑，从Kafka消费消息不会走该处理逻辑，该处理逻辑仅在 下单的web接口中进行处理
//        //---------------------------------------------------------
//        if (Consts.SourceStartPay.equals(params.getSource())) {
//            // 发起下单，以orderNo为key存入redis
//            try {
//                String set = RedisUtils.set(clickJedisSington, RedisConsts.getOrderNoKey(params.getOrderNo()), gson.toJson(params), "nx", "ex", 48 * 60 * 60);
//            } catch (Exception e) {
//                LOG.error("[redis]获取发起下单信息失败:{}", params);
//                return sinkMessageList;
//            }
//            return sinkMessageList;
//        } else if (Consts.SourcePay.equals(params.getSource())) {
//            String orderInfo = "";
//            try {
//                orderInfo = RedisUtils.get(clickJedisSington, RedisConsts.getOrderNoKey(params.getOrderNo()));
//            } catch (Exception e) {
//                LOG.error("[redis]获取发起下单信息失败:{}", params);
//                return sinkMessageList;
//            }
//            // 赋值
//            QueryReq queryReq1 = gson.fromJson(orderInfo, QueryReq.class);
//            queryReq1.setEventTime(params.getEventTime());
//            queryReq1.setSource(params.getSource());
//            params = queryReq1;
//        }
//        //---------------------------------------------------------
//
//        // 匹配回传媒体
//        if (StringUtils.isNotBlank(params.getUuid())) {
//            callbackTodayAction(params, Consts.TypeOrder,sinkMessageList);
//            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeOrder, Consts.MqDelayTimeUserAction);
//            sinkMessageList.add(sinkMessage);
//            return sinkMessageList;
//        }
//        return sinkMessageList;
//    }
//
//
//    public static long lockReturn=0;
//    public static long getRetainData=0;
//    public static long getEventTime_et=0;
//    public static long etDay_etBefore7Day=0;
//    public static long etDay_etNowDay=0;
//    public static long setRetainData=0;
//    public static long return_count=0;
//
//    /**
//     *功能描述
//     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
//     * <AUTHOR>
//     * @date 2022/2/23
//     * @param
//     * @return
//     */
//    public List<SinkMessage> register(QueryReq params) throws InterruptedException {
//        List<SinkMessage> sinkMessageList = new ArrayList();
//        callbackTodayAction(params, Consts.TypeRegister,sinkMessageList);
//        SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeRegister, Consts.MqDelayTimeUserAction);
//        sinkMessageList.add(sinkMessage);
//        // 放入延时队列，防止激活延时后匹配不到
//        return sinkMessageList;
//    }
//
//
//    public List<SinkMessage> activate(String value) throws Exception {
//        List<SinkMessage> sinkMessageList = new ArrayList();
//        QueryReq param;
//        try{
//            param = gson.fromJson(value, QueryReq.class);
//        }catch (Exception e){
//            System.out.println(value);
//            return sinkMessageList;
//        }
//
//        SearchClickResult searchClickResult = searchClickData(param);
//        Map<String,String> data = searchClickResult.getData();
//        if(data == null || data.size() == 0  || !searchClickResult.getNew()){
//            return sinkMessageList;
//        }
//        // 赋值用户类型
//        data.put(Consts.UserType,Consts.UserTypeDeliveryNew);
//        if("1".equals(data.get(Consts.AntiSpam))){
//            sinkMessageList.add(SinkMessageUtils.sendActiveDelayMsg(param,data));
//        }else{
//            callbackActivate(param,data,Consts.RiskNo,sinkMessageList);
//        }
//
//        sinkMessageList.add(SinkMessageUtils.sendLtvActiveDelayMsg(param.getUuid()));
//        return sinkMessageList;
//    }
//
//
//
//    /**
//     *功能描述
//     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
//     * @date 2022/2/23
//     * @param
//     * @return
//     */
//    public String active(QueryReq params) throws Exception {
//        List<SinkMessage> sinkMessageList = new ArrayList();
//        if(!recall(params,sinkMessageList)){
//            // 延迟发送
//            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeActivate,Consts.MqDelayTimeRecall);
//            sinkMessageList.add(sinkMessage);
//        }
//
//        // 留存回传
//        callbackRetain(params,sinkMessageList);
//
////      塞入延时队列，防止激活延时后没有匹配到次留
//        Calendar calendar = Calendar.getInstance();
//        int hourOfDay = TimeUtils.getHourOfDay(calendar);
//        int minute = TimeUtils.getMinute(calendar);
//        if(hourOfDay==23&&minute>=54)
//        {
//            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeRetain,Consts.MqDelayTimeUserAction);
//            sinkMessageList.add(sinkMessage);
//        }
//
//        return gson.toJson(sinkMessageList);
//    }
//
//
//
//    /**
//     *功能描述
//     * 返回sinkMessageList集合为0或者返回sinkMessageList集合  回调kafka消息,kafka消息，rocketmq消息(tag MsgTagActiveRisk)
//     * @date 2022/2/23
//     * @param
//     * @return
//     */
//    public Boolean recall(QueryReq params,List<SinkMessage> sinkMessageList) throws Exception {
//        Boolean success = false;
//        QueryResult result = query(params);
//        Map<String,String> data = result.getData();
//        if(data == null || data.size() == 0){
//            return success;
//        }
//        success = true;
//        switch (data.get(Consts.Recall)){
//            case Consts.TypeRecallDefault: {
//                Long et = LongUtils.valueOf(data.get(Consts.EventTime));
//                if (params.getDayFirstActiveTime() -et > 21600 || et-params.getDayFirstActiveTime() > 120) {
//                    return success;
//                }
//                data.put(Consts.UserType, Consts.UserTypeDeliveryOld);
//                break;
//            }
//            case Consts.TypeRecallSleepy: {
//                if (params.getLastActiveTime() == 0) {
//                    return success;
//                }
//
//                if (params.getLastActiveTime() > 10000000000L) {
//                    params.setLastActiveTime(params.getLastActiveTime() / 1000);
//                }
//
//                // 获取投放的用户沉默时间
//                Integer inactive = 180;
//                if (StringUtils.isNotBlank(data.get(Consts.Inactive)))
//                {
//                    Integer t = IntegerUtils.valueOf(data.get(Consts.Inactive));
//                    if(t>0)
//                    {
//                        inactive = t;
//                    }
//                }
//
//                if (params.getLastActiveTime() + inactive * 24 * 3600 <= System.currentTimeMillis()) {
//                    return success;
//                }
//                data.put(Consts.UserType, Consts.UserTypeDeliverySleepy);
//                break;
//            }
//            default:
//                return success;
//        }
//
//        if("1".equals(data.get(Consts.AntiSpam))){
//            sinkMessageList.add(SinkMessageUtils.sendActiveDelayMsg(params,data));
//        }
//        callbackActivate(params,data,Consts.RiskNo,sinkMessageList);
//
//        return success;
//    }
//
//    // 投放承接
//    private QueryResult query(QueryReq params) throws Exception {
//        QueryResp resp = new QueryResp();
//        resp.setUserId(params.getUserId());
//        resp.setOs(params.getOs());
//
//        SearchClickResult result = searchClickData(params);
//
//        Map<String,String> data = result.getData();
//        resp.setNew(result.getNew());
//        if(data==null||data.size() == 0){
//            return new QueryResult(resp,null);
//        }
//
//        resp.setDeviceType(data.get(Consts.DeviceType));
//
//        // To do 白名单
////        whiteList := config.GetWhiteListMap()
////        isWhite := false
////        if whiteList[params.Idfa] == 1 || whiteList[params.Fcuuid] == 1 || whiteList[params.AndroidId] == 1 {
////            isWhite = true
////        }
//
//        // 赋值，新用户才做承接
////        if(resp.getNew()||isWhite){
////        if(resp.getNew()){
////            resp.setModuleType(data.get(Consts.ModuleType));
////            resp.setProjectId(data.get(Consts.PackageName));
////            resp.setTags(data.get(Consts.Tags));
////            resp.setContentId(data.get(Consts.ContentId));
////            resp.setContentTagId(data.get(Consts.ContentTagId));
////        }
//
//
////        Map<String,String> newData = new HashMap();
////        newData.put(Consts.EventTime,data.get(Consts.EventTime));
////        newData.put(Consts.Media,data.get(Consts.Media));
////        newData.put(Consts.AlgoTags,data.get(Consts.AlgoTags));
////        newData.put(Consts.SpuTags,data.get(Consts.SpuTags));
////        newData.put(Consts.Tags,data.get(Consts.Tags));
////        newData.put(Consts.ContentId,data.get(Consts.ContentId));
////
////        // 用户标签存入redis
////        setUserTags(params,newData,resp);
////        // 承接标签存入redis
////        setUndertakeTags(params.getUserId(),resp.getNew(),resp.getDeviceType());
//
//        return new QueryResult(resp,data);
//    }
//
//    // To do
//    private void setUserTags(QueryReq params,Map<String,String> data,QueryResp resp){
//        if(params.getUserId() == 0){
//            return;
//        }
//        String redisKey = RedisConsts.getUserKey(params.getUserId());
//        if(clickJedisSington.getJedis().exists(redisKey)){
//            return;
//        }
//
//        // 若是通过ipua匹配的，判断点击时间，1小时内的才做承接
//        if(resp.getDeviceType().equals(Consts.IpUa) && System.currentTimeMillis()/1000 - Long.valueOf(data.get(Consts.EventTime)) > 3600){
//            return;
//        }
//    }
//
//    // To do
//    private void setUndertakeTags(Long userId,Boolean isNew,String deviceType){
//        if(userId == 0 || !isNew || "".equals(deviceType)){
//            return;
//        }
//        String redisKey = RedisConsts.getUserUndertakeKey(userId);
//        if(clickJedisSington.getJedis().exists(redisKey)){
//            return;
//        }
//        UndertakeTags tags = new UndertakeTags(userId);
//        clickJedisSington.getJedis().set(redisKey,gson.toJson(tags));
//        clickJedisSington.getJedis().expire(redisKey,7*24*3600);
//    }
//
//
//    /**
//     *功能描述 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息
//     * params  uuid, Ios则 Idfa 和 Fcuuid，Android 则 AndroidId
//     * redisdata:recall，event_time,media
//     * @date 2022/2/23
//     * @param
//     * @return
//     */
//    //   func (svc *AttributeService) CallbackTodayAction(params *request.QueryReq, eventType string) {
//    public void callbackTodayAction(QueryReq params, String eventType,List<SinkMessage> sinkMessageList) throws InterruptedException {
//        // 参数格式处理
//        formatParams(params);
//        // 加锁
//        if (!RedisUtils.lock(clickJedisSington, RedisConsts.getCallbackLockKey(params.getUuid(), eventType))) {
//            lockReturn++;
//            return;
//        }
//        // 获取查找key
//        String[] keys = getCallbackRedisKeys(params);
//
//        for (int i = 0; i < types.length; i++) {
//            String t = types[0];
//            Map<String, String> data = RedisUtils.getRetainData(clickJedisSington, keys, t);
//            //判断hset 中 eventType 的值是否已经设置为1了，如果设置则跳过本次循环
//            if (data.size() == 0 || "1".equals(data.get(eventType))) {
//                getRetainData++;
//                continue;
//            }
//
//            if (Consts.TypeRecallDefault.equals(data.get(Consts.Recall))
//                    && eventType.equals(Consts.TypeOrder)) {
//
//                String eventTime = data.get(Consts.EventTime);
//                Long et = LongUtils.valueOf(eventTime);
//                if (params.getEventTime() - et > 86400) {
//                    getEventTime_et++;
//                    continue;
//                }
//            } else {
//                String eventTime = data.get(Consts.EventTime);
//                Long et = LongUtils.valueOf(eventTime);
//
//                int etDay = TimeUtils.getyyyyMMdd(et);
//                int etBefore7Day = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(),-7);
//                int etNowDay = TimeUtils.getyyyyMMdd(params.getEventTime());
//                if (Consts.KuaiShou.equals(data.get(Consts.Media)) && eventType.equals(Consts.TypeOrder)) {
//                    if (etDay < etBefore7Day) {
//                        etDay_etBefore7Day++;
//                        continue;
//                    }
//                } else {
//                    if (etDay != etNowDay) {
//                        etDay_etNowDay++;
//                        continue;
//                    }
//                }
////                etDay:=time.Unix(et, 0).Format("2006-01-02")
////                // 快手回传7日内下单
////                if data[consts.Media] == consts.KuaiShou && eventType == consts.TypeOrder {
////                    if etDay<time.Unix (params.EventTime, 0).Add(-7 * 24 * time.Hour).Format("2006-01-02") {
////                        continue
////                    }
////                } else{
////                    // 只回传当天下单
////                    if etDay != time.Unix(params.EventTime, 0).Format("2006-01-02") {
////                        continue
////                    }
////                }
//            }
//            setRetainData++;
//            // 存入redis，标识已回传
//            RedisUtils.setRetainData(clickJedisSington, keys, eventType, t);
//            // 回传媒体
//            sinkMessageList.add(SinkMessageUtils.sendCallbackMediaKafka(params, data, eventType));
//            sinkMessageList.add(SinkMessageUtils.sendKafka(params, data, eventType));
//        }
//        return_count++;
//        return;
//    }
//
//
//    /**
//     *功能描述
//     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息
//     * @date 2022/2/23
//     * @param
//     * @return
//     */
//    public void callbackRetain(QueryReq params,List<SinkMessage> sinkMessageList) throws InterruptedException {
//
//        if (!RedisUtils.lock(clickJedisSington, RedisConsts.getCallbackLockKey(params.getUuid(), Consts.TypeRetain))) {
//            return;
//        }
//        // 获取查找key
//        String[] keys = getCallbackRedisKeys(params);
//        for(String t : types){
//            Map<String,String> data = RedisUtils.getRetainData(clickJedisSington,keys,t);
//            if(data.size() == 0){
//                continue;
//            }
//            String retainType = "";
//            Long et = LongUtils.valueOf(data.get(Consts.EventTime));
//            int etDay = TimeUtils.getyyyyMMdd(et);
//            int day1 = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -1);
//            int day7 = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -7);
//            if(etDay==day1)
//            {
//                retainType = Consts.TypeRetain;
//            }else if(etDay==day7)
//            {
//                retainType = Consts.TypeRetain7;
//            }
////            if(getTodayStartMs(params.getEventTime()) - getTodayStartMs(LongUtils.valueOf(data.get(Consts.EventTime))) == 3600 * 24){
////                retainType = Consts.TypeRetain;
////            }else if(getTodayStartMs(params.getEventTime()) - getTodayStartMs(LongUtils.valueOf(data.get(Consts.EventTime))) == 3600 * 24 * 7){
////                retainType = Consts.TypeRetain7;
////            }
//            if(StringUtils.isBlank(retainType) || "1".equals(data.get(retainType))){
//                continue;
//            }
//            RedisUtils.setRetainData(clickJedisSington,keys,retainType,t);
//
//            sinkMessageList.add(SinkMessageUtils.sendCallbackMediaKafka(params,data,retainType));
//            sinkMessageList.add(SinkMessageUtils.sendKafka(params,data,retainType));
//        }
//    }
//
//    private Long getTodayStartMs(Long ms){
//        return ms - ms % (3600 * 24);
//    }
//
//    /**
//     * 功能描述 待
//     *
//     * @param
//     * @return
//     * <AUTHOR>
//     * @date 2022/2/14
//     */
//    // 格式化处理参数
//    public void formatParams(QueryReq params) {
//        // 去除空格
//
//        params.setImei(StringUtils.trim(params.getImei()));
//        params.setAndroidId(StringUtils.trim(params.getAndroidId()));
//        params.setOaid(StringUtils.trim(params.getOaid()));
//        params.setIdfa(StringUtils.trim(params.getIdfa()));
//        params.setFcuuid(StringUtils.trim(params.getFcuuid()));
//        params.setUuid(StringUtils.trim(params.getUuid()));
//        params.setOs(StringUtils.lowerCase(params.getOs()));
//        params.setIpua(BaseUtils.calIpUa(params.getIp(), params.getUa()));
//
//
//        // os统一处理
//        if ("iphone".equals(params.getOs())) {
//            params.setOs(Consts.Ios);
//        }
//
//        if (params.getOs().equals(Consts.Ios)) {
//            // 老版本只有uuid，进行赋值
//            if (params.getUuid().contains("-")) {
//                if ("".equals(params.getIdfa())) {
//                    params.setIdfa(params.getUuid());
//                }
//            } else if (!"".equals(params.getUuid())) {
//                if ("".equals(params.getFcuuid())) {
//                    params.setFcuuid(params.getUuid());
//                }
//            }
//            params.setFcuuid(params.getFcuuid().replaceFirst("UUID", ""));
////            params.Fcuuid = strings.Replace(params.Fcuuid, "UUID", "", 1)
//            params.setUuid(params.getUuid().replaceFirst("UUID", ""));
////            params.Uuid = strings.Replace(params.Uuid, "UUID", "", 1)
//        } else {
//            if ("".equals(params.getAndroidId()) && !"".equals(params.getUuid())) {
//                params.setAndroidId(params.getUuid());
//            }
//        }
//
//        if (params.getEventTime() > 10000000000L) {
//            params.setEventTime(params.getEventTime() / 1000);
//        } else if (params.getEventTime() == 0) {
////           params.EventTime = time.Now().Unix();// 时间戳到秒
//            params.setEventTime(BaseUtils.getTimeStampSecond(System.currentTimeMillis()));
//        }
//
//        if (params.getDayFirstActiveTime() > 10000000000L) {
//            params.setDayFirstActiveTime(params.getDayFirstActiveTime() / 1000);
//        }
//    }
//
//
//    /**
//     * 功能描述 根据操作系统获取redis健
//     *  Ios 返回 Idfa 和 Fcuuid
//     *  Android 返回 AndroidId
//     *  获取设备的唯一编号，ios和Android 获取方式不同
//     * @param
//     * @return
//     * <AUTHOR>
//     * @date 2022/2/14
//     */
//    //    func (svc *AttributeService) getCallbackRedisKeys(params *request.QueryReq) (keys []string) {
//    public String[] getCallbackRedisKeys(QueryReq params) {
//        // 根据操作系统获取redis key
//        String[] keys;
//        if (params.getOs().equals(Consts.Ios)) {
//            keys = new String[]{params.getIdfa(), params.getFcuuid()};
////            keys = []string{
////                params.Idfa,
////                        params.Fcuuid,
////            }
//        } else {
//            keys = new String[]{params.getAndroidId()};
////            keys = []string{params.AndroidId}
//        }
//
//        return keys;
//    }
//
//    /**
//     * 激活数据使用设备 id 匹配点击上报数据。
//     * @param params
//     * @return
//     */
//    public SearchClickResult searchClickData(QueryReq params) throws Exception {
//        String deviceType = "";
//        formatParams(params);
//        List<String> keys;
//        Map<String,String> data;
//        Boolean isNew = false;
//        if (params.getOs().equals(Consts.Ios)){
//            keys = BaseUtils.getDeviceKeys(params.getIdfa());
//            data = RedisUtils.getClickData(clickJedisSington,keys, RedisConsts.RedisClick,0,1);
//            if(data!=null&&data.size() > 0){
//                deviceType = Consts.Idfa;
//            }else if (StringUtils.isNotBlank(params.getIpua())){
//                keys = new ArrayList();
//                keys.add(params.getIpua());
//                data = RedisUtils.getClickData(clickJedisSington,keys,RedisConsts.RedisClick,0,1);
//                if(data.size() > 0){
//                    // aso只能通过idfa归因
//                    if(data.get(Consts.Media).equals(Consts.Aso)){
//                        data = new HashMap();
//                    }else{
//                        deviceType = Consts.Ipua;
//                    }
//                }
//            }
//
//            isNew = validateNewUser(params.getFcuuid());
//            if(isNew && !validateNewUser(params.getIdfa())){
//                isNew = false;
//            }
//        }else{
//            Map<String,String> deviceIds = new LinkedHashMap();
//            deviceIds.put(Consts.Oaid,params.getOaid());
//            deviceIds.put(Consts.Imei,params.getImei());
//            deviceIds.put(Consts.AndroidId,params.getAndroidId());
//            data = new HashMap();
//            for (Iterator it = deviceIds.entrySet().iterator(); it.hasNext(); ) {
//                Map.Entry obj = (Map.Entry)it.next();
//                keys = BaseUtils.getDeviceKeys(obj.getValue().toString());
//                if(keys.size() == 0){
//                    continue;
//                }
//                data = RedisUtils.getClickData(clickJedisSington,keys,RedisConsts.RedisClick,0,1);
//                if(data.size() > 0){
//                    deviceType = obj.getKey().toString();
//                    break;
//                }
//            }
//            isNew = validateNewUser(params.getAndroidId());
//        }
//        if (!"".equals(deviceType)) {
//            data.put(Consts.DeviceType,deviceType);
//        }
//        return new SearchClickResult(data,isNew);
//    }
//
//    /**
//     * 判断是否新用户，如果没查到或者 date 是今天，则为新用户。
//     * @param deviceId
//     * @return
//     */
//    private Boolean validateNewUser(String deviceId){
//        if("".equals(deviceId)|| "null".equals(deviceId)){
//            return true;
//        }
//        String md5 = BaseUtils.md5(deviceId);
//        String date = RedisUtils.hget(tagJedisSington,md5.substring(0,5),deviceId);
//        if(date == null || date.equals(BaseUtils.getCurrentDate())){
//            return true;
//        }
//        return false;
//    }
//
//    /**
//     * 激活回传
//     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息
//     * @param params
//     * @param data
//     * @param riskLevel
//     * @param sinkMessageList
//     */
//    public void callbackActivate(QueryReq params,Map<String,String> data,Integer riskLevel,List<SinkMessage> sinkMessageList){
//        if ("1".equals(data.get(Consts.Used))) {
//            return;
//        }
//        data = pickActivateParam(data,params);
//        Map<String,String> redisData = new HashMap();
//        redisData.put(Consts.TypeActivate,"1");
//        for (String k : data.keySet()){
//            redisData.put(k,data.get(k));
//        }
//
//        String[] keys = getCallbackRedisKeys(params);
//        Map<String,String> mdata = RedisUtils.getRetainData(clickJedisSington,keys,data.get(Consts.Recall));
//        if("1".equals(mdata.get(Consts.TypeActivate)) && (StringUtils.isBlank(data.get(Consts.DeviceType)) ||
//                StringUtils.isNotBlank(mdata.get(Consts.DeviceType)) )){
//            return;
//        }
//        if(!riskLevel.equals(Consts.RiskHigh)){
//            if(Consts.TypeRecallDefault.equals(data.get(Consts.Recall))){
//                RedisUtils.mSetRetainData(clickJedisSington,keys,redisData, 48 * 3600);
//            }else{
//                RedisUtils.mSetRetainData(clickJedisSington,keys,redisData, 8 * 24 * 3600);
//            }
//            SinkMessage sinkMessage = SinkMessageUtils.sendCallbackMediaKafka(params, data, Consts.TypeActivate);
//            sinkMessageList.add(sinkMessage);
//        }
//
//        // 标记点击已使用
////        if(StringUtils.isNotBlank(data.get(Consts.DeviceType))){
////            try {
////                RedisUtils.hset(clickJedisSington, RedisConsts.RedisClick + data.get(data.get(Consts.DeviceType)), Consts.Used, "1");
////            }catch (Exception e)
////            {
//////                logs.Error(err, "[redis]设置点击已使用标签失败, %+v", data)
////                 throw e;
////            }
////        }
//        data.put(Consts.SpamLevel,String.valueOf(riskLevel));
//        sinkMessageList.add(SinkMessageUtils.sendKafka(params,data,Consts.TypeActivate));
//
//    }
//
//    private Map<String,String> pickActivateParam(Map<String,String> data,QueryReq params){
//        data.put(Consts.UserId,String.valueOf(params.getUserId()));
//        data.put(Consts.Fcuuid,params.getFcuuid());
//        data.put(Consts.Uuid,params.getUuid());
//        data.put(Consts.PkgChannel,params.getPkgChannel());
//        data.put(Consts.EventTime,String.valueOf(params.getEventTime()));
//        if("".equals(data.get(Consts.DeviceType))){
//            data.put(Consts.UserType,Consts.UserTypeNature) ;
//            data.put(Consts.Rid,BaseUtils.uuid());//分配uudi
//            data.put(Consts.Tid,data.get(Consts.Rid));
//            data.put(Consts.AppId,Consts.APP_DEWU);
//        }
//        return data;
//    }
//}
