package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @ClassName: UnixTimestampUsec
 * @Description:
 * @author: scl
 * @date: 2022/4/19  12:35 下午
 */
public class UnixTimestampUsec extends ScalarFunction {

	//处理毫秒
	@FunctionHint(
			input = {@DataTypeHint("STRING")},
			output = @DataTypeHint("BIGINT")
	)
	public long eval(String value) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		Date date = sdf.parse(value.substring(0, 23));
		return date.getTime();
	}

	//处理毫秒,通用配置
	@FunctionHint(
			input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("INT"), @DataTypeHint("INT")},
			output = @DataTypeHint("BIGINT")
	)
	public long eval(String value, String format, int startIndex, int endIndex) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		Date date = sdf.parse(value.substring(startIndex, endIndex));
		return date.getTime();
	}


	//处理微妙
	@FunctionHint(
			input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
			output = @DataTypeHint("BIGINT")
	)
	public long eval(String value, String flag) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = sdf.parse(value.substring(0, 19));
		long time = date.getTime();
		return Long.valueOf(String.valueOf(time).substring(0, 10) + value.substring(20, 26));
	}


	//处理微妙,通用配置
	//format只到秒yyyy-MM-dd HH:mm:ss
	@FunctionHint(
			input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("INT"), @DataTypeHint("INT"), @DataTypeHint("INT"), @DataTypeHint("INT")},
			output = @DataTypeHint("BIGINT")
	)
	public long eval(String value, String format, int startIndex, int endIndex, int startNanoIndex, int endNanoIndex) throws ParseException {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		Date date = sdf.parse(value.substring(startIndex, endIndex));
		long time = date.getTime();
		String beforeTime = String.valueOf(time).substring(0, 10);
		String nanoTime = value.substring(startNanoIndex, endNanoIndex);
//        String.valueOf(time).substring(0, 10) 去掉后面的毫秒精度
		return Long.valueOf(beforeTime + nanoTime);
	}


	public static void main(String[] args) throws ParseException {
		UnixTimestampUsec strParseDate = new UnixTimestampUsec();
		System.out.println(strParseDate.eval("2021-12-08 21:05:14.517000", "nano"));
		System.out.println(strParseDate.eval("2021-12-08 21:05:14.517000"));
		System.out.println(System.nanoTime());
	}
}
