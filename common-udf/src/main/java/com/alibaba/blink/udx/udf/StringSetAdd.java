package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.util.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/11/1 16:57
 */
public class StringSetAdd extends ScalarFunction {
    public String eval(String arr01,String arr02){

        Set<String> set01 = StrToArray(arr01);
        Set<String> set02 = StrToArray(arr02);
        set01.addAll(set02);

        Iterator<String> it = set01.iterator();
        if (! it.hasNext()){
            return "[]";
        }

        StringBuilder sb = new StringBuilder();
        sb.append('[');
        for (;;) {
            String e = it.next();
            sb.append(e);
            if (! it.hasNext())
                return sb.append(']').toString();
            sb.append(',');
        }
    }

    public Set<String> StrToArray(String str){

        if (StringUtils.isNullOrWhitespaceOnly(str)){
            return new HashSet<String>();
        }

        String etlStr = str.replaceAll("(\\[)|(\\])|(\\s*)","");

        return new HashSet<>(Arrays.asList(etlStr.split(",")));
    }
}
