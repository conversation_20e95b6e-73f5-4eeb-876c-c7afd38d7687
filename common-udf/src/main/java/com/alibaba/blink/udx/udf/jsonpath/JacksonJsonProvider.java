package com.alibaba.blink.udx.udf.jsonpath;


import com.jayway.jsonpath.InvalidJsonException;
import com.jayway.jsonpath.spi.json.AbstractJsonProvider;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonGenerator;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectReader;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;















public class JacksonJsonProvider
        extends AbstractJsonProvider
{
    private static final ObjectMapper defaultObjectMapper = new ObjectMapper();
    private static final ObjectReader defaultObjectReader = defaultObjectMapper.reader().withType(Object.class);

    protected ObjectMapper objectMapper;
    protected ObjectReader objectReader;

    public ObjectMapper getObjectMapper() {
        return this.objectMapper;
    }




    public JacksonJsonProvider() {
        this(defaultObjectMapper, defaultObjectReader);
    }





    public JacksonJsonProvider(ObjectMapper objectMapper) {
        this(objectMapper, objectMapper.reader().withType(Object.class));
    }






    public JacksonJsonProvider(ObjectMapper objectMapper, ObjectReader objectReader) {
        this.objectMapper = objectMapper;
        this.objectReader = objectReader;
    }


    public Object parse(String json) throws InvalidJsonException {
        try {
            return this.objectReader.readValue(json);
        } catch (IOException e) {
            throw new InvalidJsonException(e, json);
        }
    }


    public Object parse(InputStream jsonStream, String charset) throws InvalidJsonException {
        try {
            return this.objectReader.readValue(new InputStreamReader(jsonStream, charset));
        } catch (IOException e) {
            throw new InvalidJsonException(e);
        }
    }


    public String toJson(Object obj) {
        StringWriter writer = new StringWriter();
        try {
            JsonGenerator generator = this.objectMapper.getFactory().createGenerator(writer);
            this.objectMapper.writeValue(generator, obj);
            writer.flush();
            writer.close();
            generator.close();
            return writer.getBuffer().toString();
        } catch (IOException e) {
            throw new InvalidJsonException();
        }
    }


    public List<Object> createArray() {
        return new LinkedList();
    }


    public Object createMap() {
        return new LinkedHashMap<Object, Object>();
    }
}
