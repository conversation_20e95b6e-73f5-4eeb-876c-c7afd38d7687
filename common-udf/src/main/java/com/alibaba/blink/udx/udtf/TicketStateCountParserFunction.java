package com.alibaba.blink.udx.udtf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.Objects;


/**
 * 功能描述
 *
 * @param
 * <AUTHOR>
 * @date 2021/8/15
 * @return
 */
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s0 STRING, s1 STRING, s2 STRING>")
)
@Slf4j
public class TicketStateCountParserFunction extends TableFunction<Row> {

    private static final Logger logger = LoggerFactory.getLogger(TicketStateCountParserFunction.class);

    /**
     * 功能描述
     * status，assignee，assignee_group_id 字段，ddl定义不能为null，默认0
     * <AUTHOR>
     * @date 2021/8/18
     * @param
     * @return
     */
    public void eval(String status,String old_status,String newId,String oldId) throws Exception {
        try {
            // insert 操作，赋值status
            if(StringUtils.isBlank(old_status) && StringUtils.isBlank(oldId))
            {
                Row row1 = new Row(3);
                row1.setField(0,newId);
                row1.setField(1, status);
                row1.setField(2, null);
                collect(row1);
            }
            //id和status同时变更
            else if( !Objects.equals(oldId,newId) && !Objects.equals(old_status,status) )
            {
                Row row = new Row(3);
                row.setField(0,oldId);
                row.setField(1, null);
                row.setField(2, old_status);
                collect(row);

                Row row1 = new Row(3);
                row1.setField(0,newId);
                row1.setField(1, status);
                row1.setField(2, null);
                collect(row1);
            }

            //仅变更ID，不变更status
            else if( !Objects.equals(oldId,newId) && Objects.equals(old_status,status) )
            {
                Row row = new Row(3);
                row.setField(0,oldId);
                row.setField(1, null);
                row.setField(2, status);
                collect(row);

                Row row1 = new Row(3);
                row1.setField(0,newId);
                row1.setField(1, status);
                row1.setField(2, null);
                collect(row1);
            }

            //仅变更status
            else if( Objects.equals(oldId,newId) && !Objects.equals(old_status,status) )
            {
                Row row = new Row(3);
                row.setField(0,newId);
                row.setField(1, status);
                row.setField(2, null);
                collect(row);

                Row row1 = new Row(3);
                row1.setField(0,newId);
                row1.setField(1, null);
                row1.setField(2, old_status);
                collect(row1);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(stream));
            String exception = stream.toString();
            throw new Exception(exception);
        }
    }

    public static void main(String[] args) throws Exception {


    }

}
