package com.alibaba.blink.udx.udaf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.api.dataview.ListView;
import org.apache.flink.table.functions.AggregateFunction;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/3/2 17:19
 */
@Slf4j
public class MergeStringToArray extends AggregateFunction<String, MergeStringToArray.ArrayAccumulator> {

    @Override
    public String getValue(ArrayAccumulator accumulator) {
        try {
            return accumulator.getValue();
        } catch (Exception e) {
            log.error("get value error! accumulator size={};list={}", accumulator.count, accumulator.list.getList());
            throw new RuntimeException(e);
        }
    }

    public void accumulate(ArrayAccumulator accumulator, String value) throws Exception {
        if (StringUtils.isNotEmpty(value)) {
            accumulator.add(value);
        }
    }

    @Override
    public ArrayAccumulator createAccumulator() {
        return new ArrayAccumulator();
    }

    public void retract(ArrayAccumulator accumulator, String value) {

    }

    public void merge(ArrayAccumulator accumulator, Iterable<ArrayAccumulator> iterables) throws Exception {
        for (ArrayAccumulator iterable : iterables) {
            accumulator.merge(iterable);
        }
    }

    public static class ArrayAccumulator {
        @DataTypeHint("ARRAY<STRING>")
        public ListView<String> list = new ListView<>();
        public long count = 0L;

        public ArrayAccumulator() {
        }

        public void add(String value) throws Exception {
            list.add(value);
            count++;
        }

        public void merge(ArrayAccumulator other) throws Exception {

            if (other != null) {
                this.list.addAll(other.list.getList());
                this.count = this.count + other.count;
            }
        }

        public String getValue() throws Exception {
            if (count == 0L) {
                return "[]";
            }
            return "[" +
                    String.join(",", list.get()) +
                    "]"
                    ;
        }
    }

    public static void main(String[] args) throws Exception {
        MergeStringToArray a = new MergeStringToArray();
        ArrayAccumulator acc = new ArrayAccumulator();
        String v1 = "{\"data\":[{\"id\":\"28480\",\"account_id\":\"********\",\"media_id\":\"3\",\"account_mode\":\"1\",\"system\":\"2\",\"delivery_goal\":\"1\",\"delivery_mode\":\"1\",\"operation_mode\":\"1\",\"channel\":\"快手\",\"agent_id\":\"7\",\"status\":\"1\",\"modify_time\":\"2023-03-01 14:21:40\",\"create_time\":\"2022-08-08 09:55:12\",\"delivery_type\":\"1\",\"account_remark\":\"\"}],\"database\":\"dw_delivery_marketing_api\",\"es\":*************,\"id\":********,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint(20) unsigned\",\"account_id\":\"varchar(128)\",\"media_id\":\"bigint(20) unsigned\",\"account_mode\":\"tinyint(4)\",\"system\":\"tinyint(4)\",\"delivery_goal\":\"tinyint(4)\",\"delivery_mode\":\"varchar(16)\",\"operation_mode\":\"tinyint(4)\",\"channel\":\"varchar(64)\",\"agent_id\":\"bigint(20) unsigned\",\"status\":\"tinyint(4)\",\"modify_time\":\"datetime\",\"create_time\":\"datetime\",\"delivery_type\":\"tinyint(4)\",\"account_remark\":\"varchar(256)\"},\"old\":[{\"system\":\"1\",\"modify_time\":\"2023-02-28 13:53:58\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":12,\"media_id\":-5,\"account_mode\":-6,\"system\":-6,\"delivery_goal\":-6,\"delivery_mode\":12,\"operation_mode\":-6,\"channel\":12,\"agent_id\":-5,\"status\":-6,\"modify_time\":93,\"create_time\":93,\"delivery_type\":-6,\"account_remark\":12},\"table\":\"dp_mg_account\",\"ts\":*************,\"type\":\"UPDATE\"}";
        String v2 = "{\"data\":[{\"id\":\"28480\",\"account_id\":\"********\",\"media_id\":\"3\",\"account_mode\":\"1\",\"system\":\"2\",\"delivery_goal\":\"1\",\"delivery_mode\":\"1\",\"operation_mode\":\"1\",\"channel\":\"快手\",\"agent_id\":\"7\",\"status\":\"1\",\"modify_time\":\"2023-03-01 14:21:40\",\"create_time\":\"2022-08-08 09:55:12\",\"delivery_type\":\"1\",\"account_remark\":\"\"}],\"database\":\"dw_delivery_marketing_api\",\"es\":*************,\"id\":********,\"isDdl\":false,\"mysqlType\":{\"id\":\"bigint(20) unsigned\",\"account_id\":\"varchar(128)\",\"media_id\":\"bigint(20) unsigned\",\"account_mode\":\"tinyint(4)\",\"system\":\"tinyint(4)\",\"delivery_goal\":\"tinyint(4)\",\"delivery_mode\":\"varchar(16)\",\"operation_mode\":\"tinyint(4)\",\"channel\":\"varchar(64)\",\"agent_id\":\"bigint(20) unsigned\",\"status\":\"tinyint(4)\",\"modify_time\":\"datetime\",\"create_time\":\"datetime\",\"delivery_type\":\"tinyint(4)\",\"account_remark\":\"varchar(256)\"},\"old\":[{\"system\":\"1\",\"modify_time\":\"2023-02-28 13:53:58\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":-5,\"account_id\":12,\"media_id\":-5,\"account_mode\":-6,\"system\":-6,\"delivery_goal\":-6,\"delivery_mode\":12,\"operation_mode\":-6,\"channel\":12,\"agent_id\":-5,\"status\":-6,\"modify_time\":93,\"create_time\":93,\"delivery_type\":-6,\"account_remark\":12},\"table\":\"dp_mg_account\",\"ts\":*************,\"type\":\"UPDATE\"}";
        a.accumulate(acc, v1);
        a.accumulate(acc, v2);

        System.out.println(a.getValue(acc));

    }

}
