package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * community_tab_click的community_tab_type_title需要数据这边补充
 * 逻辑：写死的
 * https://duapp.yuque.com/team_product/nrwss3/spsico
 * 写个UDF，后面后变化，修改udf即可，小兰会通知甜蜜，甜蜜会通知我.
 * Author: <PERSON><PERSON><PERSON>
 */
@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class CommunityTabTypeTitleRelation extends ScalarFunction {


    public  String eval(String community_tab_id) {

        if(community_tab_id!=null){
            switch (community_tab_id) {
                case "020300":
                case "S001":
                case "S002":
                case "S003":
                    return "穿搭";
                case "010100":
                case "020402":
                case "S004":
                case "S005":
                case "S006":
                case "S007":
                case "S008":
                case "S009":
                case "S010":
                    return "好物";
                case "030000":
                case "030100":
                case "070000":
                case "100100":
                case "100200":
                case "060000":
                case "120000":
                case "140100":
                    return "兴趣";
                default:
                    return "";
            }

        }else {
            return "";
        }

    }


}
