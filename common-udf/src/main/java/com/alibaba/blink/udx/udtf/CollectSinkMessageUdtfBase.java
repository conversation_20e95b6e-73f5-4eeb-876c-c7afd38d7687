package com.alibaba.blink.udx.udtf;

import com.alibaba.blink.udx.rockmq.bean.RocketMqMess;
import com.alibaba.blink.udx.rockmq.bean.SinkMessage;
import com.google.gson.Gson;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.List;

@FunctionHint(
        output = @DataTypeHint("ROW<a STRING, b STRING, c STRING, d STRING, e BIGINT, f STRING, g BIGINT, h STRING >")
)
public class CollectSinkMessageUdtfBase extends TableFunction<Row>
{
    public void collectSinkMessage(List<SinkMessage> sinkMessageList, String channel) {
        if (sinkMessageList != null) {
            Gson gson = new Gson();
            for (int i = 0; i < sinkMessageList.size(); i++) {
                SinkMessage sinkMessage = sinkMessageList.get(i);
                String type = sinkMessage.getType();
                if ("kafka".equals(type) || "call_back_kafka".equals(type) || "debug".equals(type)) {
                    Row row = new Row(8);
                    row.setField(0, type);
                    row.setField(1, sinkMessage.getData());
                    if (sinkMessage.getMessageTag() != null) {

                        row.setField(2, sinkMessage.getMessageTag());
                    } else {

                        row.setField(2, sinkMessage.getMessageLog());
                    }
                    row.setField(3, sinkMessage.getMessageKey());
                    row.setField(4, sinkMessage.getDeliverTime());
                    row.setField(5, sinkMessage.getDesc());
                    row.setField(6, sinkMessage.getTtl());
                    row.setField(7, sinkMessage.getRedisKey());
                    collect(row);
                } else if ("rocket_mq".equals(type)) {
                    RocketMqMess rocketMqMess = new RocketMqMess(sinkMessage.getData(), sinkMessage.getMessageTag(), channel);
                    Row row = new Row(8);
                    row.setField(0, "rocket_mq");
                    row.setField(1, gson.toJson(rocketMqMess));
                    if (sinkMessage.getMessageTag() != null) {

                        row.setField(2, sinkMessage.getMessageTag());
                    } else {

                        row.setField(2, sinkMessage.getMessageLog());
                    }
                    row.setField(3, sinkMessage.getMessageKey());
                    row.setField(4, sinkMessage.getDeliverTime());
                    row.setField(5, sinkMessage.getDesc());
                    row.setField(6, sinkMessage.getTtl());
                    row.setField(7, sinkMessage.getRedisKey());
                    collect(row);
                }
            }
        }
    }


//    public DataType getResultType(Object[] arguments, Class[] argTypes) {
//        return DataTypes.createRowType(
//                new InternalType[] {
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.STRING,
//                        Types.LONG,
//                        Types.STRING,
//                        Types.LONG,
//                        Types.STRING }
//        );
//    }
}
