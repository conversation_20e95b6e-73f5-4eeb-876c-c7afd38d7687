package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/4/14 15:26
 */
public class FixEventTimeMs extends ScalarFunction {
    public long eval(Long timeStamp) {

        long currentTimeMillis = System.currentTimeMillis();

        if (timeStamp == null || timeStamp <= 0) {
            return currentTimeMillis;
        }

        int digits = getLongDigits(timeStamp);

        switch (digits) {
            case 10:
                return Math.min(timeStamp * 1000, currentTimeMillis);
            case 13:
                return Math.min(timeStamp, currentTimeMillis);
            default:
                return currentTimeMillis;
        }
    }

    @Override
    public boolean isDeterministic() {
        return false;
    }

    public int getLongDigits(long n) {
        if (n == 0) {
            return 1;  // 如果 n = 0，则位数为1
        }
        return (int) Math.log10(Math.abs(n)) + 1;  // 如果 n != 0，则计算 n 的位数
    }

}
