package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Iterator;

/**
 * <AUTHOR>
 * @Date 2021/4/13 5:36 下午
 */
@Slf4j
@FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("BIGINT")},
        output = @DataTypeHint("DOUBLE"))
public class GetSubsidyValue extends ScalarFunction {
    public double eval(String str1, String str2, Long benefit) {
        double subsidyValue = 0;
        JSONArray jsonArray = null;
        try {
            jsonArray = JSONObject.parseArray(str1);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("异常的json是: " + str1);
        }
        Iterator<Object> iterator;
        String value = "";
        if (jsonArray != null) {
            if (jsonArray.size() == 0) {
                if ("PLATFORM".equals(str2)) {
                    subsidyValue = benefit;
                } else {
                    subsidyValue = 0;
                }
            }
            iterator = jsonArray.iterator();
            while (iterator.hasNext()) {
                value = iterator.next().toString();
                if (jsonArray.size() != 1) {
                    if (str2.equals(JSONObject.parseObject(value).get("funderType"))) {
                        subsidyValue = getValue(value,benefit);
                    }
                } else {
                    if (str2.equals(JSONObject.parseObject(value).get("funderType"))) {
                        subsidyValue = getValue(value,benefit);
                    } else {
                        subsidyValue = benefit - getValue(value, benefit);
                    }
                }
            }
        }
        return subsidyValue;
    }

    public static double getValue(String value,Long benefit) {
        double subsidyValue = 0;
        if ("CONSTANT".equals(JSONObject.parseObject(value).get("subsidyType"))) {
            subsidyValue = (int) JSONObject.parseObject(value).get("subsidyValue");
        } else if ("RATIO".equals(JSONObject.parseObject(value).get("subsidyType"))) {
            subsidyValue = ((int) JSONObject.parseObject(value).get("subsidyValue"))/100.0 * benefit;
        }
        return subsidyValue;
    }

    public static void main(String[] args) {
//        String str1 = "[{\"funderType\":\"PLATFORM\",\"subsidyType\":\"RATIO\",\"subsidyValue\":40},{\"funderType\":\"MERCHANT\",\"subsidyType\":\"RATIO\",\"subsidyValue\":60}]";
        String str1 = "[{\"funderType\":\"PLATFORM\",\"subsidyType\":\"CONSTANT\",\"subsidyValue\":1500}]";
//        String str1 = "";
        System.out.println(JSONObject.parseArray(str1));
        String str2 = "PLATFORM";
        String str3 = "MERCHANT";
        GetSubsidyValue getSubsidyInfoValue = new GetSubsidyValue();
        System.out.println(getSubsidyInfoValue.eval(str1, str2,2000L));
        System.out.println(getSubsidyInfoValue.eval(str1, str3,2000L));
    }
}
