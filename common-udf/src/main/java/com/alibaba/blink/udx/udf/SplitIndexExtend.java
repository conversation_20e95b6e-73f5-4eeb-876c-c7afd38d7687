package com.poizon.udf.json;

import org.apache.flink.table.functions.ScalarFunction;

/**
 * <AUTHOR> 张天龙
 * @Project: du-tech-data-udf
 * @Package com.poizon.udf.json
 * @date Date : 2022年12月29日 13:16
 * 需求：
 * 逻辑：
 */
public class SplitIndexExtend extends ScalarFunction {

	public String eval(String str, String separator, int index, int reverse) {
		final String result;
		try {
			final String[] split = str.split(separator);
			if (reverse == 1) {
				index = split.length - index - 1;
			}
			result = split[index];
		} catch (Exception e) {
			return "";
		}
		return result;
	}

	public static void main(String[] args) {
		String str = "a_b";
		final String eval = new SplitIndexExtend().eval(str, "_", 0, 1);
		System.out.println(eval);
	}
}
