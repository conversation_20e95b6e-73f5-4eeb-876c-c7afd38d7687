package com.alibaba.blink.udx.udaf;

import java.util.LinkedList;
import java.util.Queue;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.AggregateFunction;

/**
 * @Author: Dereck Li
 * @Date: 6/29/22 3:18 下午
 */

public class KeyByFunctionTest extends AggregateFunction<String, Queue<String>> {
  /**
   * 初始化AggregateFunction的accumulator
   * 系统在第一次做aggregate计算之前调用一次这个方法
   * @return
   */
  @Override
  public Queue<String> createAccumulator() {
    return new LinkedList<>();
  }

  @Override
  public String getValue(Queue<String> accumulator) {
    return accumulator.poll();
  }

  public void accumulate(Queue<String> accumulator,@DataTypeHint(inputGroup = InputGroup.ANY) Object value) {
    accumulator.offer(String.valueOf(value));
  }

  public void retract(Queue<String> accumulator, @DataTypeHint(inputGroup = InputGroup.ANY) Object value) {
  }

  public static void main (String[] args) {
    String test = "sss$bbb";
    String[] a = test.split("\\$");
    System.out.println(a[0]);

    String str = "182591639,1,P21070256706761,1,,,1530568,603302012,1,2,,0000-00-00 00:00:00,1,0,,0000-00-00 00:00:00,0000-00-00 00:00:00,2021-07-02 09:08:34.000,182556584,110110105209252284,58928794,,缄默0515,,2021-07-02 09:08:33,GZ02,1,JDVC09391765852,JD,110110105209252284,2,2,0000-00-00 00:00:00,1,,4458301,,0000-00-00 00:00:00,0,2021-07-04 08:59:09,GZ02,192387818,2021-07-02 09:08:33,1,110110105209252284,1,1,2,2021-07-04 14:46:10,GZ02,10269209,罗飞921220,null,null,187868429,110110105209252284,1,GZ02,90,0,,20,0,,P21070256706761,GC,1,2021-07-04 21:32:44,GZ02,0,0,1,0,0000-00-00 00:00:00,0,0,0000-00-00 00:00:00,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,H202107045831102596591676,0000-00-00 00:00:00,GZ02,0,JD,null,null,null,null,null,null,null,null,null,1,2021-07-04 08:59:09,GZ02,10,2021-07-04 21:32:48,GZ02,1,2021-07-04 21:32:47,GZ02,2021-07-04 21:32:50,GZ02,1000973,199,防晒服,2,服装,null,null,,蓝色 XL ,2021-07-05 19:41:06,166885618,3";
    String[] split = str.split(",");
    System.out.println(split[33]);
    System.out.println(split[56]);
    System.out.println(split[64]);
    System.out.println("sorting_detail_t_sorting_repository:"+split[49]);
    System.out.println("handoverbill_t_handover_code:"+split[109]);
    System.out.println(split[123]);
  }

}
