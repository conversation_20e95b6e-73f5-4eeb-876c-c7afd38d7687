package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.DistinctAccWithRetract;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.util.StringUtils;

import java.util.ArrayList;

/**
 * 聚合string[] 支持retract-stream
 */
public class ArrayDistinctWithRetractStream extends AggregateFunction<String, DistinctAccWithRetract> {

    public String outputType;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        outputType = context.getJobParameter("arrayDistinctWithRetractStream.output.type", "[]");
    }

    @Override
    public DistinctAccWithRetract createAccumulator() {
        return new DistinctAccWithRetract();
    }
    public void accumulate(DistinctAccWithRetract accumulator, String value)  {
        if (isLegalValue(value)){
            for (String str : StrToArray(value)) {
                accumulator.add(str);
            }
        }
    }
    @Override
    public String getValue(DistinctAccWithRetract accumulator) {
        try {
            return accumulator.getArrayStr(outputType);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    public void retract(DistinctAccWithRetract accumulator,  String value){
        if (isLegalValue(value)){
            for (String str : StrToArray(value)) {
                accumulator.remove(str);
            }
        }
    }
    public void merge(DistinctAccWithRetract accumulator, Iterable<DistinctAccWithRetract> its){
        for (DistinctAccWithRetract acc : its) {
            accumulator.merge(acc);
        }
    }
    public void resetAccumulator(DistinctAccWithRetract accumulator){
        accumulator.reset();

    }
    private boolean isLegalValue(String value) {
        if (!StringUtils.isNullOrWhitespaceOnly(value)) {
            return !"[\"\"]".equals(value) && !"[]".equals(value) && !"[,]".equals(value);
        }
        return false;
    }

    public String[] StrToArray(String str){
        String etlStr = str.replaceAll("(\\[)|(\\])|(\\s*)","");
        return etlStr.split(",");
    }

    public static void main(String[] args) throws Exception {
        ArrayDistinctWithRetractStream distinct = new ArrayDistinctWithRetractStream();
        distinct.outputType="![]";
        DistinctAccWithRetract accWithRetract = new DistinctAccWithRetract();
        distinct.accumulate(accWithRetract,"[SITE_SET_TENCENT_VIDEO  ,SITE_SET_MOBILE_UNION]");
        distinct.retract(accWithRetract,"[SITE_SET_TENCENT_VIDEO  ,SITE_SET_MOBILE_UNION]");
        distinct.accumulate(accWithRetract,"[SITE_SET_QQ_MUSIC_GAME ,SITE_SET_KANDIAN ]");
        distinct.accumulate(accWithRetract,"[SITE_SET_TENCENT_VIDEO,     SITE_SET_QQ_MUSIC_GAME,SITE_SET_TENCENT_NEWS]");
        String value = distinct.getValue(accWithRetract);
        System.out.println(value);

        DistinctAccWithRetract xx = new DistinctAccWithRetract();
        System.out.println(distinct.getValue(xx));

        DistinctAccWithRetract retract = new DistinctAccWithRetract();
        distinct.accumulate(retract,"[SITE_NCENT_VIDEO  ,SITE_SET_BILE_UNION]");
        distinct.accumulate(retract,"[SITE_S_QQ_MUSIC_GAME ,SITEET_KANDIAN ]");
        distinct.accumulate(retract,"[SITE_SET_TEENT_VIDEO,     E_SET_QQ_MUSIC_GAME,   SET_MOBILE_UNION,SITE_SET_TENCENT_NEWS]");

        DistinctAccWithRetract withRetract = new DistinctAccWithRetract();
        ArrayList<DistinctAccWithRetract> acc = new ArrayList<>();
        acc.add(accWithRetract);
        acc.add(retract);
        distinct.merge(withRetract,acc);

        System.out.println(distinct.getValue(withRetract));
    }
}
