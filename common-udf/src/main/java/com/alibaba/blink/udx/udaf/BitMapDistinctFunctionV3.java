package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.typeSerializer.Roaring64BitmapTypeSerializer;
import com.google.common.hash.Hashing;
import lombok.Data;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.roaringbitmap.longlong.Roaring64Bitmap;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/24 10:52
 */
public class BitMapDistinctFunctionV3 extends AggregateFunction<Long, BitMapDistinctFunctionV3.BitMapAccumulator> {

    public static final Integer BITMAP_MAP_KEY = 0;

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> {
                    DataType dataType = DataTypes.STRUCTURED(
                            BitMapAccumulator.class,
                            DataTypes.FIELD("mapSize", DataTypes.BIGINT()),
                            DataTypes.FIELD("bitmapMap", DataTypes.RAW(
                                            Roaring64Bitmap.class,
                                            Roaring64BitmapTypeSerializer.INSTANCE
                                    )
                            )
                    );
                    return Optional.of(dataType);
                })
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.BIGINT()))
                .build()
                ;
    }

    public void accumulate(BitMapAccumulator accumulator, Long value) {
        if (null != value) {
            accumulator.acc(value);
        }
    }

    public void accumulate(BitMapAccumulator accumulator, String value) {
        if (null != value) {
            accumulate(accumulator, Hashing.farmHashFingerprint64().hashString(value, StandardCharsets.UTF_8).asLong());
        }
    }

    public void retract(BitMapAccumulator accumulator, String value) {

    }

    public void merge(BitMapAccumulator accumulator, Iterable<BitMapAccumulator> its) {
        BitMapAccumulator reuse = new BitMapAccumulator();
        for (BitMapAccumulator acc : its) {
            reuse.merge(acc);
        }
        accumulator.merge(reuse);
    }

    @Override
    public Long getValue(BitMapAccumulator accumulator) {
        return accumulator.getValue();
    }

    @Override
    public BitMapAccumulator createAccumulator() {
        return new BitMapAccumulator();
    }

    @Data
    public static class BitMapAccumulator {
        public long mapSize = 0;
        public Roaring64Bitmap bitmapMap = new Roaring64Bitmap();

        public BitMapAccumulator() {
        }

        public Roaring64Bitmap getBitmapMap() {
            return Optional.ofNullable(bitmapMap).orElse(new Roaring64Bitmap());
        }

        public void acc(Long value) {

            if (null == value) {
                return;
            }

            Roaring64Bitmap roaring64Bitmap = getBitmapMap();
            if (!roaring64Bitmap.contains(value)) {
                roaring64Bitmap.add(value);
                mapSize += 1;
            }
            bitmapMap = roaring64Bitmap;

        }

        public long getValue() {
            return this.mapSize;
        }

        public void merge(BitMapAccumulator other) {
            if (other == null) {
                return;
            }
            Roaring64Bitmap otherMap = other.getBitmapMap();

            Roaring64Bitmap thisMap = getBitmapMap();

            otherMap.andNot(thisMap);

            this.mapSize = otherMap.getLongCardinality() + this.mapSize;

            thisMap.or(otherMap);

        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof BitMapAccumulator)) {
                return false;
            }
            BitMapAccumulator that = (BitMapAccumulator) o;
            return Objects.equals(mapSize, that.mapSize)
                    && bitmapMap.equals(that.bitmapMap)
                    ;
        }

        @Override
        public int hashCode() {
            return Objects.hash(mapSize, bitmapMap);
        }
    }

    public static void main(String[] args) {
        BitMapAccumulator accumulator01 = new BitMapAccumulator();
        BitMapAccumulator accumulator02 = new BitMapAccumulator();

        BitMapDistinctFunctionV3 functionV2 = new BitMapDistinctFunctionV3();

        functionV2.accumulate(accumulator01, 10000L);
        functionV2.accumulate(accumulator01, 20000L);
        functionV2.accumulate(accumulator01, -10000L);

        functionV2.accumulate(accumulator02, 10000L);
        functionV2.accumulate(accumulator02, 20000L);
        functionV2.accumulate(accumulator02, -30000L);
        functionV2.accumulate(accumulator02, 40000L);

        functionV2.merge(accumulator01, Collections.singletonList(accumulator02));

        System.out.println(functionV2.getValue(accumulator01));

    }

}
