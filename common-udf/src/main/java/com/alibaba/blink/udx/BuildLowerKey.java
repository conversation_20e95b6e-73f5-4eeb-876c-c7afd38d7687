package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
public class BuildLowerKey extends ScalarFunction {
    private static final Logger log = LoggerFactory.getLogger(BuildLowerKey.class);
    Map<String, Object> result;

    //构造函数初始化aviator
    @Override
    public void open(FunctionContext context) {
        result = new HashMap<>(64);
    }

    /**
     * 判断用户是否在人群中,返回命中第一个
     * @param jsonString
     * @return
     */
    public String eval(String jsonString) {
        result.clear();
        Map<String, Object> jsonMap = JSONObject.parseObject(jsonString);

        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            result.put(entry.getKey().toLowerCase(), entry.getValue());
        }
        return JSON.toJSONString(result);
    }

}
