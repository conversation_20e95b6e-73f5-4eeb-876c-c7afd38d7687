package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

@FunctionHint(
		input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class SampleData extends TableFunction<Row>{
    public void eval(String str){
        Row row = new Row(1);
        row.setField(0, str);
        try {
            JSONObject jsonObject = JSON.parseObject(str);
            String ts = (String) jsonObject.getOrDefault("timestamp",System.currentTimeMillis()+"");
            if(Long.parseLong(ts) % 10 == 1){
                collect(row);
            }
        }catch (Exception e){
            collect(row);
        }

    }

    public static void main(String[] args) {
        String s = "{\"timestamp\":\"1648526919501\",\"contentType\":0,\"position\":4,\"contentID\":\"10903\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329242773749628928\",\"cspuId\":\"10903\"}";
        SampleData sd = new SampleData();
        sd.eval(s);
    }
}
