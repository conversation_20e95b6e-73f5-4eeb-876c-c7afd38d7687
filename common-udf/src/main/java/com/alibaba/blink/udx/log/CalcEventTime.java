package com.alibaba.blink.udx.log;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//@FunctionHint(
//		input = {@DataTypeHint("STRING"),@DataTypeHint("LONG"), @DataTypeHint("LONG"), @DataTypeHint("LONG")},
//        output = @DataTypeHint("LONG")
//)
public class CalcEventTime extends ScalarFunction {

    private static final Logger logger = LoggerFactory.getLogger(CalcEventTime.class);

    public Long eval(String event, Long time, Long timeCorrect, Long timeOffset) {
        //如果是预置事件，用time-time_offset
        try {
            if (event.startsWith("$")) {
                if (timeOffset != 0) {
                    return time - timeOffset;
                } else {
                    return time;
                }
            } else {
                if (timeCorrect <= 0) {
                    return time;
                } else {
                    return timeCorrect;
                }
            }
        } catch (Exception e) {
            return null;
        }

    }
}