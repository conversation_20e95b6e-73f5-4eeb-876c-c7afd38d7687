package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 解析"交易用户搜索词QP"里面的term字段
 */
public class AlgoSearchQueryRecall extends ScalarFunction {
    private static final Logger log = LoggerFactory.getLogger(AlgoSearchQueryRecall.class);

    public String eval(String termWords) {
        try{
            if(StringUtils.isBlank(termWords)){
                return null;
            }
            JSONArray jsonArray = JSON.parseArray(termWords);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = (JSONObject) jsonArray.get(i);
                if(sb.length() == 0){
                    sb.append(jsonObject.get("term"));
                }else{
                    sb.append(",").append(jsonObject.get("term"));
                }
            }
            return sb.toString();
        }catch (Exception e){
            log.error("parse error {},{}", e, termWords);
        }
        return null;
    }

}
