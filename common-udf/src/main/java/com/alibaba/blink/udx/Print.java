package com.alibaba.blink.udx;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.sql.Timestamp;

@Slf4j
public class Print extends ScalarFunction {
    public String eval(String symbol, String value) {
        log.info("libra debug {} {}", symbol, value);
        return value;
    }

    public Long eval(String symbol, Long value) {
        log.info("libra debug {} {}", symbol, value);
        return value;
    }

    public Integer eval(String symbol, Integer value) {
        log.info("libra debug {} {}", symbol, value);
        return value;
    }

    public Timestamp eval(String symbol, Timestamp value) {
        log.info("libra debug {} {}", symbol, value);
        return value;
    }
}
