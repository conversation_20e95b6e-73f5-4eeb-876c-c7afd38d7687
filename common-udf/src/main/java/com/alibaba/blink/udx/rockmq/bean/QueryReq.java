package com.alibaba.blink.udx.rockmq.bean;

public class QueryReq {

    private String headers="";//新增 软广查询

    private long userId;//             int64  `json:"userId" form:"userId"`                         // APP用户ID
    private String appId="";//              string `json:"appId" form:"appId"`                           // APP标识
    private String appVersion="";//         string `json:"appVersion" form:"appVersion"`                 // APP版本号
    private String os="";//            string `json:"os" form:"os"`                                 // 操作系统：android|iphone|ios
    private String pkgChannel="";//       string `json:"pkgChannel" form:"pkgChannel"`                 // 包渠道号
    private String imei="";//         string `json:"imei" form:"imei"`                             // 安卓imei
    private  String oaid="";//         string `json:"oaid" form:"oaid"`                             // 安卓oaid
    private String androidId="";//         string `json:"androidId" form:"androidId"`                   // 安卓ID
    private String idfa="";//         string `json:"idfa" form:"idfa"`                             // 苹果idfa
    private  String fcuuid="";//         string `json:"fcuuid" form:"fcuuid"`                         // 客户端生成的uuid，iOS有
    private  String uuid="";//         string `json:"uuid" form:"uuid"`                             // 设备号
    private  String clipboard="";//         string `json:"clipboard" form:"clipboard"`                   // 系统剪贴板，没有不传
    private String ua="";//         string `json:"ua" form:"ua"`                                 // 用户user_agent
    private String ip="";//         string `json:"ip" form:"ip"`                                 // 用户ip
    private String ipvx="";//         string `json:"ipvx" form:"ipvx"`                             // 用户ipvx
    private String source="";//        string `json:"source" form:"source"`                         // 来源，init|active
    private long eventTime;//        int64  `json:"eventTime" form:"eventTime"`                   // 发生时间
    private  String shumeiId="";//        string `json:"shumeiId" form:"shumeiId"`                     // 数美ID
    private  String orderNo="";//        string `json:"orderNo" form:"orderNo"`                       // 订单号
    private String ipua="";//        string `json:"ipua" form:"ipua"`                             // md5(lower(ip)+lower(trim_all_space(ua)))
    private String ipvxua="";//        string `json:"ipvxua" form:"ipvxua"`                             // md5(lower(ip)+lower(trim_all_space(ua)))
    private long lastActiveTime;//    int64  `json:"lastActiveTime" form:"lastActiveTime"`         // 上次活跃时间
    private long dayFirstActiveTime;//  int64  `json:"dayFirstActiveTime" form:"dayFirstActiveTime"` // 当日首次活跃时间

    private String model="";//新增 软广查询
    private String brand="";//新增 软广查询

    private long logTime;//增加字段

    public String getIpvxua() {
        return ipvxua;
    }

    public void setIpvxua(String ipvxua) {
        this.ipvxua = ipvxua;
    }

    public long getLogTime() {
        return logTime;
    }

    public void setLogTime(long logTime) {
        this.logTime = logTime;
    }

    public String getHeaders() {
        return headers;
    }

    public void setHeaders(String headers) {
        this.headers = headers;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        if(appId ==null)
        {
            this.appId="";
            return;
        }
        this.appId = appId;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        if(appVersion ==null)
        {
            this.appVersion="";
            return;
        }
        this.appVersion = appVersion;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        if(os ==null)
        {
            this.os="";
            return;
        }
        this.os = os;
    }

    public String getPkgChannel() {
        return pkgChannel;
    }

    public void setPkgChannel(String pkgChannel) {
        if(pkgChannel ==null)
        {
            this.pkgChannel="";
            return;
        }
        this.pkgChannel = pkgChannel;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        if(imei ==null)
        {
            this.imei="";
            return;
        }
        this.imei = imei;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        if(oaid ==null)
        {
            this.oaid="";
            return;
        }
        this.oaid = oaid;
    }

    public String getAndroidId() {
        return androidId;
    }

    public void setAndroidId(String androidId) {
        if(androidId ==null)
        {
            this.androidId="";
            return;
        }
        this.androidId = androidId;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        if(idfa ==null)
        {
            this.idfa="";
            return;
        }
        this.idfa = idfa;
    }

    public String getFcuuid() {
        return fcuuid;
    }

    public void setFcuuid(String fcuuid) {
        if(fcuuid ==null)
        {
            this.fcuuid="";
            return;
        }
        this.fcuuid = fcuuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        if(uuid ==null)
        {
            this.uuid="";
            return;
        }
        this.uuid = uuid;
    }

    public String getClipboard() {
        return clipboard;
    }

    public void setClipboard(String clipboard) {
        if(clipboard ==null)
        {
            this.clipboard="";
            return;
        }
        this.clipboard = clipboard;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        if(ua ==null)
        {
            this.ua="";
            return;
        }
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        if(ip ==null)
        {
            this.ip="";
            return;
        }
        this.ip = ip;
    }

    public String getIpvx() {
        return ipvx;
    }

    public void setIpvx(String ipvx) {
        if(ipvx ==null)
        {
            this.ipvx="";
            return;
        }
        this.ipvx = ipvx;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        if(source ==null)
        {
            this.source="";
            return;
        }
        this.source = source;
    }

    public long getEventTime() {
        return eventTime;
    }

    public void setEventTime(long eventTime) {
        this.eventTime = eventTime;
    }

    public String getShumeiId() {
        return shumeiId;
    }

    public void setShumeiId(String shumeiId) {
        if(shumeiId ==null)
        {
            this.shumeiId="";
            return;
        }
        this.shumeiId = shumeiId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        if(orderNo ==null)
        {
            this.orderNo="";
            return;
        }
        this.orderNo = orderNo;
    }

    public String getIpua() {
        return ipua;
    }

    public void setIpua(String ipua) {
        if(ipua ==null)
        {
            this.ipua="";
            return;
        }
        this.ipua = ipua;
    }

    public long getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(long lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public long getDayFirstActiveTime() {
        return dayFirstActiveTime;
    }

    public void setDayFirstActiveTime(long dayFirstActiveTime) {
        this.dayFirstActiveTime = dayFirstActiveTime;
    }

    @Override
    public String toString() {
        return "QueryReq{" +
                "userId=" + userId +
                ", appId='" + appId + '\'' +
                ", appVersion='" + appVersion + '\'' +
                ", os='" + os + '\'' +
                ", pkgChannel='" + pkgChannel + '\'' +
                ", imei='" + imei + '\'' +
                ", oaid='" + oaid + '\'' +
                ", androidId='" + androidId + '\'' +
                ", idfa='" + idfa + '\'' +
                ", fcuuid='" + fcuuid + '\'' +
                ", uuid='" + uuid + '\'' +
                ", clipboard='" + clipboard + '\'' +
                ", ua='" + ua + '\'' +
                ", ip='" + ip + '\'' +
                ", ipvx='" + ipvx + '\'' +
                ", source='" + source + '\'' +
                ", eventTime=" + eventTime +
                ", shumeiId='" + shumeiId + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", ipua='" + ipua + '\'' +
                ", lastActiveTime=" + lastActiveTime +
                ", dayFirstActiveTime=" + dayFirstActiveTime +
                ", logTime=" + logTime +
                '}';
    }
}
