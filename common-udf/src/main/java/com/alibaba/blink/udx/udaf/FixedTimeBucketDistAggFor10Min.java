package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.FixedTimeBucketDistAcc;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

public class FixedTimeBucketDistAggFor10Min extends AggregateFunction<String, FixedTimeBucketDistAcc> {

    private static final Logger logger = LoggerFactory.getLogger(FixedTimeBucketDistAggFor10Min.class);

    @Override
    public FixedTimeBucketDistAcc createAccumulator() {
        return new FixedTimeBucketDistAcc(600);
    }

    @Override
    public String getValue(FixedTimeBucketDistAcc fixedTimeBucketAcc) {
        if (null == fixedTimeBucketAcc) {
            logger.error(String.format("meet null acc here.\n%s", Arrays.toString(Thread.currentThread().getStackTrace())));
            return "{}";
        }
        return fixedTimeBucketAcc.getSnapshot();
    }

    public void accumulate(FixedTimeBucketDistAcc accumulator, String timeLabel, Long value) {
//        logger.warn(String.format("accumulate value [%s:%s] ", timeLabel, value));
        if (null == value || value == 0) {
            return;
        }
        innerUpdate(accumulator, timeLabel, value);
    }

    public void retract(FixedTimeBucketDistAcc accumulator, String timeLabel, Long value) {
        if (null == value || value == 0) {
            return;
        }
        innerUpdate(accumulator, timeLabel, -value);
    }

    private void innerUpdate(FixedTimeBucketDistAcc accumulator, String timeLabel, Long value) {
        if (!StringUtils.isNullOrWhitespaceOnly(timeLabel)) {
            if (!accumulator.update(timeLabel, value)) {
                logger.error(String.format("fail to deal with [%s.%s]", timeLabel, value));
            }
        } else {
            logger.error(String.format("found an empty timeLabel [%s.%s], ignore.", timeLabel, value));
        }
    }

    public void merge(FixedTimeBucketDistAcc accumulator, Iterable<FixedTimeBucketDistAcc> its) {
        // window merge should be careful.
        for (FixedTimeBucketDistAcc acc : its) {
            accumulator.merge(acc);
        }
    }
}
