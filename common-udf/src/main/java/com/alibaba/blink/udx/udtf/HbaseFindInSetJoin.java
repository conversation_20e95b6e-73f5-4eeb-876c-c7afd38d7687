package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.CompareOperator;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.TableName;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.client.Connection;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.client.Get;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.client.Result;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.client.Table;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.filter.FilterList;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.filter.QualifierFilter;
import org.apache.flink.hbase.shaded.org.apache.hadoop.hbase.util.Bytes;
import org.apache.flink.shaded.guava30.com.google.common.cache.Cache;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.apache.hadoop.conf.Configuration;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.concurrent.ThreadLocalRandom.current;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/12/18 11:28
 */
@Slf4j
public class HbaseFindInSetJoin extends TableFunction<Row> {

    private final List<String> needColumns = new ArrayList<>();
    private final AtomicBoolean firstInit = new AtomicBoolean(false);
    private final AtomicBoolean needInit = new AtomicBoolean(true);
    private transient Connection connection;
    private transient Table table;
    private transient byte[] family;

    public transient Cache<String, Row> cache;
    private boolean cacheEmpty = true;

    private boolean resultIsStringArray = true;

    private static final String ROW_KEY_PLACEHOLDER = "%";

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            if (argumentDataTypes.size() - 5 + 1 >= 1) {
                DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 5 + 1];
                for (int i = 0; i < (argumentDataTypes.size() - 5 + 1); i++) {
                    outputDataTypes[i] = DataTypes.STRING();
                }
                return Optional.of(DataTypes.ROW(outputDataTypes));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();
    }

    public void eval(String tableName, String family, String hbaseConfig, String rowKeySet, String rowKeyExpression, String... keys) throws Exception {

        initConfig(tableName, family, hbaseConfig, keys);

        if (StringUtils.isEmpty(rowKeySet)) {
            collect(new Row(needColumns.size() + 1));
            return;
        }

        List<String> rowKeys = null;

        Stream<String> rowKeyStream = !rowKeySet.contains("[") ? Arrays.stream(strToArray(rowKeySet)).distinct() : JSON.parseArray(rowKeySet, String.class).stream().distinct();

        if (rowKeyExpression == null || ROW_KEY_PLACEHOLDER.equals(rowKeyExpression)) {

            rowKeys = rowKeyStream.collect(Collectors.toList());

        } else {
            rowKeys = rowKeyStream.map(element -> {

                String newRowKey = rowKeyExpression;

                if (rowKeyExpression.toLowerCase(Locale.ROOT).contains("md5")) {
                    Pattern pattern = Pattern.compile("md5", Pattern.CASE_INSENSITIVE);
                    Matcher matcher = pattern.matcher(rowKeyExpression);
                    newRowKey = matcher.replaceAll(DigestUtils.md5Hex(element));
                }

                newRowKey = newRowKey.replaceAll(ROW_KEY_PLACEHOLDER, element);

                return newRowKey;
            }).collect(Collectors.toList());
        }

        List<Row> rows = getRows(rowKeys);

        Map<Integer, List<Object>> result = new HashMap<>(needColumns.size());

        for (Row row : rows) {

            for (int i = 0; i < needColumns.size(); i++) {

                List<Object> tmpRs = Optional.ofNullable(result.get(i)).orElse(new ArrayList<>());

                tmpRs.add(row.getField(i));

                result.put(i, tmpRs);
            }
        }

        Row row = new Row(needColumns.size() + 1);

        for (int i = 0; i < result.size(); i++) {
            row.setField(i, arrayToString(result.get(i)));
        }

        row.setField(needColumns.size(), arrayToString(rowKeys));
        collect(row);

    }

    public void initConfig(String tableName, String familyName, String hbaseConfig, String... keys) {

        if (null == keys || keys.length == 0) {
            return;
        }
        while (needInit.get()) {
            try {
                if (firstInit.compareAndSet(false, true)) {

                    needColumns.clear();
                    needColumns.addAll(Arrays.asList(keys));
                    //初始化一个hbase client
                    Configuration configuration = HBaseConfiguration.create();

                    JSONObject cfg = JSONObject.parseObject(hbaseConfig);

                    Long cacheSize = Optional.ofNullable(cfg.getLong("cache.size")).orElse(1000L);
                    Long cacheTtl = Optional.ofNullable(cfg.getLong("cache.ttl")).orElse(1000L * 60 * 60);
                    cacheEmpty = Optional.ofNullable(cfg.getBoolean("cache.empty-null")).orElse(true);
                    resultIsStringArray = Optional.ofNullable(cfg.getBoolean("result.is-array")).orElse(true);
                    cache = CacheBuilder.newBuilder()
                            .maximumSize(cacheSize)
                            .expireAfterWrite(cacheTtl, TimeUnit.MILLISECONDS)
                            .build();
                    cfg.remove("cache.size");
                    cfg.remove("cache.ttl");
                    cfg.remove("cache.empty-null");
                    cfg.remove("result.is-array");

                    cfg.forEach((x, y) -> configuration.set(x, String.valueOf(y)));

                    connection = ConnectionFactory.createConnection(configuration);

                    family = Bytes.toBytes(familyName);

                    table = connection.getTable(TableName.valueOf(tableName));

                    log.info("innit hbase clint:\n tableName:{} \n familyName:{} \n config:{} joinNeedColumns:{} \nconnection-config{}", tableName, Bytes.toString(family), cfg, needColumns, connection.getConfiguration());

                    FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);

                    for (String columns : needColumns) {
                        filterList.addFilter(buildQualifierFilter(columns));
                    }

                    needInit.compareAndSet(true, false);
                } else {
                    TimeUnit.MILLISECONDS.sleep(current().nextInt(10));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }
    }

    public QualifierFilter buildQualifierFilter(String columns) {
        return new QualifierFilter(CompareOperator.EQUAL, new BinaryComparator(Bytes.toBytes(columns)));
    }

    public List<Row> getRows(Collection<String> rowKeys) {

        List<Row> rows = new ArrayList<>();

        rowKeys.forEach(rowKey -> {
            try {
                Row buffRow = cache.getIfPresent(rowKey);
                if (buffRow != null) {
                    rows.add(buffRow);
                } else {
                    Result result = table.get(new Get(Bytes.toBytes(rowKey)));

                    if (result.isEmpty()) {
                        rows.add(new Row(needColumns.size()));
                        if (cacheEmpty) {
                            cache.put(rowKey, new Row(needColumns.size()));
                        }
                    } else {
                        Row row = new Row(needColumns.size());
                        for (int i = 0; i < needColumns.size(); i++) {
                            row.setField(i, Bytes.toString(result.getValue(family, Bytes.toBytes(needColumns.get(i)))));
                        }
                        rows.add(row);
                        cache.put(rowKey, Row.copy(row));
                    }

                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        });

        return rows;
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (table != null) {
            table.close();
        }
        if (connection != null) {
            connection.close();
        }
    }

    public String[] strToArray(String str) {
        String etlStr = str.replaceAll("(\\[)|(\\])|(\\s*)", "");
        return etlStr.split(",");
    }

    public <T> String arrayToString(Collection<T> c) {

        if (c == null) {
            return null;
        }

        if (resultIsStringArray) {
            return JSON.toJSONString(c);
        }

        if (c.size() == 1) {
            return String.valueOf(c.iterator().next());
        }

        Iterator<T> iterator = c.iterator();
        StringBuilder buffer = new StringBuilder();
        while (true) {
            buffer.append(String.valueOf(iterator.next()));
            if (iterator.hasNext()) {
                buffer.append(",");
            } else
                break;
        }
        return buffer.toString();
    }

}
