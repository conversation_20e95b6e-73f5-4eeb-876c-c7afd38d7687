package com.alibaba.blink.udx.udtf;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonParser;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonToken;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/10/9 15:30
 */
public class DeliverMetricSplitFunction extends TableFunction<Row> {

    private Set<String> commonColumnSet;
    private final ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private static final int prxLength = "increase_hard_ad_".length();
    private static final int endLength = "_p1hour".length();

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            if (argumentDataTypes.size() - 1 >= 1) {
                DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1 + 2];
                for (int i = 0; i < (argumentDataTypes.size() - 1 + 2); i++) {
                    outputDataTypes[i] = DataTypes.STRING();
                }
                return Optional.of(DataTypes.ROW(outputDataTypes));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();
    }


    public void eval(String jsonStr, String... commonColumns) {

        if (commonColumnSet == null) {
            commonColumnSet = new LinkedHashSet<>();
            commonColumnSet.addAll(Arrays.asList(commonColumns));
        }

        Tuple2<Map<String, Object>, Map<String, Object>> parserJson = parserJson(jsonStr);

        if (parserJson.f1.isEmpty()) {
            return;
        }

        parserJson.f1.forEach((k,v) -> {
            Row row = new Row(commonColumnSet.size() + 2);
            int i = 0;
            for (String column : commonColumnSet) {
                row.setField(i, parserJson.f0.get(column));
                i++;
            }

            row.setField(i++,k);
            row.setField(i, v);

            collect(row);
        });

    }

    private Tuple2<Map<String, Object>, Map<String, Object>> parserJson(String jsonStr) {

        HashMap<String, Object> commonMap = new HashMap<>();
        HashMap<String, Object> splitMap = new HashMap<>();
        try (JsonParser jsonParser = mapper.getFactory().createParser(jsonStr)) {
            while (jsonParser.nextToken() != null) {
                if (jsonParser.getCurrentToken() == JsonToken.FIELD_NAME) {
                    String currentName = jsonParser.getCurrentName();
                    if (commonColumnSet.contains(currentName)) {
                        commonMap.put(currentName, getValue(jsonParser));
                    }
                    if (currentName.startsWith("increase_hard_ad_") && currentName.endsWith("_p1hour")) {
                        splitMap.put(currentName.substring(prxLength, currentName.length() - endLength), getValue(jsonParser));
                    }

                }
            }
            jsonParser.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return Tuple2.of(commonMap, splitMap);
    }

    private String getValue(JsonParser jsonParser) throws IOException {
        jsonParser.nextToken();
        return jsonParser.getValueAsString();
    }

    public static void main(String[] args) {
        DeliverMetricSplitFunction jsonSplitFunction = new DeliverMetricSplitFunction();

        jsonSplitFunction.eval(
                "{\n" +
                        "    \"date\": \"20231009\",\n" +
                        "    \"increase_hard_ad_cost_p1hour\": 4,\n" +
                        "    \"dim_type\": \"planHourDim\",\n" +
                        "    \"batch_label_id\": \"47e8ab2d-f731-4126-bd45-a1df9893567f\",\n" +
                        "    \"increase_hard_ad_click_p1hour\": 0,\n" +
                        "    \"device_type\": \"recall_device\",\n" +
                        "    \"increase_hard_ad_real_cost_p1hour\": 3.***************,\n" +
                        "    \"increase_hard_ad_view_p1hour\": 4,\n" +
                        "    \"creative_id\": \"ALL\",\n" +
                        "    \"operator\": \"ALL\",\n" +
                        "    \"account_id\": \"****************\",\n" +
                        "    \"hour\": \"16\",\n" +
                        "    \"group_id\": \"ALL\",\n" +
                        "    \"media_id\": \"1\",\n" +
                        "    \"current_watermark\": \"2023-10-09 17:38:00\",\n" +
                        "    \"plan_id\": \"7287496322356985895\",\n" +
                        "    \"current_time\": \"2023-10-09 17:40:22\"\n" +
                        "}",
                "date"
                ,"dim_type"
                ,"device_type","creative_id","operator","account_id","hour","group_id","media_id","current_watermark","plan_id"
        );
    }
}
