package com.alibaba.blink.udx.utils;


import com.alibaba.fastjson.JSON;
import com.dewu.alg.acm.core.constant.AcmConstantsFields;
import com.dewu.alg.acm.core.entity.AcmEntity;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import java.util.*;


public class AcmUtils {

    private static final String VERSION = "_version";
    private static final String DOMAIN = "_domain";
    private static final String CONTENT = "_content";
    private static final String EXP = "_experiments";
    private static final String RESOURCE = "_resource";
    private static final String DATA = "_data";

    private static final String DPP_MODE = "dpp";
    private static final String ACM_MODE = "acm";

    /**
     *  部分字符会被自动屏蔽掉，不允许出现在acm里面
     *  . 被替换为 #
     *  - 被替换为 $
     *  _ 被替换为 |
     */
    public static final Map<Character, Character> HIDDEN_CHAR = ImmutableMap.of(
            '.','#',
            '-','$',
            '_','|'
    );

    public static final Map<Character, Character> DECODE_MAP = ImmutableMap.of(
            '#','.',
            '$','-',
            '|','_'
    );

    /**
     * 抹除字符串中的特殊的字符
     * @param oldVle
     * @param encrypt
     * @return
     */
    public static String hiddenParser(String oldVle, boolean encrypt) {
        char[] chars = oldVle.toCharArray();
        Map<Character,Character> charMap = DECODE_MAP;
        // 加密
        if (encrypt) { charMap = HIDDEN_CHAR; }
        for (int i = 0; i < chars.length; i++) {
            if (charMap.containsKey(chars[i])) {
                chars[i] = charMap.get(chars[i]);
            }
        }
        return String.valueOf(chars);
    }

    /**
     * 解析所有的kv结构
     * @param acm
     * @return
     */
    public static Map<String, String> decodeAcm(String acm) {
        Map<String, String> result = new HashMap<>();
        if (acm.startsWith(AcmConstantsFields.JSON_PREFIX)) {
            try {
                result = JSON.parseObject(acm, Map.class);
            } catch (Exception e) {
            }
            return result;
        }

        AcmEntity entity = AcmEntity.of(acm);
        result.put(VERSION, entity.getVersion().toString());
        result.put(DOMAIN,entity.getDomain().toString());
        result.put(CONTENT, entity.getContent().toString());
        result.put(EXP, entity.getExps().toString());
        result.put(RESOURCE, entity.getResource().toString());
        result.put(DATA, entity.getFields().toString());

        // 补充content信息
        if (Objects.nonNull(entity.getContent().getType())
                && Objects.nonNull(entity.getContent().getText())) {
            result.put(entity.getContent().getType().toString(), entity.getContent().getText().toString());
        }
        // 补充自定义的字段信息
        if (Objects.nonNull(entity.getFields())
                && !entity.getFields().isEmpty()) {
            result.putAll(entity.getFields().getKeyValuePairs());
        }
        return result;
    }

    public static Map<String, String> getContentFromAcm(String acmStr) {
        Map<String, String> contentMap = new HashMap<>();
        if (acmStr.startsWith(AcmConstantsFields.JSON_PREFIX)) {
            try {
                contentMap = JSON.parseObject(acmStr, Map.class);
            } catch (Exception e) {
            }
            return contentMap;
        }

        AcmEntity entity = AcmEntity.of(acmStr);
        contentMap.put(entity.getContent().getType().toString(), entity.getContent().getText().toString());
        return contentMap;
    }

    public static String getContentFromAcm(String acm, String type) {
        return getContentFromAcm(acm).getOrDefault(type, "");
    }

    public static List<String> getExpsFromAcm(String acm) {
        return getExpsFromAcm(acm, ACM_MODE);
    }

    public static List<String> getExpsFromAcm(String acm, String mode) {
        if (acm.startsWith(AcmConstantsFields.JSON_PREFIX)) {
            Map<String, String> decodeMap = decodeAcm(acm);
            String exps = decodeMap.get(AcmConstantsFields.EXP);
            if (DPP_MODE.equals(mode) && decodeMap.containsKey(AcmConstantsFields.DPP_EXP)) {
                exps += AcmConstantsFields.DASH+decodeMap.get(AcmConstantsFields.DPP_EXP);
            }
            return Lists.newArrayList(exps.split(AcmConstantsFields.DASH));
        }

        String[] parts = acm.split(AcmConstantsFields.POINT);
        if (parts.length >= 5) {
            String expsStr = parts[4];
            if (DPP_MODE.equals(mode)) {
                // 临时兼容一下dppe
                String[] customPairs = parts[5].split(AcmConstantsFields.DASH);
                for (String pair: customPairs) {
                    if (pair.startsWith(AcmConstantsFields.DPP_EXP)) {
                        List<String> expDpps = Lists.newArrayList(expsStr.split(AcmConstantsFields.DASH));
                        Collections.addAll(expDpps, pair.substring(5).split(AcmConstantsFields.DPPE_SPLITER));
                        return expDpps;
                    }
                }
            }
            return Lists.newArrayList(expsStr.split(AcmConstantsFields.DASH));
        }
        return Lists.newArrayList();
    }

    public static Map<String, String> getFieldsFromAcm(String acm) {
        AcmEntity entity = AcmEntity.of(acm);
        return entity.getFields().getKeyValuePairs();
    }

}
