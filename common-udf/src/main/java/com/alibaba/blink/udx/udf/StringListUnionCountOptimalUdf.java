package com.alibaba.blink.udx.udf;

import com.google.common.base.Strings;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashSet;
import java.util.Set;

/**
 * 最优化字符串列表并集去重计数UDF
 * 
 * 终极优化版本：真正的O(M+N)时间复杂度
 * - 单次遍历两个字符串
 * - 避免StringBuilder开销
 * - 最小化字符串操作
 * - 预估容量避免HashSet扩容
 * 
 * 时间复杂度：O(M + N)，其中M和N分别是两个字符串的长度
 * 空间复杂度：O(K)，其中K是唯一元素的数量
 */
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class StringListUnionCountOptimalUdf extends ScalarFunction {

    /**
     * 计算两个字符串列表并集去重后的数量
     * 
     * @param list1 第一个列表，以逗号分隔的字符串，可以为空
     * @param list2 第二个列表，以逗号分隔的字符串，可以为空
     * @return 并集去重后的元素数量
     */
    public int eval(String list1, String list2) {
        // 快速路径：两个列表都为空
        if (Strings.isNullOrEmpty(list1) && Strings.isNullOrEmpty(list2)) {
            return 0;
        }
        
        // 快速路径：只有一个列表为空
        if (Strings.isNullOrEmpty(list1)) {
            return countUniqueElements(list2);
        }
        if (Strings.isNullOrEmpty(list2)) {
            return countUniqueElements(list1);
        }
        
        // 两个列表都不为空，使用最优算法
        return countUnionOptimal(list1, list2);
    }
    
    /**
     * 计算单个列表的去重元素数量
     */
    private int countUniqueElements(String list) {
        Set<String> uniqueElements = new HashSet<>(estimateCapacity(list));
        parseAndAddElements(list, uniqueElements);
        return uniqueElements.size();
    }
    
    /**
     * 最优化的并集计算：真正的O(M+N)单次遍历
     */
    private int countUnionOptimal(String list1, String list2) {
        // 预估容量，避免HashSet扩容
        int capacity = estimateCapacity(list1) + estimateCapacity(list2);
        Set<String> unionSet = new HashSet<>(capacity);
        
        // 同时解析两个字符串，实现真正的单次遍历
        int len1 = list1.length();
        int len2 = list2.length();
        int pos1 = 0, pos2 = 0;
        int start1 = 0, start2 = 0;
        
        // 主循环：同时遍历两个字符串
        while (pos1 <= len1 || pos2 <= len2) {
            // 处理第一个字符串
            if (pos1 <= len1) {
                if (pos1 == len1 || list1.charAt(pos1) == ',') {
                    if (pos1 > start1) {
                        addTrimmedElement(list1, start1, pos1, unionSet);
                    }
                    start1 = pos1 + 1;
                }
                pos1++;
            }
            
            // 处理第二个字符串
            if (pos2 <= len2) {
                if (pos2 == len2 || list2.charAt(pos2) == ',') {
                    if (pos2 > start2) {
                        addTrimmedElement(list2, start2, pos2, unionSet);
                    }
                    start2 = pos2 + 1;
                }
                pos2++;
            }
        }
        
        return unionSet.size();
    }
    
    /**
     * 解析字符串并添加元素到Set中
     */
    private void parseAndAddElements(String list, Set<String> set) {
        int len = list.length();
        int start = 0;
        
        for (int pos = 0; pos <= len; pos++) {
            if (pos == len || list.charAt(pos) == ',') {
                if (pos > start) {
                    addTrimmedElement(list, start, pos, set);
                }
                start = pos + 1;
            }
        }
    }
    
    /**
     * 添加修剪后的元素到Set中，避免创建不必要的字符串对象
     */
    private void addTrimmedElement(String source, int start, int end, Set<String> set) {
        // 跳过前导空格
        while (start < end && source.charAt(start) == ' ') {
            start++;
        }
        
        // 跳过尾随空格
        while (end > start && source.charAt(end - 1) == ' ') {
            end--;
        }
        
        // 只有非空元素才添加
        if (start < end) {
            set.add(source.substring(start, end));
        }
    }
    
    /**
     * 估算HashSet的初始容量，避免扩容开销
     */
    private int estimateCapacity(String list) {
        if (Strings.isNullOrEmpty(list)) {
            return 0;
        }
        
        // 快速计算逗号数量
        int commaCount = 0;
        for (int i = 0; i < list.length(); i++) {
            if (list.charAt(i) == ',') {
                commaCount++;
            }
        }
        
        // 元素数量 = 逗号数量 + 1，考虑负载因子0.75
        int elementCount = commaCount + 1;
        return (int) (elementCount / 0.75) + 1;
    }
}
