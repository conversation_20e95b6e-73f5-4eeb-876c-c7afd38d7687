package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;


// function that takes (value INT), stores intermediate results in a structured
// type of Top2Accumulator, and returns the result as a structured type of Tuple2<Integer, Integer>
// for value and rank
public class MergeAdsIndexWithMetricValue extends TableAggregateFunction<Row, MergeAdsIndexWithMetricValue.JsonAcc> {

	public static List<String> needKeys = new ArrayList<>();

	@Override
	public void open(FunctionContext context) throws Exception {
		super.open(context);
		needKeys = new ArrayList<>(Arrays.asList(context.getJobParameter("MergeAdsTemplateJson.needKeys", "").split(",")));
	}

	@Override
	public TypeInference getTypeInference(DataTypeFactory typeFactory) {
		return TypeInference.newBuilder()
			.outputTypeStrategy(callContext -> {
				//根据函数入参情况确定返回值
				List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
				DataTypes.Field[] outputDataTypes = new DataTypes.Field[argumentDataTypes.size() - 2];
				for (int i = 2; i < argumentDataTypes.size(); i++) {
					outputDataTypes[i - 2] = DataTypes.FIELD("F" + i, DataTypes.STRING());
				}
				DataType accDataType = DataTypes.ROW(outputDataTypes)
					.bridgedTo(Row.class);
				return Optional.of(accDataType);
			})
			.accumulatorTypeStrategy(callContext -> {
				DataType accDataType = DataTypes.STRUCTURED(
					MergeAdsIndexWithMetricValue.JsonAcc.class,
					DataTypes.FIELD(
						"viewMap",
						MapView.newMapViewDataType(
							DataTypes.STRING().notNull(),
							DataTypes.STRUCTURED(
								Tuple2.class,
								DataTypes.FIELD("f0", DataTypes.STRING().nullable()),
								DataTypes.FIELD("f1", DataTypes.BIGINT())
							)))
				);

				return Optional.of(accDataType);
			})
			.build();
	}

	@Override
	public MergeAdsIndexWithMetricValue.JsonAcc createAccumulator() {
		return new MergeAdsIndexWithMetricValue.JsonAcc();
	}

	public void accumulate(MergeAdsIndexWithMetricValue.JsonAcc acc, String value, Long ts, String... outputKeys) {
//		if (CollectionUtil.isNullOrEmpty(needKeys)) {
//			synchronized (MergeAdsIndexWithMetricValue.class) {
//				if (CollectionUtil.isNullOrEmpty(needKeys)) {
//					if (null == outputKeys || outputKeys.length < 1) {
//						throw new RuntimeException("MergeAdsIndexWithMetricValue need output cols in call function.");
//					}
//					needKeys.addAll(Arrays.asList(outputKeys));
//				}
//			}
//		}
		JSONObject source = object2Json(value);
		acc.acc(source, ts);
	}

	private JSONObject object2Json(Object o) {
		//嵌套Json 避免转译
		if (o instanceof String && JSONValidator.from((String) o).validate()) {
			return JSON.parseObject((String) o);
		} else if (o instanceof JSONObject) {
			return (JSONObject) o;
		} else
			throw new RuntimeException("arg is not json. arg: " + o);
	}

	public void retract(MergeAdsIndexWithMetricValue.JsonAcc accumulator, String value, Long ts, String... outputKeys) {
	}

	public void merge(MergeAdsIndexWithMetricValue.JsonAcc accumulator, Iterable<MergeAdsIndexWithMetricValue.JsonAcc> iterables) {
		for (MergeAdsIndexWithMetricValue.JsonAcc acc : iterables) {
			accumulator.merge(acc);
		}
	}

	public void emitValue(MergeAdsIndexWithMetricValue.JsonAcc acc, Collector<Row> out) {
		try {
			out.collect(acc.getCache());
		} catch (Exception e) {
			throw new RuntimeException("Fail to emit value while merging indexes.", e);
		}
	}

	public static class JsonAcc {

		public MapView<String, Tuple2<String, Long>> viewMap;

		public JsonAcc() {
			this.viewMap = new MapView<>();
		}

		public Row getCache() throws Exception {
			Row row = new Row(needKeys.size());
			for (int i = 0; i < needKeys.size(); i++) {
				try {
					Tuple2<String, Long> vl = viewMap.get(needKeys.get(i));
					if (vl == null || vl.f0 == null) {
						row.setField(i, "0");
					} else {
						row.setField(i, vl.f0);
					}
				} catch (Exception e) {
					throw new RuntimeException("fetch metric cache err.", e);
				}
			}
			return row;
		}

		/**
		 * 默认将 metric_value ext dim_value 透出
		 */
		public void setCache(JSONObject metrics, Long ts) {
			metrics.forEach((s, o) -> setValue(s, o, ts));
		}

		private void setValue(String s, Object o, Long ts) {

			if (!isNeeded(s)) {
				return;
			}

			try {
				if (viewMap.contains(s)) {
					Tuple2<String, Long> tuple2 = viewMap.get(s);
					if (tuple2.f1 < ts || tuple2.f1.equals(ts)) {
						tuple2.f0 = String.valueOf(o);
						tuple2.f1 = ts;
						viewMap.put(s, tuple2);
					}
				} else {
					viewMap.put(s, Tuple2.of(String.valueOf(o), ts));
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}

		public boolean isNeeded(String key) {
			return needKeys.contains(key);
		}

		public boolean isJsonObjectBlankOrNull(JSONObject value) {
			return Objects.isNull(value) || value.isEmpty();
		}

		public void acc(JSONObject value, Long ts) {
			if (!isJsonObjectBlankOrNull(value)) {
				setCache(value, ts);
			}
		}

		public void merge(MergeAdsIndexWithMetricValue.JsonAcc otherAcc) {
			try {
				String key;
				Tuple2<String, Long> otherMetric;
				Tuple2<String, Long> thisMetric;
				for (Map.Entry<String, Tuple2<String, Long>> entry : otherAcc.viewMap.entries()) {
					key = entry.getKey();
					otherMetric = entry.getValue();
					if (this.viewMap.contains(key)) {
						thisMetric = this.viewMap.get(key);
						if (thisMetric.f1 < otherMetric.f1 || thisMetric.f1.equals(otherMetric.f1))
							this.viewMap.put(key, otherMetric);
					} else
						this.viewMap.put(key, otherMetric);
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

		}

	}
}