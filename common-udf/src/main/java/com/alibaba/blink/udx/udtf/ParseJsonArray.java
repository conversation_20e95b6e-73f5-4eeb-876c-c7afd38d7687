package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSONArray;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * 这是个统一的方法，用来解析jsonArray，将每一个json字符串传出去，然后外面由json_value单独解析每个json字符串需要的字段
 */
public class ParseJsonArray extends TableFunction<Row> {
    private static final Logger LOG = LoggerFactory.getLogger(ParseJsonArray.class);

    public void eval(String message) {

        try {

            //解析json数组
            List<String> list = JSONArray.parseArray(message, String.class);

            for (String jsonItem : list) {

                Row row = new Row(1);
                row.setField(0, jsonItem);

                //输出一行
                collect(row);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            if (argumentDataTypes.size() >= 1) {
                return Optional.of(DataTypes.ROW( DataTypes.STRING()));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();
    }

//    @Override
//    public DataType getResultType(Object[] arguments, Class[] argTypes) {
//        return DataTypes.createRowType(DataTypes.STRING);
//    }

//    public static void main(String[] args) {
//        String str = "[\"1\",\"2\"]";
//        ParseJsonArray parseJsonArray = new ParseJsonArray();
//        parseJsonArray.eval(str);
//    }
}
