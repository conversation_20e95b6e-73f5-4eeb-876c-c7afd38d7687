package com.alibaba.blink.udx.udaf.acc;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;

/**
 * 累积收藏人数计算
 * 20221113 online 10
 * 20221112 offline 100
 * 20221111 offline 88
 * 20221112 online 12
 * 如果 20221112 offline 不存在 使用 20221112 的 online+20221111 的offline+20221113 online 数据作为总收藏人数，
 * 20221112 offline 存在则使用 20221112 offline + 20221113 online 作为结果
 */
public class FavoriteUvAccumulator {
    public long sum = 0;

    private static final Logger logger = LoggerFactory.getLogger(FavoriteUvAccumulator.class);


    @DataTypeHint("RAW")
//   < 2022-11-12_online,10>
    public HashMap<String, Long> dayTypeSumHash;

    public FavoriteUvAccumulator() {
        dayTypeSumHash = new HashMap<>();
    }

    public void add(String formatDay, String type, Long dayChangedQty) {
        dayTypeSumHash.put(formatDay + '_' + type, dayChangedQty);
    }

    public String getValue() {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));

            Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
            calendar.setTime(date);
            String today = dateFormat.format(calendar.getTime());

            Date yestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24), "yyyy-MM-dd"));
            calendar.setTime(yestDate);
            String yestday = dateFormat.format(calendar.getTime());

            Date beforeYestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 48), "yyyy-MM-dd"));
            calendar.setTime(beforeYestDate);
            String beforeYestDay = dateFormat.format(calendar.getTime());

            if (dayTypeSumHash.containsKey(yestday + "_offline")) {
                Long t1 = dayTypeSumHash.getOrDefault(yestday + "_offline", 0L);
                Long t2 = dayTypeSumHash.getOrDefault(today + "_online", 0L);

                if(dayTypeSumHash.containsKey(yestday + "_online")){
                    dayTypeSumHash.remove(yestday + "_online");
                }

                if(dayTypeSumHash.containsKey(beforeYestDay + "_offline")){
                    dayTypeSumHash.remove(beforeYestDay + "_offline");
                }

                return String.valueOf(t1 + t2);
            } else {
                Long s1 = dayTypeSumHash.getOrDefault(today + "_online", 0L);
                Long s2 = dayTypeSumHash.getOrDefault(yestday + "_online", 0L);
                Long s3 = dayTypeSumHash.getOrDefault(beforeYestDay + "_offline", 0L);

                return String.valueOf(s1 + s2 + s3);
            }

        } catch (Exception e) {
            logger.error("get value failed.", e);
        }

        return String.valueOf(sum);
    }

}
