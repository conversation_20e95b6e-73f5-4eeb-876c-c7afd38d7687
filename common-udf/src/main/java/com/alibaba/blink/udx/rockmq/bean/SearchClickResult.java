package com.alibaba.blink.udx.rockmq.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SearchClickResult {
    private Map<String,String> data;//点击数据
    private Boolean isNew;//是否新设备
    private String log;
    private List<String> keys;//查询到点击数据的key集合
    private String clickKey;//具体通过哪个字段查询到点击数据
    private long ttl=-998L;//点击数据ttl

    public SearchClickResult() {
        data=new HashMap<>();
        keys=new ArrayList<>();
    }

    public SearchClickResult(Map<String, String> data, Boolean isNew) {
        this.data = data;
        this.isNew = isNew;
    }

    public List<String> getKeys() {
        return keys;
    }

    public void setKeys(List<String> keys) {
        this.keys = keys;
    }

    public void setData(Map<String, String> data) {
        this.data = data;
    }

    public void setNew(Boolean aNew) {
        isNew = aNew;
    }

    public String getClickKey() {
        return clickKey;
    }

    public void setClickKey(String clickKey) {
        this.clickKey = clickKey;
    }

    public Map<String, String> getData() {
        return data;
    }

    public Boolean getNew() {
        return isNew;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }
}
