package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.Optional;

public class JsonStringUdf extends ScalarFunction {

    public String eval(Object... args) {
        if (args.length <= 1) {
            return "{}";
        }

        if (!(args.length % 2 == 0)) {
            return "{}";
        }

        JSONObject jsonObject = JSON.parseObject("{}");
        for (int i = 0; i < args.length/2; i++) {
            jsonObject.put(args[2*i].toString(),args[2*i+1]);
        }

        return JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
                .build();
    }

    public static void main(String[] args) {
        String eval = new JsonStringUdf().eval("f", null, "a", null, "f", null, "g", "");
        System.out.println(eval);
    }
}
