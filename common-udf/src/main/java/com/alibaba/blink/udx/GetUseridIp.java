package com.alibaba.blink.udx;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 *
 * <AUTHOR>
 * @Date 2021/8/25 2:02 下午
 */
@FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING"))
public class GetUseridIp extends ScalarFunction {

    public String eval(String msg, String target) {
        int length = msg.length();
        int startFlag = msg.indexOf("[algo-tags]");
        String substring = null;
        if (startFlag != -1) {
            int userFlag = msg.indexOf(target + ":");
            System.out.println(userFlag);
            if (userFlag == -1) {
                System.out.println("不存在UserId" + msg);
            } else {
                substring = msg.substring(userFlag, length);
                int startIndex = substring.indexOf(":");
//                int lastIndex = target == "UserId" ? substring.indexOf(" ") : substring.indexOf("}");
                int lastIndex = substring.indexOf(" ");
                substring = substring.substring(startIndex + 1, lastIndex);
            }
        }
        return substring;
    }
}