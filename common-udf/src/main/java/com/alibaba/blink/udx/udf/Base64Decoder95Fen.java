package com.alibaba.blink.udx.udf;

import com.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Base64;

/**
 * <AUTHOR> 张天龙
 * @Project: du-tech-data-udf
 * @Package com.alibaba.blink.udx.udf
 * @date Date : 2024年05月07日 17:47
 * 需求：
 * 逻辑：
 */
@Slf4j
public class Base64Decoder95Fen extends ScalarFunction {

	public String eval(String input) {
		final byte[] decode;
		try {
			decode = Base64.getDecoder().decode(input);
		} catch (Exception e) {
			if (!StringUtils.isBlank(input)) {
				if (input.startsWith("[\"") && input.endsWith("\"]")) {
					try {
						final byte[] retryDecode = Base64.getDecoder().decode(input.substring(2, input.length() - 2));
						return new String(retryDecode);
					} catch (Exception ex) {
						log.info("error base64 decode :{}", input);
					}
				}
				if (input.startsWith("{")) {
					return input;
				}
				log.info("error base64 decode :{}", input);
			}
			return null;
		}
		return new String(decode);
	}
}
