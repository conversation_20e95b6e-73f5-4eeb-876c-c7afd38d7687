package com.alibaba.blink.udx.udtf.dynamic.express;

import java.io.Serializable;

public interface FactoryExpression extends Serializable {

    UDExpression compile(final String expression);

    String EXPRESSION_TYPE="aviator";

    static FactoryExpression createFactoryExpression(String type) throws Exception {
        if ("aviator".equalsIgnoreCase(type)) {
            return new AviatorFactoryExpression();
        } else if ("mvel".equalsIgnoreCase(type)) {
//            return new MvelFactoryExpression();
            throw new Exception("不支持该类型！");
        } else {
            throw new Exception("不支持该类型！");
        }
    }
}
