package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class JsonParseArrayNotPkAndExpand extends TableFunction<Row> {
    @Override
    public void open(FunctionContext context) throws Exception {

    }

    /**
     * @param message array类型json
     * @param keys    指定需要返回json的key
     */
    public void eval(String message, String... keys) {
        if (keys == null) {
            throw new IllegalArgumentException("Parse array args cannot be empty!");
        }
        Row row = new Row(keys.length);

        List<JSONObject> jsonObjectList = parseInput(message);

        if (jsonObjectList == null || jsonObjectList.isEmpty()) {
            collect(row);
            return;
        }
        for (JSONObject jsonObject : jsonObjectList) {
            for (int i = 0; i < keys.length; i++) {
                row.setField(i, String.valueOf(jsonObject.get(keys[i])));
            }
            collect(row);
        }

    }

    private List<JSONObject> parseInput(String message) {

        if (StringUtils.isEmpty(message) && !"{}".equals(message) && !"[]".equals(message) && !"[{}]".equals(message)) {
            return null;
        }

        try (JSONValidator jv = JSONValidator.from(message)) {

            if (jv.validate() && jv.getType().equals(JSONValidator.Type.Array)) {
                try {
                    return JSONObject.parseArray(message, JSONObject.class);
                } catch (JSONException e) {
                    return JSONObject.parseArray(message, String.class)
                            .stream()
                            .map(JSONObject::parseObject)
                            .collect(Collectors.toList());
                }
            } else if (jv.validate() && jv.getType().equals(JSONValidator.Type.Object)) {
                return Collections.singletonList(JSONObject.parseObject(message));
            }

        } catch (Exception e) {
            throw new RuntimeException(String.format("JsonParseArrayNotPkAndExpand ParseInput error. input: %s", message), e);
        }

        return null;
    }

    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1];
                    for (int i = 0; i < argumentDataTypes.size() - 1; i++) {
                        outputDataTypes[i] = DataTypes.STRING();
                    }
                    return Optional.of(DataTypes.ROW(outputDataTypes));
                })
                .build();
    }

    @Override
    public void close() throws Exception {
    }

    public static void main(String[] args) {
        String str = "[{\"expenseType\":1,\"expenseName\":\"技术服务费\",\"originalExpense\":12495,\"currentExpense\":12495,\"originalPercent\":500,\"currentPercent\":500,\"extendJson\":\"{\\\"currentPercent\\\":500,\\\"currentexpense\\\":12495,\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalTechnicalFeeLimit\\\":{\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalExpense\\\":12495,\\\"originalPercent\\\":500},\\\"salaInfoInnerVo\\\":{\\\"salaPercent\\\":10000,\\\"salaType\\\":5}}\",\"salePercent\":10000},{\"expenseType\":3,\"expenseName\":\"转账手续费\",\"originalExpense\":2499,\"currentExpense\":2499,\"originalPercent\":100,\"currentPercent\":100,\"extendJson\":\"{\\\"currentExpense\\\":2499,\\\"currentPercent\\\":100,\\\"originalExpense\\\":2499,\\\"originalPercent\\\":100}\"},{\"expenseType\":5,\"expenseName\":\"查验费\",\"originalExpense\":800,\"currentExpense\":800,\"extendJson\":\"{\\\"currentExpense\\\":800,\\\"originalExpense\\\":800}\"},{\"expenseType\":2,\"expenseName\":\"鉴别费\",\"originalExpense\":1500,\"currentExpense\":1500,\"extendJson\":\"{\\\"currentExpense\\\":1500,\\\"originalExpense\\\":1500}\"},{\"expenseType\":4,\"expenseName\":\"包装服务费\",\"originalExpense\":1000,\"currentExpense\":1000,\"extendJson\":\"{\\\"currentExpense\\\":1000,\\\"originalExpense\\\":1000}\"},{\"expenseType\":7,\"expenseName\":\"预计收入\",\"originalExpense\":231606,\"currentExpense\":231606},{\"expenseType\":6,\"expenseName\":\"总费用\",\"originalExpense\":18294,\"currentExpense\":18294}]";
        String str2 = "[\"{\\\"processType\\\":3,\\\"propertyValueId\\\":0,\\\"showMaterialId\\\":0,\\\"sourceFrom\\\":22,\\\"spuId\\\":0}\"]";
        JsonParseArrayNotPkAndExpand jsonParseArray = new JsonParseArrayNotPkAndExpand();
        System.out.println(jsonParseArray.parseInput(str2));
        jsonParseArray.eval(str2, "processType", "propertyValueId");
    }

}
