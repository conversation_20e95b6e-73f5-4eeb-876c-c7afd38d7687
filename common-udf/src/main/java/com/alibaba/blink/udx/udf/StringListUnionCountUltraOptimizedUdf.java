package com.alibaba.blink.udx.udf;

import com.google.common.base.Strings;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashSet;
import java.util.Set;

/**
 * 简洁版本的字符串列表并集计数UDF
 * 时间复杂度：O(M + N)
 */
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class StringListUnionCountUltraOptimizedUdf extends ScalarFunction {

    public int eval(String list1, String list2) {
        if (Strings.isNullOrEmpty(list1) && Strings.isNullOrEmpty(list2)) {
            return 0;
        }
        if (Strings.isNullOrEmpty(list1)) {
            return countElements(list2);
        }
        if (Strings.isNullOrEmpty(list2)) {
            return countElements(list1);
        }

        return countUnion(list1, list2);
    }

    private int countElements(String list) {
        Set<String> set = new HashSet<>();
        int start = 0;
        int len = list.length();

        for (int i = 0; i <= len; i++) {
            if (i == len || list.charAt(i) == ',') {
                if (i > start) {
                    String element = list.substring(start, i).trim();
                    if (!element.isEmpty()) {
                        set.add(element);
                    }
                }
                start = i + 1;
            }
        }

        return set.size();
    }

    private int countUnion(String list1, String list2) {
        Set<String> set = new HashSet<>();

        // 处理第一个列表
        addElements(list1, set);

        // 处理第二个列表
        addElements(list2, set);

        return set.size();
    }

    private void addElements(String list, Set<String> set) {
        int start = 0;
        int len = list.length();

        for (int i = 0; i <= len; i++) {
            if (i == len || list.charAt(i) == ',') {
                if (i > start) {
                    String element = list.substring(start, i).trim();
                    if (!element.isEmpty()) {
                        set.add(element);
                    }
                }
                start = i + 1;
            }
        }
    }
}
