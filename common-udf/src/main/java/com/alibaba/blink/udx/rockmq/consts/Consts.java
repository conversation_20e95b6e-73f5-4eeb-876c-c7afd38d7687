package com.alibaba.blink.udx.rockmq.consts;

public class Consts {

    public static String TouTiao            = "toutiao";
    public static String GuangDianTong      = "guangdiantong";
    public static String KuaiShou           = "kuaishou";
    public static String KuaiShouBlt        = "kuaishou_blt";
    public static String KuaiShouSp         = "kuaishou_sp";
    public static String KuaiJieDanActivate = "kuaijiedan_activate";
    public static String KuaiJieDan         = "kuaijiedan";
    public static String Xinlang            = "xinlang";
    public static String Aso                = "aso";
    public static String Oppo               = "oppo";
    public static String Xiaomi             = "xiaomi";
    public static String XiaomiApk          = "xiaomi_apk";
    public static String XiaomiFeed         = "xiaomi_feed";
    public static String Bilibili           = "bilibili";
    public static String YunYinYue          = "wangyi_yunyinyue";
    public static String YouDao             = "wangyi_youdao";
    public static String XingTu             = "xingtu";
    public static String Asa                = "asa";
    public static String HuaWei             = "huawei";
    public static String Soul               = "soul";
    public static String Kuaikan            = "kuaikan";
    public static String Vivo               = "vivo";
    public static String OppoFeed           = "oppo_feed";
    public static String HuaweiFeed         = "huawei_feed";
    public static String Baidu              = "baidu";


    public static final String TypeActivate = "1";
    public static final String TypeRegister = "2";
    public static final String TypeOrder    = "3";//付费
    public static final String TypeRetain   = "4";
    public static final String TypeRetain7  = "5"; // 7日留存
    public static final String TypeStartOrder = "6"; // 下单;



    public static final String Android = "android";
    public static final String  Ios     = "ios";


    public static final String TypeRecallDefault = "1";// 普通召回
    public static final String TypeRecallSleepy  = "2";// 沉默用户召回

//    投放APP
    public static String APP_DEWU = "duapp";


    public static String SourceInit      = "init" ;     // init接口
    public static String SourceActivate  = "activate";  // kafka激活新设备
    public static String SourceUndertake = "undertake"; // 新客承接
    public static String SourceRestore   = "restore";   // 场景还原
    public static String SourceRegister  = "register";  // 用户注册
    public static String SourceActive    = "active";    // 用户活跃
    public static String SourceStartPay  = "startPay";  // 用户下单
    public static String SourcePay       = "pay";       // 用户支付


    public static String Tid          = "tid";          // trackID
    public static String  Rid          = "rid";          // 广告唯一ID，全链路匹配
    public static String Media        = "media";        // 投放媒体
    public static String AdSite       = "ad_site";      // 媒体广告位
    public static String AdType       = "ad_type";      // 媒体广告位类型
    public static String Aid          = "aid";          // 广告主ID
    public static String Gid          = "gid";          // 广告组ID
    public static String Pid          = "pid";          // 广告计划ID
    public static String  Cid          = "cid";          // 广告创意ID
    public static String AdName       = "ad_name";      // 广告名称
    public static String Imei         = "imei";         // 安卓imei
    public static String Oaid         = "oaid";         // 安卓oaid
    public static String AndroidId    = "android_id";   // 安卓ID
    public static String Idfa         = "idfa";         // 苹果idfa
    public static String Ipua         = "ipua";

    public static String Caid         = "caid";         // caid
    public static String  Mac          = "mac";          // mac
    public static String Ua           = "ua";           // 用户浏览器user agent
    public static String Ip           = "ip";           // 用户IP
    public static String Ipv6         = "ipv6";         // 用户ipv6
    public static String IpvxUa       = "ipvxua"        ; // IP和UA计算结果
    public static String Os           = "os";           // 用户手机操作系统
    public static String Model        = "model";        // 手机型号
    public static String AppId        = "app_id";       // 投放APPID
    public static String ModuleType   = "module_type";  // 投放类型，0：社区，1：交易
    public static String PackageName  = "package_name"; // 五级渠道
    public static String FourChannel  = "four_channel"; // 四级渠道
    public static String ContentId    = "content_id";   // 投放社区内容ID
    public static String Tags         = "tags";         // 投放标签
    public static String AlgoTags     = "algo_tags";    // 算法标签
    public static String DeliveryType = "delivery_type";// 跳转类型
    public static String SpuId        = "spu_id";       // 投放商品ID
    public static String SpuTags      = "spu_tags";     // 投放商品标签
    public static String Categories   = "categories";   // 分类等标签
    public static String EventTime    = "event_time";   // 行为发生时间
    public static String Channel      = "channel";      // 渠道号
    public static String Callback     = "callback";     // 回传URL
    public static String JumpTag      = "jump_tag";     // 场景还原跳转地址ID
    public static String JumpUrl      = "jump_url";     // 场景还原跳转URL
    public static String Inview       = "inview";       // 是否展示，1：是
    public static String IpUa         = "ipua";         // IP和UA计算结果
    public static String LogTime      = "log_time";     // 日志记录时间
    public static String Soft         = "soft";         // 是否软广，1：是
    public static String OrderId      = "order_id";     // 软广任务ID
    public static String VideoId      = "video_id";     // 软广视频ID
    public static String KolId        = "kol_id";       // 软广kol标识
    public static String Market       = "market";       // 是否商店投放，1：是
    public static String Recall       = "recall";       // 点击消息类型 1：召回投放 2：拉新&180天沉默用户混投
    public static String Used         = "used";         // 是否已使用，1：是

    public static String UserType   = "user_type";  // 用户类型，1：自然新增，2：投放新增，3：投放拉活
    public static String UserId     = "user_id";    // APP用户ID
    public static String DeviceType = "device_type";// 匹配方式，imei，oaid，android_id，idfa，ipua，clipboard，为空表示自然量
    public static String Type       = "type";       // 类型，1：激活，2：注册，3：下单，4：次留，5：拉活
    public static String Fcuuid     = "fcuuid";     // ios设备生成的唯一标识
    public static String ShumeiId   = "shumei_id";  // 数美ID
    public static String PkgChannel = "pkg_channel";// 安装包的原始渠道号
    public static String Uuid       = "uuid";       // 设备唯一标识
    public static String AntiSpam   = "anti_spam";  // 反作弊标识
    public static String SpamLevel  = "spam_level"; // 反作弊等级
    public static String HashOaid   = "hash_oaid";  // oaid md5
    public static String Inactive   = "inactive";   // 投放用户沉默天数

    // 素材策略相关
    public static String ContentTagId             = "content_tag_id";             // 内容标签id
    public static String MaterialStrategyLaoYueId = "material_strategy_laoyue_id";// 素材策略对应的捞月id

    public static final String UserTypeNature = "1"; // 自然新增
    public static final String UserTypeDeliveryNew = "2"; // 投放新增
    public static final String UserTypeDeliveryOld = "3"; // 投放拉活
    public static final String UserTypeDeliverySleepy = "4"; // 沉默用户召回

    public static final String 	InvalidDeviceId = "00000000";

    public static final int RiskNo = 10000;

    // To do
    public static final int RiskHigh = 10001;
    public static final Integer RiskLow = null;


    // tag
    public static final String MsgTagActiveRisk = "activeRisk"; // 激活风控
    public static final String MsgTagLtvQuery   = "ltvQuery";   // ltv查询
    public static final String MsgTagUserAction = "userAction"; // 用户关键行为延时队列
    public static final String MsgTagActivity   = "activity";   // 活动延时队列

    // 延时时间
    public static final long MqDelayTimeUserAction = 420L; // 用户行为延时7分钟
    public static final long MqDelayTimeRecall     = 120; // 用户召回延时2分钟


    public static final String DEBUG = "debug";
    public static final String KAFKA = "kafka";
    public static final String CALL_BACK_KAFKA = "call_back_kafka";

    public static final String ROCKETMQ = "rocket_mq";
    public static final String ROCKETMQ_DEBUG = "rocket_mq_debug";


    public static final String LTV_TOPIC ="ods_adv_delivery_attribution_ltv";
    public static final String ATTRIBUTE_TOPIC ="ods_dewu_algo_market_launch_label";
    public static final String CALL_BACK_KAFKA_TOPIC ="realtime_topic_demo";


    public static final String ModuleTypeSns   = "0"; // 社区
    public static final String ModuleTypeTrade = "1"; // 交易


    public static final String USE_FLAG = "1";
    public static final String NO_USE_FLAG = "0";
}
