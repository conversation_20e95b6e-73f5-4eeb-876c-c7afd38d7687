package com.alibaba.blink.udx;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class JacksonJsonStringUdf extends ScalarFunction {

	private static final ObjectMapper objectMapper = new ObjectMapper();

	static {
		// 配置ObjectMapper以包含null值
		objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
	}

	public String eval(Object... args) {
		if (args.length <= 1 || args.length % 2 != 0) {
			return "{}";
		}

		Map<String, Object> map = new HashMap<>();
		for (int i = 0; i < args.length; i += 2) {
			// 确保key是String类型，否则可能会抛出异常
			if (args[i] instanceof String) {
				map.put(args[i].toString(), args[i + 1]);
			} else {
				// 如果key不是String，可以选择忽略或抛出异常
				// 这里选择忽略
				continue;
			}
		}


		try {

			return objectMapper.writeValueAsString(map);

		} catch (Exception e) {

			// 处理异常，例如记录日志或抛出运行时异常

			e.printStackTrace();

			return "{}";

		}

	}

	@Override
	public TypeInference getTypeInference(DataTypeFactory typeFactory) {
		return TypeInference.newBuilder()
				.outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
				.build();
	}

	public static void main(String[] args) {
		String eval = new JacksonJsonStringUdf().eval("f", null, "a", null, "f", null, "g", "");
		System.out.println(eval);
		String eval2 = new JacksonJsonStringUdf().eval();
		System.out.println(eval2);
	}
}
