
package com.alibaba.blink.udx.rockmq.consts;

public class RedisConsts {
    public static String RedisPrefix = "attr:";

    public static String RedisRetain = RedisPrefix + "test_retain:";
    public static String DeliveryRedisRetain = RedisPrefix + "retain:";
    public static String HbaseShowRetain = RedisPrefix + "show:retain:";

    public static String RedisRetainRecall = RedisPrefix + "test_retain_recall:";
    public static String DeliveryRedisRetainRecall = RedisPrefix + "retain_recall:";
    public static String HbaseShowRecall = RedisPrefix + "show:retain_recall:";

    public static final String RedisClick = RedisPrefix + "click:";
    public static String HbaseShow = RedisPrefix + "show:";

    public static String RedisSoftClick  = RedisPrefix + "soft_click:";

    public static String RedisCallbackLock = RedisPrefix + "test_callback_lock:";
    public static String RedisOrderNo = RedisPrefix + "test_order_no:";

    // seconds
    public static Integer RedisClickEx = 48 * 3600;
    public static Integer RedisClickRecallEx = 12 * 3600;

    /**
     *功能描述 et值为：
     *     public static final String TypeActivate = "1";
     *     public static final String TypeRegister = "2";
     *     public static final String TypeOrder    = "3";
     * <AUTHOR>
     * @date 2022/2/17
     * @param
     * @return
     */
    public static String getCallbackLockKey(String uuid, String et) {
        return RedisCallbackLock + uuid + ":" + et;
    }
    public static String getOrderNoKey(String orderNo) {
        return RedisOrderNo + orderNo;
    }





    /**
     *功能描述 recall=1则返回RedisRetainRecall，否则返回 RedisRetain
     * <AUTHOR>
     * @date 2022/2/18
     * @param
     * @return
     */
    public static String getRetainKey(String recall) {
        if (Consts.TypeRecallDefault.equals(recall)) {
            return RedisRetainRecall;//2天
        }
        return RedisRetain;//8天
    }

    public static String getShowRetainKey(String recall) {
        if (Consts.TypeRecallDefault.equals(recall)) {
            return HbaseShowRecall;
        }
        return HbaseShowRetain;
    }

    public static String getDeliveryRetainKey(String recall) {
        if (Consts.TypeRecallDefault.equals(recall)) {
            return DeliveryRedisRetainRecall;
        }
        return DeliveryRedisRetain;
    }
}
