package com.alibaba.blink.udx.udtf;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.msgpack.jackson.dataformat.MessagePackFactory;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * 解析用户app登录成功返回的日志
 */
public class AppLoginCheckParse extends TableFunction<Row> {

	private ObjectMapper objectMapper;

	@Override
	public void open(FunctionContext context) {
		objectMapper = new ObjectMapper(new MessagePackFactory());
		//驼峰下划线装欢
		objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
		//忽略不存在字段
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

	public void eval(byte[] msg) throws IOException {
		Row row = new Row(5);
		AppLoginCheck appLoginCheck = objectMapper.readValue(msg, AppLoginCheck.class);
		if (Objects.nonNull(appLoginCheck)) {
			int pos = 0;
			String name = appLoginCheck.getName();
			long timestamp = appLoginCheck.getTimestamp();
			row.setField(pos++, name);
			row.setField(pos++, timestamp);
			AppLoginCheck.Tags tags = appLoginCheck.getTags();
			Integer code = tags.getCode();
			row.setField(pos++, code);
			row.setField(pos++, String.valueOf(tags.getUserId()));
			row.setField(pos, tags.getUuid());

		}
		collect(row);
	}

	@Override
	public TypeInference getTypeInference(DataTypeFactory typeFactory) {
		return TypeInference.newBuilder()
				.outputTypeStrategy(callContext -> Optional.of(DataTypes.ROW(DataTypes.STRING(), DataTypes.BIGINT(), DataTypes.INT(), DataTypes.STRING(), DataTypes.STRING())))
				.build();
	}
}

class AppLoginCheck {
	private String app;
	private String name;
	private Long timestamp;
	private Tags tags;

	public AppLoginCheck() {

	}

	public String getApp() {
		return app;
	}

	public void setApp(String app) {
		this.app = app;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(Long timestamp) {
		this.timestamp = timestamp;
	}

	public Tags getTags() {
		return tags;
	}

	public void setTags(Tags tags) {
		this.tags = tags;
	}

	class Tags {
		private Integer code;
		private Long userId;
		private String uuid;

		public Tags() {
		}

		public Integer getCode() {
			return code;
		}

		public void setCode(Integer code) {
			this.code = code;
		}

		public Long getUserId() {
			return userId;
		}

		public void setUserId(Long userId) {
			this.userId = userId;
		}

		public String getUuid() {
			return uuid;
		}

		public void setUuid(String uuid) {
			this.uuid = uuid;
		}

	}
}