package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAccessor;
import java.util.Locale;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/8/27 16:42
 */
public class StringTimeValidator extends ScalarFunction {
    private static final DateTimeFormatter HH_TIME_FORMATTER;
    private static final DateTimeFormatter yyyyMMdd_TIME_FORMATTER;
    private static final DateTimeFormatter H_TIME_FORMATTER;
    private static final DateTimeFormatter yyyy_MM_dd_DATE_FORMATTER;
    private static final DateTimeFormatter yyyy_MM_dd_HHmmss_DATE_FORMATTER;
    private static final DateTimeFormatter yyyy_MM_dd_HHmmssSSS_DATE_FORMATTER;
    private static final DateTimeFormatter yyyy_MM_ddTHHmmssSSSZ_DATE_FORMATTER;

    static {

        HH_TIME_FORMATTER = new DateTimeFormatterBuilder()
                .appendValue(ChronoField.HOUR_OF_DAY, 2)
                .toFormatter(Locale.ENGLISH);

        yyyyMMdd_TIME_FORMATTER = new DateTimeFormatterBuilder()
                .parseStrict()
                .appendValue(ChronoField.YEAR, 4)
                .appendValue(ChronoField.MONTH_OF_YEAR, 2)
                .appendValue(ChronoField.DAY_OF_MONTH, 2)
                .toFormatter(Locale.ENGLISH);

        H_TIME_FORMATTER = new DateTimeFormatterBuilder()
                .parseStrict()
                .appendValue(ChronoField.HOUR_OF_DAY, 1)
                .toFormatter(Locale.ENGLISH);

        yyyy_MM_dd_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        yyyy_MM_dd_HHmmss_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        yyyy_MM_dd_HHmmssSSS_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        yyyy_MM_ddTHHmmssSSSZ_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    }

    public boolean eval(String time, String format) {

        DateTimeFormatter formatter = null;

        if (format == null) {
            formatter = yyyy_MM_dd_HHmmss_DATE_FORMATTER;
        } else {
            switch (format) {
                case "HH":
                    formatter = HH_TIME_FORMATTER;
                    break;
                case "yyyyMMdd":
                    formatter = yyyyMMdd_TIME_FORMATTER;
                    break;
                case "H":
                    formatter = H_TIME_FORMATTER;
                    break;
                case "yyyy-MM-dd":
                    formatter = yyyy_MM_dd_DATE_FORMATTER;
                    break;
                case "yyyy-MM-dd HH:mm:ss":
                    formatter = yyyy_MM_dd_HHmmss_DATE_FORMATTER;
                    break;
                case "yyyy-MM-dd HH:mm:ss.SSS":
                    formatter = yyyy_MM_dd_HHmmssSSS_DATE_FORMATTER;
                    break;
                case "yyyy-MM-dd'T'HH:mm.ss.SSS'Z'":
                    formatter = yyyy_MM_ddTHHmmssSSSZ_DATE_FORMATTER;
                    break;
                default:
                    formatter = DateTimeFormatter.ofPattern(format);

            }
        }


        return isTimeValid(time, formatter);


    }

    public boolean isTimeValid(String time, DateTimeFormatter formatter) {
        boolean isValid = true;

        // 解析字符串时间
        try {
            TemporalAccessor javaTime = formatter.parse(time);
            // 校验时间是否与原字符串一致
            isValid = time.equals(formatter.format(javaTime));
        } catch (DateTimeParseException e) {
            isValid = false;
        }
        return isValid;
    }

    public static void main(String[] args) {
        String str="24";
        StringTimeValidator stringTimeValidator = new StringTimeValidator();
        System.out.println(stringTimeValidator.eval(str, "HH"));
    }

}
