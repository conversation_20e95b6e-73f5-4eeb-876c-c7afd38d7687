package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;

import java.util.*;


@Slf4j
public class MergeJsonForOffset extends AggregateFunction<String, MergeJsonForOffset.JsonAcc> {

    public static List<String> needKeys = new ArrayList<>();
    public static Boolean isFilter = true;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        isFilter = Boolean.valueOf(context.getJobParameter("MergeJsonForOffset.isFilter", "true"));
        needKeys = new ArrayList<String>(Arrays.asList(context.getJobParameter("MergeJsonForOffset.needKeys", "").split(",")));
        log.info("keys:{}",needKeys);
    }

    @Override
    public JsonAcc createAccumulator() {
        return new JsonAcc();
    }

    @Override
    public String getValue(JsonAcc accumulator) {
        return accumulator.getCache();
    }

    public void accumulate(MergeJsonForOffset.JsonAcc accumulator, String value,Long offset) {

        if (offset == null) {
            return;
        }

        JSONObject source = object2Json(value);

        accumulator.acc(source, offset);

    }

    public void merge(MergeJsonForOffset.JsonAcc accumulator, Iterable<MergeJsonForOffset.JsonAcc> iterables) {
        for (MergeJsonForOffset.JsonAcc acc : iterables) {
            accumulator.merge(acc);
        }
    }

    private JSONObject object2Json(Object o) {
        //嵌套Json 避免转译
        if (o instanceof String && JSONValidator.from((String) o).validate()) {
            return JSON.parseObject((String) o);
        } else if (o instanceof JSONObject) {
            return (JSONObject) o;
        } else
            throw new RuntimeException("arg is not json. arg: " + o);
    }

    public static class JsonAcc {
        // TODO: 2022/11/10  map<metricKey ,offset>
        public MapView<String, Tuple2<String,Long>> viewMap;

        public JsonAcc() {
            this.viewMap = new MapView<>();
        }

        public String getCache() {

            JSONObject object = new JSONObject();

            try {
                viewMap.entries().forEach(stringTuple2Entry -> object.put(stringTuple2Entry.getKey(),stringTuple2Entry.getValue().f0));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            return JSON.toJSONString(object);

        }

        /**
         * 默认将 metric_value ext dim_value 透出
         * */
        public void setCache(JSONObject cache,Long offset) {

            cache.forEach((s, o) -> setValue(s, o, offset));
        }

        private void setValue(String s, Object o, Long ts) {

            if (!filter(s)){
                return;
            }

            try {
                if (viewMap.contains(s)){

                    Tuple2<String, Long> tuple2 = viewMap.get(s);
                    if (tuple2.f1 < ts || tuple2.f1.equals(ts)){
                        tuple2.f0 = String.valueOf(o);
                        tuple2.f1 = ts;
                    }

                    viewMap.put(s,tuple2);

                }else {
                    viewMap.put(s,Tuple2.of(String.valueOf(o),ts));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }


        }

        public boolean filter(String key){

            if (isFilter){
                return needKeys.contains(key);
            }
            return true;
        }

        public boolean isBlankOrNull(JSONObject value) {
            return Objects.isNull(value) || value.isEmpty();
        }

        public void acc(JSONObject value,Long offset) {
            if (!isBlankOrNull(value)) {
                setCache(value,offset);
            }
        }

        public void merge(JsonAcc otherAcc) {
            try {
                for (Map.Entry entry : otherAcc.viewMap.entries()) {
                    String key = (String) entry.getKey();

                    Tuple2<String, Long> otherMetric = (Tuple2<String, Long>) entry.getValue();

                    if (this.viewMap.contains(key)) {

                        Tuple2<String, Long> thisMetric = this.viewMap.get(key);
                        if (thisMetric.f1 < otherMetric.f1 || thisMetric.f1.equals(otherMetric.f1))
                        this.viewMap.put(key, otherMetric);

                    } else
                        this.viewMap.put(key, otherMetric);
                }

            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }

    }
    
}
