package com.alibaba.blink.udx.udtf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;


/**
 * Author: WangLin
 * Date: 2022/10/9 上午10:14
 * Description: 解析列表数据，一变多
 */
@FunctionHint(output = @DataTypeHint("ROW<f0 STRING>"))
public class SplitStrTable extends TableFunction<Row> {

    public void eval(String input, String delimiter) throws Exception {

        try {
            String[] split = input.split(delimiter);
            for (int i = 0; i < split.length; i++) {
                String s1 = split[i];
                Row row = new Row(1);
                row.setField(0, s1.trim());
                collect(row);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("input:"+input+" delimiter:"+delimiter,e);
        }
    }

    public static void main(String[] args) {

        String[] split = "RE101003274991874111".split("_");
        for (int i = 0; i < split.length; i++) {
            String s1 = split[i];
            Row row = new Row(1);
            row.setField(0, s1.trim());
//            collect(row);
        }
    }


}
