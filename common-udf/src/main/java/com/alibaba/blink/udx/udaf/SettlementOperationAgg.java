package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.SettlementOperationAccumulator;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.table.types.utils.DataTypeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * Created on 2023/03/09.
 * Union uniqueKey join
 *
 * <AUTHOR>
 */
public class SettlementOperationAgg extends AggregateFunction<String, SettlementOperationAccumulator> {
    private static final Logger logger = LoggerFactory.getLogger(SettlementOperationAgg.class);

    public void accumulate(SettlementOperationAccumulator accumulator, String groupKey, String tableName, String uniqueKey, String row) throws Exception {
        if (null == row) {
            return;
        }
        accumulator.add(tableName, uniqueKey, row);
    }

    public void retract(SettlementOperationAccumulator accumulator, String groupKey, String tableName, String uniqueKey, String row) {

    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {

                    List<DataType> dataTypes = callContext.getArgumentDataTypes();

                    DataType argDataType;

                    if (dataTypes.get(0)
                            .getLogicalType()
                            .getTypeRoot()
                            .getFamilies()
                            .contains(LogicalTypeFamily.CHARACTER_STRING)) {
                        argDataType = DataTypes.STRING();
                    } else
                        argDataType = DataTypeUtils.toInternalDataType(dataTypes.get(0));

                    return Optional.of(argDataType);
                })
                .accumulatorTypeStrategy(callContext -> {

                    List<DataType> dataTypes = callContext.getArgumentDataTypes();

                    DataType argDataType;
                    if (dataTypes.get(0)
                            .getLogicalType()
                            .getTypeRoot()
                            .getFamilies()
                            .contains(LogicalTypeFamily.CHARACTER_STRING)) {
                        argDataType = DataTypes.STRING();
                    } else
                        argDataType = DataTypeUtils.toInternalDataType(dataTypes.get(0));


                    DataType accDataType = DataTypes.STRUCTURED(
                            SettlementOperationAccumulator.class,
                            DataTypes.FIELD("resViewMap", MapView.newMapViewDataType(DataTypes.STRING(), DataTypes.MAP(DataTypes.STRING(), DataTypes.STRING()).bridgedTo(Map.class)).bridgedTo(MapView.class)),
                            DataTypes.FIELD("updatedKeyMap", DataTypes.MAP(DataTypes.STRING(), DataTypes.ARRAY(DataTypes.STRING()).bridgedTo(List.class)).bridgedTo(Map.class))
                    );

                    return Optional.of(accDataType);
                })
                .build()
                ;

    }

    @Override
    public String getValue(SettlementOperationAccumulator settlementOperationAccumulator) {
        try {
            return settlementOperationAccumulator.getSnapshot();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public SettlementOperationAccumulator createAccumulator() {
        return new SettlementOperationAccumulator();
    }

    public void resetAccumulator(SettlementOperationAccumulator settlementOperationAccumulator) {
        settlementOperationAccumulator.updatedKeyMap.clear();
    }

    ;

    public static void main(String[] args) throws Exception {
        SettlementOperationAgg testagg = new SettlementOperationAgg();
        SettlementOperationAccumulator acc = new SettlementOperationAccumulator();
        testagg.accumulate(acc, "m", "a", "1", "{\"uniquekey\":1,\"tablea\":\"m\",\"a_1\":\"a_1\"}");
        testagg.accumulate(acc, "m", "a", "1", "{\"uniquekey\":1,\"tablea\":\"m\",\"a_1\":\"b_1\"}");
        testagg.accumulate(acc, "m", "b", "1", "{\"uniquekeyb\":2,\"tableb\":\"m\",\"b\":\"1\"}");
        testagg.accumulate(acc, "m", "b", "3", "{\"uniquekeyb\":3,\"tableb\":\"m\",\"b\":\"3\"}");
        testagg.accumulate(acc, "m", "c", "d", "{\"uniquekeyc\":5,\"tablec\":\"m\",\"c\":\"hh\"}");


        System.out.println(testagg.getValue(acc));
//        [{"uniquekeyb":2,"spu_idb":"m","a_1":"a_1","uniquekey":1,"a_2":"a_2","spu_ida":"m"},{"uniquekeyb":2,"spu_idb":"m","a_1":"b_1","uniquekey":1,"a_2":"a_2","spu_ida":"m"}]

    }
}