package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("BOOLEAN")}, output = @DataTypeHint("STRING"))
public class MidHour extends ScalarFunction {

    public String eval(String input,Boolean isBefore){
        Integer inputNum = formatInput(input);
        String ret = "";
        if(isBefore){
            for (int i = 0 ; i <= inputNum ; i++){
                ret += (formatOutput(i) + ",");
            }
        }else {
            for (int i = inputNum ;i <= 23 ; i++){
                ret += (formatOutput(i) + ",");
            }
        }

        return ret.substring(0,ret.length()-1);
    }

    private String formatOutput(Integer input){
        if(input < 10){
            return "0"+input;
        }else {
            return input+"";
        }
    }

    private Integer formatInput(String input){
        if(input.startsWith("0")){
            return Integer.valueOf(input.substring(1,2));
        }else{
            return Integer.valueOf(input);
        }
    }

    public static void main(String[] args) {
        System.out.println(new MidHour().eval("07",true));
    }
}
