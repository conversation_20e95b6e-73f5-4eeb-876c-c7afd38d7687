package com.alibaba.blink.udx.udaf;


import com.alibaba.blink.udx.udaf.acc.WideJsonAcc;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.ArrayList;


/**
 * Created on 2022/8/19.
 *
 * <AUTHOR>
 */

public class MergeMetricToWideJson extends AggregateFunction<String, WideJsonAcc> {
    @Override
    public WideJsonAcc createAccumulator() {
        return new WideJsonAcc();
    }

    public void accumulate(WideJsonAcc accumulator, String... args) throws Exception {
        if (args.length / 2 !=0 && args.length<2){
            throw new RuntimeException("args must k/v");
        }
        for (int i = 0; i < args.length-1; i=i+2) {
            if (args[i]!=null && args[i+1]!=null){
                accumulator.add(args[i],args[i+1]);
            }
        }
    }

    @Override
    public String getValue(WideJsonAcc accumulator) {
        return accumulator.toString();
    }

    public void merge(WideJsonAcc accumulator, Iterable<WideJsonAcc> its){
        for (WideJsonAcc acc : its) {
            accumulator.merge(acc);
        }
    }
    public void retract(WideJsonAcc accumulator, String... args){

    }

    public static void main(String[] args) throws Exception {
        MergeMetricToWideJson wideJson = new MergeMetricToWideJson();
        WideJsonAcc acc = new WideJsonAcc();
        wideJson.accumulate(acc,"xx1","222.222","id","99");
        wideJson.accumulate(acc,"xx2","222.222","id","99");
        wideJson.accumulate(acc,"xx3","222.222","id","99");
        wideJson.accumulate(acc,"xx1","3333","id","99");
        System.out.println(wideJson.getValue(acc));

        WideJsonAcc acc1 = new WideJsonAcc();
        wideJson.accumulate(acc1,"xx4","222.222","id","99");
        wideJson.accumulate(acc1,"xx7","222.222","id","99");
        wideJson.accumulate(acc1,"xx8","222.222","id","99");
        wideJson.accumulate(acc1,null,"3333","id","99");

        ArrayList<WideJsonAcc> accs = new ArrayList<>();
        accs.add(acc);
        accs.add(acc1);

        WideJsonAcc acc2 = new WideJsonAcc();
        wideJson.merge(acc2,accs);

        System.out.println(wideJson.getValue(acc2));
    }


}
