package com.alibaba.blink.udx.udf;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/8/7 14:50
 */

@Slf4j
public class FindMinDiffDate extends ScalarFunction {

    public String eval(Long diff, String stringArr, String date) {

        try {
            List<String> elementList = new ArrayList<>(Optional.ofNullable(stringArr)
                    .map(str -> Arrays.asList(str.split(",")))
                    .orElse(Collections.emptyList()));
            List<LocalDate> orderList = elementList.stream()
                    .map(x -> LocalDate.parse(x, java.time.format.DateTimeFormatter.BASIC_ISO_DATE)) // 将字符串解析成 LocalDate
                    .sorted(Comparator.reverseOrder())
                    .collect(Collectors.toList());

            LocalDate nowDate = LocalDate.parse(date, java.time.format.DateTimeFormatter.BASIC_ISO_DATE);

            return orderList.stream()
                    .filter(str -> {
                        long tmpDiff = dateDiff(str, nowDate);
                        return tmpDiff <= diff && tmpDiff >= 0;
                    })
                    .findFirst()
                    .map(str -> str.format(DateTimeFormatter.BASIC_ISO_DATE))
                    .orElse(null);


        } catch (Exception e) {
            log.error("error,stringArr:{},date:{}", stringArr, date);
            throw new RuntimeException(e);
        }

    }

    public long dateDiff(LocalDate strDate, LocalDate nowDate) {
        return ChronoUnit.DAYS.between(strDate, nowDate);
    }

}
