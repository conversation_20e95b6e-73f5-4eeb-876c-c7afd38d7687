package com.alibaba.blink.udx.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.util.LinkedHashSet;
import java.util.Set;

public class UuidListMerge extends ScalarFunction {
    public String eval(String uuid, String uuidList) {

        // 处理空值情况
        if (uuid == null || uuid.trim().isEmpty()) {
            return uuidList != null ? uuidList : "";
        }

        if (uuidList == null || uuidList.trim().isEmpty()) {
            return uuid.trim();
        }

        String trimmedUuid = uuid.trim();

        // 使用LinkedHashSet保持插入顺序
        Set<String> uuidSet = new LinkedHashSet<>();

        // 先添加原列表中的UUID
        String[] existingUuids = uuidList.split(",");
        for (String existingUuid : existingUuids) {
            String trimmed = existingUuid.trim();
            if (!trimmed.isEmpty()) {
                uuidSet.add(trimmed);
            }
        }

        // 检查是否已包含该UUID，如果已包含则直接返回原列表
        if (uuidSet.contains(trimmedUuid)) {
            return uuidList;
        }

        // 否则添加新UUID并返回合并后的列表
        uuidSet.add(trimmedUuid);
        return String.join(",", uuidSet);
    }
}
