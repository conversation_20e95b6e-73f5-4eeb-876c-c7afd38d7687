package com.alibaba.blink.udx.udaf;


import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.HashSet;
import java.util.Set;


public class ConcatString extends AggregateFunction<String, ConcatString.ConcatStringAccum> {


    public static class ConcatStringAccum {
        @DataTypeHint("RAW")
        public HashSet<String> retSet;

        public ConcatStringAccum() {
            retSet = new HashSet<>();
        }
    }

    @Override
    public ConcatStringAccum createAccumulator() {
        ConcatStringAccum acc = new ConcatStringAccum();
//        acc.retSet = new HashSet<>();
        return acc;
    }


    @Override
    public String getValue(ConcatStringAccum accumulator) {
        String ret = "";
        for (String str: accumulator.retSet){
            ret += str;
        }
        return ret;
    }

    public void accumulate(ConcatStringAccum accumulator, String iValue) {

        if(iValue != null){
            accumulator.retSet.add(iValue);

        }
    }

    public void retract(ConcatStringAccum accumulator, String iValue) {
    }


    public void merge(ConcatStringAccum accumulator, Iterable<ConcatStringAccum> its) {
        for (ConcatStringAccum other : its) {
//            if(!set.contains(other.retString)){
//                accumulator.retString += (","+other.retString);
//                set.add(other.retString);
//            }
            accumulator.retSet.addAll(other.retSet);
        }
    }
}
