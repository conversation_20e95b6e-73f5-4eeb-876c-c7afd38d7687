package com.alibaba.blink.udx.udf;

import com.alibaba.blink.udx.udf.jsonpath.JacksonJsonProvider;
import com.alibaba.blink.udx.udf.jsonpath.JacksonMappingProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;

import com.jayway.jsonpath.spi.mapper.MappingProvider;
import org.apache.flink.metrics.Counter;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class JsonValue extends ScalarFunction {

    private static final long serialVersionUID = 1L;

    private transient Counter counter;

    private transient JSONObject object;

    private transient String lastInput;

    @Override
    public boolean isDeterministic() {
        return true;
    }

    @Override
    public void open(FunctionContext context) throws Exception {
        counter = context.getMetricGroup().counter("json_value_exception");
    }

    /**
     * json_path_value
     *
     * @param jsonString
     * @param jsonPath
     * @return
     */
    public String eval(String jsonString, String jsonPath) {
        String errorMessagePrefix = "function json_path_value(String jsonString, String jsonPath) ";
        if (jsonPath == null || jsonPath.trim().equals("")) {
            throw new RuntimeException(errorMessagePrefix + " jsonPath must not be null or empty.");
        }

        if (lastInput != null && lastInput.equals(jsonString) && object != null) {

        } else {
            try {
                object = JSON.parseObject(jsonString);
            } catch (Exception e) {
                if (counter != null) {
                    counter.inc();
                }
                return null;
            }
        }

        if (object instanceof JSONObject) {
            Object valueObject = JSONPath.eval(object, jsonPath);
            if (valueObject == null) {
                return null;
            } else if (valueObject instanceof JSON) {
                return ((JSON) valueObject).toJSONString();
            } else {
                return valueObject.toString();
            }
        } else {
            if (counter != null) {
                counter.inc();
            }
            return null;
        }
    }

    public static void main(String[] args) {
        JsonValue json = new JsonValue();

        String rst = json.eval("{\"a\":\"b\"}", "$.a");
        System.err.println(rst);
    }

}
