package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.InvQtyAccumulator;
import org.apache.flink.table.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * Created on 2022/4/13.
 * 计算当前2days的库存数据
 * <AUTHOR>
 */
public class Fixed2DaysBucketAgg extends AggregateFunction<String, InvQtyAccumulator> {
    private static final Logger logger = LoggerFactory.getLogger(Fixed2DaysBucketAgg.class);

    //    timeLabel 就是formatday
    public void accumulate(InvQtyAccumulator accumulator, String timeLabel, Long value) {

        if (null == value) {
            value = 0L;
        }
        accumulator.add(timeLabel,value);
    }

    public void retract(InvQtyAccumulator accumulator, String timeLabel, Long value){

    }

    @Override
    public String getValue(InvQtyAccumulator invQtyAccumulator) {
         return invQtyAccumulator.getValue();
    }

    @Override
    public InvQtyAccumulator createAccumulator() {
        return new InvQtyAccumulator();
    }

//    public static void main(String[] args) throws ParseException {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
//        Date yestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24), "yyyyMMdd"));
//        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
//        calendar.setTime(yestDate);
//        String yestday = dateFormat.format(calendar.getTime());
////        System.out.println(yestday);
//        Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyyMMdd"));
//        calendar.setTime(date);
//        String today = dateFormat.format(calendar.getTime());
////        System.out.println(today);
////        calendar.setTime(date);
//
//    }
}
