package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.functions.ScalarFunction;
import org.msgpack.jackson.dataformat.MessagePackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Author: WangLin (Modified)
 * Date: 2022/11/10 下午6:08
 * Description: 解析投放的数据 - V2版本，修复了类型转换异常问题
 * 
 * 修复问题：
 * - 原版本在遇到非Map类型的MessagePack数据时会抛出JsonMappingException
 * - V2版本支持处理各种类型的MessagePack数据（Map、基本类型、数组等）
 */
public class TouFangDataAnalysisV2 extends ScalarFunction {

	private final static Logger logger = LoggerFactory.getLogger(TouFangDataAnalysisV2.class);

	// 避免静态初始化导致的类加载问题，使用延迟初始化
	private transient ObjectMapper msgpackMapper;

	/**
	 * 获取 MessagePack ObjectMapper 实例，使用延迟初始化避免类加载问题
	 */
	private ObjectMapper getMsgpackMapper() {
		if (msgpackMapper == null) {
			try {
				msgpackMapper = new ObjectMapper(new MessagePackFactory());
			} catch (Exception e) {
				logger.error("初始化 MessagePack ObjectMapper 失败", e);
				throw new RuntimeException("无法初始化 MessagePack ObjectMapper", e);
			}
		}
		return msgpackMapper;
	}

	public String eval(byte[] body) {
		try {
			// 参数校验
			if (body == null || body.length == 0) {
				logger.warn("TouFangDataAnalysisV2: 输入的字节数组为空");
				return null;
			}

			// 先尝试解析为通用对象，然后根据类型处理
			Object parsedObject = getMsgpackMapper().readValue(body, Object.class);

			// 如果解析出来的是 Map，直接转换（保持与原版本相同的行为）
			if (parsedObject instanceof Map) {
				return JSON.toJSONString(parsedObject);
			}
			// 如果是其他类型（如数字、字符串、数组等），包装成一个简单的 JSON 对象
			else {
				Map<String, Object> wrapper = new java.util.HashMap<>();
				wrapper.put("value", parsedObject);
				wrapper.put("type", parsedObject.getClass().getSimpleName());
				return JSON.toJSONString(wrapper);
			}

		} catch (Exception e) {
			logger.error("TouFangDataAnalysisV2 解析失败，数据长度: {}, 错误信息: {}", 
					body != null ? body.length : 0, e.getMessage(), e);
			return null;
		}
	}
}
