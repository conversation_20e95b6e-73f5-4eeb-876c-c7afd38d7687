package com.alibaba.blink.udx.rockmq.bean;

public class RequestInfo {

    private String imei;
    private String oaid;
    private String deviceId;
    private long uid;
    private String clientIp;
    private String userAgent;
    private String channel;
    private String shumeiId;
    private String version;



//    Imei      string `json:"imei"`
//    Oaid      string `json:"oaid"`
//    DeviceId  string `json:"deviceId"`
//    Uid       int64  `json:"uid"`
//    ClientIp  string `json:"clientIp"`
//    UserAgent string `json:"userAgent"`
//    Channel   string `json:"channel"`
//    ShumeiId  string `json:"shumeiId"`
//    Version   string `json:"version"`



    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getShumeiId() {
        return shumeiId;
    }

    public void setShumeiId(String shumeiId) {
        this.shumeiId = shumeiId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
