package com.alibaba.blink.udx;

import com.util.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


// ua 说明文档移动端浏览器 userAgent 汇总 截止2021-06-22_peade的博客-CSDN博客_移动端useragent
// https://blog.csdn.net/peade/article/details/118105339
// Dalvik/2.1.0 (Linux; U; Android 10; JEF-AN00 Build/HUAWEIJEF-AN00)
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
public class LogUaParse extends ScalarFunction {
    public String eval(String useragent) {
        if (StringUtils.isBlank(useragent))
            return null;
        //处理  U; 和 zh-cn; 字符串问题替换
        useragent=useragent.replace("(Linux; U;","(Linux;").toLowerCase().replace("zh-cn;","");
        String[] result = new String[3];
        Pattern pattern1 = Pattern.compile("(linux);\\s?([a-z]+)\\s?(.*\\d);\\s?(.*)\\s?(;|build)/");
        Pattern pattern3 = Pattern.compile("(linux);\\s?([a-z]+)\\s?(.*\\d);\\s?(.*)\\s?(; wv)");
        Pattern pattern2 = Pattern.compile("(ipad|iphone);\\s?\\b(cpu.*os)\\b\\s?(.*\\d)\\b\\s(like)");
        Matcher matcher1 = pattern1.matcher(useragent.toLowerCase());
        Matcher matcher2 = pattern2.matcher(useragent.toLowerCase());
        Matcher matcher3 = pattern3.matcher(useragent.toLowerCase());
        try {
            if(useragent.startsWith("com.ss.android.ugc.aweme")) {
                String[] strSplitAttr= useragent.split(";");
                result[0] = strSplitAttr[1].trim().split(" ")[0];
                result[1] = strSplitAttr[1].trim().split(" ")[1];
                result[2] = strSplitAttr[3].trim();

            }else
            if (matcher1.find()) {
                result[0] = matcher1.group(2);
                result[1] = matcher1.group(3);
                result[2] = matcher1.group(4);
            } else
            if (matcher3.find()) {
                result[0] = matcher3.group(2);
                result[1] = matcher3.group(3);
                result[2] = matcher3.group(4);
            } else if (matcher2.find()) {
                result[0] = "ios";
                result[1] = matcher2.group(3).replace("_", ".");
                result[2] = matcher2.group(1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result[0] + "#" + result[1] + "#" + result[2];
    }

    public static void main(String[] args) {
        String userAgent = "Mozilla/5.0 (Linux; Android 8.1.0; EML-AL00 Build/HUAWEIEML-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.91 Mobile Safari/537.36/duapp/4.35.0(android;8.1.0)";
        String userAgent2 = "Mozilla/5.0 (Linux; U; Android 11; zh-cn; Redmi K20 Pro Build/RKQ1.200826.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36 XiaoMi/MiuiBrowser/15.5.12; android 11";
        userAgent="Mozilla/5.0 (Linux; Android 10; V2057A Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36  aweme_190200 JsSdk/1.0 NetType/WIFI Channel/vivo_1128_64 AppName/aweme app_version/19.2.0 ByteLocale/zh-CN Region/CN AppSkin/white AppTheme/light BytedanceWebview/d8a21c6 TTWebView/0751130025426";
        userAgent2="Mozilla/5.0 (iPhone; CPU iPhone OS 15_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 aweme_19.0.0 JsSdk/2.0 NetType/WIFI Channel/App Store ByteLocale/zh Region/CN AppTheme/dark RevealType/Dialog WKWebView/1 BytedanceWebview/d8a21c6";
        String userAgent3="com.ss.android.ugc.aweme/190201 (Linux; U; Android 10; zh_CN_#Hans; ART-AL00x; Build/HUAWEIART-AL00x; Cronet/TTNetVersion:28eaf52b 2021-12-28 QuicVersion:68cae75d 2021-08-12)";
        String userAgent4="Mozilla/5.0 (Linux; Android 9; ONEPLUS A5010; wv) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.101 Mobile Safari/537.36 JsSdk/2 NewsArticle/7.3.1 NetType/wifi";
        String userAgent5 = "Mozilla/5.0 (Linux; Android 9; Redmi K20 Build/PKQ1.190302.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Mobile Safari/537.36 JsSdk/2 NewsArticle/7.2.3";

        LogUaParse logUaParse = new LogUaParse();
        System.out.println(logUaParse.eval(userAgent));
        System.out.println(logUaParse.eval(userAgent2));
        System.out.println(logUaParse.eval(userAgent3));
        System.out.println(logUaParse.eval(userAgent4));
        System.out.println(logUaParse.eval(userAgent5).split("#")[2]);
    }
}