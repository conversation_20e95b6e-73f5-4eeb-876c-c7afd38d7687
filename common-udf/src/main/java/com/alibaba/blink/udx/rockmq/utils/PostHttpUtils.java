package com.alibaba.blink.udx.rockmq.utils;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.BufferedWriter;


/**
 * Created by 刘洋 on 2018/9/10.
 */
public class PostHttpUtils extends HttpBase {

    protected BufferedWriter out;

    private static volatile PostHttpUtils singleton;

    private PostHttpUtils() {
        restTemplate = createSimpleRestTemplate();
    }

    public static PostHttpUtils getInstance() {
        if (singleton == null) {
            synchronized (PostHttpUtils.class) {
                if (singleton == null) {
                    singleton = new PostHttpUtils();
                }
            }
        }
        return singleton;
    }


    public void open() throws Exception {
        restTemplate = createSimpleRestTemplate();
    }


    public String eval(String url,String jsonData,MultiValueMap<String, String> map,HttpHeaders headers) {
        HttpEntity<String> request = new HttpEntity<>(jsonData, headers);

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url).queryParams(map);

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(builder.toUriString(), request, String.class);
        return responseEntity.getBody();
    }

}
