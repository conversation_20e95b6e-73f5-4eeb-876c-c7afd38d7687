package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.ScalarFunction;

public class ToJsonStringFunction extends ScalarFunction {

	public String getName() {
		return "to_json_string";
	}

	public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object objectValue) {
		try {
			return JSON.toJSONString(objectValue);
		} catch (Exception e) {
			return "{}";
		}
	}

}
