package com.alibaba.blink.udx;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * @Author: <PERSON><PERSON>
 * @Date: 4/14/22 8:19 下午
 */

@FunctionHint(
    input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"), @DataTypeHint("INT")},
    output = @DataTypeHint("STRING")
)
public class SubStringBySep extends ScalarFunction {

  private static final int HEX_LENGTH = 32;

  private static final int BINARY_LENGTH = 128;

  @Override
  public void open(FunctionContext context) {
    // ... ...
  }

  public String eval(String str, String separator, int index) {
    String[] split = str.split(separator);
    if (index < 0) {
      return split[split.length + index];
    } else {
      return split[index];
    }
  }

  // 可选，close方法可以不写
  @Override
  public void close() {
    // ... ...
  }

  public static void main(String[] args) {
//        System.out.println(new SubStringBySep().eval("https://fast.dewu.com/nezha-plus/detail/5f58a709b108ca095a5142b8",
//        "/", -1));
    System.out.println(new SubStringBySep().eval(
        "https://fast.dewu.com/nezha-plus/detail/5f58a709b108ca095a5142b8", "/", 0));
  }

}
