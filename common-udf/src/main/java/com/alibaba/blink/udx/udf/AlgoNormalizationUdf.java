package com.alibaba.blink.udx.udf;

import com.shizhuang.duapp.algo.analyzer.core.util.CharacterUtil;
import com.util.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * 算法针对搜索词归一化
 */
public class AlgoNormalizationUdf extends ScalarFunction {

    public String eval(String keyWord) {

        if (StringUtils.isBlank(keyWord)) {
            return keyWord;
        }
        keyWord = CharacterUtil.normalizeWord(keyWord);
        // keyWord = keyWord + " " + extend(keyWord);
        //解决badcase Dior 迪奥 全新烈艳蓝金唇膏正装 新版丝绒999/720/840 30色可选 3.5g
        // 3400-9FEF
        keyWord = keyWord.replaceAll("[^\\u3400-\\u9FEFa-zA-Z0-9 /]", "");
        keyWord = keyWord.replaceAll("/"," ");
        // 将多个空格替换成一个空格，移除首尾空格，然后转成小写
        keyWord = keyWord.replaceAll("\\s+", " ").trim().toLowerCase();
        return keyWord;

    }

//    public static void main(String[] args) {
//        System.out.println(new AlgoNormalizationUdf().eval("Dior 迪奥 全新烈艳蓝金唇膏正装 新版丝绒999/720/840 30色可选 3.5g"));
//    }

}
