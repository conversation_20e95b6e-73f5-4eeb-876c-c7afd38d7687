package com.alibaba.blink.udx.rockmq.utils;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IntegerUtils {

    public static Logger LOG = LoggerFactory.getLogger(IntegerUtils.class);


    public static Integer valueOf(String s) throws NumberFormatException {
        try {
            if (StringUtils.isBlank(s)) {
                return 0;
            }
            return Integer.valueOf(s);
        } catch (Exception e) {
            LOG.error("strconv.Atoi --------------转换失败，数据格式：{}",s);
            LOG.error("strconv.Atoi --------------转换失败，数据格式：{}",s);
            LOG.error("strconv.Atoi --------------转换失败，数据格式：{}",s);
            return 0;
        }
    }

}
