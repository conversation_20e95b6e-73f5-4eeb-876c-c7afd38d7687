package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s STRING>")
)
public class SetConvertSingle extends TableFunction<Row> {

    private static final Logger logger = LoggerFactory.getLogger(SetConvertSingle.class);
    private static final int BASE_ROW_SIZE = 1;
    private static final Gson gson = new Gson();

    public void eval(String message) {
        Row row = new Row(BASE_ROW_SIZE);
        Row emptyRow = new Row(BASE_ROW_SIZE);
        try {
            if (Objects.nonNull(message)) {
                if (isValidObject(message)) {
                    row.setField(0, message);
                    collect(row);
                } else if (isValidArray(message)) {
                    List<String> list = JSONArray.parseArray(message, String.class);
                    if (Objects.isNull(list) || list.size() == 0) {
                        collect(emptyRow);
                    } else {
                        for (String s : list) {
                            row.setField(0, s);
                            collect(row);
                        }
                    }
                } else {
                    System.out.println("message is not object and array :" + message);
                    collect(emptyRow);
                }
            } else {
                collect(emptyRow);
            }
        } catch (JSONException e) {
            System.out.println("message is :  " + message + ", parse error:" + e.getCause());
            collect(emptyRow);
        }
    }

    private boolean isValidObject(String value) {
        if (value.trim().startsWith("[")) {
            return false;
        }
        try {
            gson.fromJson(value, JSONObject.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidArray(String value) {
        try {
            gson.fromJson(value, JSONArray.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {

//        String s = "[{\"contentType\":0,\"position\":4,\"contentID\":\"1170342\",\"propertyValueId\":\"26089126\",\"channel\":\"FAN\",\"requestID\":\"1329610612402212864\",\"cspuId\":\"3360649\"}]";
//        String s = "[{\"contentType\":0,\"position\":2,\"contentID\":\"1147180\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329610647090032640\",\"cspuId\":\"3327641\"}]";
//        String s = "[{\"contentType\":0,\"position\":4,\"contentID\":\"10903\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329242773749628928\",\"cspuId\":\"10903\"}]";
        String s = "{\"contentType\":0,\"position\":4,\"contentID\":\"10903\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329242773749628928\",\"cspuId\":\"10903\"}]";
        JSONArray.parseArray(s);
//        System.out.println(JSONObject.isValidObject(s));
//        List<String> strings = JSONArray.parseArray(s, String.class);
//        System.out.println(strings);

//        new SetConvertSingle().eval(s);


    }
}
