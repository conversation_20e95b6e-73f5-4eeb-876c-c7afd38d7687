package com.alibaba.blink.udx.udf;


import com.util.AssociateContentUrlParserUtil;
import com.util.ContentUrlParserUtil;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * 解决神策社区content_id和content_type从content_url里面解析的问题，还有类似的.
 * Author: Stephen<PERSON><PERSON>
 */
@FunctionHint(
		input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"),@DataTypeHint("STRING")},
		output = @DataTypeHint("STRING")
)
public class ShenceLogParse extends ScalarFunction {


	/**
	 * @param type   本次转化的类型
	 * @param need   需要被解析的字段（type：从路径   id：从参数）
	 * @param source 从哪里解析（是一个url）
	 * @return 返回need被解析之后的值
	 */
	public String eval(String type, String need, String source) throws Exception {

		try {
			switch (type) {
				case "contentUrl":
					return ContentUrlParserUtil.parse(need, source);
				case "associateContentUrl":
					return AssociateContentUrlParserUtil.parse(need, source);
				default:
					return "";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "";
		}
	}

}
