package com.alibaba.blink.udx.udf;

import com.alibaba.blink.udx.utils.AcmUtils;
import com.alibaba.fastjson.JSONObject;
import com.util.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Map;

/**
 * 算法针对搜索词归一化
 */
public class AlgoAcmDecoderUdf extends ScalarFunction {

    public String eval(String acm) {

        if (StringUtils.isBlank(acm)) {
            return acm;
        }
        Map<String, String> result = null;
        try {
            result = AcmUtils.decodeAcm(acm);
        } catch (Exception e) {
            System.out.println("acm : " + acm + " is error");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.fluentPutAll(result);

        return jsonObject.toJSONString();
    }

    public static void main(String[] args) {
        AlgoAcmDecoderUdf algoAcmDecoderUdf = new AlgoAcmDecoderUdf();
        String eval = algoAcmDecoderUdf.eval("1.rcmd.live_17715.ct_live_tab.3157-3158-3159-3160-3161-3163.cn_TAB_LIVE-rid_15ba72f1c1324589-pos_26-cid_-bn_基准桶");
        System.out.println(eval);
    }

}
