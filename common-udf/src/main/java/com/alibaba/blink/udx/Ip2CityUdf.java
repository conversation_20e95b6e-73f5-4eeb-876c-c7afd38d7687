package com.alibaba.blink.udx;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.lionsoul.ip2region.DataBlock;
import org.lionsoul.ip2region.DbConfig;
import org.lionsoul.ip2region.DbSearcher;
import org.lionsoul.ip2region.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

public class Ip2CityUdf extends ScalarFunction {
    final Logger Log = LoggerFactory.getLogger(getClass());
    DbConfig config = null;
    DbSearcher searcher = null;

    @Override
    public void open(FunctionContext context) throws Exception {
        try {
            // 因为jar无法读取文件,复制创建临时文件
            // /dump/1/nm-local-dir/usercache/admin/appcache/application_1623921146842_2185/container_e03_1623921146842_2185_01_000003/temp/ip2region.db
            String tmpDir = System.getProperty("user.dir") + File.separator + "lib" + File.separator + "temp";
            String dbPath = tmpDir + File.separator + "ip2region.db";
            System.out.println(dbPath);
            Log.info("init ip region db path [{}]", dbPath);
            File file = new File(dbPath);

            file.getParentFile().mkdirs();
            file.createNewFile();
//
//          //从资源文件中拿db文件
            InputStream dbF = Ip2CityUdf.class.getClassLoader().getResourceAsStream("ip2region.db");
//
//          //操作复制
            Files.copy(dbF, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
            config = new DbConfig();
            searcher = new DbSearcher(config, dbPath);
            Log.info("bean [{}]", config);
            Log.info("bean [{}]", searcher);
        } catch (Exception e) {
            Log.error("init ip region error:{}", e);
        }
        super.open(context);
    }

    public String eval(String str) {
        String region = "未识别";

        // check is ip
        if (!Util.isIpAddress(str)) {
            return region;
        }

        try {
            //db
            if (searcher == null) {
                Log.error("DbSearcher is null");
                return null;
            }
//            long startTime = System.currentTimeMillis();
            //查询算法
            int algorithm = DbSearcher.MEMORY_ALGORITYM;
            //B-tree
            //DbSearcher.BINARY_ALGORITHM //Binary
            //DbSearcher.MEMORY_ALGORITYM //Memory
            //define the method
            Method method = null;
            switch (algorithm) {
                case DbSearcher.BTREE_ALGORITHM:
                    method = searcher.getClass().getMethod("btreeSearch", String.class);
                    break;
                case DbSearcher.BINARY_ALGORITHM:
                    method = searcher.getClass().getMethod("binarySearch", String.class);
                    break;
                case DbSearcher.MEMORY_ALGORITYM:
                    method = searcher.getClass().getMethod("memorySearch", String.class);
                    break;
                default:
            }

            DataBlock dataBlock = null;
            if (!Util.isIpAddress(str)) {
                Log.warn("warning: Invalid ip address");
            }
            dataBlock = (DataBlock) method.invoke(searcher, str);
            String result = dataBlock.getRegion();

            // 国家|区域|省份|城市|ISP
            // 中国|华东|江苏省|苏州市|联通
            return result.split("\\|")[3];
        } catch (Exception e) {
            Log.error("error:{}", e);
        }

        return region;
    }
}
