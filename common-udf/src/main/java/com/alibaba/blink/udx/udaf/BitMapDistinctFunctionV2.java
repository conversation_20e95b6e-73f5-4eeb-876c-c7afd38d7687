package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.typeSerializer.Roaring64BitmapTypeSerializer;
import com.google.common.hash.Hashing;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.roaringbitmap.longlong.Roaring64Bitmap;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/24 10:52
 */
public class BitMapDistinctFunctionV2 extends AggregateFunction<Long, Roaring64Bitmap> {

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {

        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> {
                    DataType type = DataTypes.RAW(
                            Roaring64Bitmap.class,
                            Roaring64BitmapTypeSerializer.INSTANCE
                    );
                    return Optional.of(type);
                })
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.BIGINT()))
                .build()
                ;
    }

    public void accumulate(Roaring64Bitmap accumulator, Long value) {
        if (null != value) {
            accumulator.addLong(value);
        }
    }

    public void accumulate(Roaring64Bitmap accumulator, String value) {
        if (null != value) {
            accumulate(accumulator, Hashing.farmHashFingerprint64().hashString(value, StandardCharsets.UTF_8).asLong());
        }
    }

    public void retract(Roaring64Bitmap accumulator, String value) {

    }

    public void merge(Roaring64Bitmap accumulator, Iterable<Roaring64Bitmap> its) {
        for (Roaring64Bitmap acc : its) {
            accumulator.or(acc);
        }
    }

    @Override
    public Long getValue(Roaring64Bitmap accumulator) {
        return accumulator.getLongCardinality();
    }

    @Override
    public Roaring64Bitmap createAccumulator() {
        return new Roaring64Bitmap();
    }

}
