package com.alibaba.blink.udx.udtf.dynamic;

import com.alibaba.blink.udx.udtf.dynamic.bean.AppLogSubscribes;
import com.alibaba.blink.udx.udtf.dynamic.express.FactoryExpression;
import com.alibaba.blink.udx.udtf.dynamic.express.UDExpression;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.poizon.fusion.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheLoader;
import org.apache.flink.shaded.guava30.com.google.common.cache.LoadingCache;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<ip STRING,topic STRING,messageData STRING >")
)
public class AviatorRuleCommonSerivce extends TableFunction<Row> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AviatorRuleCommonSerivce.class);

    private final Map<String, UDExpression> compiledExpression = new HashMap();

    private FactoryExpression factoryExpression;

    private Counter noMatchCount;

    private Counter errorMatchCount;

    private String jobId;

    private String jobName;

    private String url;

    private String username;

    private String password;

    private static final Integer INITIAL_CAPACITY = 200;
    private static final Integer CONCURRENCY_LEVEL = 200;
    private static final Long MAXIMUM_SIZE = 200L;
    private static final Long EXPIRE_AFTER_ACCESS_TIME = 5L;
    private static final Long EXPIRE_AFTER_WRITE_TIME = 5L;

    private LoadingCache<String, Optional<List<AppLogSubscribes>>> COMMON_RULE_CACHE;

    @Override
    public void open(FunctionContext context) throws Exception {
        factoryExpression = FactoryExpression.createFactoryExpression("aviator");
        MetricGroup metricGroup = context.getMetricGroup();
        noMatchCount = metricGroup.counter("no-match-count");
        errorMatchCount = metricGroup.counter("error-match-count");

//      jobName = context.getJobParameter("job_name", "");
        jobId = context.getJobParameter("dynamic.rules.job.id", "");
        url = context.getJobParameter("execute.job.jdbc.url", "");
        username = context.getJobParameter("execute.job.jdbc.username", "");
        password = context.getJobParameter("execute.job.jdbc.password", "");
//        LOGGER.warn("作业参数jobName:{}", jobName);
//        LOGGER.warn("作业参数jobName:{}", jobName.replaceFirst("rt_", ""));
//        jobName.replaceFirst("rt_", "");
        LOGGER.warn("jobId:{}", jobId);
        LOGGER.warn("作业参数url:{}", url);
        LOGGER.warn("作业参数username:{}", username);
        LOGGER.warn("作业参数password:{}", password);
        if (StringUtils.isBlank(jobId)) {
            throw new Exception("job_name,jobId没有获取到，请确认该作业的全局作业参数中是否设置了job_name,jobId参数，job_name值应等于该flink作业的任务名");
        }
        if (StringUtils.isBlank(url) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
            throw new Exception("数据库连接参数没有获取到，请确认该作业的全局作业参数中是否设置了参数");
        }


        COMMON_RULE_CACHE = CacheBuilder.newBuilder()
                .initialCapacity(INITIAL_CAPACITY)
                .concurrencyLevel(CONCURRENCY_LEVEL)
                .maximumSize(MAXIMUM_SIZE)
                .expireAfterAccess(EXPIRE_AFTER_ACCESS_TIME, TimeUnit.MINUTES)
                .refreshAfterWrite(EXPIRE_AFTER_WRITE_TIME, TimeUnit.MINUTES)
                .recordStats()
                .build(new CacheLoader<String, Optional<List<AppLogSubscribes>>>() {
                    @Override
                    public Optional<List<AppLogSubscribes>> load(String job_name) throws Exception {
                        Properties druidP = new Properties();
                        druidP.put("driverClassName", "com.mysql.cj.jdbc.Driver");
                        //生产
                        druidP.put("url", url);
                        druidP.put("username", username);
                        druidP.put("password", password);

                        druidP.put("initialSize", "1");
                        druidP.put("maxActive", 1 + "");
                        druidP.put("minIdle", "0");
                        druidP.put("maxWait", 10000 + "");
                        DruidDataSource dataSource = (DruidDataSource) DruidDataSourceFactory
                                .createDataSource(druidP);
                        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
                        String sql = "select dsm.table_name,ds.ip,tjre.rule_express from te_job_rule_express tjre  " +
                                "join re_data_source_metadata dsm on tjre.re_data_source_metadata_id=dsm.id " +
                                "join re_data_source ds on dsm.re_data_source_id=ds.id " +
                                "join te_job_info tj on tj.id=tjre.te_job_info_id " +
                                "where tj.id=?";
                        System.out.println("查询数据库");
                        System.out.println("查询数据库");
                        System.out.println("查询数据库");
                        System.out.println("查询数据库");
                        List<AppLogSubscribes> appLogSubscribes = jdbcTemplate.query(sql, new RowMapper<AppLogSubscribes>() {
                            @Override
                            public AppLogSubscribes mapRow(ResultSet rs, int rowNum) throws SQLException {
                                String avRules = rs.getString("rule_express");
                                String clusterIp = rs.getString("ip");
                                String topicName = rs.getString("table_name");
                                AppLogSubscribes appLogSubscribest = new AppLogSubscribes();
                                appLogSubscribest.setAvRules(avRules);
                                appLogSubscribest.setClusterIp(clusterIp);
                                appLogSubscribest.setTopicName(topicName);
                                return appLogSubscribest;
                            }
//                        }, jobName.replaceFirst("rt_", ""));
                        },jobId);
                        dataSource.close();
                        if (appLogSubscribes == null || appLogSubscribes.size() == 0) {
                            throw new Exception("没有查询到规则");
//                            return Optional.empty();
                        }
                        LOGGER.warn("查询规则条数:{}", appLogSubscribes.size());
                        LOGGER.warn("查询规则条数:{}", appLogSubscribes.size());
                        return Optional.of(appLogSubscribes);
                    }
                });

    }

    public void eval(String message) throws Exception {
        Optional<List<AppLogSubscribes>> appLogSubscribes = COMMON_RULE_CACHE.get(jobName);
        HashMap value = JsonUtils.deserialize(message, HashMap.class);

        int count = 0;
        appLogSubscribes.ifPresent(appLogSubscribes1 -> appLogSubscribes1.stream()
                .filter(AppLogSubscribe -> {
                    boolean isSubscribe = false;
                    try {
                        final String avRules = AppLogSubscribe.getAvRules();
                        final UDExpression udExpression = compiledExpression.computeIfAbsent(avRules, new Function<String, UDExpression>() {
                            @Override
                            public UDExpression apply(String key) {
                                return factoryExpression.compile(key);
                            }
                        });

                        final Boolean result = (Boolean) udExpression.execute(value, true);
                        isSubscribe = (result != null && result);
                    } catch (Exception e) {
                        errorMatchCount.inc();
                        LOGGER.error(String.format("[kafka subscribe] subscribe avatar execute error! mess: [%s]", message), e.getMessage());
                    }
                    return isSubscribe;//通过返回true，false来决定改元素是否放到后面便利集合中
                })
                //todo 改为hash结构。获取后则直接下发。避免遍历
                .forEach(AppLogSubscribe -> {
                    Row row = new Row(3);
                    row.setField(0, AppLogSubscribe.getClusterIp());
                    row.setField(1, AppLogSubscribe.getTopicName());
                    row.setField(2, message);
                    collect(row);
                }));
    }


    public static void main(String[] args) throws Exception {
        AviatorRuleCommonSerivce aviatorRuleSerivce = new AviatorRuleCommonSerivce();
        aviatorRuleSerivce.open(null);

        for (int i = 0; i < 1000; i++) {
            aviatorRuleSerivce.eval("{\"scene_type\":\"CARGO_DAMAGE_REFUND\",\"num\":10,\"message\":\"2023-01-16 08:41:35.334|3003812|INFO|[dpp-ae-cluster-m,0aec27a763c49d3f506fb30823b70db9,7a3ad73c27dc3f36,1508666552,11871_13608_13736_13744_13745_13986_,doDealRecommend]|ecTstreamPool thread-199|c.s.d.d.u.AnaLogUtils//0aec27a763c49d3f506fb30823b70db9/7a3ad73c27dc3f36/01//: traceId[07a5572bec350329]-userId[1508666552]-AnaLog:processName REPEAT_RANKI2I check:false\",\"filename\":\"/var/lib/kubelet/pods/7ffd8c79-8152-49b5-b0f6-36e1d2305dbd/volumes/kubernetes.io~empty-dir/tmp-dir/dpp-ae-cluster-m/csprd/*************/volumes/kubernetes.io~csi/d-bp1elz0v3m2ejub7nu1p/mount/dpp-ae-cluster-m-info.log\",\"log\":{\"file\":{\"path\":\"/var/lib/kubelet/pods/7ffd8c79-8152-49b5-b0f6-36e1d2305dbd/volumes/kubernetes.io~empty-dir/tmp-dir/dpp-ae-cluster-m/csprd/*************/volumes/kubernetes.io~csi/d-bp1elz0v3m2ejub7nu1p/mount/dpp-ae-cluster-m-info.log\"},\"offset\":\"201460733\"},\"fields\":{\"sourceKafkaPartition\":\"jsonNode.get(KafkaConstants.SOURCE_KAFKA_PARTITION).asText()\",\"sourceKafkaOffset\":\"jsonNode.get(KafkaConstants.SOURCE_KAFKA_OFFSET).asText()\"},\"environment\":\"csprd\",\"application\":\"dpp-ae-cluster-m\",\"ip\":\"*************\",\"extendFields\":{},\"topic\":\"jsonNode.get(KafkaConstants.SOURCE_KAFKA_TOPIC).asText()\",\"meta\":\"{\\\"@timestamp\\\":\\\"2023-01-16T00:41:35.341Z\\\",\\\"message\\\":\\\"2023-01-16 08:41:35.334|3003812|INFO|[dpp-ae-cluster-m,0aec27a763c49d3f506fb30823b70db9,7a3ad73c27dc3f36,1508666552,11871_13608_13736_13744_13745_13986_,doDealRecommend]|ecTstreamPool thread-199|c.s.d.d.u.AnaLogUtils//0aec27a763c49d3f506fb30823b70db9/7a3ad73c27dc3f36/01//: traceId[07a5572bec350329]-userId[1508666552]-AnaLog:processName REPEAT_RANKI2I check:false\\\",\\\"kafka-topic\\\":\\\"DATA_PLATEFORM_LOG_COLLECTOR\\\",\\\"application\\\":\\\"dpp-ae-cluster-m\\\",\\\"environment\\\":\\\"csprd\\\",\\\"log\\\":{\\\"offset\\\":201460733,\\\"file\\\":{\\\"path\\\":\\\"/var/lib/kubelet/pods/7ffd8c79-8152-49b5-b0f6-36e1d2305dbd/volumes/kubernetes.io~empty-dir/tmp-dir/dpp-ae-cluster-m/csprd/*************/volumes/kubernetes.io~csi/d-bp1elz0v3m2ejub7nu1p/mount/dpp-ae-cluster-m-info.log\\\"}},\\\"host\\\":{\\\"ip\\\":[\\\"*************\\\"],\\\"hostname\\\":\\\"k8s-prd-standard-hz-i63039prd\\\"},\\\"fields\\\":{\\\"docker\\\":true,\\\"log_topics\\\":\\\"DATA_PLATEFORM_LOG_COLLECTOR\\\"}}\"}");
            Thread.sleep(5000);
        }
    }
}
