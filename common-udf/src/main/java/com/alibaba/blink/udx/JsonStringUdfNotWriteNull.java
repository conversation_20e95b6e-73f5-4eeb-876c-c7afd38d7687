package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * @description: /
 * @Author: lmh
 * @date: 2022/11/4 10:29 上午
 */
public class JsonStringUdfNotWriteNull extends ScalarFunction {
    // 可选， open方法可以不写,若写的话需要import org.apache.flink.table.functions.FunctionContext;
    @Override
    public void open(FunctionContext context) {
    }

    public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object... args) {
        if (args.length <= 1) {
            return "{}";
        }
        if (!(args.length % 2 == 0)) {
            return "{}";
        }
        JSONObject jsonObject = JSON.parseObject("{}");
        for (int i = 0; i < args.length/2; i++) {
            jsonObject.put(args[2*i].toString(),args[2*i+1]);
        }
        return JSON.toJSONString(jsonObject);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

    public static void main(String[] args) {
        String eval = new JsonStringUdfNotWriteNull().eval("f", null, "a", null, "f", null, "g", "");
        System.out.println(eval);


    }
}
