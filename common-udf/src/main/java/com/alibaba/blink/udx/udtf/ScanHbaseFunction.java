package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.ResultScanner;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.QualifierFilter;
import org.apache.hadoop.hbase.util.Bytes;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static java.util.concurrent.ThreadLocalRandom.current;

/**
 * @description: /
 * @Author: lmh
 * @date: 2023/5/29 13:50
 */
@Slf4j
public class ScanHbaseFunction extends TableFunction<Row> {

    private  final List<String> needColumns = new ArrayList<>();
    private  final AtomicBoolean firstInit = new AtomicBoolean(false);
    private  final AtomicBoolean needInit = new AtomicBoolean(true);
    private  Connection connection;
    private  Table table;
    private  byte[] family;
    private  FilterList filterList;

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            if (argumentDataTypes.size() - 5 + 1 >= 1) {
                DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 5 + 1];
                for (int i = 0; i < (argumentDataTypes.size() - 5 + 1); i++) {
                    outputDataTypes[i] = DataTypes.STRING();
                }
                return Optional.of(DataTypes.ROW(outputDataTypes));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();
    }

    public void eval(String tableName, String family, String hbaseConfig, String rowStart, String rowEnd, String... keys) throws Exception {

        initConfig(tableName, family, hbaseConfig, keys);

        scanHbase(rowStart, rowEnd);
    }

    public void initConfig(String tableName, String familyName, String hbaseConfig, String... keys) {

        if (null == keys || keys.length == 0) {
            return;
        }
        while (needInit.get()) {
            try {
                if (firstInit.compareAndSet(false, true)) {

                    needColumns.clear();
                    needColumns.addAll(Arrays.asList(keys));
                    //初始化一个hbase client
                    Configuration configuration = HBaseConfiguration.create();

                    JSONObject cfg = JSONObject.parseObject(hbaseConfig);

                    cfg.forEach((x, y) -> configuration.set(x, String.valueOf(y)));

                    connection = ConnectionFactory.createConnection(configuration);

                    family = strToB(familyName);

                    table = connection.getTable(TableName.valueOf(tableName));

                    log.info("innit hbase clint:\n tableName:{} \n familyName:{} \n config:{} joinNeedColumns:{} \nconnection-config{}", tableName, Bytes.toString(family), cfg, needColumns, connection.getConfiguration());

                    filterList = new FilterList(FilterList.Operator.MUST_PASS_ONE);

                    for (String columns : needColumns) {
                        filterList.addFilter(buildQualifierFilter(columns));
                    }

                    needInit.compareAndSet(true, false);
                } else {
                    TimeUnit.MILLISECONDS.sleep(current().nextInt(10));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }
    }

    public void scanHbase(String rowKeyStart, String rowKeyEnd) throws Exception {
        ResultScanner scanner = null;
        long startTime = System.currentTimeMillis();
        long num = 0;
        try {
            Scan scan = new Scan()
                    .withStartRow(Bytes.toBytes(rowKeyStart))
                    .withStopRow(Bytes.toBytes(rowKeyEnd));

            scan.setFilter(filterList);
            scanner = table.getScanner(scan);
            for (Result result : scanner) {
                Row row = new Row(needColumns.size() + 1);

                for (int i = 0; i < needColumns.size(); i++) {
                    row.setField(i, Bytes.toString(result.getValue(family, strToB(needColumns.get(i)))));
                }

                row.setField(needColumns.size(), Bytes.toString(result.getRow()));

                num++;
                collect(row);

            }
        } catch (Exception exception) {
            log.error("scanner error.IOException!\n rowKeyStart:{}--rowKeyEnd:{}\n interval-time:{}\n scanNum:{} \n Caused:{}", rowKeyStart, rowKeyEnd, System.currentTimeMillis() - startTime, num, exception);
            throw new RuntimeException(exception);
        } finally {
            if (scanner != null) {
                scanner.close();
            }
        }
    }

    public QualifierFilter buildQualifierFilter(String columns) {
        return new QualifierFilter(CompareOperator.EQUAL, new BinaryComparator(Bytes.toBytes(columns)));
    }

    public byte[] strToB(String str) {
        return Bytes.toBytes(str);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (table != null) {
            table.close();
        }
        if (connection != null) {
            connection.close();
        }
    }

}
