package com.alibaba.blink.udx.udtf;

import com.alibaba.fastjson.JSON;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Created on 2022/4/13.
 *
 * <AUTHOR>
 */

@FunctionHint(output = @DataTypeHint("ROW<a STRING, b BIGINT>"))
public class ParseFixedTimeAggJson extends TableFunction<Row> {
    private static final Logger logger = LoggerFactory.getLogger(ParseFixedTimeAggJson.class);

    public void eval(String message) {
        try {
            Map<String, Object> values = JSON.parseObject(message, Map.class);
            //json对象转Map
            // logger.warn("parse json: " + message);
            for (String key : values.keySet()) {
                long value = Long.parseLong(values.get(key).toString());
                Row row = new Row(2);
                row.setField(0, key);
                row.setField(1, value);
                //输出一行
                collect(row);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
