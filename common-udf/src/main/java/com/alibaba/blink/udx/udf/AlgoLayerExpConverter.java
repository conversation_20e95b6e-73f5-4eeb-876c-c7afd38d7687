package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AlgoLayerExpConverter extends ScalarFunction {
    public String eval(String items) {
        List<String> itemList = JSONArray.parseArray(items, String.class);
        Map<String, String> map = new HashMap<>();
        for (String item : itemList) {
            JSONObject jsonObject = JSON.parseObject(item);
            if (jsonObject.get("raid") != null) {
                map.put("精排层", jsonObject.get("raid").toString());
            } else if (jsonObject.get("mid") != null) {
                map.put("merge 层", jsonObject.get("mid").toString());
            } else if (jsonObject.get("prid") != null) {
                map.put("粗排层", jsonObject.get("prid").toString());
            } else if (jsonObject.get("reid") != null) {
                map.put("召回层", jsonObject.get("reid").toString());
            } else if (jsonObject.get("poid") != null) {
                map.put("postRank 层", jsonObject.get("poid").toString());
            } else if (jsonObject.get("iid") != null) {
                map.put("init 层", jsonObject.get("iid").toString());
            }
        }
        String ret = "";
        for(String k:map.keySet()){
            ret += (k+":"+map.get(k)+"|");
        }
        return ret.length() == 0 ? ret : ret.substring(0,ret.length() - 1);
    }
}
