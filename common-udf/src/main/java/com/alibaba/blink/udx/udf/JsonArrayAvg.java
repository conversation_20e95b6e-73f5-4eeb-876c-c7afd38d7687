package com.alibaba.blink.udx.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

@Slf4j
public class JsonArrayAvg extends ScalarFunction {
    public Double eval(String json, String field) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        try {
            JSONArray jsonArray = JSON.parseArray(json);

            double sum = 0.0;
            int count = jsonArray.size();

            // 遍历数组并计算总和
            for (int i = 0; i < count; i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                sum += jsonObject.getDouble(field);
            }

            return sum / count;
        } catch (Exception e) {
            log.error("JsonArrayAvg 执行失败！json:{},filed:{}", json, field, e);
        }
        return null;
    }


}
