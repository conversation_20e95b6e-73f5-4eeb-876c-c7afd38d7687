package com.alibaba.blink.udx.udtf;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

/**
 * 规则引擎监控使用
 */
public class FixTimeBucket extends TableFunction<Row> {
    private static final Logger logger = LoggerFactory.getLogger(FixTimeBucket.class);

    private final List<String> timeList = Collections.synchronizedList(new ArrayList<>());

    @Override
    public void open(FunctionContext context) {
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.ROW(DataTypes.STRING())))
                .build();
    }

    public void eval(String timeString, Integer timeStep) throws ParseException {

        if (timeList.isEmpty()) {
            initTimeList(timeStep);
        }

        String[] times = timeString.split(" ");
        int index = timeList.indexOf(times[1]);

        if (index == -1) {
            throw new RuntimeException("date format error, time :" + timeString + "need is:" + timeList);
        }

        for (int i = index; i < timeList.size(); i++) {
            Row row = new Row(1);
            row.setField(0, times[0] + " " + timeList.get(i));
            collect(row);
        }

    }

    public void initTimeList(Integer timeStep) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        calendar.setTime(date);
        int size = (int) (86400 / timeStep);
        dateFormat.applyPattern("HH:mm:ss");
        while (timeList.size() < size) {
            timeList.add(dateFormat.format(calendar.getTime()));
            calendar.add(Calendar.SECOND, (int) timeStep);
        }
        logger.info("time-list:" + timeList);
    }
}
