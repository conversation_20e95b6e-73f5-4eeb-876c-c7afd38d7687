package com.alibaba.blink.udx.udaf;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.DeserializationFeature;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import static java.util.concurrent.ThreadLocalRandom.current;

/**
 * <p>合并ads dim_value一样的数据
 *
 * <p>1.每个指标根据ts 大小 取last_value
 * <p>2.默认透出ext dim_value metric_value 中的数据 todo:key 不能重复
 * <p>3.要求 ts 不为null
 *
 * <p>metric_name 不同的case:
 * <pre>{@code
 * case 1:
 *    {
 *     "dim_name": "account_id,date,device_type,media_id,operator,plan_id",
 *     "ext": {
 *         "dim_type": "planDim"
 *     },
 *     "metric_name": "test_01",
 *     "unique_key": "a7653d73f0f0690c385c1eebe1c88569",
 *     "track_id": "f6fbc649-1b0e-430f-9a70-b5d9f325f688",
 *     "metric_value": {
 *         "pay_ue_24h": null,
 *         "cost": 7173.0
 *     },
 *     "job": "test_01",
 *     "dim_value": {
 *         "date": "********",
 *         "account_id": "****************",
 *         "operator": "刘浩南"
 *     },
 *     "ts": *************
 *
 *   case 2:
 *      {
 *      "dim_name": "account_id,date,device_type,media_id,operator,plan_id",
 *      "ext": {
 *          "dim_type": "planDim"
 *      },
 *      "metric_name": "test_02",
 *      "unique_key": "a7653d73f0f0690c385c1eebe1c88569",
 *      "track_id": "f6fbc649-1b0e-430f-9a70-b5d9f325f688",
 *      "metric_value": {
 *          "ord_uv": 22
 *      },
 *      "job": "test_02",
 *      "dim_value": {
 *          "date": "********",
 *          "account_id": "****************",
 *          "operator": "刘浩南"
 *      },
 *      "ts": *************
 *
 *   MergeAdsTemplateJson(case 2)
 *   MergeAdsTemplateJson(case 1)
 *
 *   res:
 *      {
 *      "date": "********",
 *      "account_id": "****************",
 *      "operator": "刘浩南",
 *      "ord_uv": 22,
 *      "pay_ue_24h": null,
 *      "cost":9999.0,
 *      "dim_type":"planDim"
 *      }
 *
 * }
 * }</pre>
 *
 * @Author: lmh
 * @date: 2022/10/18 11:47 上午
 */
@Slf4j
public class MergeAdsTemplateJsonV2 extends AggregateFunction<String, MergeAdsTemplateJsonV2.JsonAcc> {

    public static Set<String> needKeys = new HashSet<>();
    public static boolean isFilter = false;
    private static final AtomicBoolean firstInit = new AtomicBoolean(false);
    private static final AtomicBoolean needInit = new AtomicBoolean(true);
    public static ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);


    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
    }

    @Override
    public JsonAcc createAccumulator() {
        return new JsonAcc();
    }

    @Override
    public String getValue(JsonAcc accumulator) {
        try {
            return accumulator.getCache();
        } catch (JsonProcessingException e) {
            log.error("get value err! acc:{}", accumulator);
            throw new RuntimeException(e);
        }
    }

    public void accumulate(MergeAdsTemplateJsonV2.JsonAcc accumulator, String value) {

        accumulate(accumulator, value, null);
    }

    public void accumulate(MergeAdsTemplateJsonV2.JsonAcc accumulator, String value, String... keys) {

        initNeedKeys(keys);

        accumulator.acc(parseObject(value));

    }

    public void initNeedKeys(String... keys) {

        if (null == keys || keys.length == 0) {
            return;
        }

        while (needInit.get()) {
            if (firstInit.compareAndSet(false, true)) {
                needKeys.clear();
                MergeAdsTemplateJsonV2.needKeys.addAll(Arrays.asList(keys));
                isFilter = true;
                log.info("accumulate innit. size:{}; keys:{};", needKeys.size(), needKeys);
                needInit.compareAndSet(true, false);
            } else {
                try {
                    TimeUnit.MILLISECONDS.sleep(current().nextInt(10));
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    public void merge(MergeAdsTemplateJsonV2.JsonAcc accumulator, Iterable<MergeAdsTemplateJsonV2.JsonAcc> iterables) {
        for (MergeAdsTemplateJsonV2.JsonAcc acc : iterables) {
            accumulator.merge(acc);
        }
    }

    private AdsTemplate parseObject(String jsonStr) {
        try {
            return mapper.readValue(jsonStr, AdsTemplate.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("json parse error, msg:" + jsonStr, e);
        }
    }

    public static class JsonAcc {

        public Map<String, Tuple2<String, Long>> viewMap;

        public JsonAcc() {
            this.viewMap = new HashMap<>();
        }

        public String getCache() throws JsonProcessingException {

            if (viewMap.isEmpty()) {
                return "{}";
            }

            Map<String, Object> rowMap = new HashMap<>(viewMap.size());
            viewMap.forEach((k, v) -> rowMap.put(k, v.f0));
            return mapper.writeValueAsString(rowMap);
        }

        /**
         * 默认将 metric_value ext dim_value 透出
         */
        public void acc(AdsTemplate template) {

            if (template == null) {
                return;
            }

            if (template.getMetric_value() != null) {
                for (Map.Entry<String, String> entry : template.getMetric_value().entrySet()) {
                    setValue(entry.getKey(), entry.getValue(), template.getTs());
                }
            }

            if (template.getExt() != null) {
                for (Map.Entry<String, String> entry : template.getExt().entrySet()) {
                    setValue(entry.getKey(), entry.getValue(), template.getTs());
                }
            }

            if (template.getDim_value() != null) {
                for (Map.Entry<String, String> entry : template.getDim_value().entrySet()) {
                    setValue(entry.getKey(), entry.getValue(), template.getTs());
                }
            }

        }

        private void setValue(String s, String o, Long ts) {

            if (filter(s)) {
                return;
            }

            if (viewMap.containsKey(s)) {

                Tuple2<String, Long> tuple2 = viewMap.get(s);

                if (tuple2.f1 <= ts) {
                    tuple2.f0 = o;
                    tuple2.f1 = ts;
                }

                viewMap.put(s, tuple2);

            } else {
                viewMap.put(s, Tuple2.of(o, ts));
            }

        }

        public boolean filter(String key) {

            if (isFilter) {
                return !needKeys.contains(key);
            }
            return false;
        }

        public void merge(JsonAcc otherAcc) {

            for (Map.Entry<String, Tuple2<String, Long>> entry : otherAcc.viewMap.entrySet()) {
                String key = entry.getKey();

                Tuple2<String, Long> otherMetric = entry.getValue();

                if (this.viewMap.containsKey(key)) {

                    Tuple2<String, Long> thisMetric = this.viewMap.get(key);
                    if (thisMetric.f1 <= otherMetric.f1)
                        this.viewMap.put(key, otherMetric);

                } else
                    this.viewMap.put(key, otherMetric);
            }

        }

    }

    @Data
    public static class AdsTemplate {
        private Map<String, String> dim_value;
        private Long ts;
        private Map<String, String> metric_value;
        private Map<String, String> ext;

    }

    public static void main(String[] args) {
        MergeAdsTemplateJsonV2 mergeAdsTemplateJson = new MergeAdsTemplateJsonV2();
        JsonAcc jsonAcc = new JsonAcc();

        String v1 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"be7e3e7c-3b38-4201-8c8b-259d3c108ab6\",\"metric_value\":{\"ord_uv\":null,\"pay_ue_24h\":null,\"cost\":1.0,\"remain_cnt_1d\":null,\"orderCAC\":null,\"productExposureRate\":null,\"pay_ue\":null,\"activationCAC\":null,\"click\":0.0,\"roi\":null,\"real_cost\":0.****************,\"ltv\":null,\"charge1\":null,\"gmv1\":null,\"view\":3.0,\"ad_site\":\"[]\",\"order_count1\":null,\"roi24\":null,\"first_day_spu_exposure_cnt\":null,\"activate\":null,\"retain2Rate\":null,\"order_count_24h\":null},\"job\":\"ads_dewu_increase_metrics_about_rule_engine\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        String v2 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"b06b8740-1dd4-48d0-bf8e-ac46671e12ec\",\"metric_value\":{\"agg5Day_activate\":0,\"agg7Day_activate\":0},\"job\":\"ads_dewu_interface_hard_ad_metric_about_agg7day\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        String v3 = "{\"dim_name\":\"account_id,date,device_type,media_id,operator,plan_id\",\"ext\":{\"dim_type\":\"planDim\"},\"metric_name\":\"increase_hard_ad_metrics\",\"unique_key\":\"63c19a1c756a4fa2f8828356241dd4ac\",\"track_id\":\"b050fffe-172f-4358-bad7-938a66d99f9b\",\"metric_value\":{\"agg5Day_activate\":1,\"agg7Day_activate\":1},\"job\":\"ads_dewu_interface_hard_ad_metric_about_agg7day\",\"dim_value\":{\"date\":\"********\",\"account_id\":\"********\",\"media_id\":\"3\",\"device_type\":\"recall_device\",\"plan_id\":\"**********\",\"operator\":\"李震\"},\"ts\":*************}";
        JsonAcc jsonAcc2 = new JsonAcc();
        mergeAdsTemplateJson.accumulate(jsonAcc, v1, "agg5Day_activate");
        mergeAdsTemplateJson.accumulate(jsonAcc, v3, "agg5Day_activate");
        mergeAdsTemplateJson.accumulate(jsonAcc2, v2, "agg5Day_activate");

        ArrayList<JsonAcc> jsonAccs = new ArrayList<>();
        jsonAccs.add(jsonAcc);
        jsonAccs.add(jsonAcc2);
        JsonAcc jsonAcc1 = new JsonAcc();

        mergeAdsTemplateJson.merge(jsonAcc1, jsonAccs);


        System.out.println(mergeAdsTemplateJson.getValue(jsonAcc1));
    }
}
