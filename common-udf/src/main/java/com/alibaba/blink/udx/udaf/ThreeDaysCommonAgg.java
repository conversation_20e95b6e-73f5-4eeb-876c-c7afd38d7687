package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.ThreeDaysAccumulator;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.table.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;


/**
 * Created on 2022/4/13.
 * 计算近3天的通用计算结果
 * <AUTHOR>
 * @return jsonstr
 */
public class ThreeDaysCommonAgg extends AggregateFunction<String, ThreeDaysAccumulator> {
    private static final Logger logger = LoggerFactory.getLogger(ThreeDaysCommonAgg.class);

    public void accumulate(ThreeDaysAccumulator accumulator, String timeLabel, Long value) {

        if (null == value) {
            value = 0L;
        }
        accumulator.add(timeLabel,value);
    }

    public void retract(ThreeDaysAccumulator accumulator, String timeLabel, Long value){

    }

    @Override
    public String getValue(ThreeDaysAccumulator invQtyAccumulator) {
        return invQtyAccumulator.getValue();
    }

    @Override
    public ThreeDaysAccumulator createAccumulator() {
        return new ThreeDaysAccumulator();
    }

    public static void main(String[] args) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

        Date beforeYestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 86400 * 2 ),  "yyyyMMdd"));
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
        calendar.setTime(beforeYestDate);
        String beforeYestday = dateFormat.format(calendar.getTime());

        Date yestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24), "yyyyMMdd"));
        calendar.setTime(yestDate);
        String yestday = dateFormat.format(calendar.getTime());

        Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyyMMdd"));
        calendar.setTime(date);
        String today = dateFormat.format(calendar.getTime());

        System.out.println(beforeYestday);
        System.out.println(yestday);
        System.out.println(today);

    }
}