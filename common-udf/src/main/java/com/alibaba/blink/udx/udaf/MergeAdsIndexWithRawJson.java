package com.alibaba.blink.udx.udaf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.dataview.MapView;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;


// function that takes (value INT), stores intermediate results in a structured
// type of Top2Accumulator, and returns the result as a structured type of Tuple2<Integer, Integer>
// for value and rank
public class MergeAdsIndexWithRawJson extends TableAggregateFunction<Row, MergeAdsIndexWithRawJson.JsonAcc> {

	public static List<String> needKeys = new ArrayList<>();
	//	public static List<String> dims = new ArrayList<>();
//	public static Boolean isFilter = false;


	@Override
	public void open(FunctionContext context) throws Exception {
		super.open(context);
//		isFilter = Boolean.valueOf(context.getJobParameter("MergeAdsTemplateJson.isFilter", "false"));
//		needKeys = new ArrayList<>(Arrays.asList(context.getJobParameter("MergeAdsTemplateJson.needKeys", "").split(",")));
//		dims = new ArrayList<>(Arrays.asList(context.getJobParameter("MergeAdsTemplateJson.dims", "").split(",")));
	}

	@Override
	public TypeInference getTypeInference(DataTypeFactory typeFactory) {
		int rstRowSize = needKeys.size();
		DataType[] rstDataTypes = new DataType[rstRowSize];
		for (int i = 0; i < rstRowSize; i++) {
			rstDataTypes[i] = DataTypes.STRING();
		}
		return TypeInference.newBuilder()
			.outputTypeStrategy(callContext -> Optional.of(DataTypes.ROW(rstDataTypes)))
			.build();
	}

	@Override
	public MergeAdsIndexWithRawJson.JsonAcc createAccumulator() {
		return new MergeAdsIndexWithRawJson.JsonAcc();
	}

	public void accumulate(MergeAdsIndexWithRawJson.JsonAcc acc, Object value) {
		JSONObject source = object2Json(value);
		acc.acc(source);
	}

	private JSONObject object2Json(Object o) {
		//嵌套Json 避免转译
		if (o instanceof String && JSONValidator.from((String) o).validate()) {
			return JSON.parseObject((String) o);
		} else if (o instanceof JSONObject) {
			return (JSONObject) o;
		} else
			throw new RuntimeException("arg is not json. arg: " + o);
	}

	public void retract(MergeAdsIndexWithRawJson.JsonAcc accumulator, Object value) {
	}

	public void merge(MergeAdsIndexWithRawJson.JsonAcc accumulator, Iterable<MergeAdsIndexWithRawJson.JsonAcc> iterables) {
		for (MergeAdsIndexWithRawJson.JsonAcc acc : iterables) {
			accumulator.merge(acc);
		}
	}

	public void emitValue(MergeAdsIndexWithRawJson.JsonAcc acc, Collector<Row> out) {
		out.collect(acc.getCache());
	}

	public static class JsonAcc {
		public MapView<String, Tuple2<String, Long>> viewMap;

		private static final String JOB = "job";
		private static final String METRIC_NAME = "metric_name";
		private static final String METRIC_VALUE = "metric_value";
		private static final String DIM_VALUE = "dim_value";
		private static final String DIM_NAME = "dim_name";
		private static final String EXT = "ext";
		private static final String TS = "ts";

		public JsonAcc() {
			this.viewMap = new MapView<>();
		}

		public Row getCache() {
			Row row = new Row(needKeys.size());
			for (int i = 0; i < needKeys.size(); i++) {
				try {
					row.setField(i, viewMap.get(needKeys.get(i)));
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			return row;
		}

		/**
		 * 默认将 metric_value ext dim_value 透出
		 */
		public void setCache(JSONObject cache) {

			JSONObject metrics = cache.getJSONObject(METRIC_VALUE);
			JSONObject ext = cache.getJSONObject(EXT);
//			JSONObject dim = cache.getJSONObject(DIM_VALUE);

			Long ts = cache.getLong(TS);

			metrics.forEach((s, o) -> setValue(s, o, ts));

			ext.forEach((s, o) -> setValue(s, o, ts));

//			dim.forEach((s, o) -> setValue(s, o, ts));

		}

		private void setValue(String s, Object o, Long ts) {

			if (!isNeeded(s)) {
				return;
			}

			try {
				if (viewMap.contains(s)) {
					Tuple2<String, Long> tuple2 = viewMap.get(s);
					if (tuple2.f1 < ts || tuple2.f1.equals(ts)) {
						tuple2.f0 = String.valueOf(o);
						tuple2.f1 = ts;
						viewMap.put(s, tuple2);
					}
				} else {
					viewMap.put(s, Tuple2.of(String.valueOf(o), ts));
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}

		public boolean isNeeded(String key) {
			return needKeys.contains(key);
		}

		public boolean isJsonObjectBlankOrNull(JSONObject value) {
			return Objects.isNull(value) || value.isEmpty();
		}

		public void acc(JSONObject value) {
			if (!isJsonObjectBlankOrNull(value)) {
				setCache(value);
			}
		}

		public void merge(MergeAdsIndexWithRawJson.JsonAcc otherAcc) {
			try {
				String key;
				Tuple2<String, Long> otherMetric;
				Tuple2<String, Long> thisMetric;
				for (Map.Entry<String, Tuple2<String, Long>> entry : otherAcc.viewMap.entries()) {
					key = entry.getKey();
					otherMetric = entry.getValue();
					if (this.viewMap.contains(key)) {
						thisMetric = this.viewMap.get(key);
						if (thisMetric.f1 < otherMetric.f1 || thisMetric.f1.equals(otherMetric.f1))
							this.viewMap.put(key, otherMetric);
					} else
						this.viewMap.put(key, otherMetric);
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}

		}

	}
}





