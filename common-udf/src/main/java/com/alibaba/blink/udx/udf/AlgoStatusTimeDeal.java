package com.alibaba.blink.udx.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;
import java.util.List;

/**
 * 处理 状态:时间 这种数据，判断那种状态是最后的状态
 * 1:1619407754860
 * -1:1619407754960
 * Author: <PERSON><PERSON><PERSON>
 */
public class AlgoStatusTimeDeal extends ScalarFunction {

	public String eval(String... statusTime) {

		try {
			if (statusTime.length == 0) {
				return null;
			} else {
				return getLastStatus(Arrays.asList(statusTime));
			}
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}


	public static String getLastStatus(List<String> list) {

		Long tmp = 0L;
		String lastStatus = null;

		for (String s : list) {
			if (StringUtils.isEmpty(s)) {
				continue;
			}
			String[] split = s.split(":");
			Long time = Long.parseLong(split[1]);
			if (time > tmp) {
				tmp = time;
				lastStatus = split[0];
			}
		}

		return lastStatus;
	}


	public static void main(String[] args) {


		String s1 = "1:1619407759860";
		String s2 = "-1:1619407754960";


		AlgoStatusTimeDeal algoStatusTimeDeal = new AlgoStatusTimeDeal();

		String eval = algoStatusTimeDeal.eval(s1, s2);

		System.out.println(eval);

	}

}
