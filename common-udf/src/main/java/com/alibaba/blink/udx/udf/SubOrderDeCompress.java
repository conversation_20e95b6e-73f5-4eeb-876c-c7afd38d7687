package com.alibaba.blink.udx.udf;

import com.shizhuang.duapp.order.common.compress.CommonCompressor;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SubOrderDeCompress extends ScalarFunction {

    private static final Logger logger = LoggerFactory.getLogger(SubOrderDeCompress.class);

    public String eval(String value, String type) throws Exception {

        if (StringUtils.isEmpty(type)){
            throw new IllegalArgumentException("arg error, type is empty");
        }

        try {
            switch (type) {
                case "item_info":
                    return CommonCompressor.deCompressItemInfo(value);
                case "poundage_info":
                    return CommonCompressor.deCompressPoundageInfo(value);
                case "ii":
                    return CommonCompressor.deCompress(value);
                case "address_info":
                    return CommonCompressor.depressAddressInfo(value);
                case "merchant_info":
                    return CommonCompressor.depressMerchantInfo(value);
                case "feature":
                    return CommonCompressor.depressFeature(value);
                default:
                    return value;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("deCompress error!! type:{} value:{}",type,value);
            return value;
        }
    }
}
