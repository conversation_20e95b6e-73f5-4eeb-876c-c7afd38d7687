package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.FavoriteUvAccumulator;
import org.apache.flink.table.functions.AggregateFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created on 2022/11/13.
 * 计算累积收藏uv
 *
 * <AUTHOR>
 */
public class FavoriteUvAgg extends AggregateFunction<String, FavoriteUvAccumulator> {
    private static final Logger logger = LoggerFactory.getLogger(FavoriteUvAgg.class);

    //    timeLabel 就是formatday
    public void accumulate(FavoriteUvAccumulator accumulator, String timeLabel, String type, Long value) {

        if (null == value) {
            value = 0L;
        }
        accumulator.add(timeLabel, type, value);
    }

    public void retract(FavoriteUvAccumulator accumulator, String timeLabel,String type, Long value) {

    }

    @Override
    public String getValue(FavoriteUvAccumulator invQtyAccumulator) {
        return invQtyAccumulator.getValue();
    }

    @Override
    public FavoriteUvAccumulator createAccumulator() {
        return new FavoriteUvAccumulator();
    }

//    public static void main(String[] args) throws ParseException {
//
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Shanghai"));
//
//        Date date = dateFormat.parse(DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
//        calendar.setTime(date);
//        String today = dateFormat.format(calendar.getTime());
//
//        Date yestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24), "yyyy-MM-dd"));
//        calendar.setTime(yestDate);
//        String yestday = dateFormat.format(calendar.getTime());
//
//        Date beforeYestDate = dateFormat.parse(DateFormatUtils.format(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 48), "yyyy-MM-dd"));
//        calendar.setTime(beforeYestDate);
//        String beforeYestDay = dateFormat.format(calendar.getTime());
//        System.out.println(today);
//        System.out.println(yestday);
//        System.out.println(beforeYestDay);
//    }
}
