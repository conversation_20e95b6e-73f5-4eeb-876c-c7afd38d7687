package com.alibaba.blink.udx.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import com.util.MongoDBUtil;
import com.util.treadpoolutils.TreadPoolUtils;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.apache.flink.table.functions.FunctionContext;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created on 2022/8/15.
 *
 * <AUTHOR>
 */

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("BIGINT")
)
public class MongoDBDimCacheAll extends ScalarFunction {
    private CopyOnWriteArraySet<String> buff = new CopyOnWriteArraySet();
    private MongoDBUtil mongoDBClient;
    private String dbName;
    private String tableName;
    private String field;
    private ScheduledThreadPoolExecutor poolExecutor;
    private static final Logger log = LoggerFactory.getLogger(MongoDBDimCacheAll.class);

    public MongoDBDimCacheAll() {
    }

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        String liveTimeStr = context.getJobParameter("mongodb.dim.liveTime.minute", "5");
        String url = context.getJobParameter("mongodb.dim.url", "");
        this.dbName = context.getJobParameter("mongodb.dim.dbName", "");
        this.tableName = context.getJobParameter("mongodb.dim.tableName", "");
        this.field = context.getJobParameter("mongodb.dim.field", "");
        long liveTime = Long.parseLong(liveTimeStr);
        this.mongoDBClient = new MongoDBUtil(url);
        this.getBuff();
        this.poolExecutor = TreadPoolUtils.getScheduledThreadPoolExecutor();
        this.poolExecutor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                MongoDBDimCacheAll.this.getBuff();
            }
        }, liveTime, liveTime, TimeUnit.MINUTES);
    }

    public Long eval(String field) {
        return !this.buff.isEmpty() && this.buff.contains(field) ? 1L : 0L;
    }

    public void getBuff() {
        Set<String> onlyFieldValue = this.mongoDBClient.findOnlyFieldValue(this.dbName, this.tableName, this.field, String.class, new Document("isDelete", false));
        log.info("+++++++buff size:{};timestamp:{}", onlyFieldValue.size(), System.currentTimeMillis());
        this.buff = new CopyOnWriteArraySet(onlyFieldValue);
    }

    @Override
    public void close() throws Exception {
        super.close();
//        if (this.poolExecutor != null) {
//            this.poolExecutor.shutdown();
//        }

        if (this.mongoDBClient != null) {
            this.mongoDBClient.close();
        }

    }


}
