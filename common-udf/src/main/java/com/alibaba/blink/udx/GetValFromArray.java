package com.alibaba.blink.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.*;

/**
 * @ClassName: GetValFromArray
 * @Description:
 * @author: scl
 * @date: 2022/4/19  12:39 下午
 */

public class GetValFromArray extends ScalarFunction {
	public String eval(String message,String extractField,Boolean isSort,String order,String sortField,Integer index,String filterField,String filterOption, String... filterValues) {

		List<String> list = JSONArray.parseArray(message, String.class);
		if (Objects.isNull(list) || list.size() == 0) {
			return "";
		} else {
			if(isSort){
				Queue<JSONObject> queue = new PriorityQueue(new Comparator() {
					@Override
					public int compare(Object o1, Object o2) {
						if(order.equals("desc")){
							return ((JSONObject)o1).getString(sortField).compareTo(((JSONObject) o2).getString(sortField));
						}else{
							return ((JSONObject)o2).getString(sortField).compareTo(((JSONObject) o1).getString(sortField));
						}

					}
				});


				for (String s:list){
					JSONObject messageFields = JSON.parseObject(s);
					if(filterField .equals("")){
						if(queue.size() < index){
							queue.add(messageFields);
						}else{
							if(order.equals("desc")){
								if(messageFields.getString(sortField).compareTo( queue.peek().getString(sortField))> 0 ){
									queue.remove();
									queue.add(messageFields);
								}
							}else{
								if(messageFields.getString(sortField).compareTo( queue.peek().getString(sortField))< 0 ){
									queue.remove();
									queue.add(messageFields);
								}
							}

						}
					}else{
						Boolean isMatch = isFieldLike(messageFields,filterField,filterValues);
						if(filterOption.equals("like") && isMatch || filterOption.equals("nlike") && !isMatch ){
							if(queue.size() < index){
								queue.add(messageFields);
							}else{
								if(messageFields.getString(sortField).compareTo( queue.peek().getString(sortField))< 0 ){
									queue.remove();
									queue.add(messageFields);
								}
							}

						}
					}

				}
				return queue.size() < index ? "" : queue.peek().getString(extractField);
			}else{
				for (String s : list) {
					JSONObject messageFields = JSON.parseObject(s);
					Boolean isMatch = isFieldLike(messageFields,filterField,filterValues);
					if(filterOption.equals("like") && isMatch || filterOption.equals("nlike") && !isMatch ){
						return messageFields.getString(extractField);
					}
				}
			}

		}
		return "";
	}

	private boolean isFieldLike(JSONObject jsonObject,String filterField,String... filterValues){
		String filterFieldVal = jsonObject.getString(filterField);
		boolean isMatch = false;
		for (int i = 0 ; i < filterValues.length;i++){
			if(StringUtils.isNotEmpty(filterFieldVal) && filterFieldVal.matches(filterValues[i])){
				isMatch = true;
				break;
			}
		}
		return isMatch;
	}

	public static void main(String[] args) {
//        System.out.println(new GetValFromArray().eval("[{\\\"amount\\\":0,\\\"categoryId\\\":1,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117206\\\",\\\"endTime\\\":1643867977,\\\"limitAmount\\\":29900,\\\"startTime\\\":1643263176},{\\\"amount\\\":1000,\\\"categoryId\\\":0,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117207\\\",\\\"endTime\\\":1643867976,\\\"limitAmount\\\":30000,\\\"startTime\\\":1643263176},{\\\"amount\\\":2000,\\\"categoryId\\\":0,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117208\\\",\\\"endTime\\\":1643867976,\\\"limitAmount\\\":60000,\\\"startTime\\\":1643263176},{\\\"amount\\\":4000,\\\"categoryId\\\":0,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117209\\\",\\\"endTime\\\":1643867976,\\\"limitAmount\\\":130000,\\\"startTime\\\":1643263176},{\\\"amount\\\":9000,\\\"categoryId\\\":0,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117210\\\",\\\"endTime\\\":1643867976,\\\"limitAmount\\\":280000,\\\"startTime\\\":1643263176},{\\\"amount\\\":15000,\\\"categoryId\\\":0,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117211\\\",\\\"endTime\\\":1643867976,\\\"limitAmount\\\":450000,\\\"startTime\\\":1643263176},{\\\"amount\\\":21000,\\\"categoryId\\\":0,\\\"couponName\\\":\\\"新用户专属\\\",\\\"detailsNo\\\":\\\"105599117212\\\",\\\"endTime\\\":1643867976,\\\"limitAmount\\\":640000,\\\"startTime\\\":1643263176}]",
		System.out.println(new GetValFromArray().eval("[{\"amount\":0,\"categoryId\":1,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597421\",\"endTime\":1643866803,\"limitAmount\":29900,\"startTime\":1643262002},{\"amount\":1000,\"categoryId\":0,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597422\",\"endTime\":1643866801,\"limitAmount\":30000,\"startTime\":1643262002},{\"amount\":2000,\"categoryId\":0,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597423\",\"endTime\":1643866802,\"limitAmount\":60000,\"startTime\":1643262002},{\"amount\":4000,\"categoryId\":0,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597424\",\"endTime\":1643866802,\"limitAmount\":130000,\"startTime\":1643262002},{\"amount\":9000,\"categoryId\":0,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597425\",\"endTime\":1643866802,\"limitAmount\":280000,\"startTime\":1643262002},{\"amount\":15000,\"categoryId\":0,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597426\",\"endTime\":1643866802,\"limitAmount\":450000,\"startTime\":1643262002},{\"amount\":21000,\"categoryId\":0,\"couponName\":\"新用户专属\",\"detailsNo\":\"105598597427\",\"endTime\":1643866802,\"limitAmount\":640000,\"startTime\":1643262002}]",
				"endTime",true,"desc","endTime",1,"","nlike",""));
	}
}
