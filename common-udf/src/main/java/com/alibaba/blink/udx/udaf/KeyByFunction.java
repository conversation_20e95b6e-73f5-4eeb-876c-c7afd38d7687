package com.alibaba.blink.udx.udaf;

import com.alibaba.blink.udx.udaf.acc.ElementHolder;
import org.apache.flink.table.functions.AggregateFunction;

/**
 * <AUTHOR>
 * @Date 2020/11/30 4:16 下午
 */
public class KeyByFunction extends AggregateFunction<String, ElementHolder<String>> {
    /**
     * 初始化AggregateFunction的accumulator
     * 系统在第一次做aggregate计算之前调用一次这个方法
     * @return
     */
    @Override
    public ElementHolder<String> createAccumulator() {
        return new ElementHolder<>();
    }

    @Override
    public String getValue(ElementHolder<String> accumulator) {
        return accumulator.getElement();
    }

    public void accumulate(ElementHolder<String> accumulator, String value) {
        accumulator.setElement(value);
    }

    public void retract(ElementHolder<String> accumulator, String value) {
    }

}

    