package com.shizhuang.duapp.sorted.stream.udx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shizhuang.util.StringUtil;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.nio.charset.StandardCharsets;
import java.util.Objects;


@FunctionHint(output = @DataTypeHint("ROW<f0 STRING, f1 STRING, f2 STRING, f3 STRING, f4 STRING, f5 STRING, f6 STRING>"))
public class BinlogParserFunction extends TableFunction {

    private static final String BINLOG_SOURCE_DATABASE = "database";
    private static final String BINLOG_SOURCE_TABLE = "table";
    private static final String BINLOG_DATA = "data";
    private static final String BINLOG_OLD_DATA = "old";
    private static final String BINLOG_TYPE = "type";
    private static final String BINLOG_DELETE_TYPE = "D";
    private static final String DTS_BEFORE_FLAG = "N";
    private static final String DTS_AFTER_FLAG = "Y";
    private static final int BASE_ROW_SIZE = 7;

    public void eval(byte[] message) {
        String msg = new String(message, StandardCharsets.UTF_8);
        JSONObject jsonObject = JSON.parseObject(msg);
        if (Objects.nonNull(jsonObject)) {
            //解析data数据
            String binlogSourceDatabase = jsonObject.getString(BINLOG_SOURCE_DATABASE);
            String binlogSourceTable = jsonObject.getString(BINLOG_SOURCE_TABLE);
            String binlogType = jsonObject.getString(BINLOG_TYPE).substring(0, 1);
            String arrayData = jsonObject.getString(BINLOG_DATA);
            String oldArrayData = jsonObject.getString(BINLOG_OLD_DATA);
            int size = JSON.parseArray(arrayData).size();
            for (int i = 0; i < size; i++) {
                String data = JSON.parseArray(arrayData).getString(i);
                String oldData = null;
                if (StringUtil.isNotEmpty(oldArrayData)) {
                    JSONArray oldDataArray = JSON.parseArray(oldArrayData);
                    oldData = oldDataArray.size() > 0 ? oldDataArray.getString(i) : null;
                }
                Row row = new Row(BASE_ROW_SIZE);
                int pos = 0;
                row.setField(pos++, data);
                row.setField(pos++, oldData);
                row.setField(pos++, binlogType);
                row.setField(pos++, binlogSourceDatabase);
                row.setField(pos++, binlogSourceTable);
                if (BINLOG_DELETE_TYPE.equals(binlogType)) {
                    row.setField(pos++, DTS_AFTER_FLAG);
                    row.setField(pos, DTS_BEFORE_FLAG);
                } else {
                    row.setField(pos++, DTS_BEFORE_FLAG);
                    row.setField(pos, DTS_AFTER_FLAG);
                }
                collect(row);
            }
        }
    }
}
