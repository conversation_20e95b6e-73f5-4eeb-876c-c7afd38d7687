package com.dewu.flink.udf;


import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;


public class SumJsonParseArray extends ScalarFunction {

    private static final Logger LOGGER = LoggerFactory.getLogger(SumJsonParseArray.class);

    String skip;

    @Override
    public void open(FunctionContext context) throws Exception {
        skip = context.getJobParameter("execute.udx.dpc.skip", "false");
    }


    public String eval(String message,String keyColumn) {
        long sumResult=0;
        if (Objects.nonNull(message) && !"{}".equals(message) && message.length()!=0) {
            try {
                List<JSONObject> jsonObjectList = JSONObject.parseArray(message, JSONObject.class);
                Iterator<JSONObject> iterator = jsonObjectList.iterator();
                while (iterator.hasNext()) {
                    JSONObject jsonObject = iterator.next();
                    long key = Long.valueOf(jsonObject.getString(keyColumn));
                    sumResult=sumResult+key;
                }
            } catch (Exception e) {
                LOGGER.error("error SumJsonParseArray:{}",message);
                e.printStackTrace();
                if("false".equalsIgnoreCase(skip)) {
                    throw e;
                }
            }
            return String.valueOf(sumResult);
        } else {
            return String.valueOf(sumResult);
        }
    }


    public static void main(String[] args) {
//        String str="[{\"expenseType\":1,\"expenseName\":\"技术服务费\",\"originalExpense\":12495,\"currentExpense\":12495,\"originalPercent\":500,\"currentPercent\":500,\"extendJson\":\"{\\\"currentPercent\\\":500,\\\"currentexpense\\\":12495,\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalTechnicalFeeLimit\\\":{\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalExpense\\\":12495,\\\"originalPercent\\\":500},\\\"salaInfoInnerVo\\\":{\\\"salaPercent\\\":10000,\\\"salaType\\\":5}}\",\"salePercent\":10000},{\"expenseType\":3,\"expenseName\":\"转账手续费\",\"originalExpense\":2499,\"currentExpense\":2499,\"originalPercent\":100,\"currentPercent\":100,\"extendJson\":\"{\\\"currentExpense\\\":2499,\\\"currentPercent\\\":100,\\\"originalExpense\\\":2499,\\\"originalPercent\\\":100}\"},{\"expenseType\":5,\"expenseName\":\"查验费\",\"originalExpense\":800,\"currentExpense\":800,\"extendJson\":\"{\\\"currentExpense\\\":800,\\\"originalExpense\\\":800}\"},{\"expenseType\":2,\"expenseName\":\"鉴别费\",\"originalExpense\":1500,\"currentExpense\":1500,\"extendJson\":\"{\\\"currentExpense\\\":1500,\\\"originalExpense\\\":1500}\"},{\"expenseType\":4,\"expenseName\":\"包装服务费\",\"originalExpense\":1000,\"currentExpense\":1000,\"extendJson\":\"{\\\"currentExpense\\\":1000,\\\"originalExpense\\\":1000}\"},{\"expenseType\":7,\"expenseName\":\"预计收入\",\"originalExpense\":231606,\"currentExpense\":231606},{\"expenseType\":6,\"expenseName\":\"总费用\",\"originalExpense\":18294,\"currentExpense\":18294}]";
        String str="{\"funderType\":1,\"funderNo\":\"\",\"subsidyAmount\":0}";
        str="[\n" +
                "    {\n" +
                "        \"type\": 1,\n" +
                "        \"val\": 300,\n" +
                "        \"end\": 1706630400,\n" +
                "        \"bal\": 90000\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\": 2,\n" +
                "        \"val\": 200,\n" +
                "        \"end\": 1706630400,\n" +
                "        \"bal\": 90000\n" +
                "    }\n" +
                "]";
        SumJsonParseArray jsonParseArray = new SumJsonParseArray();
        System.out.println(jsonParseArray.eval(str,"val"));
    }

}
