package com.dewu.flink.udf;

import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class MaxValue extends ScalarFunction {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaxValue.class);

    String skip;

    public void open(FunctionContext context) throws Exception {
        skip = context.getJobParameter("execute.udx.dpc.skip", "false");
    }


    public Long eval(String... value) {
        try {
            List<String> valueList = Arrays.asList(value);
            // 将字符串 List 转换为长整型 List
            List<Long> longList = new ArrayList<>();
            for (String str : valueList) {
                try {
                    longList.add(Long.parseLong(str));
                } catch (NumberFormatException e) {
                    System.out.println("无法转换字符串: " + str);
                }
            }
            if (longList.size() == 0) {
                return null;
            }
            long maxValue = Collections.max(longList);
            return maxValue;
        }catch (Exception e) {
            if("false".equalsIgnoreCase(skip)) {
                throw e;
            }
        }
        return null;
    }


    public static void main(String[] args) {
        MaxValue jsonParseArray = new MaxValue();
        System.out.println(jsonParseArray.eval(null,"","","23"));
    }

}
