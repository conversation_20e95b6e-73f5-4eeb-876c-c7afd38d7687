package com.dewu.flink.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @create 2025-05-26 20:56
 **/
@Slf4j
public class JsonArrayStringJoin extends ScalarFunction {


    public String eval(String jsonArray,String separator) {
        if(Strings.isNullOrEmpty(jsonArray)) {
            return null;
        }
        try {
            List<String> arr = JSON.parseArray(jsonArray, String.class);
            return String.join(separator, arr);
        } catch (Exception ex) {
            return null;
        }
    }

}
