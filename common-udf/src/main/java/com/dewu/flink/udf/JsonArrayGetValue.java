package com.dewu.flink.udf;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

public class JsonArrayGetValue extends ScalarFunction {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonArrayGetValue.class);

    String skip;

    @Override
    public void open(FunctionContext context) throws Exception {
        skip = context.getJobParameter("execute.udx.dpc.skip", "false");
    }


    public String eval(String message,String keyColumn,String keyCondition ,String valueColumn) {
        if (Objects.nonNull(message) && !"{}".equals(message) && message.length()!=0) {
            try {
                List<JSONObject> jsonObjectList = JSONObject.parseArray(message, JSONObject.class);
                Iterator<JSONObject> iterator = jsonObjectList.iterator();
                while (iterator.hasNext()) {
                    JSONObject jsonObject = iterator.next();
                    String key = String.valueOf(jsonObject.getString(keyColumn));
                    if(keyCondition.equalsIgnoreCase(key)) {
                        String value = String.valueOf(jsonObject.getString(valueColumn));
                        return value;
                    }
                }
                return null;
            } catch (Exception e) {
                LOGGER.error("error SumJsonParseArray:{}",message);
                e.printStackTrace();
                if("false".equalsIgnoreCase(skip)) {
                    throw e;
                }
            }
            return null;
        } else {
            return null;
        }
    }


    public static void main(String[] args) {

    }

}
