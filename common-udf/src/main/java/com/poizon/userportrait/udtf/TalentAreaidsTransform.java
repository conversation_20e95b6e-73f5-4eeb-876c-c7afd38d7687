package com.poizon.userportrait.udtf;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.HashMap;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<s1 STRING, s2 STRING")
)
public class TalentAreaidsTransform extends TableFunction<Row> {
    HashMap<String, String> map;

    public void open(FunctionContext context) throws Exception {
        super.open(context);
        map = new HashMap<String, String>() {{
            put("1", "球鞋");
            put("2", "穿搭");
            put("3", "手表");
            put("4", "科技数码");
            put("5", "玩具");
            put("6", "美食");
            put("7", "旅游");
            put("8", "健身");
            put("9", "运动");
            put("10", "汽车");
            put("11", "游戏");
            put("12", "音乐");
            put("13", "舞蹈");
            put("14", "摄影");
            put("15", "动漫");
            put("16", "美妆");
            put("17", "男士理容");
            put("18", "美妆理容");
            put("19", "艺术");
        }};
    }

    public void eval(String string) {
        if (StringUtils.isEmpty(string)) {
            collect(Row.of("", "未知"));
        } else {
            try {
                ArrayList<String> list = JSON.parseObject(string, ArrayList.class);
                ArrayList<String> nameList = new ArrayList(list.size());
                for (String s : list) {
                    nameList.add(map.getOrDefault(s, "未知"));
                }
                collect(Row.of(StringUtils.join(list, ","), StringUtils.join(nameList, ",")));
            } catch (Exception e) {
                e.printStackTrace();
                collect(Row.of("", "未知"));
            }
        }
    }
}
