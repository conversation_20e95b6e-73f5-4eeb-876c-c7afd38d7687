package com.poizon.userportrait.udtf;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;

@FunctionHint(
		input = {@DataTypeHint("STRING")}
		,
		output = @DataTypeHint("ROW<s STRING, index BIGINT>")
)
// 存在两个 SetConvertSingle 类（包不同）都被任务依赖了，实现上一个是基于 Gson 另一个是 FastJson。
public class JacksonSetConvertSingle extends TableFunction<Row> {

	private static final ObjectMapper objectMapper = new ObjectMapper();
	private static final Logger logger = LoggerFactory.getLogger(JacksonSetConvertSingle.class);
	private static final int BASE_ROW_SIZE = 2;

	public void eval(String message) {
		Row emptyRow = new Row(BASE_ROW_SIZE);

		if (message == null) {
			collect(emptyRow);
			return;
		}

		try {
			if (isValidObject(message)) {
				Row row = new Row(BASE_ROW_SIZE);
				handleValidMessage(message, row);
			} else {
				handleInvalidMessage(message, emptyRow);
			}
		} catch (Exception e) {
			logger.error("Processing error for message '{}': {}", message, e.getMessage());
			collect(emptyRow);
		}
	}

	private void handleValidMessage(String message, Row row) {
		row.setField(0, message);
		row.setField(1, 0L);
		collect(row);
	}

	private void handleInvalidMessage(String message, Row emptyRow) {
		try {
			final JsonNode jsonNode = objectMapper.readTree(message);
			final Iterator<JsonNode> elements = jsonNode.elements();
			if (elements.hasNext()) {
				Object nextObj = null;
				try {
					Long i = 0L;
					do {
						Row row = new Row(BASE_ROW_SIZE);
						final JsonNode next = elements.next();
						if (next.isTextual()) {
							final String text = next.asText();
							nextObj = text;
							row.setField(0, text);
						} else {
							final String text = next.toString();
							nextObj = text;
							row.setField(0, text);
						}
						row.setField(1, i);
						collect(row);
						i = i + 1;
					} while (elements.hasNext());
				} catch (Exception e) {
					e.printStackTrace();
					logger.info("next Obj: {}", nextObj);
					throw new RuntimeException(e);
				}
			} else {
				collect(emptyRow);
			}
		} catch (Exception exception) {
			logger.error("Failed to parse message as JSON: '{}'.", message, exception);
			collect(emptyRow);
		}
	}


	private boolean isValidObject(String value) {
		if (value.trim().startsWith("[")) {
			return false;
		}
		try {
			final JsonNode jsonNode = objectMapper.readTree(value);
			return jsonNode.isObject();
		} catch (Exception e) {
			return false;
		}
	}

//	private boolean isValidArray(String value) {
//		try {
//			JSON.parseArray(value);
//			return true;
//		} catch (Exception e) {
//			return false;
//		}
//	}

	public static void main(String[] args) {

//        String s = "[{\"contentType\":0,\"position\":4,\"contentID\":\"1170342\",\"propertyValueId\":\"26089126\",\"channel\":\"FAN\",\"requestID\":\"1329610612402212864\",\"cspuId\":\"3360649\"}]";
//        String s = "[{\"contentType\":0,\"position\":2,\"contentID\":\"1147180\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329610647090032640\",\"cspuId\":\"3327641\"}]";
		String s = "[{\"contentType\":0,\"position\":4,\"contentID\":\"10903\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329242773749628928\",\"cspuId\":\"10903\"}]";
//		String s = "{\"contentType\":0,\"position\":4,\"contentID\":\"10903\",\"propertyValueId\":\"0\",\"channel\":\"CLICK\",\"requestID\":\"1329242773749628928\",\"cspuId\":\"10903\"}]";
//		JSONArray.parseArray(s);
//        System.out.println(JSONObject.isValidObject(s));
//        List<String> strings = JSONArray.parseArray(s, String.class);
//        System.out.println(strings);
		s = "[{\"ii\":\"10001701736\",\"sc\":0.41353,\"ct\":null,\"price\":\"13800\",\"spuid\":\"52582555\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10001367237\",\"sc\":0.41118,\"ct\":null,\"price\":\"7500\",\"spuid\":\"52541389\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000013160\",\"sc\":0.55999,\"ct\":null,\"price\":\"10100\",\"spuid\":\"52370522\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000048440\",\"sc\":0.41096,\"ct\":null,\"price\":\"9600\",\"spuid\":\"52375849\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000029869\",\"sc\":0.41126,\"ct\":null,\"price\":\"12100\",\"spuid\":\"52411019\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10001362140\",\"sc\":0.41115,\"ct\":null,\"price\":\"9000\",\"spuid\":\"52381359\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10001010857\",\"sc\":0.41163000000000005,\"ct\":null,\"price\":\"6900\",\"spuid\":\"52400267\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000033412\",\"sc\":0.41855,\"ct\":null,\"price\":\"13500\",\"spuid\":\"52382349\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000036041\",\"sc\":0.41000000000000003,\"ct\":null,\"price\":\"15500\",\"spuid\":\"52206596\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10001152728\",\"sc\":0.41433,\"ct\":null,\"price\":\"8900\",\"spuid\":\"52576882\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10002305905\",\"sc\":0.41155,\"ct\":null,\"price\":\"4800\",\"spuid\":\"57778148\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000026085\",\"sc\":0.41074,\"ct\":null,\"price\":\"6300\",\"spuid\":\"52331067\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000071689\",\"sc\":0.41012000000000004,\"ct\":null,\"price\":\"8900\",\"spuid\":\"52269088\",\"l1ci\":48,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10002260521\",\"sc\":0.41564,\"ct\":null,\"price\":\"7000\",\"spuid\":\"53472498\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10001144306\",\"sc\":0.41228,\"ct\":null,\"price\":\"5700\",\"spuid\":\"54181568\",\"l1ci\":2,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10005924493\",\"sc\":0.41741000000000006,\"ct\":null,\"price\":\"2100\",\"spuid\":\"72686104\",\"l1ci\":1002857,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000061894\",\"sc\":0.41695000000000004,\"ct\":null,\"price\":\"42500\",\"spuid\":\"52638825\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10003363048\",\"sc\":0.41093,\"ct\":null,\"price\":\"9200\",\"spuid\":\"61535081\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10000016238\",\"sc\":0.4173,\"ct\":null,\"price\":\"7500\",\"spuid\":\"52560939\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"},{\"ii\":\"10002774162\",\"sc\":0.5353300000000001,\"ct\":null,\"price\":\"9400\",\"spuid\":\"54051011\",\"l1ci\":29,\"it\":\"product\",\"cn\":\"HOT\"}]";

		new JacksonSetConvertSingle().eval(s);


	}
}
