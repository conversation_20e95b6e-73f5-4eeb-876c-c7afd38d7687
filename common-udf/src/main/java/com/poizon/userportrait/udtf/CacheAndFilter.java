package com.poizon.userportrait.udtf;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.concurrent.TimeUnit;
public class CacheAndFilter extends TableFunction<Row> {
    Cache<String, ArrayList<String>> cache;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        cache = CacheBuilder.newBuilder()
                .initialCapacity(500000)
                .expireAfterAccess(30, TimeUnit.MINUTES)
                .build();
    }

    @FunctionHint(output = @DataTypeHint("ROW<col1 STRING, col2 STRING, col3 STRING,col4 STRING,col5 STRING,col6 STRING>"))
    public void eval(String userId, String field, String... args) {
        ArrayList<String> ifPresent = cache.getIfPresent(userId);
        Object[] strings = new String[args.length + 2];
        strings[0] = userId;
        strings[1] = field;
        System.arraycopy(args, 0, strings, 2, args.length);
        if (ifPresent == null) {
            collect(Row.of(strings));
            cache.put(userId, new ArrayList<String>(){{add(field);}});
        } else {
            if (!ifPresent.contains(field)) {
                collect(Row.of(strings));
                ifPresent.add(field);
                if (ifPresent.size()>3){
                    ifPresent.remove(0);
                }
            }
        }
    }
}
