package com.poizon.userportrait.udtf;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.util.JdbcUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udtf
 * @date Date  2023-05-24 19:02
 * @Description :TODO
 */
@Slf4j
public class ListStrJoin extends TableFunction<Row> {
    protected transient Connection connection = null;
    protected volatile boolean isClosed = false;
    private transient DruidDataSource dataSource = null;
    private String query;
    private transient PreparedStatement statement;
    private transient Cache<String, ArrayList<String>> cache;
    String[] fieldNames;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        dataSource = buildDataSource(context);
        query = context.getJobParameter("query.sql", null);
        int cacheSize = Integer.parseInt(context.getJobParameter("cache.size", null));
        long cacheTTL = Long.parseLong(context.getJobParameter("cache.ttl.ms", null));
        String regex = context.getJobParameter("field.regex", null);
        fieldNames = context.getJobParameter("field.names", null).split(regex);
        createConnectionAndStatement(query);
        createCache(cacheSize, cacheTTL);
    }

    private void createCache(int cacheSize, long cacheTTL) {
        cache = CacheBuilder.newBuilder()
                .initialCapacity(cacheSize)
                .maximumSize(cacheSize)
                .expireAfterWrite(cacheTTL, TimeUnit.MILLISECONDS)
                .build();

    }


    public void eval(String keys, String regex, String... dims) {
        Object[] result = new String[dims.length];
        if (!StringUtils.isEmpty(keys)) {
            getValueFromCache(keys, regex, result);
        }
        collect(Row.of(result));
    }

    public void getValueFromCache(String keys, String regex, Object[] result) {
        final HashMap<Integer, String> queryMap = new HashMap<>();
        String[] split = keys.split(regex);
        ArrayList<String>[] resultList = new ArrayList[split.length];
        for (int i = 0; i < split.length; i++) {
            ArrayList<String> ifPresent = cache.getIfPresent(split[i]);
            if (ifPresent == null) {
                queryMap.put(i, split[i]);
            } else {
                resultList[i] = ifPresent;
            }
        }
        if (!queryMap.isEmpty()) {
            queryRds(queryMap, resultList);
        }
        buildResult(regex, resultList, result);
    }

    private void buildResult(String regex, ArrayList<String>[] result, Object[] objects) {
        for (int i = 0; i < fieldNames.length; i++) {
            StringBuilder b = new StringBuilder();
            for (int i1 = 0; i1 < result.length; i1++) {
                String value = result[i1].get(i);
                if (StringUtils.isNotEmpty(value)) {
                    if (i1 != 0) {
                        b.append(regex).append(value);
                    } else {
                        b.append(value);
                    }
                }
                objects[i] = b.toString();
            }
        }
    }

    private void queryRds(HashMap<Integer, String> queryMap, ArrayList<String>[] resultList) {
        int maxRetryTimes = 5;
        for (int retry = 0; retry <= maxRetryTimes; retry++) {
            try {
                for (Map.Entry<Integer, String> entry : queryMap.entrySet()) {
                    if (statement.isClosed()) {
                        createConnectionAndStatement(query);
                    }
                    statement.clearParameters();
                    statement.setString(1, entry.getValue());
                    ArrayList<String> list = new ArrayList<>(fieldNames.length);
                    try (ResultSet resultSet = statement.executeQuery()) {
                        while (resultSet.next()) {
                            for (int i = 0; i < fieldNames.length; i++) {
                                String field = null;
                                if (resultSet.getObject(fieldNames[i]) != null) {
                                    field = resultSet.getObject(fieldNames[i]).toString();
                                }
                                list.add(i, field);
                            }
                        }
                    }
                    resultList[entry.getKey()] = list;
                    cache.put(entry.getValue(), list);
                }
                break;
            } catch (SQLException e) {
                log.error(String.format("JDBC executeBatch error, retry times = %d", retry), e);
                if (retry >= maxRetryTimes) {
                    throw new RuntimeException("Execution of JDBC statement failed.", e);
                }
                try {
                    if (!isConnectionValid(connection)) {
                        createConnectionAndStatement(query);
                    }
                } catch (SQLException SQLException) {
                    log.error(
                            "JDBC connection is not valid, and reestablish connection failed",
                            SQLException);
                    throw new RuntimeException("Reestablish JDBC connection failed", SQLException);
                }
                try {
                    Thread.sleep(1000L * retry);
                } catch (InterruptedException e1) {
                    throw new RuntimeException(e1);
                }
            }
        }
    }

    private boolean isConnectionValid(Connection connection) throws SQLException {
        return connection != null && connection.isValid(dataSource.getRemoveAbandonedTimeout());
    }

    @Override
    public void close() {
        isClosed = true;
        if (cache != null) {
            cache.cleanUp();
        }
        if (statement != null) {
            try {
                statement.close();
            } catch (SQLException e) {
                log.info("JDBC statement could not be closed: " + e.getMessage());
            } finally {
                statement = null;
            }
        }
        try {
            if (this.connection != null) {
                this.connection.close();
            }
        } catch (SQLException var5) {
            throw new RuntimeException("Fail to close connection for table:" + this.connection, var5);
        } finally {
            dataSource.close();
        }

    }


    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 2];
            for (int i = 0; i < outputDataTypes.length; i++) {
                outputDataTypes[i] = DataTypes.STRING();
            }
            return Optional.of(DataTypes.ROW(outputDataTypes));
        }).build();
    }


    private DruidDataSource buildDataSource(FunctionContext config) {
        DruidDataSource dataSource = new DruidDataSource();
        String url = config.getJobParameter("url", null);
        String userName = config.getJobParameter("userName", null);
        String password = config.getJobParameter("password", null);
        if (!url.startsWith("jdbc:mysql://")) {
            throw new IllegalArgumentException(String.format("url of %s must starts with jdbc:mysql:// format, but actual url is %s", "rds", url));
        } else {
            int connectionMaxActive = Integer.parseInt(config.getJobParameter("connectionMaxActive", "4"));
            long connectionMaxWait = Long.parseLong(config.getJobParameter("connectionMaxWait", "15000"));
            int connectionInitialSize = Integer.parseInt(config.getJobParameter("connectionInitialSize", "1"));
            int connectionMinIdle = Integer.parseInt(config.getJobParameter("connectionMinIdle", "0"));
            int removeAbandonedTimeout = Integer.parseInt(config.getJobParameter("removeAbandonedTimeout", "600"));
            dataSource.setUrl(url);
            dataSource.setUsername(userName);
            dataSource.setPassword(password);
            dataSource.setDriverClassName("com.mysql.jdbc.Driver");
            dataSource.setMaxActive(connectionMaxActive);
            dataSource.setMaxWait(connectionMaxWait);
            dataSource.setInitialSize(connectionInitialSize);
            dataSource.setMinIdle(connectionMinIdle);
            dataSource.setRemoveAbandoned(true);
            dataSource.setRemoveAbandonedTimeout(removeAbandonedTimeout);
            return dataSource;
        }
    }


    protected void createConnectionAndStatement(String query) throws SQLException {
        JdbcUtils.close(this.statement);
        JdbcUtils.close(this.connection);
        this.connection = dataSource.getConnection();
        log.debug("query: {}", query);
        this.statement = this.connection.prepareStatement(query);
    }
}

