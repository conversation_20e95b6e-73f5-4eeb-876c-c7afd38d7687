package com.poizon.userportrait.udtf;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.Map;

@FunctionHint(output = @DataTypeHint("ROW<f0 STRING, f1 STRING>"))
public class JsonEntrySet extends TableFunction<Row> {
    public void eval(String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            collect(Row.of(entry.getKey(),entry.getValue().toString()));
        }
    }

}
