package com.poizon.userportrait.udtf;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udtf
 * @date Date  2023-04-11 14:46
 * @Description :TODO
 */

@Slf4j
public class TopicClickTrace extends TableFunction<Row> {
    Cache<String, String> cache;
    private static final int initialCapacity = 500000;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        cache = CacheBuilder.newBuilder()
                .initialCapacity(initialCapacity)
                .expireAfterAccess(30, TimeUnit.MINUTES)
                .build();
    }

    public void eval(String userId, String rn, String condition,String field , String... args) {
        if (condition.equals(rn)){
            cache.put(userId,field);
        }else {
            String ifPresent = cache.getIfPresent(userId);
            if (ifPresent != null){
                Object[] strings = new String[args.length + 2];
                System.arraycopy(args, 0, strings, 2, args.length);
                strings[0] = userId;
                strings[1] = ifPresent;
                collect(Row.of(strings));
                cache.invalidate(userId);
            }
        }
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 2];
            for (int i = 0; i < argumentDataTypes.size()-2; i++) {
                outputDataTypes[i] = DataTypes.STRING();
            }
            return Optional.of(DataTypes.ROW(outputDataTypes));
        }).build();
    }
}
