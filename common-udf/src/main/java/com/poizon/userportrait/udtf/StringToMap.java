package com.poizon.userportrait.udtf;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;

@Slf4j
//blink无法直接读取odps Map数据类型 在odps端转为jsonStr 之后作为varchar读入
public class StringToMap extends ScalarFunction{
    public static HashMap<Object,Integer> eval(String json) {
        try {
            HashMap<Object,Integer> hashMap = JSON.parseObject(json, HashMap.class);
            return hashMap;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("检查数据,有不符合json的数据,{}",json);
        }
        return new HashMap<>();
    }
}
