package com.poizon.userportrait.udtf;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.*;

@Slf4j
//获取acm中kv对的函数 field 传key 返回value 默认值为"" 避免sql中做null值处理
public class SimpleDealWithAcmV2 extends TableFunction<Row> {
    private static final String VERSION = "_version";
    private static final String DOMAIN = "_domain";
    private static final String CONTENT = "_content";
    private static final String EXP = "_experiments";
    private static final String RESOURCE = "_resource";
    private static final String DATA = "_data";
    private static final String POINT = "\\.";
    private static final String DEFAULT = "0";
    private static final String DASH = "-";
    private static final String UNDERSCORE = "_";

    public void eval(String acm, String... fields) {

        if (StringUtils.isEmpty(acm)) {
            return;
        }

        if (acm.length() > 1024) {
            log.error("acm:{},length is too long, keep it less than 1024 !!",acm);
            return;
        }

        HashMap<String, String> result = new HashMap<>();

        String[] parts = acm.split(POINT);
        if (parts.length < 6) {
            log.error("acm:{}, format is illegal !!! check it",acm);
            return;
        }

        try {
            result.put(VERSION, parts[0]);
            result.put(DOMAIN, parts[1]);
            result.put(CONTENT, parts[2]);
            parseData(result, parts[2]);
            result.put(EXP, parts[4]);
            result.put(RESOURCE, parts[3]);
            if (parts.length == 6) {
                result.put(DATA, parts[5]);
                parseData(result, parts[5]);
//                 如果第六位 自定义值中使用了.才会执行
            } else {
                result.put(DATA, String.join(".",
                        Arrays.asList(parts).subList(5, parts.length)));
                for (int i = 5; i < parts.length; i++) {
                    parseData(result, parts[i]);
                }
            }
        } catch (Exception e) {
            log.error("acm:{}, format is incorrect !!! {}",acm,e);
            return;
        }
        Object[] strings = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            strings[i] = result.getOrDefault(fields[i], "");
        }
        collect(Row.of(strings));
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1];
            if (argumentDataTypes.size() >= 1) {
                for (int i = 0; i < argumentDataTypes.size()-1; i++) {
                    outputDataTypes[i] = DataTypes.STRING();
                }
                return Optional.of(DataTypes.ROW(outputDataTypes));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();
    }

    private static void parseData(HashMap<String, String> result, String ctx) {
        if (!DEFAULT.equals(ctx) && StringUtils.isNotEmpty(ctx)) {
            final String[] split = ctx.split(DASH);
            for (String value : split) {
                final String[] s = value.split(UNDERSCORE);
                if (s.length == 2) {
                    result.put(s[0], s[1]);
                }else {
                    if (s.length==1){
                        result.put(s[0],"");
                    }
                    if (s.length>2){
                        StringJoiner joiner = new StringJoiner(UNDERSCORE);
                        for (int i = 1; i < s.length; i++) {
                            joiner.add(s[i]);
                        }
                        result.put(s[0],joiner.toString());
                    }
                }
            }
        }
    }
}
