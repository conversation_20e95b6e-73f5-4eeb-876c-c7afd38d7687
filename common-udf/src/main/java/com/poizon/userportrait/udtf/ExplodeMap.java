package com.poizon.userportrait.udtf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: ExplodeMap
 * @Description:
 * @author: scl
 * @date: 2022/4/19  12:46 下午
 */
@FunctionHint(output = @DataTypeHint("ROW<f0 STRING, f1 DECIMAL>"))

public class ExplodeMap extends TableFunction<Row> {

	public void eval(HashMap<String, BigDecimal> map) {
		if (map == null) {
			throw new IllegalArgumentException("map  cannot be empty!");
		}
		for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
			collect(Row.of(entry.getKey(),entry.getValue()));
		}
	}

	public static void main(String[] args) {
		ExplodeMap explodeMap = new ExplodeMap();
		String str = "[\"2\",\"16\"]";
		HashMap<String, BigDecimal> map = new HashMap();
		map.put("scl", BigDecimal.valueOf(0.01d));
		explodeMap.eval(map);
		System.out.println();
	}

}

