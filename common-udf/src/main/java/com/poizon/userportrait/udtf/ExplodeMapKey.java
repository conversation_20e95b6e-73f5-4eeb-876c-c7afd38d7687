package com.poizon.userportrait.udtf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udtf
 * @date Date  2023-04-20 11:22
 * @Description :TODO
 */
@FunctionHint(output = @DataTypeHint("ROW<f0 MAP<STRING,BIGINT>, f1 STRING>"))
public class ExplodeMapKey extends TableFunction<Row> {
    public void eval(Map<String, Long> map,String regex) {
        if (map == null) {
            return;
        }
        HashSet<String> set = new HashSet<>();
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            if (entry.getKey().contains(regex)){
                String[] s = entry.getKey().split(regex);
                set.add(s[1]);
            }
        }
        if (set.size()>0){
            set.forEach(s -> collect(Row.of(map,s)));
        }else {
            collect(Row.of(map,""));
        }

    }

}