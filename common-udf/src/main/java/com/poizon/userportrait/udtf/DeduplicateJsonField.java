package com.poizon.userportrait.udtf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udtf
 * @date Date  2023-04-27 16:05
 * @Description :TODO
 */

@Slf4j
public class DeduplicateJsonField extends TableFunction<Row> {
    Cache<String, Map<String,String>> cache;
    private static final int initialCapacity = 500000;
    TypeReference<HashMap<String, String>> typeRef;
    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        typeRef = new TypeReference<HashMap<String, String>>() {};
        cache = CacheBuilder.newBuilder()
                .initialCapacity(initialCapacity)
                .expireAfterAccess(30, TimeUnit.MINUTES)
                .build();
        new Timer().scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                log.info("used cache size:{}", cache.size());
            }
        }, 10000, 120 * 1000);
    }

    public void eval(String key, String value, String... args) {
        Map<String,String> ifPresent = cache.getIfPresent(key);
        HashMap<String, String> newMap = JSONObject.parseObject(value, typeRef);
        Object[] strings = new String[args.length + 3];
        strings[0] = key;
        System.arraycopy(args, 0, strings, 3, args.length);
        if (ifPresent == null) {
            for (Map.Entry<String, String> entry : newMap.entrySet()) {
                strings[1] = entry.getKey();
                strings[2] = entry.getValue();
                collect(Row.of(strings));
                cache.put(key,newMap);
            }
        } else {
            for (Map.Entry<String, String> entry : newMap.entrySet()) {
                if (!entry.getValue().equals(ifPresent.get(entry.getKey()))) {
                    strings[1] = entry.getKey();
                    strings[2] = entry.getValue();
                    collect(Row.of(strings));
                    ifPresent.put(entry.getKey(),entry.getValue());
                }
            }
        }
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            DataType[] outputDataTypes = new DataType[argumentDataTypes.size() + 1];
            for (int i = 0; i <= argumentDataTypes.size(); i++) {
                outputDataTypes[i] = DataTypes.STRING();
            }
            return Optional.of(DataTypes.ROW(outputDataTypes));
        }).build();
    }
}
