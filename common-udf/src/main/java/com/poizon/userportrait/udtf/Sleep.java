package com.poizon.userportrait.udtf;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.*;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udtf
 * @date Date  2023-03-20 20:28
 * @Description : 减轻下游数据库压力
 */

public class Sleep extends TableFunction<Row> {
    public void eval(Long millis,String... fields) throws InterruptedException {
        Object[] strings = new String[fields.length];
        System.arraycopy(fields, 0, strings, 0, fields.length);
        Thread.sleep(millis);
        collect(Row.of(strings));
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> {
            String functionName = callContext.getName();
            //根据函数入参情况确定返回值
            List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
            DataType[] outputDataTypes = new DataType[argumentDataTypes.size()-1];
            if (argumentDataTypes.size() >= 2) {
                for (int i = 0; i < argumentDataTypes.size()-1 ; i++) {
                    outputDataTypes[i] = DataTypes.STRING();
                }
                return Optional.of(DataTypes.ROW(outputDataTypes));
            } else {
                throw new ValidationException(String.format(
                        "function %s argument is empty",
                        functionName));
            }
        }).build();
    }
}
