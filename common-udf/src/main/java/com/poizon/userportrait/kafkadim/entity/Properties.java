package com.poizon.userportrait.kafkadim.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonAlias;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2020/8/8 11:01 上午
 * @description：
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class Properties implements Serializable {
    private static final long serialVersionUID = -4724195203532177169L;

    @JsonAlias("dw_userid")
    private String dw_userId;

    @JsonAlias("current_page")
    private String currentPage = "";

    @JsonAlias("current_page_title")
    private String currentPageTitle = "";

    @JsonAlias("block_type")
    private String blockType = "";

    @JsonAlias("block_type_title")
    private String blockTypeTitle = "";

    @JsonAlias("jump_type")
    private String jumpType = "";

    @JsonAlias("jump_content_id")
    private String jumpContentId = "";

    @JsonAlias("$os")
    private String os = "";

    @JsonAlias("product_brand_name")
    private String productBrandName = "";

    @JsonAlias("$wifi")
    private Boolean wifi = false;

    @JsonAlias("$network_type")
    private String networkType = "";

    @JsonAlias("$ip")
    private String ip = "";

    @JsonAlias("product_name")
    private String productName = "";

    @JsonAlias("$app_version")
    private String appVersion = "";

    @JsonAlias("$lib")
    private String lib = "";

    @JsonAlias("product_category_name")
    private String productCategoryName = "";

    @JsonAlias("spu_id")
    private String spuId = "";

    @JsonAlias({"algorithm_product_property_value","property_value_id"})
    private String algorithmProductPropertyValue = "";

    @JsonAlias("sku_id")
    private String skuId = "";

    @JsonAlias("$city")
    private String city = "";

    @JsonAlias("$province")
    private String province = "";

    @JsonAlias("$country")
    private String country = "";

    @JsonAlias("search_key_word")
    private String searchKeyWord = "";

    @JsonAlias("recommend_content_info_list")
    private String recommendContentInfo = "";

    @JsonAlias("algorithm_channel_Id")
    private String algorithmChannelId = "";

    @JsonAlias("algorithm_request_Id")
    private String algorithmRequestId = "";
    /**
     * 负反馈类型 0商品 2主题
     */
    @JsonAlias("feedback_content_type")
    private String feedbackContentType = "";

    /**
     * 负反馈spuId
     */
    @JsonAlias("block_content_id")
    private String blockContentId = "";

    @JsonIgnore
    private List<RecommendContentInfo> recommendContentInfoList;

    @JsonAlias("target_spu_id")
    private String targetSpuId = "";

    @JsonAlias("algorithm_target_spu_property_value")
    private String algorithmTargetSpuPropertyValue = "";

    @JsonAlias("jump_content_url")
    private String jumpContentUrl = "";

    @JsonAlias("device_id")
    private String deviceId = "";

    @JsonAlias("content_id")
    private String contentId = "";

    @JsonAlias("content_type")
    private String contentType = "";

    @JsonAlias("content_url")
    private String contentUrl = "";

    @JsonAlias("feedback_type")
    private String feedbackType = "";

    @JsonAlias("block_content_position")
    private String blockContentPosition = "";

    @JsonAlias("position")
    private String position = "";

    @JsonAlias("search_filter_info_list")
    private String searchFilterInfoList = "";

    @JsonAlias("search_key_word_position")
    private String searchKeyWordPosition = "";

    @JsonAlias("search_key_word_source")
    private String searchKeyWordSource = "";

    @JsonAlias("search_key_word_type")
    private String searchKeyWordType = "";

    @JsonAlias("search_position_rule")
    private String searchPositionRule = "";

    @JsonAlias("search_result_position")
    private String searchResultPosition = "";

    @JsonAlias("search_result_type")
    private String searchResultType = "";

    @JsonAlias("search_source")
    private String searchSource = "";

    @JsonAlias("trade_tab_id")
    private String tradeTabId = "";

    @JsonAlias("trade_tab_title")
    private String tradeTabTitle = "";

    @JsonAlias("trade_type")
    private String tradeType = "";

    @JsonAlias("venue_id")
    private String venueId = "";

    @JsonAlias("payment_method")
    private String paymentMethod;

    @JsonAlias("if_success")
    private String ifSuccess;

    @JsonAlias("app_list")
    private String appList;

    @JsonAlias("smart_menu_id")
    private String smartMenuId;

    @JsonAlias("smart_menu_content_type")
    private String smartMenuContentType;

    @JsonAlias("receive_time")
    private Long receiveTime;

    @JsonAlias("duration")
    private Long duration;

    @JsonAlias("is_up")
    private Long isUp;

    @JsonAlias("is_back")
    private Long isBack;

    @JsonAlias("acm")
    private String acm;
}
