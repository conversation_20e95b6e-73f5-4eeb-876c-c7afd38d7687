package com.poizon.userportrait.kafkadim;


import com.poizon.userportrait.kafkadim.entity.SensorsActionEvent;
import com.poizon.userportrait.kafkadim.entity.SensorsDealLog;
import com.poizon.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/3/18
 */
@Slf4j
public class SensorsDealLogParserUDTF extends TableFunction<Row> {
    SensorsDealLog sensorsDealLog = null;
    public void eval(String input) {

            try {
                 sensorsDealLog = JsonUtil.readValue(input, SensorsActionEvent.class).toSensorsDealAction();
            } catch (Throwable e) {
                System.out.println("log:"+input +", msg: "+ e.getStackTrace());
                e.printStackTrace();
                log.error("[SensorsDealLogParserUDTF] parser failed, log: {}, msg:{}", input, e.getStackTrace());
            }

            if (sensorsDealLog != null) {
                Row row = new Row(70);
                row.setField(0, sensorsDealLog.getUserId());
                row.setField(1, sensorsDealLog.getTime());
                row.setField(2, sensorsDealLog.getEvent());
                row.setField(3, sensorsDealLog.getCurrentPage());
                row.setField(4, sensorsDealLog.getCurrentPageTitle());
                row.setField(5, sensorsDealLog.getBlockType());
                row.setField(6, sensorsDealLog.getBlockTypeTitle());
                row.setField(7, sensorsDealLog.getJumpType());
                row.setField(8, sensorsDealLog.getJumpContentId());
                row.setField(9, sensorsDealLog.getOs());
                row.setField(10, sensorsDealLog.getWifi());
                row.setField(11, sensorsDealLog.getNetworkType());
                row.setField(12, sensorsDealLog.getIp());
                row.setField(13, sensorsDealLog.getAppVersion());
                row.setField(14, sensorsDealLog.getSpuId());
                row.setField(15, sensorsDealLog.getAlgorithmProductPropertyValue());
                row.setField(16, sensorsDealLog.getTargetSpuId());
                row.setField(17, sensorsDealLog.getAlgorithmTargetSpuPropertyValue());
                row.setField(18, sensorsDealLog.getAlgorithmChannelId());
                row.setField(19, sensorsDealLog.getAlgorithmRequestId());
                row.setField(20, sensorsDealLog.getSkuId());
                row.setField(21, sensorsDealLog.getCspuId());
                row.setField(22, sensorsDealLog.getTargetCspuId());
                row.setField(23, sensorsDealLog.getCategoryId());
                row.setField(24, sensorsDealLog.getBrandId());
                row.setField(25, sensorsDealLog.getArticleNum());
                row.setField(26, sensorsDealLog.getColor());
                row.setField(27, sensorsDealLog.getSeriesId());
                row.setField(28, sensorsDealLog.getFitId());
                row.setField(29, sensorsDealLog.getCity());
                row.setField(30, sensorsDealLog.getProvince());
                row.setField(31, sensorsDealLog.getCountry());
                row.setField(32, sensorsDealLog.getSearchKeyWord());
                row.setField(33, sensorsDealLog.getRecommendContentInfoList());
                row.setField(34, sensorsDealLog.getJumpContentUrl());
                row.setField(35, sensorsDealLog.getDeviceId());
                row.setField(36, sensorsDealLog.getContentId());
                row.setField(37, sensorsDealLog.getContentType());
                row.setField(38, sensorsDealLog.getContentUrl());
                row.setField(39, sensorsDealLog.getFeedbackContentType());
                row.setField(40, sensorsDealLog.getFeedbackType());
                row.setField(41, sensorsDealLog.getBlockContentId());
                row.setField(42, sensorsDealLog.getBlockContentPosition());
                row.setField(43, sensorsDealLog.getPosition());
                row.setField(44, sensorsDealLog.getSearchFilterInfoList());
                row.setField(45, sensorsDealLog.getSearchKeyWordPosition());
                row.setField(46, sensorsDealLog.getSearchKeyWordSource());
                row.setField(47, sensorsDealLog.getSearchKeyWordType());
                row.setField(48, sensorsDealLog.getSearchPositionRule());
                row.setField(49, sensorsDealLog.getSearchResultPosition());
                row.setField(50, sensorsDealLog.getSearchResultType());
                row.setField(51, sensorsDealLog.getSearchSource());
                row.setField(52, sensorsDealLog.getTradeTabId());
                row.setField(53, sensorsDealLog.getTradeTabTitle());
                row.setField(54, sensorsDealLog.getTradeType());
                row.setField(55, sensorsDealLog.getVenueId());
                row.setField(56, sensorsDealLog.getPaymentMethod());
                row.setField(57, sensorsDealLog.getIfSuccess());
                row.setField(58, sensorsDealLog.getProperties());
                row.setField(59, sensorsDealLog.getAppList());
                row.setField(60, sensorsDealLog.getSmartMenuId());
                row.setField(61, sensorsDealLog.getSmartMenuContentType());
                row.setField(62, sensorsDealLog.getMinPrice());
                row.setField(63, sensorsDealLog.getLevel1CategoryId());
                row.setField(64, sensorsDealLog.getFlushTime());
                row.setField(65, sensorsDealLog.getReceiveTime());

                row.setField(66, sensorsDealLog.getIsUp());
                row.setField(67, sensorsDealLog.getIsBack());
                row.setField(68, sensorsDealLog.getEventPageBlock());
                row.setField(69, sensorsDealLog.getAcm());
                collect(row);
            }else{
                System.out.println("数据失败");
            }

    }

    private DataType[] setResultType() {
        return new DataType[]{
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
                DataTypes.STRING(),
//                flushtime
                DataTypes.BIGINT(),
                DataTypes.BIGINT(),
//                isup
                DataTypes.BIGINT(),
                DataTypes.BIGINT(),
                DataTypes.STRING(),
//                acm
                DataTypes.STRING()

        };
    }
    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    DataType[] outputDataTypes = setResultType();
                    return Optional.of(DataTypes.ROW(outputDataTypes));
                })
                .build();
    }

}
