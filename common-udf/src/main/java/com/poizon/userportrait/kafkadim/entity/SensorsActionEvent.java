package com.poizon.userportrait.kafkadim.entity;

import com.poizon.utils.JsonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonAlias;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2020/8/8 10:51 上午
 * @description： 神策event
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
@Slf4j
public class SensorsActionEvent {
    private static final long serialVersionUID = -2400517705823565893L;
    @JsonIgnore
    private final ObjectMapper mapper = new ObjectMapper();
    @JsonAlias("login_id")
    private String loginId = "";

    @JsonAlias("_track_id")
    private String trackId;

    @JsonAlias("distinct_id")
    private String userId;

    @JsonAlias("_flush_time")
    private Long flushTime;

    @JsonAlias("time")
    private Long time;

    @JsonAlias("event")
    private String event;

    @JsonAlias("event_page_block")
    private String eventPageBlock;

    @JsonAlias("properties")
    private Properties properties = new Properties();

    @JsonAlias("type")
    private String type;

    @JsonAlias("map_id")
    private String mapId;

    public static final String DEAL_RCMD_FEED_CLICK = "trade_recommend_feed_click";

    public SensorsDealLog toSensorsDealAction() throws InvocationTargetException, IllegalAccessException, IOException {
        SensorsDealLog sensorsDealAction = new SensorsDealLog();
        if (StringUtils.isNotBlank(properties.getDw_userId())) {
            sensorsDealAction.setUserId(properties.getDw_userId());
        } else {
            sensorsDealAction.setUserId(this.userId);
        }
        sensorsDealAction.setTime(this.time == null ? "" : this.time.toString());
        sensorsDealAction.setEvent(this.event);
        sensorsDealAction.setCurrentPage(properties.getCurrentPage());
        sensorsDealAction.setCurrentPageTitle(properties.getCurrentPageTitle());
        sensorsDealAction.setBlockType(properties.getBlockType());
        sensorsDealAction.setBlockTypeTitle(properties.getBlockTypeTitle());
        sensorsDealAction.setJumpType(properties.getJumpType());
        sensorsDealAction.setJumpContentId(properties.getJumpContentId());
        sensorsDealAction.setOs(properties.getOs());
        sensorsDealAction.setWifi(properties.getWifi().toString());
        sensorsDealAction.setNetworkType(properties.getNetworkType());
        sensorsDealAction.setIp(properties.getIp());
        sensorsDealAction.setAppVersion(properties.getAppVersion());
        sensorsDealAction.setSpuId(properties.getSpuId());
        sensorsDealAction.setAlgorithmProductPropertyValue(properties.getAlgorithmProductPropertyValue());
        sensorsDealAction.setTargetSpuId(properties.getTargetSpuId());
        sensorsDealAction.setAlgorithmTargetSpuPropertyValue(properties.getAlgorithmTargetSpuPropertyValue());
        sensorsDealAction.setAlgorithmChannelId(properties.getAlgorithmChannelId());
        sensorsDealAction.setAlgorithmRequestId(properties.getAlgorithmRequestId());
        sensorsDealAction.setSkuId(properties.getSkuId());
        sensorsDealAction.setProvince(properties.getProvince());
        sensorsDealAction.setCity(properties.getCity());
        sensorsDealAction.setCountry(properties.getCountry());
        sensorsDealAction.setSearchKeyWord(properties.getSearchKeyWord());
        sensorsDealAction.setRecommendContentInfoList(properties.getRecommendContentInfo());
        sensorsDealAction.setJumpContentUrl(properties.getJumpContentUrl());
        sensorsDealAction.setDeviceId(properties.getDeviceId());
        sensorsDealAction.setContentId(properties.getContentId());
        sensorsDealAction.setContentType(properties.getContentType());
        sensorsDealAction.setContentUrl(properties.getContentUrl());
        sensorsDealAction.setFeedbackContentType(properties.getFeedbackContentType());
        sensorsDealAction.setFeedbackType(properties.getFeedbackType());
        sensorsDealAction.setBlockContentId(properties.getBlockContentId());
        sensorsDealAction.setBlockContentPosition(properties.getBlockContentPosition());
        sensorsDealAction.setPosition(properties.getPosition());
        sensorsDealAction.setSearchFilterInfoList(properties.getSearchFilterInfoList());
        sensorsDealAction.setSearchKeyWordPosition(properties.getSearchKeyWordPosition());
        sensorsDealAction.setSearchKeyWordSource(properties.getSearchKeyWordSource());
        sensorsDealAction.setSearchKeyWordType(properties.getSearchKeyWordType());
        sensorsDealAction.setSearchPositionRule(properties.getSearchPositionRule());
        sensorsDealAction.setSearchResultPosition(properties.getSearchResultPosition());
        sensorsDealAction.setSearchResultType(properties.getSearchResultType());
        sensorsDealAction.setSearchSource(properties.getSearchSource());
        sensorsDealAction.setTradeTabId(properties.getTradeTabId());
        sensorsDealAction.setTradeTabTitle(properties.getTradeTabTitle());
        sensorsDealAction.setTradeType(properties.getTradeType());
        sensorsDealAction.setVenueId(properties.getVenueId());
        sensorsDealAction.setIfSuccess(properties.getIfSuccess());
        sensorsDealAction.setPaymentMethod(properties.getPaymentMethod());
        sensorsDealAction.setFlushTime(this.flushTime);
        sensorsDealAction.setReceiveTime(properties.getReceiveTime());
        sensorsDealAction.setIsUp(properties.getIsUp());
        sensorsDealAction.setIsBack(properties.getIsBack());
        sensorsDealAction.setEventPageBlock(this.eventPageBlock);
        sensorsDealAction.setAcm(properties.getAcm());

        try {
            sensorsDealAction.setProperties(mapper.writeValueAsString(this.properties));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        sensorsDealAction.setAppList(properties.getAppList());
        sensorsDealAction.setSmartMenuId(properties.getSmartMenuId());
        sensorsDealAction.setSmartMenuContentType(properties.getSmartMenuContentType());
        //将点击trade_recommend_feed_click，list单条数据打平
        if (DEAL_RCMD_FEED_CLICK.equals(this.event)) {
            String recommendContentInfoMessage = properties.getRecommendContentInfo();
            RecommendContentInfo recommendContentInfo = null;
            if (recommendContentInfoMessage.startsWith("[")) {
                List<RecommendContentInfo> recommendContentInfoList = JsonUtil.readValue(recommendContentInfoMessage, JsonUtil.getCollectionType(List.class, RecommendContentInfo.class));
                if (!recommendContentInfoList.isEmpty()) {
                    recommendContentInfo = recommendContentInfoList.get(0);
                }
            } else if (recommendContentInfoMessage.startsWith("{")){
                recommendContentInfo = JsonUtil.readValue(recommendContentInfoMessage, RecommendContentInfo.class);
            }
            if (null != recommendContentInfo) {
                sensorsDealAction.setContentId(recommendContentInfo.getContentID());
                sensorsDealAction.setContentType(recommendContentInfo.getContentType());
                sensorsDealAction.setAlgorithmChannelId(recommendContentInfo.getChannel());
                sensorsDealAction.setAlgorithmRequestId(recommendContentInfo.getRequestID());
                sensorsDealAction.setAlgorithmProductPropertyValue(recommendContentInfo.getPropertyValueId());
                if ("0".equals(recommendContentInfo.getContentType())) {
                    sensorsDealAction.setSpuId(recommendContentInfo.getContentID());
                }
            }
        }
        return sensorsDealAction;
    }
}
