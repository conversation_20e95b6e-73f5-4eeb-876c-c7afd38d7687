package com.poizon.userportrait.kafkadim.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：2020/8/16 8:38 下午
 * @description：
 */
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class RecommendContentInfo implements Serializable {
    private static final long serialVersionUID = -509598799420657338L;

    private String contentType = "0";

    private String position;

    private String contentID = "";

    private String propertyValueId = "";

    private String channel;

    private String requestID;

    private String cspuId;

    private String categoryId;
    private String brandId;
    private String articleNum;
    private String color;
    private String SeriesId;
    private String fitId;

    private String minPrice;
    private String level1CategoryId;

}
