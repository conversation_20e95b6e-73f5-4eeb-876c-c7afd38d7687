package com.poizon.userportrait.udaf;

import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.functions.AggregateFunction;

public class MaxValueWithTimestamp extends AggregateFunction<String, Tuple2<Double, String>> {

    @Override
    public String getValue(Tuple2<Double, String> doubleLongTuple2) {
        return doubleLongTuple2.f1;
    }

    @Override
    public Tuple2<Double, String> createAccumulator() {
        return Tuple2.of(0.0, "");
    }

    public void accumulate(Tuple2<Double, String> accumulator, Double value, String time) {
        if (value > accumulator.f0) {
            accumulator.f0 = value;
            accumulator.f1 = time;
        }
    }

    public void merge(Tuple2<Double, String> accumulator, Iterable<Tuple2<Double, String>> its) {
        for (Tuple2<Double, String> it : its) {
            if (it.f0 > accumulator.f0) {
                accumulator.f0 = it.f0;
                accumulator.f1 = it.f1;
            }
        }

    }
}
