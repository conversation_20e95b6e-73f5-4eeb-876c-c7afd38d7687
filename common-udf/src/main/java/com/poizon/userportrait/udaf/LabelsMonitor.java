package com.poizon.userportrait.udaf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple5;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.functions.FunctionContext;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.LinkedHashMap;

import static com.util.CompressUtils.decompress;

@Slf4j
public class LabelsMonitor extends AggregateFunction<HashMap<String, BigDecimal>, Tuple5<Integer, Integer, Integer, Integer, Short>> {

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
    }

    //      标签值小于1的用户数   map长度达到40的用户数   标签为null的用户数  总用户数
    @Override
    public Tuple5<Integer, Integer, Integer, Integer, Short> createAccumulator() {
        return Tuple5.of(0, 0, 0, 0, new Short("0"));
    }

    @Override
    public HashMap<String, BigDecimal> getValue(Tuple5<Integer, Integer, Integer, Integer, Short> acc) {
        HashMap<String, BigDecimal> map = new HashMap<>();
        BigDecimal lessThen = new BigDecimal(acc.f0).divide(new BigDecimal(acc.f3), 4, RoundingMode.HALF_DOWN);
        BigDecimal lengthLimit = new BigDecimal(acc.f1).divide(new BigDecimal(acc.f3), 4, RoundingMode.HALF_DOWN);
        BigDecimal nullValue = new BigDecimal(acc.f2).divide(new BigDecimal(acc.f3), 4, RoundingMode.HALF_DOWN);
        map.put("length" + acc.f4 + "andMin>1", lessThen);
        map.put("lengthEqualLimit", lengthLimit);
        map.put("nullOrEmptyValue", nullValue);
        return map;
    }

    //     acc 小于1的   长度40的  null值的  user总数
    public void accumulate(Tuple5<Integer, Integer, Integer, Integer, Short> accumulator, String newValue, int limit) {
        accumulator.f4 = (short) limit;
        accumulator.f3 += 1;
        if (StringUtils.isEmpty(newValue)) {
            accumulator.f2 += 1;
            return;
        }
        try {
            Object decompress = decompress(newValue);
            if (decompress instanceof LinkedHashMap) {
                LinkedHashMap<String, Number> map = (LinkedHashMap<String, Number>) decompress;
                if (!map.isEmpty()) {
                    if (map.size() == limit) {
                        accumulator.f1 += 1;
                        double min = map.values().parallelStream().mapToDouble(Number::doubleValue).min().getAsDouble();
                        if (min > 1) {
                            accumulator.f0 += 1;
                        }
                    }
                } else {
                    accumulator.f2 += 1;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public void merge(Tuple5<Integer, Integer, Integer, Integer, Short> accumulator, Iterable<Tuple5<Integer, Integer, Integer, Integer, Short>> its) {
        for (Tuple5<Integer, Integer, Integer, Integer, Short> other : its) {
            accumulator.f0 += other.f0;
            accumulator.f1 += other.f1;
            accumulator.f2 += other.f2;
            accumulator.f3 += other.f3;
        }
    }
}
