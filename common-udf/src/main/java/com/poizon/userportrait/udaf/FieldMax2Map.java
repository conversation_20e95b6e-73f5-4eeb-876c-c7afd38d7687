package com.poizon.userportrait.udaf;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.AggregateFunction;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udaf
 * @date Date  2023-04-19 21:38
 * @Description :TODO
 */
@Slf4j
public class FieldMax2Map extends AggregateFunction<Map<String, Long>, Map<String, Long>> {
    @Override
    public Map<String, Long> getValue(Map<String, Long> acc) {
        return acc;
    }

    @Override
    public Map<String, Long> createAccumulator() {
        return new HashMap<>();
    }

    //不同的field的最大值聚合成map 方便转为多列
    public void accumulate(Map<String, Long> accumulator, String field, Long value) {
        if (value == null) return;
        Long orDefault = accumulator.getOrDefault(field, 0L);
        if (value >= orDefault) {
            accumulator.put(field, value);
        }
    }

    public void retract(Map<String, Long> acc, String field, Long value) {
    }


    public void merge(Map<String, Long> accumulator, Iterable<Map<String, Long>> its) {
        for (Map<String, Long> other : its) {
            for (Map.Entry<String, Long> entry : other.entrySet()) {
                if (entry.getValue() > accumulator.getOrDefault(entry.getKey(), 0L)) {
                    accumulator.put(entry.getKey(), entry.getValue());
                }
            }
        }
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .accumulatorTypeStrategy(callContext -> Optional.of(DataTypes.MAP(DataTypes.STRING(), DataTypes.BIGINT())))
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.MAP(DataTypes.STRING(), DataTypes.BIGINT()))).build();
    }
}
