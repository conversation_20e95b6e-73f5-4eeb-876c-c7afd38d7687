package com.poizon.userportrait.udf;

import org.apache.flink.table.functions.ScalarFunction;

import java.util.regex.Pattern;

public class QueryNorm extends ScalarFunction {

    static final Pattern pattern = Pattern.compile("[^\u3400-\u9FEFa-zA-Z0-9 ]");

    public String eval(String query) {
        if (query == null || query.isEmpty()) {
            return "";
        }

        query = pattern.matcher(query).replaceAll("").trim().toLowerCase();

        return query;
    }

}
