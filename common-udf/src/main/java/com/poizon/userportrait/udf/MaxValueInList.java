package com.poizon.userportrait.udf;

import com.google.common.collect.Lists;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Collections;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2023-05-25 21:29
 * @Description :TODO
 */

@FunctionHint(
        input = @DataTypeHint("ARRAY<STRING>"),
        output = @DataTypeHint("BIGINT")
)
public class MaxValueInList extends ScalarFunction {
    public Long eval(String[] input) {
        if (input == null || input.length == 0) {
            return null;
        }

        return Long.parseLong(Collections.max(Lists.newArrayList(input)));
    }
}