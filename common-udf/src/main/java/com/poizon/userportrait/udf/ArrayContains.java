package com.poizon.userportrait.udf;

import com.google.common.collect.Lists;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2023-05-30 21:45
 * @Description :TODO
 */

public class ArrayContains extends ScalarFunction {
    public Boolean eval(String[] input, String e) {
        if (input == null || input.length == 0) {
            return false;
        }
        ArrayList<String> list = Lists.newArrayList(input);
        return list.contains(e);
    }
}
