package com.poizon.userportrait.udf;

import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Arrays;

public class SplitStr extends ScalarFunction {
    public String eval(String str, String regex, String separator, int limit) {
        if (StringUtils.isEmpty(str)) return "";
        String[] split = str.split(regex);
        if (split.length > limit) {
            return StringUtils.join(Arrays.asList(split)
                                          .subList(0, limit), separator);
        } else {
            return StringUtils.join(Arrays.asList(split), separator);
        }
    }
}

