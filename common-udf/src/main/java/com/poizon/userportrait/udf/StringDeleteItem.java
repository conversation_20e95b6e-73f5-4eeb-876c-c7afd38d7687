package com.poizon.userportrait.udf;


import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING"),@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)

public class StringDeleteItem extends ScalarFunction {
    public String eval(String str, String regex, String item) {
        if (StringUtils.isNotEmpty(str) && StringUtils.isNotEmpty(regex) && StringUtils.isNotEmpty(item)) {
            final String[] split = str.split(regex);
            List<String> list = new ArrayList<>(split.length);
            Collections.addAll(list,split);
            list.remove(item);
            return StringUtils.join(list, regex);
        }
        return str;
    }
}