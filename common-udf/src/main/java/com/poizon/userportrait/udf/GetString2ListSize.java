package com.poizon.userportrait.udf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class GetString2ListSize extends ScalarFunction{
    public int eval(String str, String regex) {
        if (str != null && regex != null) {
            return str.split(regex).length;
        } else return 1;
    }
}
