package com.poizon.userportrait.udf;

import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashSet;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2023-05-29 21:04
 * @Description :TODO
 */

public class ArrayStr2SetStr extends ScalarFunction {
    public String eval(String input,String regex) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        String[] split = input.split(regex);
        HashSet<String> set = Sets.newHashSet(split);
        return StringUtils.join(set, regex);
    }
}
