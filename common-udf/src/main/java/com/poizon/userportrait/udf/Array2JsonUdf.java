package com.poizon.userportrait.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: Array2JsonUdf
 * @Description:
 * @author: scl
 * @date: 2022/4/19  11:53 上午
 */
@FunctionHint(
		input = {@DataTypeHint("STRING"), @DataTypeHint("DECIMAL(12, 3)")},
		output = @DataTypeHint("MAP<STRING, DOUBLE>"))
public class Array2JsonUdf extends ScalarFunction {

	public Map<String,Double> eval(String input, BigDecimal decimal){

		Map<String,Double> hashMap  = new HashMap<>();
		try {
			JSONArray objects = JSONObject.parseArray(input.replaceAll("\\\\",""));

			for(Object row: objects) {
				if (row != null) {
					hashMap.put(String.valueOf(row),decimal.doubleValue());
				}
			}

		} catch (Throwable e) {
			System.out.println("log:"+input +", msg: "+ e.getStackTrace());
		}
		return  hashMap;
	}


	public static void main(String[] args) {
		Array2JsonUdf array2JsonUdf = new Array2JsonUdf();
		String str = "[\"2\",\"16\"]";
		Map<String, Double> eval = array2JsonUdf.eval(str, BigDecimal.valueOf(0.01d));
		System.out.println(JSONObject.toJSONString(eval));

	}
}
