package com.poizon.userportrait.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;


/**
 * <AUTHOR> wangkang
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2024-04-18
 * @Description :TODO
 */

@Slf4j
public class JsonArrayGet extends ScalarFunction {
    public String eval(String input, String k, String v) {
        try {
            if (input == null || input.isEmpty()) {
                return null;
            }

            JSONArray jsonArray = JSONArray.parseArray(input);
            // 遍历JSONArray并访问其中的元素
            for (Object obj : jsonArray) {
                JSONObject jsonObj = (JSONObject) obj; // 将object转换为JSONObject
                String value = jsonObj.getString(k);
                if (value.equals(v)) return obj.toString();
            }
            return null;
        } catch (Exception e) {
            log.error("JsonArrayGet Exception. input:{},k:{}v:{}", input, k, v, e);
        }
        return null;
    }

    public String eval(String input, Integer index) {
        try {
            if (input == null || input.isEmpty()) {
                return null;
            }

            JSONArray jsonArray = JSONArray.parseArray(input);

            if (index >= jsonArray.size() || index < 0) return null;
            return jsonArray.getString(index);
        } catch (Exception e) {
            log.error("JsonArrayGet Exception. input:{},index:{}", input, index, e);
        }
        return null;
    }


    public String eval(String input, String... kvs) {
        try {
            if (StringUtils.isEmpty(input) || kvs.length <= 1 || kvs.length % 2 != 0) {
                return null;
            }

            JSONArray jsonArray = JSONArray.parseArray(input);
            // 遍历JSONArray并访问其中的元素
            for (Object obj : jsonArray) {
                JSONObject jsonObj = (JSONObject) obj; // 将object转换为JSONObject
                boolean flag = true;
                for (int i = 0; i < kvs.length / 2; i++) {
                    String k = kvs[2 * i];
                    String v = kvs[2 * i + 1];
                    String value = jsonObj.getString(k);
                    if (!v.equals(value)) {
                        flag = false;
                        break;
                    }
                }
                if (flag) return obj.toString();
            }
            return null;
        } catch (Exception e) {
            log.error("JsonArrayGet Exception. input:{},kvs:{}", input, kvs, e);
        }
        return null;
    }

}
