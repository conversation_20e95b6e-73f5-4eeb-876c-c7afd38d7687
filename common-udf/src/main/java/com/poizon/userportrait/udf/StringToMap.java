package com.poizon.userportrait.udf;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.java.typeutils.runtime.kryo.KryoSerializer;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.data.binary.BinaryStringData;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
//blink无法直接读取odps Map数据类型 在odps端转为jsonStr 之后作为varchar读入
public class StringToMap extends ScalarFunction{

    public Map<Object,Integer> eval(String json) {
        try {
            HashMap<Object,Integer> hashMap = JSON.parseObject(json, HashMap.class);
            return hashMap;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("检查数据,有不符合json的数据,{}",json);
        }
        return new HashMap<>();
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    KryoSerializer<Object> serializer = new KryoSerializer<>(Object.class, new ExecutionConfig());
                    DataType objectType = DataTypes.RAW(Object.class, serializer);
                    return Optional.of(DataTypes.MAP(objectType, DataTypes.INT()));
                }).build();

    }
}
