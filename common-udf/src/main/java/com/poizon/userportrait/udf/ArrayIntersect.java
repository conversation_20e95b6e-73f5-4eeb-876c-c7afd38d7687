package com.poizon.userportrait.udf;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2023-05-23 21:21
 * @Description :TODO
 */

public class ArrayIntersect extends ScalarFunction {
    public List<String> eval(String left, String right, String delimiter) {
        if (StringUtils.isEmpty(left) || StringUtils.isEmpty(right)) {
            return Lists.newArrayList();
        }else {
            ArrayList<String> leftList = Lists.newArrayList(left.split(delimiter));
            ArrayList<String> rightList = Lists.newArrayList(right.split(delimiter));
           return (ArrayList)CollectionUtils.intersection(leftList,rightList);
        }
    }
}
