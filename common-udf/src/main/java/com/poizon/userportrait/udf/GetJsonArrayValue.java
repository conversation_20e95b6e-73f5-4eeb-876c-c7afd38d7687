package com.poizon.userportrait.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class GetJsonArrayValue extends ScalarFunction {

    private static final Logger log = LoggerFactory.getLogger(GetJsonArrayValue.class);

    public String eval(String string, String field) {
        if (StringUtils.isNotEmpty(string) || StringUtils.isNotEmpty(field)) {
            try {
                if (string.trim().startsWith("[")) {
                    JSONArray jsonArray = JSON.parseArray(string);
                    StringBuilder result = new StringBuilder();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = (JSONObject) jsonArray.get(i);
                        String item = jsonObject.getString(field);
                        if (StringUtils.isNotEmpty(item) && i != jsonArray.size() - 1) {
                            result.append(item).append(",");
                        } else {
                            result.append(item);
                        }
                    }
                    return result.toString();
                }
                if (string.trim().startsWith("{")) {
                    JSONObject jsonObject = JSONObject.parseObject(string);
                    String item = jsonObject.getString(field);
                    return item != null ? item : "";
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("数据解析失败,原始数据{},输入字段:{}", string, field);
            }
        }
        return "";
    }

    public static void main(String[] args) {
        String cspuId = new GetJsonArrayValue().eval("[{\"spu_id\":66651,\"spu_type\":0}]", "spu_id");
        System.out.println(cspuId);

    }


}
