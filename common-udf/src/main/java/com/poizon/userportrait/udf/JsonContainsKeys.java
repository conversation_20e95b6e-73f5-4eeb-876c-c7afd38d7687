package com.poizon.userportrait.udf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Collections;

/**
 * <AUTHOR> zhuxingming
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2024-01-11 14:22
 * @Description :TODO
 */
@Slf4j
public class JsonContainsKeys extends ScalarFunction {
    public boolean eval(String input,String... fields) {
        if (StringUtils.isEmpty(input)){
            return false;
        }
        try {
            if (input.trim().startsWith("{")) {
                JSONObject jsonObject = JSONObject.parseObject(input);
                for (String field : fields) {
                    if (jsonObject.containsKey(field)) {
                        return true;
                    }
                }
            }
            if (input.trim().startsWith("[")) {
                JSONArray jsonArray = JSONObject.parseArray(input);
                for (Object o : jsonArray) {
                    JSONObject jsonObject = (JSONObject) o;
                    for (String field : fields) {
                        if (jsonObject.containsKey(field)) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("数据解析错误:{}",input);
        }
        return false;
    }
}
