package com.poizon.entity;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class UserPortrait {
    private static final long serialVersionUID = 1L;
    @JsonProperty("ui")
    private String userId;

    /**
     * 购买列表
     */
    @JsonProperty("buy")
    private List<String> buyList;


    /**
     * 支付成功列表
     */
    @JsonProperty("payl")
    private List<String> payList;

    /**
     * 支付成功 标签
     */
    @JsonProperty("pay")
    private Map<String,Long> pay;

    /**
     * 立即购买
     */
    @JsonProperty("buyn")
    private List<String> buyNowList;

    /**
     * 点击求购
     */
    @JsonProperty("wb")
    private List<String> wantBuyList;

    /**
     * 点击求购
     */

    /**
     * 收藏列表
     */
    @JsonProperty("col")
    private List<String> collectList;

    @JsonProperty("col_epd")
    private List<String> collectExpandList;

    /**
     * 取消收藏列表
     */
    @JsonProperty("uncol")
    private List<String> unCollectList;

    /**
     * 偏好商品字典
     */
    @JsonProperty("fi")
    private Map<String, Float> favourItemId;

    /**
     * 偏好品牌字典
     */
    @JsonProperty("fb")
    private Map<String, Float> favourBrandId;

    /**
     * 偏好品牌列表
     */
    @JsonProperty("fblist")
    private List<String> favourBrandIdList;

    /**
     * 偏好类目字典
     */
    @JsonProperty("fc")
    private Map<String, Float> favourCategoryId;

    /**
     * 偏好类目列表
     */
    @JsonProperty("fclist")
    private List<String> favourCategoryIdList;

    /**
     * 偏好颜色字典
     */
    @JsonProperty("fclr")
    private Map<String, Float> favourColor;

    /**
     * 偏好系列
     */
    @JsonProperty("fs")
    private Map<String, Float> favourSeriesId;


    @JsonProperty("sehkeylist")
    private List<String> searchKeyList;



    /**
     * 偏好货号 article num列表 Map<ArticleNum,score>, score值按照次数距离进行衰减(衰减因子0.8)，保留 score Top20
     */
    @JsonProperty("fa")
    private Map<String, Float> favourArticleNum;

    /**
     * 交易 strategy weight
     */
    @JsonProperty("sw")
    private Map<String, Float> strategyWeight;

    /**
     * 点击列表 Map<String,Float>
     */
    @JsonProperty("clk")
    private LinkedHashMap<String, Integer> clickMap;

    @JsonProperty("clk_epd")
    private LinkedHashMap<String, Integer> clickExpandMap;

    /**
     * 点击列表（可衰减） Map<String, Float>
     */
    @JsonProperty("clkn")
    private Map<String, Float> clickNewMap;

    @JsonProperty("fmixpi")
    private Map<String, Float> favMixPi;
    /**
     * 点击列表List<String>
     */
    @JsonProperty("clklist")
    private List<String> clickList;

    /**
     * 主题id 列表
     */
    @JsonProperty("thi")
    private List<String> themeId;

    /**
     * 主题id 点击数
     */
    @JsonProperty("thm")
    private Map<String, Float> themeMap;

    /**
     * 分享列表
     */
    @JsonProperty("shrlist")
    private List<String> shareList;
    /**
     * 不感兴趣的商品集合
     */
    @JsonProperty("unii")
    private List<String> uninterestedItemList;

    /**
     * 不感兴趣的主题集合
     */
    @JsonProperty("unti")
    private List<String> uninterestedThemeList;

    /**
     * 搜索list
     */
    @JsonProperty("searchlist")
    private List<String> searchList;

    /**
     * 搜索商品点击列表
     */
    @JsonProperty("sehclklist")
    private List<String> searchClickList;

    /**
     * 偏好性别
     */
    @JsonProperty("ss" )
    private Float ss;


    /**
     * 一级类目性别偏好, 男女款偏好比例，>0.5男款，<0.5女款
     */
    @JsonProperty("css")
    private Map<String, Float> categorySexScore;

    /**
     * 图文点击数
     */
    @JsonProperty("citcc")
    private Float imageTrendClickCount;

    /**
     * 视频点击数
     */
    @JsonProperty("cvtcc")
    private Float videoTrendClickCount;


    /**
     * 视频点击数
     */
    @JsonProperty("clsi")
    private List<String> liveStreamIdList;


    /**
     * 专栏点击数
     */
    @JsonProperty("cpcc")
    private Float postClickCount;

    /**
     *
     */
    @JsonProperty("fvr")
    private Double favVideoRatio;
    /**
     * 动态点击列表
     */
    @JsonProperty("tclk")
    private List<String> trendClickList;

    /**
     * 动态点击列表
     */
    @JsonProperty("pclk")
    private List<String> postClickList;

    /**
     * 专栏点击列表
     */
    @JsonProperty("plike")
    private List<String> postLikeList;

    /**
     * 动态点击列表
     */
    @JsonProperty("tlike")
    private List<String> trendLikeList;

    /**
     * 动态分享列表
     */
    @JsonProperty("tshr")
    private List<String> trendShareList;

    @JsonProperty("pshr")
    private List<String> postShareList;

    @JsonProperty("fauth")
    private Map<String, Float> favAuthor;

    @JsonProperty("fnewc")
    private Map<String, Float> favNewCommunityCategory;

    @JsonProperty("fpi")
    private Map<String, Float> favProductId;

    @JsonProperty("femalecc")
    private Float femaleClickCount;

    @JsonProperty("fssc")
    private Float favSexyClickCount;

    @JsonProperty("fss")
    private Float favSexyClickScore;

    @JsonProperty("female")
    private Double female;

    @JsonProperty("cw")
    private Map<String, Float> channelWeight;

    @JsonProperty("fba")
    private List<String> feedBackAuthor;

    @JsonProperty("fbncc")
    private Map<String, Float> feedBackNewCategoryCount;

    @JsonProperty("fbncl")
    private List<String> feedBackNewCategoryList;

    @JsonProperty("fbsc")
    private Map<String, Float> feedBackSexCount;

    @JsonProperty("fbsl")
    private List<String> feedBackSexList;

    @JsonProperty("ft3")
    private Map<String, Float> favTag3;

    @JsonProperty("cspu")
    private Map<String, Float> cspuIds;


    @JsonProperty("ccnc")
    private List<String> communityNewCategoryList;

    /**
     * 圈子点击
     */
    @JsonProperty("circle")
    private Map<String, Float> favourCircle;

    // #############################################社区实时字段#####################################################

    /**
     * 用户画像在redis中的过期时间
     */
    @JsonProperty("ep")
    private Long expire;


    private String refreshCode;
    /**
     * 社区 实时字段过期时间 位: 秒
     */
    @JsonProperty("crtce")
    private Long communityRealTimeColumnExpire;

    /**
     * 交易 实时字段过期时间 单位: 秒
     */
    @JsonProperty("drtce")
    private Long dealRealTimeColumnExpire;


    @JsonIgnore
    private Boolean isNeedClean = false;

    @JsonIgnore
    private Boolean isNone = true;

    @JsonIgnore
    private Long timestamp;


//=================新加的字段=========
    /**
     * 一级类目性别偏好, 点击男女款偏好，仅衰减不过期
     */
    /**
     * 短期偏好类目列表
     */
//    @Scope(value = {"deal"})
    @JsonProperty("fsclist")
    private List<String> favourShortCategoryIdList;

    @JsonProperty("t_dca")
    private Map<String, Map<String, Integer>> dealLevel1CategoryThresholdCountMap;

    @JsonProperty("l1c_coupon_t")
    private Map<String, Integer> level1CategoryCouponThresholdMap;

    //    用户聚类
//    @Scope(value = {"com"})
    @JsonProperty("cluster")
    private String clusterId;
    /**
     * 用户现在applist 探索
     */
//    @Scope(value = {"com"})
    @JsonProperty("faveel")
    private List<String> favExploreList;

    /**
     * 相似用户id
     */
//    @Scope(value = {"deal"})
    @JsonProperty("suids")
    private List<String> similarUserIds;

    /**
     * 聚类id
     * es中这个类型是int的，但是实时侧用的String类型
     * 这里兼容下，字段设置为String类型的
     */
    @JsonProperty(value = "t_clu")
    private String clusterLabelId;

    @JsonProperty(value = "sex")
    private Integer gender;

    @JsonProperty("dealv")
    private Long dealVersion;

    @JsonProperty("comv")
    private Long comVersion;

    @JsonProperty("favvt")
    private Map<String, Float> favVideoTimeMap;

    //1、点击最近购买列表
    @JsonProperty("rbuyl")
    private List<String> recentBuyList;
    //2、点击评论列表，长度 30
    @JsonProperty("cmtl")
    private List<String> commentList;
    //3、点击商详图片列表
    @JsonProperty("phtl")
    private List<String> pictureList;
    //4、点击穿搭精选列表
    @JsonProperty("pickl")
    private List<String> pickList;

    //5、访问商品列表（访问页面时长大于3s)
    @JsonProperty("gdsl")
    private List<String> goodsList;

    //话题相关字段
    @JsonProperty("clm")
    private LinkedHashMap<String, Float> comLabelMap;

    //社区新添加标签  https://poizon.feishu.cn/docs/doccnnEaKzH4Y2XtdDaRt7guffd
    @JsonProperty("cskw")
    private List<String> comSearchKeyWord;


    //社区的直播  一期  召回相关   https://poizon.feishu.cn/docs/doccn8LxRyBicff8APxMmcRSyuf#iJ97hW
    //对直播分类的偏好  保存了分类的id  和分数
    @JsonProperty("lic")
    private LinkedHashMap<String,Float> liveCategoryPreference;
    //对直播标签的偏好  保存了标签的id  和分数
    @JsonProperty("lit")
    private LinkedHashMap<String,Float> liveTagsPreference;
    //对直播作者的偏好    保存了 作者id  对应的分数
    @JsonProperty("lia")
    private LinkedHashMap<String,Float> LiveAuthorPreference;

    @JsonProperty("cst")
    private List<String> liveFollowList;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<String> getBuyList() {
        return buyList;
    }

    public void setBuyList(List<String> buyList) {
        this.buyList = buyList;
    }

    public List<String> getPayList() {
        return payList;
    }

    public void setPayList(List<String> payList) {
        this.payList = payList;
    }

    public Map<String, Long> getPay() {
        return pay;
    }

    public void setPay(Map<String, Long> pay) {
        this.pay = pay;
    }

    public List<String> getBuyNowList() {
        return buyNowList;
    }

    public void setBuyNowList(List<String> buyNowList) {
        this.buyNowList = buyNowList;
    }

    public List<String> getWantBuyList() {
        return wantBuyList;
    }

    public void setWantBuyList(List<String> wantBuyList) {
        this.wantBuyList = wantBuyList;
    }

    public List<String> getCollectList() {
        return collectList;
    }

    public void setCollectList(List<String> collectList) {
        this.collectList = collectList;
    }

    public List<String> getCollectExpandList() {
        return collectExpandList;
    }

    public void setCollectExpandList(List<String> collectExpandList) {
        this.collectExpandList = collectExpandList;
    }

    public List<String> getUnCollectList() {
        return unCollectList;
    }

    public void setUnCollectList(List<String> unCollectList) {
        this.unCollectList = unCollectList;
    }

    public Map<String, Float> getFavourItemId() {
        return favourItemId;
    }

    public void setFavourItemId(Map<String, Float> favourItemId) {
        this.favourItemId = favourItemId;
    }

    public Map<String, Float> getFavourBrandId() {
        return favourBrandId;
    }

    public void setFavourBrandId(Map<String, Float> favourBrandId) {
        this.favourBrandId = favourBrandId;
    }

    public List<String> getFavourBrandIdList() {
        return favourBrandIdList;
    }

    public void setFavourBrandIdList(List<String> favourBrandIdList) {
        this.favourBrandIdList = favourBrandIdList;
    }

    public Map<String, Float> getFavourCategoryId() {
        return favourCategoryId;
    }

    public void setFavourCategoryId(Map<String, Float> favourCategoryId) {
        this.favourCategoryId = favourCategoryId;
    }

    public List<String> getFavourCategoryIdList() {
        return favourCategoryIdList;
    }

    public void setFavourCategoryIdList(List<String> favourCategoryIdList) {
        this.favourCategoryIdList = favourCategoryIdList;
    }

    public Map<String, Float> getFavourColor() {
        return favourColor;
    }

    public void setFavourColor(Map<String, Float> favourColor) {
        this.favourColor = favourColor;
    }

    public Map<String, Float> getFavourSeriesId() {
        return favourSeriesId;
    }

    public void setFavourSeriesId(Map<String, Float> favourSeriesId) {
        this.favourSeriesId = favourSeriesId;
    }

    public List<String> getSearchKeyList() {
        return searchKeyList;
    }

    public void setSearchKeyList(List<String> searchKeyList) {
        this.searchKeyList = searchKeyList;
    }

    public Map<String, Float> getFavourArticleNum() {
        return favourArticleNum;
    }

    public void setFavourArticleNum(Map<String, Float> favourArticleNum) {
        this.favourArticleNum = favourArticleNum;
    }

    public Map<String, Float> getStrategyWeight() {
        return strategyWeight;
    }

    public void setStrategyWeight(Map<String, Float> strategyWeight) {
        this.strategyWeight = strategyWeight;
    }

    public LinkedHashMap<String, Integer> getClickMap() {
        return clickMap;
    }

    public void setClickMap(LinkedHashMap<String, Integer> clickMap) {
        this.clickMap = clickMap;
    }

    public LinkedHashMap<String, Integer> getClickExpandMap() {
        return clickExpandMap;
    }

    public void setClickExpandMap(LinkedHashMap<String, Integer> clickExpandMap) {
        this.clickExpandMap = clickExpandMap;
    }

    public Map<String, Float> getClickNewMap() {
        return clickNewMap;
    }

    public void setClickNewMap(Map<String, Float> clickNewMap) {
        this.clickNewMap = clickNewMap;
    }

    public Map<String, Float> getFavMixPi() {
        return favMixPi;
    }

    public void setFavMixPi(Map<String, Float> favMixPi) {
        this.favMixPi = favMixPi;
    }

    public List<String> getClickList() {
        return clickList;
    }

    public void setClickList(List<String> clickList) {
        this.clickList = clickList;
    }

    public List<String> getThemeId() {
        return themeId;
    }

    public void setThemeId(List<String> themeId) {
        this.themeId = themeId;
    }

    public Map<String, Float> getThemeMap() {
        return themeMap;
    }

    public void setThemeMap(Map<String, Float> themeMap) {
        this.themeMap = themeMap;
    }

    public List<String> getShareList() {
        return shareList;
    }

    public void setShareList(List<String> shareList) {
        this.shareList = shareList;
    }

    public List<String> getUninterestedItemList() {
        return uninterestedItemList;
    }

    public void setUninterestedItemList(List<String> uninterestedItemList) {
        this.uninterestedItemList = uninterestedItemList;
    }

    public List<String> getUninterestedThemeList() {
        return uninterestedThemeList;
    }

    public void setUninterestedThemeList(List<String> uninterestedThemeList) {
        this.uninterestedThemeList = uninterestedThemeList;
    }

    public List<String> getSearchList() {
        return searchList;
    }

    public void setSearchList(List<String> searchList) {
        this.searchList = searchList;
    }

    public List<String> getSearchClickList() {
        return searchClickList;
    }

    public void setSearchClickList(List<String> searchClickList) {
        this.searchClickList = searchClickList;
    }

    public Float getSs() {
        return ss;
    }

    public void setSs(Float ss) {
        this.ss = ss;
    }

    public Map<String, Float> getCategorySexScore() {
        return categorySexScore;
    }

    public void setCategorySexScore(Map<String, Float> categorySexScore) {
        this.categorySexScore = categorySexScore;
    }

    public Float getImageTrendClickCount() {
        return imageTrendClickCount;
    }

    public void setImageTrendClickCount(Float imageTrendClickCount) {
        this.imageTrendClickCount = imageTrendClickCount;
    }

    public Float getVideoTrendClickCount() {
        return videoTrendClickCount;
    }

    public void setVideoTrendClickCount(Float videoTrendClickCount) {
        this.videoTrendClickCount = videoTrendClickCount;
    }

    public List<String> getLiveStreamIdList() {
        return liveStreamIdList;
    }

    public void setLiveStreamIdList(List<String> liveStreamIdList) {
        this.liveStreamIdList = liveStreamIdList;
    }

    public Float getPostClickCount() {
        return postClickCount;
    }

    public void setPostClickCount(Float postClickCount) {
        this.postClickCount = postClickCount;
    }

    public Double getFavVideoRatio() {
        return favVideoRatio;
    }

    public void setFavVideoRatio(Double favVideoRatio) {
        this.favVideoRatio = favVideoRatio;
    }

    public List<String> getTrendClickList() {
        return trendClickList;
    }

    public void setTrendClickList(List<String> trendClickList) {
        this.trendClickList = trendClickList;
    }

    public List<String> getPostClickList() {
        return postClickList;
    }

    public void setPostClickList(List<String> postClickList) {
        this.postClickList = postClickList;
    }

    public List<String> getPostLikeList() {
        return postLikeList;
    }

    public void setPostLikeList(List<String> postLikeList) {
        this.postLikeList = postLikeList;
    }

    public List<String> getTrendLikeList() {
        return trendLikeList;
    }

    public void setTrendLikeList(List<String> trendLikeList) {
        this.trendLikeList = trendLikeList;
    }

    public List<String> getTrendShareList() {
        return trendShareList;
    }

    public void setTrendShareList(List<String> trendShareList) {
        this.trendShareList = trendShareList;
    }

    public List<String> getPostShareList() {
        return postShareList;
    }

    public void setPostShareList(List<String> postShareList) {
        this.postShareList = postShareList;
    }

    public Map<String, Float> getFavAuthor() {
        return favAuthor;
    }

    public void setFavAuthor(Map<String, Float> favAuthor) {
        this.favAuthor = favAuthor;
    }

    public Map<String, Float> getFavNewCommunityCategory() {
        return favNewCommunityCategory;
    }

    public void setFavNewCommunityCategory(Map<String, Float> favNewCommunityCategory) {
        this.favNewCommunityCategory = favNewCommunityCategory;
    }

    public Map<String, Float> getFavProductId() {
        return favProductId;
    }

    public void setFavProductId(Map<String, Float> favProductId) {
        this.favProductId = favProductId;
    }

    public Float getFemaleClickCount() {
        return femaleClickCount;
    }

    public void setFemaleClickCount(Float femaleClickCount) {
        this.femaleClickCount = femaleClickCount;
    }

    public Float getFavSexyClickCount() {
        return favSexyClickCount;
    }

    public void setFavSexyClickCount(Float favSexyClickCount) {
        this.favSexyClickCount = favSexyClickCount;
    }

    public Float getFavSexyClickScore() {
        return favSexyClickScore;
    }

    public void setFavSexyClickScore(Float favSexyClickScore) {
        this.favSexyClickScore = favSexyClickScore;
    }

    public Double getFemale() {
        return female;
    }

    public void setFemale(Double female) {
        this.female = female;
    }

    public Map<String, Float> getChannelWeight() {
        return channelWeight;
    }

    public void setChannelWeight(Map<String, Float> channelWeight) {
        this.channelWeight = channelWeight;
    }

    public List<String> getFeedBackAuthor() {
        return feedBackAuthor;
    }

    public void setFeedBackAuthor(List<String> feedBackAuthor) {
        this.feedBackAuthor = feedBackAuthor;
    }

    public Map<String, Float> getFeedBackNewCategoryCount() {
        return feedBackNewCategoryCount;
    }

    public void setFeedBackNewCategoryCount(Map<String, Float> feedBackNewCategoryCount) {
        this.feedBackNewCategoryCount = feedBackNewCategoryCount;
    }

    public List<String> getFeedBackNewCategoryList() {
        return feedBackNewCategoryList;
    }

    public void setFeedBackNewCategoryList(List<String> feedBackNewCategoryList) {
        this.feedBackNewCategoryList = feedBackNewCategoryList;
    }

    public Map<String, Float> getFeedBackSexCount() {
        return feedBackSexCount;
    }

    public void setFeedBackSexCount(Map<String, Float> feedBackSexCount) {
        this.feedBackSexCount = feedBackSexCount;
    }

    public List<String> getFeedBackSexList() {
        return feedBackSexList;
    }

    public void setFeedBackSexList(List<String> feedBackSexList) {
        this.feedBackSexList = feedBackSexList;
    }

    public Map<String, Float> getFavTag3() {
        return favTag3;
    }

    public void setFavTag3(Map<String, Float> favTag3) {
        this.favTag3 = favTag3;
    }

    public Map<String, Float> getCspuIds() {
        return cspuIds;
    }

    public void setCspuIds(Map<String, Float> cspuIds) {
        this.cspuIds = cspuIds;
    }

    public List<String> getCommunityNewCategoryList() {
        return communityNewCategoryList;
    }

    public void setCommunityNewCategoryList(List<String> communityNewCategoryList) {
        this.communityNewCategoryList = communityNewCategoryList;
    }

    public Map<String, Float> getFavourCircle() {
        return favourCircle;
    }

    public void setFavourCircle(Map<String, Float> favourCircle) {
        this.favourCircle = favourCircle;
    }

    public Long getExpire() {
        return expire;
    }

    public void setExpire(Long expire) {
        this.expire = expire;
    }

    public String getRefreshCode() {
        return refreshCode;
    }

    public void setRefreshCode(String refreshCode) {
        this.refreshCode = refreshCode;
    }

    public Long getCommunityRealTimeColumnExpire() {
        return communityRealTimeColumnExpire;
    }

    public void setCommunityRealTimeColumnExpire(Long communityRealTimeColumnExpire) {
        this.communityRealTimeColumnExpire = communityRealTimeColumnExpire;
    }

    public Long getDealRealTimeColumnExpire() {
        return dealRealTimeColumnExpire;
    }

    public void setDealRealTimeColumnExpire(Long dealRealTimeColumnExpire) {
        this.dealRealTimeColumnExpire = dealRealTimeColumnExpire;
    }

    public Boolean getNeedClean() {
        return isNeedClean;
    }

    public void setNeedClean(Boolean needClean) {
        isNeedClean = needClean;
    }

    public Boolean getNone() {
        return isNone;
    }

    public void setNone(Boolean none) {
        isNone = none;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public List<String> getFavourShortCategoryIdList() {
        return favourShortCategoryIdList;
    }

    public void setFavourShortCategoryIdList(List<String> favourShortCategoryIdList) {
        this.favourShortCategoryIdList = favourShortCategoryIdList;
    }

    public Map<String, Map<String, Integer>> getDealLevel1CategoryThresholdCountMap() {
        return dealLevel1CategoryThresholdCountMap;
    }

    public void setDealLevel1CategoryThresholdCountMap(Map<String, Map<String, Integer>> dealLevel1CategoryThresholdCountMap) {
        this.dealLevel1CategoryThresholdCountMap = dealLevel1CategoryThresholdCountMap;
    }

    public Map<String, Integer> getLevel1CategoryCouponThresholdMap() {
        return level1CategoryCouponThresholdMap;
    }

    public void setLevel1CategoryCouponThresholdMap(Map<String, Integer> level1CategoryCouponThresholdMap) {
        this.level1CategoryCouponThresholdMap = level1CategoryCouponThresholdMap;
    }

    public String getClusterId() {
        return clusterId;
    }

    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    public List<String> getFavExploreList() {
        return favExploreList;
    }

    public void setFavExploreList(List<String> favExploreList) {
        this.favExploreList = favExploreList;
    }

    public List<String> getSimilarUserIds() {
        return similarUserIds;
    }

    public void setSimilarUserIds(List<String> similarUserIds) {
        this.similarUserIds = similarUserIds;
    }

    public String getClusterLabelId() {
        return clusterLabelId;
    }

    public void setClusterLabelId(String clusterLabelId) {
        this.clusterLabelId = clusterLabelId;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Long getDealVersion() {
        return dealVersion;
    }

    public void setDealVersion(Long dealVersion) {
        this.dealVersion = dealVersion;
    }

    public Long getComVersion() {
        return comVersion;
    }

    public void setComVersion(Long comVersion) {
        this.comVersion = comVersion;
    }

    public Map<String, Float> getFavVideoTimeMap() {
        return favVideoTimeMap;
    }

    public void setFavVideoTimeMap(Map<String, Float> favVideoTimeMap) {
        this.favVideoTimeMap = favVideoTimeMap;
    }

    public List<String> getRecentBuyList() {
        return recentBuyList;
    }

    public void setRecentBuyList(List<String> recentBuyList) {
        this.recentBuyList = recentBuyList;
    }

    public List<String> getCommentList() {
        return commentList;
    }

    public void setCommentList(List<String> commentList) {
        this.commentList = commentList;
    }

    public List<String> getPictureList() {
        return pictureList;
    }

    public void setPictureList(List<String> pictureList) {
        this.pictureList = pictureList;
    }

    public List<String> getPickList() {
        return pickList;
    }

    public void setPickList(List<String> pickList) {
        this.pickList = pickList;
    }

    public List<String> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<String> goodsList) {
        this.goodsList = goodsList;
    }

    public LinkedHashMap<String, Float> getComLabelMap() {
        return comLabelMap;
    }

    public void setComLabelMap(LinkedHashMap<String, Float> comLabelMap) {
        this.comLabelMap = comLabelMap;
    }

    public List<String> getComSearchKeyWord() {
        return comSearchKeyWord;
    }

    public void setComSearchKeyWord(List<String> comSearchKeyWord) {
        this.comSearchKeyWord = comSearchKeyWord;
    }

    public LinkedHashMap<String, Float> getLiveCategoryPreference() {
        return liveCategoryPreference;
    }

    public void setLiveCategoryPreference(LinkedHashMap<String, Float> liveCategoryPreference) {
        this.liveCategoryPreference = liveCategoryPreference;
    }

    public LinkedHashMap<String, Float> getLiveTagsPreference() {
        return liveTagsPreference;
    }

    public void setLiveTagsPreference(LinkedHashMap<String, Float> liveTagsPreference) {
        this.liveTagsPreference = liveTagsPreference;
    }

    public LinkedHashMap<String, Float> getLiveAuthorPreference() {
        return LiveAuthorPreference;
    }

    public void setLiveAuthorPreference(LinkedHashMap<String, Float> liveAuthorPreference) {
        LiveAuthorPreference = liveAuthorPreference;
    }

    public List<String> getLiveFollowList() {
        return liveFollowList;
    }

    public void setLiveFollowList(List<String> liveFollowList) {
        this.liveFollowList = liveFollowList;
    }

    @Override
    public String toString() {
        return "UserPortrait{" +
                "userId='" + userId + '\'' +
                ", buyList=" + buyList +
                ", payList=" + payList +
                ", pay=" + pay +
                ", buyNowList=" + buyNowList +
                ", wantBuyList=" + wantBuyList +
                ", collectList=" + collectList +
                ", collectExpandList=" + collectExpandList +
                ", unCollectList=" + unCollectList +
                ", favourItemId=" + favourItemId +
                ", favourBrandId=" + favourBrandId +
                ", favourBrandIdList=" + favourBrandIdList +
                ", favourCategoryId=" + favourCategoryId +
                ", favourCategoryIdList=" + favourCategoryIdList +
                ", favourColor=" + favourColor +
                ", favourSeriesId=" + favourSeriesId +
                ", searchKeyList=" + searchKeyList +
                ", favourArticleNum=" + favourArticleNum +
                ", strategyWeight=" + strategyWeight +
                ", clickMap=" + clickMap +
                ", clickExpandMap=" + clickExpandMap +
                ", clickNewMap=" + clickNewMap +
                ", favMixPi=" + favMixPi +
                ", clickList=" + clickList +
                ", themeId=" + themeId +
                ", themeMap=" + themeMap +
                ", shareList=" + shareList +
                ", uninterestedItemList=" + uninterestedItemList +
                ", uninterestedThemeList=" + uninterestedThemeList +
                ", searchList=" + searchList +
                ", searchClickList=" + searchClickList +
                ", ss=" + ss +
                ", categorySexScore=" + categorySexScore +
                ", imageTrendClickCount=" + imageTrendClickCount +
                ", videoTrendClickCount=" + videoTrendClickCount +
                ", liveStreamIdList=" + liveStreamIdList +
                ", postClickCount=" + postClickCount +
                ", favVideoRatio=" + favVideoRatio +
                ", trendClickList=" + trendClickList +
                ", postClickList=" + postClickList +
                ", postLikeList=" + postLikeList +
                ", trendLikeList=" + trendLikeList +
                ", trendShareList=" + trendShareList +
                ", postShareList=" + postShareList +
                ", favAuthor=" + favAuthor +
                ", favNewCommunityCategory=" + favNewCommunityCategory +
                ", favProductId=" + favProductId +
                ", femaleClickCount=" + femaleClickCount +
                ", favSexyClickCount=" + favSexyClickCount +
                ", favSexyClickScore=" + favSexyClickScore +
                ", female=" + female +
                ", channelWeight=" + channelWeight +
                ", feedBackAuthor=" + feedBackAuthor +
                ", feedBackNewCategoryCount=" + feedBackNewCategoryCount +
                ", feedBackNewCategoryList=" + feedBackNewCategoryList +
                ", feedBackSexCount=" + feedBackSexCount +
                ", feedBackSexList=" + feedBackSexList +
                ", favTag3=" + favTag3 +
                ", cspuIds=" + cspuIds +
                ", communityNewCategoryList=" + communityNewCategoryList +
                ", favourCircle=" + favourCircle +
                ", expire=" + expire +
                ", refreshCode='" + refreshCode + '\'' +
                ", communityRealTimeColumnExpire=" + communityRealTimeColumnExpire +
                ", dealRealTimeColumnExpire=" + dealRealTimeColumnExpire +
                ", isNeedClean=" + isNeedClean +
                ", isNone=" + isNone +
                ", timestamp=" + timestamp +
                ", favourShortCategoryIdList=" + favourShortCategoryIdList +
                ", dealLevel1CategoryThresholdCountMap=" + dealLevel1CategoryThresholdCountMap +
                ", level1CategoryCouponThresholdMap=" + level1CategoryCouponThresholdMap +
                ", clusterId='" + clusterId + '\'' +
                ", favExploreList=" + favExploreList +
                ", similarUserIds=" + similarUserIds +
                ", clusterLabelId='" + clusterLabelId + '\'' +
                ", gender=" + gender +
                ", dealVersion=" + dealVersion +
                ", comVersion=" + comVersion +
                ", favVideoTimeMap=" + favVideoTimeMap +
                ", recentBuyList=" + recentBuyList +
                ", commentList=" + commentList +
                ", pictureList=" + pictureList +
                ", pickList=" + pickList +
                ", goodsList=" + goodsList +
                ", comLabelMap=" + comLabelMap +
                ", comSearchKeyWord=" + comSearchKeyWord +
                ", liveCategoryPreference=" + liveCategoryPreference +
                ", liveTagsPreference=" + liveTagsPreference +
                ", LiveAuthorPreference=" + LiveAuthorPreference +
                ", liveFollowList=" + liveFollowList +
                '}';
    }
}
