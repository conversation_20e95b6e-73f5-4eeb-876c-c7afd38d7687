package com.poizon.utils;

/**
 * @Author: wpp
 * @Date: 2021/6/18 14:57
 */
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JavaType;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.PropertyNamingStrategy;

import java.io.IOException;
import java.io.InputStream;

public class JsonUtil {
    public static final ObjectMapper OBJECTMAPPER = new ObjectMapper();
    public static final ObjectMapper OBJECTMAPPER2 = new ObjectMapper();

    public JsonUtil() {
    }

    public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return OBJECTMAPPER.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }

    public static String writeValueAsString(Object value) {
        String result = null;

        try {
            result = OBJECTMAPPER.writeValueAsString(value);
        } catch (JsonProcessingException var3) {
            var3.printStackTrace();
        }

        return result;
    }

    public static String writeValueAsStringWithDefaultPretty(Object value) {
        String result = null;

        try {
            result = OBJECTMAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(value);
        } catch (JsonProcessingException var3) {
            var3.printStackTrace();
        }

        return result;
    }

    public static byte[] writeValueAsBytes(Object value) {
        byte[] result = null;

        try {
            result = OBJECTMAPPER.writeValueAsBytes(value);
        } catch (JsonProcessingException var3) {
            var3.printStackTrace();
        }

        return result;
    }

    public static <T> T readValue(String jsonData, Class<T> valueType) throws IOException {
        return OBJECTMAPPER.readValue(jsonData, valueType);
    }

    public static <T> T readValue(String jsonData, JavaType valueType) throws IOException {
        return OBJECTMAPPER.readValue(jsonData, valueType);
    }

    public static <T> T readValue(InputStream inputStream, Class<T> valueType) throws IOException {
        return OBJECTMAPPER.readValue(inputStream, valueType);
    }

    public static <T> T toSnakeObject(String json, Class<T> clazz) throws IOException {
        OBJECTMAPPER2.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        T reqJson = OBJECTMAPPER2.readValue(json, clazz);
        return reqJson;
    }
}