package com.poizon.utils;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;

import java.math.BigInteger;

public class EcSmartPushHashUtil {

    private static final String PREFIX = "ec_smart_push.";

    public static Long md5Hash(String key) {
        return Long.parseLong(base_convert(Hashing.md5().hashString(String.valueOf(key), Charsets.UTF_8).toString(), 16, 10).substring(0, 15));
    }

    public static String base_convert(String inputValue, Integer fromBase, Integer toBase) {
        return (new BigInteger(inputValue, fromBase)).toString(toBase);
    }

    public static int hash(String key) {
        return Long.valueOf(Math.floorMod(md5Hash(key), 100L)).intValue();
    }

    /**
     *
     * CASE   WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('0','1','2','3','4') THEN 'Q内基准1'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('5','6','7','8','9') THEN 'Q内基准2'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('10','11','12','13','14') THEN '召回实验18'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('15','16','17','18','19') THEN '召回实验17'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('20','21','22','23','24') THEN '召回实验16'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('25','26','27','28','29') THEN '召回实验15'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('30','31','32','33','34') THEN '召回实验14'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('35','36','37','38','39') THEN '召回实验13'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('40','41','42','43','44') THEN '召回实验12'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('45','46','47','48','49') THEN '召回实验11'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('50','51','52','53','54') THEN '召回实验10'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('55','56','57','58','59') THEN '召回实验9'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('60','61','62','63','64') THEN '召回实验8'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('65','66','67','68','69') THEN '召回实验7'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('70','71','72','73','74') THEN '召回实验6'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('75','76','77','78','79') THEN '召回实验5'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('80','81','82','83','84') THEN '召回实验4'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('85','86','87','88','89') THEN '召回实验3'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('90','91','92','93','94') THEN '召回实验2'
     *                     WHEN ABTEST_HASH(CAST(CONCAT('ec_smart_push.',a1.userid) AS STRING)) IN ('95','96','97','98','99') THEN '召回实验1'
     *                     ELSE '其他'
     * @param uid
     * @return
     */
    public static String getABTestName(String uid) {
        int bucketNum = hash(PREFIX + uid);

        switch (bucketNum) { 
            case 0: case 1: case 2: case 3: case 4:
                return "Q内基准1";
            case 5: case 6: case 7: case 8: case 9:
                return "Q内基准2";
            case 10: case 11: case 12: case 13: case 14:
                return "召回实验18";
            case 15: case 16: case 17: case 18: case 19:
                return "召回实验17";
            case 20: case 21: case 22: case 23: case 24:
                return "召回实验16";
            case 25: case 26: case 27: case 28: case 29:
                return "召回实验15";
            case 30: case 31: case 32: case 33: case 34:
                return "召回实验14";
            case 35: case 36: case 37: case 38: case 39:
                return "召回实验13";
            case 40: case 41: case 42: case 43: case 44:
                return "召回实验12";
            case 45: case 46: case 47: case 48: case 49:
                return "召回实验11";
            case 50: case 51: case 52: case 53: case 54:
                return "召回实验10";
            case 55: case 56: case 57: case 58: case 59:
                return "召回实验9";
            case 60: case 61: case 62: case 63: case 64:
                return "召回实验8";
            case 65: case 66: case 67: case 68: case 69:
                return "召回实验7";
            case 70: case 71: case 72: case 73: case 74:
                return "召回实验6";
            case 75: case 76: case 77: case 78: case 79:
                return "召回实验5";
            case 80: case 81: case 82: case 83: case 84:
                return "召回实验4";
            case 85: case 86: case 87: case 88: case 89:
                return "召回实验3";
            case 90: case 91: case 92: case 93: case 94:
                return "召回实验2";
            case 95: case 96: case 97: case 98: case 99:
                return "召回实验1";
            default:
                return "其他";
        }
    }
}