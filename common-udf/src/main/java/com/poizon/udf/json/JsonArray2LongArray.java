package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.List;

@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("ARRAY<BIGINT>"))
@Slf4j
public class JsonArray2LongArray extends ScalarFunction {
    public Long[] eval(String json) {
        List<Long> returnArray = new ArrayList<>();
        try {
            JSONArray jsonArray = JSON.parseArray(json);
            returnArray = jsonArray.toJavaList(Long.class);
        } catch (Exception throwable) {
            log.error("数组转换失败，原值 = {}; exception = {}",json,throwable.getMessage(),throwable);
        }
        return returnArray.toArray(new Long[0]);
    }
}
