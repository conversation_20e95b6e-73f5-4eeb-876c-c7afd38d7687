package com.poizon.udf.json;

import com.alibaba.fastjson.JSONObject;
import com.poizon.entity.UserPortrait;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wpp
 * @Date: 2021/6/29 11:29
 *  直播的三个标签问题排查
 *
 */
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
public class userPortraitCntByFieldUdf extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {

    }

    public String eval(String json) {

        if(StringUtils.isEmpty(json)){
            return null;
        }
        Map<String,Integer> resMap = new HashMap<>();

        try {
           UserPortrait userPortrait = JSONObject.parseObject(json, UserPortrait.class);

           if(userPortrait != null){
               if(userPortrait.getBuyList() != null){
                    resMap.put("buy",userPortrait.getBuyList().size());
               }else{
                    resMap.put("buy",0);
               }

               if(userPortrait.getPayList() != null){
                   resMap.put("payl",userPortrait.getPayList().size());
               }else{
                   resMap.put("payl",0);
               }

               if(userPortrait.getPay() != null){
                   resMap.put("pay",userPortrait.getPay().size());
               }else{
                   resMap.put("pay",0);
               }

               if(userPortrait.getBuyNowList() != null){
                   resMap.put("buyn",userPortrait.getBuyNowList().size());
               }else{
                   resMap.put("buyn",0);
               }

               if(userPortrait.getWantBuyList() != null){
                   resMap.put("wb",userPortrait.getWantBuyList().size());
               }else{
                   resMap.put("wb",0);
               }

               if(userPortrait.getCollectList() != null){
                   resMap.put("col",userPortrait.getCollectList().size());
               }else{
                   resMap.put("col",0);
               }

               if(userPortrait.getCollectExpandList() != null){
                   resMap.put("col_epd",userPortrait.getCollectExpandList().size());
               }else{
                   resMap.put("col_epd",0);
               }

               if(userPortrait.getUnCollectList() != null){
                   resMap.put("uncol",userPortrait.getUnCollectList().size());
               }else{
                   resMap.put("uncol",0);
               }

               if(userPortrait.getFavourItemId() != null){
                   resMap.put("fi",userPortrait.getFavourItemId().size());
               }else{
                   resMap.put("fi",0);
               }

               if(userPortrait.getFavourBrandId() != null){
                   resMap.put("fb",userPortrait.getFavourBrandId().size());
               }else{
                   resMap.put("fb",0);
               }

               if(userPortrait.getFavourBrandIdList() != null){
                   resMap.put("fblist",userPortrait.getFavourBrandIdList().size());
               }else{
                   resMap.put("fblist",0);
               }

               if(userPortrait.getFavourCategoryId() != null){
                   resMap.put("fc",userPortrait.getFavourCategoryId().size());
               }else{
                   resMap.put("fc",0);
               }

               if(userPortrait.getFavourCategoryIdList() != null){
                   resMap.put("fclist",userPortrait.getFavourCategoryIdList().size());
               }else{
                   resMap.put("fclist",0);
               }

               if(userPortrait.getFavourColor() != null){
                   resMap.put("fclr",userPortrait.getFavourColor().size());
               }else{
                   resMap.put("fclr",0);
               }

               if(userPortrait.getFavourSeriesId() != null){
                   resMap.put("fs",userPortrait.getFavourSeriesId().size());
               }else{
                   resMap.put("fs",0);
               }

               if(userPortrait.getSearchKeyList() != null){
                   resMap.put("sehkeylist",userPortrait.getSearchKeyList().size());
               }else{
                   resMap.put("sehkeylist",0);
               }

               if(userPortrait.getFavourArticleNum() != null){
                   resMap.put("fa",userPortrait.getFavourArticleNum().size());
               }else{
                   resMap.put("fa",0);
               }

               if(userPortrait.getClickExpandMap() != null){
                   resMap.put("clk_epd",userPortrait.getClickExpandMap().size());
               }else{
                   resMap.put("clk_epd",0);
               }

               if(userPortrait.getThemeId() != null){
                   resMap.put("thi",userPortrait.getThemeId().size());
               }else{
                   resMap.put("thi",0);
               }

               if(userPortrait.getThemeMap() != null){
                   resMap.put("thm",userPortrait.getThemeMap().size());
               }else{
                   resMap.put("thm",0);
               }

               if(userPortrait.getShareList() != null){
                   resMap.put("shrlist",userPortrait.getShareList().size());
               }else{
                   resMap.put("shrlist",0);
               }

               if(userPortrait.getUninterestedItemList() != null){
                   resMap.put("unii",userPortrait.getUninterestedItemList().size());
               }else{
                   resMap.put("unii",0);
               }

               if(userPortrait.getUninterestedThemeList() != null){
                   resMap.put("unti",userPortrait.getUninterestedThemeList().size());
               }else{
                   resMap.put("unti",0);
               }

               if(userPortrait.getSearchClickList() != null){
                   resMap.put("sehclklist",userPortrait.getSearchClickList().size());
               }else{
                   resMap.put("sehclklist",0);
               }

               if(userPortrait.getTrendClickList() != null){
                   resMap.put("tclk",userPortrait.getTrendClickList().size());
               }else{
                   resMap.put("tclk",0);
               }

               if(userPortrait.getPostClickList() != null){
                   resMap.put("pclk",userPortrait.getPostClickList().size());
               }else{
                   resMap.put("pclk",0);
               }

               if(userPortrait.getTrendShareList() != null){
                   resMap.put("tshr",userPortrait.getTrendShareList().size());
               }else{
                   resMap.put("tshr",0);
               }

               if(userPortrait.getFavNewCommunityCategory() != null){
                   resMap.put("fnewc",userPortrait.getFavNewCommunityCategory().size());
               }else{
                   resMap.put("fnewc",0);
               }

               if(userPortrait.getFeedBackNewCategoryList() != null){
                   resMap.put("fbncl",userPortrait.getFeedBackNewCategoryList().size());
               }else{
                   resMap.put("fbncl",0);
               }

               if(userPortrait.getFeedBackSexList() != null){
                   resMap.put("fbsl",userPortrait.getFeedBackSexList().size());
               }else{
                   resMap.put("fbsl",0);
               }

               if(userPortrait.getCspuIds() != null){
                   resMap.put("cspu",userPortrait.getCspuIds().size());
               }else{
                   resMap.put("cspu",0);
               }

               if(userPortrait.getCommunityNewCategoryList() != null){
                   resMap.put("ccnc",userPortrait.getCommunityNewCategoryList().size());
               }else{
                   resMap.put("ccnc",0);
               }
           }

        }catch (Exception e){
            System.out.println("解析数据异常："+e.getMessage());
        }

        return JSONObject.toJSONString(resMap);
    }

    // 可选，close方法可以不写
    @Override
    public void close() {

    }

    public static void main (String[] args) {

//        for (Field field : UserPortrait.class.getDeclaredFields()) {
//            JsonProperty annotation = field.getAnnotation(JsonProperty.class);
////			System.out.println(field.getName()+",");
//            if (null != annotation) {
//                System.out.println(field.getName()+","+annotation.value());
////				coninue;
//            }
//        }

    }

}