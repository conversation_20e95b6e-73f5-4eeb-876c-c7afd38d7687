package com.poizon.udf.json;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.net.URLDecoder;

@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class UrlDecoder extends ScalarFunction {

    public String eval(String urlOrigin) {
        try {
            if (null != urlOrigin) {
                return URLDecoder.decode(urlOrigin);
            } else {
                return null;
            }
        } catch (Exception e) {
        }

        return urlOrigin;
    }

}
