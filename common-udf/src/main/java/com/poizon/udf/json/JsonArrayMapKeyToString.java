package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 返回Array中的每一个Map元素的Key
 * <AUTHOR>
 * @Date 2025/8/6 下午6:08
 **/
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
@Slf4j
public class JsonArrayMapKeyToString  extends ScalarFunction {

    public String eval(String json) {
        String result = "";
        if (json == null || json.trim().isEmpty()) {
            log.warn("输入的JSON字符串为空或null");
            return result;
        }
        try {
            JSONArray jsonArray = JSON.parseArray(json);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("JSON数组为空，原值 = {}", json);
                return result;
            }
            // 遍历数组中的每个MAP元素的key
            List<String> keysList = new ArrayList<>(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject inputMap = jsonArray.getJSONObject(i);
                if (inputMap == null || inputMap.isEmpty()) {
                        continue;
                }
                keysList.addAll(inputMap.keySet());
            }
            return String.join("|", keysList);
        } catch (Exception throwable) {
            log.error("数组转换失败，原值 = {}; exception = {}",json,throwable.getMessage(),throwable);
        }
        return result;
    }
}