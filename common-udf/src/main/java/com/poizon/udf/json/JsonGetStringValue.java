package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;


public class JsonGetStringValue extends ScalarFunction {

  @Override
  public void open(FunctionContext context) {
  }

  public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object... args) {
    try {
      if (args.length < 2 || null == args[0]) {
        return null;
      }
      JSONObject originJson;
      if (args[0] instanceof JSONObject) {
        originJson = (JSONObject) args[0];
      } else {
        originJson = JSON.parseObject((String) args[0]);
      }
      Object result = originJson.get(args[1].toString());
      if (null == result) {
        return null;
      }
      return result.toString();
    } catch (Exception e) {
    }
    return null;
  }

  //可选，close方法可以不写
  @Override
  public void close() {
  }

  public static void main(String[] args) {
    new JsonGetStringValue().eval("{'@lib':'ff'}", "@lib");
  }

}