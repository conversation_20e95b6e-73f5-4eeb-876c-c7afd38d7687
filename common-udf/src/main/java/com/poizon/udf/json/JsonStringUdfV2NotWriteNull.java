package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.Optional;

/**
 * @Description 构建一个JSON,不写入null值,同时可以嵌套JSON，避免转译
 * <AUTHOR>
 * @Date 2025/8/8 下午4:21
 **/
public class JsonStringUdfV2NotWriteNull extends ScalarFunction {

    // 可选， open方法可以不写,若写的话需要import org.apache.flink.table.functions.FunctionContext;
    @Override
    public void open(FunctionContext context) {
    }

    public String eval(Object... args) {
        if (args.length <= 1) {
            return "{}";
        }
        if (!(args.length % 2 == 0)) {
            return "{}";
        }
        JSONObject jsonObject = JSON.parseObject("{}");
        for (int i = 0; i < args.length/2; i++) {
            Object arg = args[2 * i + 1];

            //判断value值是否为空，如果为空则不输出，直接结束
            if (arg == null) {
                continue;
            }

            //嵌套Json，避免转译
            if (arg instanceof  String && JSON.isValidObject((String) arg)) {
                jsonObject.put(args[2*i].toString(),JSON.parseObject((String)args[2*i+1]));
            } else {
                jsonObject.put(args[2*i].toString(),args[2*i+1]);
            }
        }
        return JSON.toJSONString(jsonObject);
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
                .build();
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

}
