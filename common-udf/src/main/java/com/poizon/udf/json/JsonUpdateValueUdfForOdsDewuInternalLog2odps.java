package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

public class JsonUpdateValueUdfForOdsDewuInternalLog2odps extends ScalarFunction {

    private static final Logger logger = LoggerFactory.getLogger(JsonUpdateValueUdfForOdsDewuInternalLog2odps.class);

    private static final String PARSE_FAILED_RETURN = "{}";

    @Override
    public void open(FunctionContext context) {
    }

    public String eval(String jsonString, Object... args) {
        JSONObject originJson;
        try {
            originJson = JSON.parseObject(jsonString);
        } catch (JSONException | UnsupportedOperationException e) {
            return PARSE_FAILED_RETURN;
        }

        if (args.length > 0 && args.length % 2 == 0) {
            return deal(originJson, args).toJSONString();
        } else {
            return originJson.toJSONString();
        }
    }

    private JSONObject deal(JSONObject originJson, Object[] args) {
        for (int i = 0; i < args.length / 2; i++) {
            originJson.put(args[2 * i].toString(), args[2 * i + 1]);
        }
        return originJson;
    }


    //可选，close方法可以不写
    @Override
    public void close() {
    }


    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() % 2 == 0 || argumentDataTypes.size() < 3) {
                        throw new ValidationException(String.format(
                                "function %s argument should be (STRING json, STRING jsonPath, Object value ...)",
                                functionName));
                    }

                    //校验jsonpath 字段类型为STRING
                    List<DataType> jsonPathValues = argumentDataTypes.subList(
                            1,
                            argumentDataTypes.size());
                    for (int i = 0; i < jsonPathValues.size(); i++) {
                        DataType dataType = jsonPathValues.get(i);
                        if (i % 2 == 0 && !dataType
                                .getLogicalType()
                                .getTypeRoot()
                                .getFamilies()
                                .contains(
                                        LogicalTypeFamily.CHARACTER_STRING)) {
                            throw new ValidationException(String.format(
                                    "index %s argument is jsonPath should be STRING",
                                    i + 1));
                        }
                    }

                    return Optional.of(DataTypes.STRING());
                })
                .build();
    }
}
