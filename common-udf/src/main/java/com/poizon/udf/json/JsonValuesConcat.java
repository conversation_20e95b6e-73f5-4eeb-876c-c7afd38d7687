package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;

@Slf4j
public class JsonValuesConcat extends ScalarFunction {

    public String eval(String jsonString, String keys) {
        ArrayList<String> stringList = new ArrayList<>();
        if (Strings.isBlank(jsonString) || keys == null) {
            return "";
        }

        try {
            JSONObject originJson = JSON.parseObject(jsonString);
            String[] keyArray = keys.split(",");
            for (String key : keyArray) {
                if (originJson.containsKey(key)) {
                    stringList.add(originJson.getString(key));
                }
            }
        } catch (Exception e) {
            log.error("JsonValuesConcat UDF 执行异常。参数为 [{}, {}] ", jsonString, keys, e);
        }
        return String.join(",", stringList);
    }

}
