package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

public class JsonArrayUpdateForDealCommentMerge extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    /**
     * @param args
     * @return
     */
    public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object... args) {
        JSONArray originJson;

        if (args[0] == null) {
            originJson = new JSONArray();
        } else if (args[0] instanceof JSONArray) {
            originJson = (JSONArray) args[0];
        } else {
            originJson = JSON.parseArray((String) args[0]);
        }

        if (args.length > 1) {
            return deal(originJson, args);
        } else {
            return JSONArray.toJSONString(originJson, SerializerFeature.WriteMapNullValue);
        }
    }

    private String deal(JSONArray originJson, Object[] args) {
        for (int i = 1; i < args.length; i++) {
            JSONObject jsonObject;
            if (args[i] instanceof JSONObject) {
                jsonObject = (JSONObject) args[i];
            } else {
                jsonObject = JSON.parseObject((String) args[i]);
            }
            updateJsonArray(originJson, jsonObject);
        }
        return JSONArray.toJSONString(originJson, SerializerFeature.WriteMapNullValue);
    }


    private void updateJsonArray(JSONArray originJson, JSONObject jsonObject) {
        for (int i = 0; i < originJson.size(); i++) {
            JSONObject o = (JSONObject) originJson.get(i);
            if (o.get("id").equals(jsonObject.get("id"))) {
                if (Long.parseLong(o.get("time").toString()) < Long.parseLong(jsonObject.get("time").toString())) {
                    o.put("is_del", jsonObject.get("is_del"));
                    o.put("time", jsonObject.get("time"));
                }
                return;
            }
        }
        originJson.add(jsonObject);
    }


    //可选，close方法可以不写
    @Override
    public void close() {
    }

}
