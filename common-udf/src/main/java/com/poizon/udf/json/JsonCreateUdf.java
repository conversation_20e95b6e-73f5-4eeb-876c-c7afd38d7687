package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.Optional;

/**
 * Created on 2022/4/15.
 *
 * <AUTHOR>
 */
public class JsonCreateUdf  extends ScalarFunction {
    // 可选， open方法可以不写,若写的话需要import org.apache.flink.table.functions.FunctionContext;
    @Override
    public void open(FunctionContext context) {
    }

    /**
     * 实时计算中使用的udf，用于传入多个键值对，(key,value),将多个键值对形成json返回。
     * 如果传入的键值对非偶数，则返回空json。
     * @param args
     * @return
     */
    public String eval(Object... args) {
        if (args.length <= 1) {
            return "{}";
        }
        if (!(args.length % 2 == 0)) {
            return "{}";
        }
        JSONObject jsonObject = JSON.parseObject("{}");
        for (int i = 0; i < args.length/2; i++) {
            jsonObject.put(args[2*i].toString(),args[2*i+1]);
        }
        return JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                            .outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
                            .build();
    }
}