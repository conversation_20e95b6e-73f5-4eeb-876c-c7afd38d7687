package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;

import java.util.List;
import java.util.Optional;

/**
 * @Author: Dereck Li
 * @Date: 4/13/22 3:43 下午
 */
public class JsonCreateUdfNotNull extends ScalarFunction {
  public String eval(Object... args) {
    if (args.length <= 1) {
      return "{}";
    }
    if (!(args.length % 2 == 0)) {
      return "{}";
    }
    JSONObject jsonObject = JSON.parseObject("{}");
    for (int i = 0; i < args.length/2; i++) {
      //判断value值是否为空，如果为空则不输出，直接结束
      if ("".equals(args[2*i+1]) || args[2*i+1] == null){
        continue;
      }
      jsonObject.put(args[2*i].toString(),args[2*i+1]);
    }
    return JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue);
  }

  @Override
  public TypeInference getTypeInference(DataTypeFactory typeFactory) {
    return TypeInference.newBuilder()
                        .outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
                        .build();
  }
}
