package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * @Description 返回Array中的第一个元素的Key
 * <AUTHOR>
 * @Date 2025/8/6 下午5:33
 **/
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
@Slf4j
public class JsonArrayFirstToString extends ScalarFunction {

     // "[{\"兴趣\":0.999},{\"年龄\":0.899}]"
    public String eval(String json) {
        String result = "";
        if (json == null || json.trim().isEmpty()) {
            log.warn("输入的JSON字符串为空或null");
            return result;
        }
        try {
            JSONArray jsonArray = JSON.parseArray(json);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("JSON数组为空，原值 = {}", json);
                return result;
            }
            JSONObject inputMap = jsonArray.getJSONObject(0);
            if (inputMap == null || inputMap.isEmpty()) {
                return result;
            }
            // 遍历Map中的键值对，获取第一个键
            result = inputMap.keySet().iterator().next();
        } catch (Exception throwable) {
            log.error("数组转换失败，原值 = {}; exception = {}",json,throwable.getMessage(),throwable);
        }
        return result;
    }
}
