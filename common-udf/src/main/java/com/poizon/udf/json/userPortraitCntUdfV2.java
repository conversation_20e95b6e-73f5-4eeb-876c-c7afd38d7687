package com.poizon.udf.json;

import com.alibaba.fastjson.JSONObject;
import com.poizon.entity.UserPortrait;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * @Author: wpp
 * @Date: 2021/6/29 11:29
 *  针对用户画像计算list信息，只采集关键几个指标
 *
 */
@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("INT")
)
public class userPortraitCntUdfV2 extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    public Integer eval(String json) {
        Integer cnt = 0;
        if(StringUtils.isEmpty(json)){
            return 0;
        }

        try {
           UserPortrait userPortrait = JSONObject.parseObject(json, UserPortrait.class);

           if(userPortrait.getClickList() != null){
               cnt= cnt+ userPortrait.getClickList().size();
           }

           return  cnt;
        }catch (Exception e){
            System.out.println("解析数据异常："+e.getMessage());
        }

        return  0;
    }

    // 可选，close方法可以不写
    @Override
    public void close() {

    }

    public static void main (String[] args) {
        String str ="{\"buyCategorySexScore\":{\"lastDecayTime\":1624607678068,\"scoreMap\":{}},\"buyList\":null,\"buyNowList\":[\"3683514\"],\"categorySexScore\":{\"33\":3.0,\"31\":2.0},\"channelWeight\":null,\"clickCategorySexScore\":{\"lastDecayTime\":1624866775064,\"scoreMap\":{\"33\":12.6,\"38\":12.392,\"31\":6.212,\"1000596\":3.159,\"37\":2.511,\"12\":0.941,\"25\":0.478,\"6\":0.282}},\"clickExpandMap\":{\"3683514\":2,\"37126\":3},\"clickList\":[\"3683514\",\"37126\"],\"clickMap\":{\"3683514\":2,\"37126\":3},\"clickNewMap\":{\"37126\":2.95,\"3683514\":1.9},\"clusterId\":\"44\",\"clusterLabelId\":\"10\",\"collectExpandList\":null,\"collectList\":null,\"comLabelMap\":null,\"comSearchKeyWord\":null,\"comVersion\":1624866762930,\"commentList\":null,\"communityNewCategoryList\":null,\"communityRealTimeColumnExpire\":null,\"cspuIds\":null,\"dealLevel1CategoryThresholdCountMap\":{\"29\":{\"2900\":1,\"200\":2,\"300\":1,\"400\":2,\"600\":1,\"2200\":1,\"700\":3,\"800\":1}},\"dealRealTimeColumnExpire\":1625349600,\"dealVersion\":1624866784539,\"expire\":null,\"favAuthor\":null,\"favExploreList\":null,\"favMixPi\":{\"3688489\":2.85,\"3212228\":1.95,\"3854666\":5.95,\"3835766\":3.95,\"3803997\":10.95,\"3583087\":2.55,\"3854729\":2.95,\"33050\":2.95,\"77871\":5.6,\"3247774\":4.85,\"3601273\":14.55,\"3161456\":8.95,\"3484859\":1.2,\"3871165\":2.95,\"5019\":1.2,\"71016\":2.75,\"25675\":1.8,\"3920963\":1.2,\"3730656\":1.2,\"3825599\":2.95,\"3703939\":7.4,\"3758207\":1.95,\"3841443\":3.05,\"3678758\":3.85,\"666\":1.1,\"3725383\":4.95,\"3670134\":1.2,\"3486000\":1.55,\"3678938\":2.85,\"3678917\":1.85},\"favNewCommunityCategory\":null,\"favProductId\":null,\"favSexyClickCount\":null,\"favSexyClickScore\":null,\"favTag3\":null,\"favVideoRatio\":0.16,\"favVideoTimeMap\":null,\"favourArticleNum\":{\"AT8240-100\":2.97,\"DH3227-105\":1.94},\"favourBrandId\":{\"144\":2.97,\"13\":1.94},\"favourBrandIdList\":[\"13\",\"144\"],\"favourCategoryId\":{\"33\":2.97,\"31\":1.94},\"favourCategoryIdList\":[\"31\",\"33\"],\"favourCircle\":null,\"favourColor\":{\"黑/白\":2.97,\"白/黑/蓝\":1.94},\"favourItemId\":{\"37126\":2.97,\"3683514\":1.94},\"favourPrice\":{\"buyMap\":{},\"clkMap\":{\"29\":{\"averagePrice\":66540.0,\"count\":5}},\"favMap\":{}},\"favourSearchInfo\":[{\"327 new balance\":[{\"isBid\":true,\"itemId\":\"3161456\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3517187\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3787068\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3670126\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3411162\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3424528\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3605102\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3780904\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3247259\",\"score\":1.0},{\"isBid\":true,\"itemId\":\"3703939\",\"score\":1.0}]}],\"favourSeriesId\":{\"314\":2.97,\"1\":1.94},\"favourShortCategoryIdList\":[],\"favourShortCategoryInfo\":{\"createTime\":1624866775064,\"favourCategoryId\":null},\"feedBackAuthor\":null,\"feedBackNewCategoryCount\":null,\"feedBackNewCategoryList\":null,\"feedBackSexCount\":null,\"feedBackSexList\":null,\"female\":0.2,\"femaleClickCount\":null,\"gender\":2,\"goodsList\":null,\"imageTrendClickCount\":null,\"isNeedClean\":false,\"isNone\":true,\"level1CategoryCouponThresholdMap\":{\"29\":699},\"liveAuthorPreference\":null,\"liveCategoryPreference\":null,\"liveStreamIdList\":null,\"liveTagsPreference\":null,\"pay\":null,\"payList\":null,\"pickList\":null,\"pictureList\":null,\"postClickCount\":null,\"postClickList\":null,\"postLikeList\":null,\"postShareList\":null,\"recentBuyList\":null,\"refreshCode\":null,\"searchClickList\":null,\"searchKeyList\":[\"327 new balance\"],\"searchKeyTimeList\":[{\"key\":\"327 new balance\",\"time\":\"1624866770310\"}],\"searchList\":[\"3161456\",\"3517187\",\"3787068\"],\"shareList\":null,\"similarUserIds\":[\"34556274\",\"31978817\",\"49343170\",\"6160515\",\"12033796\",\"14098669\",\"1567444894\",\"2058093\",\"9446498\",\"9058616\"],\"smartMenuScore\":null,\"ss\":3.93,\"strategyWeight\":null,\"themeId\":null,\"themeMap\":null,\"timestamp\":null,\"trendClickList\":null,\"trendLikeList\":null,\"trendShareList\":null,\"unCollectList\":null,\"uninterestedItemList\":null,\"uninterestedThemeList\":null,\"userId\":\"64360645\",\"videoTrendClickCount\":null,\"wantBuyList\":null}";

        UserPortrait userPortrait = JSONObject.parseObject(str, UserPortrait.class);
        System.out.println(userPortrait.getClickList());
        System.out.println(userPortrait.getClickList().size());
        System.out.println(userPortrait.getCommentList());

        if(userPortrait.getCommentList() == null){
            System.out.println("weikong");
        }else {
            System.out.println("buweikong");
        }

        if(userPortrait.getFavMixPi() == null){
            System.out.println("weikong2");
        }else {
            System.out.println("buweikong2:"+userPortrait.getFavMixPi().size());
        }
    }

}
