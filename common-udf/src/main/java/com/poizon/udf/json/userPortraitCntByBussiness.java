package com.poizon.udf.json;

import com.alibaba.fastjson.JSONObject;
import com.poizon.entity.UserPortrait;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wpp
 * @Date: 2021/6/29 11:29
 *  直播的三个标签问题排查
 *
 */
@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class userPortraitCntByBussiness extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {

    }

    public String eval(String json) {

        if(StringUtils.isEmpty(json)){
            return null;
        }
        Map<String,Integer> resMap = new HashMap<>();

        try {
           UserPortrait userPortrait = JSONObject.parseObject(json, UserPortrait.class);

           if(userPortrait != null){

               Integer fivelabelCnt = 0;
               if(userPortrait.getClickList() != null){
                   fivelabelCnt= fivelabelCnt+ userPortrait.getClickList().size();
               }

               if(userPortrait.getCollectList() != null){
                   fivelabelCnt= fivelabelCnt+ userPortrait.getCollectList().size();
               }

               if(userPortrait.getFavourArticleNum() != null){
                   fivelabelCnt= fivelabelCnt+ userPortrait.getFavourArticleNum().size();
               }

               if(userPortrait.getFavourBrandIdList() != null){
                   fivelabelCnt= fivelabelCnt+ userPortrait.getFavourBrandIdList().size();
               }

               if(userPortrait.getFavMixPi() != null){
                   fivelabelCnt= fivelabelCnt+ userPortrait.getFavMixPi().size();
               }
               resMap.put("fiveLabelCnt",fivelabelCnt);


               Integer clickLabelCnt = 0;
               if(userPortrait.getClickList() != null){
                   clickLabelCnt= clickLabelCnt+ userPortrait.getClickList().size();
               }
               resMap.put("clickLabelCnt",clickLabelCnt);

           }

        }catch (Exception e){
            System.out.println("解析数据异常："+e.getMessage());
        }

        return JSONObject.toJSONString(resMap);
    }

    // 可选，close方法可以不写
    @Override
    public void close() {

    }

    public static void main (String[] args) {

    }

}