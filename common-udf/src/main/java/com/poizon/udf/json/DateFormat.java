package com.poizon.udf.json;

import org.apache.flink.table.data.TimestampData;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.utils.DateTimeUtils;

public class DateFormat extends ScalarFunction {
    public String eval(String tsText, String oldDateFormat, String newDateFormat) {
        TimestampData timestampData = DateTimeUtils.parseTimestampData(tsText, oldDateFormat);

        if (timestampData == null) {
            return null;
        }

        return DateTimeUtils.dateFormat(timestampData, newDateFormat);
    }
}
