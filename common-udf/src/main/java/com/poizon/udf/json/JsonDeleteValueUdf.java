package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Map;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class JsonDeleteValueUdf extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    /**
     * 传入奇数个参数  分别为 args[0]原来的json字符串或者JSONObject类型  args[2i-1] 准备替换的key值  args[2i] 准备替换的value值
     *
     * @param json
     * @return
     */
    public String eval(String json, String deleteKey) {
        Map<String, String> jsonMap = JSON.parseObject(json, Map.class);
        if (jsonMap.containsKey(deleteKey)) {
            jsonMap.remove(deleteKey);
        }
        JSONObject jsonObj = JSONObject.parseObject(JSON.toJSONString(jsonMap));
        return jsonObj.toJSONString();
    }

    private JSONObject deal(JSONObject originJson, Object[] args) {
        for (int i = 0; i < args.length / 2; i++) {
            originJson.put(args[2 * i + 1].toString(), args[2 * i + 2]);
        }
        return originJson;
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

    public static void main (String[] args) {
        String a = new JsonDeleteValueUdf().eval("{\"_track_id\":-2090983874,\"lib\":{\"$lib\":\"Android\",\"$lib_method\":\"code\",\"$lib_version\":\"4.0.0\",\"$lib_detail\":\"com.sensorsdata.analytics.android.sdk.SensorsDataAPI##trackEvent##SensorsDataAPI.java##108\",\"$app_version\":\"4.56.8\"},\"distinct_id\":\"202233895\",\"_flush_time\":1605663632979,\"time\":1605663625098,\"type\":\"track\",\"event\":\"trade_recommend_feed_exposure\",\"properties\":{\"$carrier\":\"中国移动\",\"$os_version\":\"10\",\"trade_tab_id\":\"11\",\"block_type\":\"35\",\"$is_first_day\":false,\"$model\":\"ELS-AN00\",\"$os\":\"Android\",\"$screen_width\":1200,\"$wifi\":true,\"$network_type\":\"WIFI\",\"$ip\":\"*************\",\"$screen_height\":2640,\"$app_version\":\"4.56.8\",\"$lib\":\"Android\",\"$device_id\":\"be76d9c9993efb70\",\"$lib_version\":\"4.0.0\",\"recommend_content_info_list\":\"[{\\\"contentType\\\":0,\\\"position\\\":23,\\\"contentID\\\":\\\"1015704\\\",\\\"contentTitle\\\":\\\"Guuka 古由卡 百搭个性双面笑脸吊坠嘻哈街头笑脸项链\\\",\\\"requestID\\\":\\\"1328875600819376128\\\",\\\"channel\\\":\\\"PCCF\\\",\\\"propertyValueId\\\":20315803},{\\\"contentType\\\":0,\\\"position\\\":24,\\\"contentID\\\":\\\"1017553\\\",\\\"contentTitle\\\":\\\"BABAMA 时尚轮胎风情侣对戒 戒指 银色\\\",\\\"requestID\\\":\\\"1328875600819376128\\\",\\\"channel\\\":\\\"FT\\\",\\\"propertyValueId\\\":20366846}]\",\"$manufacturer\":\"HUAWEI\",\"current_page\":\"300000\"}}", "distinct_id");
        System.out.println(a);
    }
}
