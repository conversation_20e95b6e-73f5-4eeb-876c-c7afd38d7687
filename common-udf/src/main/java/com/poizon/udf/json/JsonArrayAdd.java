package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.InputGroup;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

public class JsonArrayAdd extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    /**
     * @param args
     * @return
     */
    public String eval(@DataTypeHint(inputGroup = InputGroup.ANY) Object... args) {
        JSONArray originJson;

        if (args[0] == null){
            originJson=new JSONArray();
        }else if (args[0] instanceof JSONArray)
        {
            originJson = (JSONArray) args[0];
        } else {
            originJson = JSON.parseArray((String)args[0]);
        }

        if (args.length > 1 ) {
            return deal(originJson, args);
        } else {
            return JSONArray.toJSONString(originJson, SerializerFeature.WriteMapNullValue);
        }
    }

    private String deal(JSONArray originJson, Object[] args) {
        for (int i = 1; i < args.length ; i++) {
            if (args[i] instanceof JSONObject){
                originJson.add((JSONObject)args[i]);
            }else {
                originJson.add(JSON.parseObject((String) args[i]));
            }

        }
        return JSONArray.toJSONString(originJson,SerializerFeature.WriteMapNullValue);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }

}
