package com.poizon.udf.json;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;


// TODO: 2021/6/25 用于解析app_start_v4 加密的appList 兼容v2
public class Base64DecoderToAesDecryptUdf extends ScalarFunction {

    //key     a8dd58076db7733c  生产key

    @Override
    public void open(FunctionContext context) throws Exception {
    }

    /**
     *
     * @param value AES加密的appList base64
     * @param key   AES KEY
     * @return
     */
    @FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
    public String eval(String value,String key){

        if (value!=null && value.length()!=0){

            //todo 含有\n替换-----v2 android
            if(value.contains("\n")){
                value = value.replaceAll("\\n", "");
            }
            byte[] decodeByte = new byte[0];
            //todo 解析base64
            try {
                decodeByte = Base64.getDecoder().decode(value);
            }catch (Exception e){
                e.printStackTrace();
            }
            //todo 判断是否是GZIP---android
            if (isGzip(decodeByte)){

                return gzipDecompress(decodeByte);

            }else {
                //todo 非GZIP走AES---iOS
                byte[] keyByte = new byte[0];

                try{
                    keyByte = key.getBytes(StandardCharsets.UTF_8);
                }catch (Exception e){
                    e.printStackTrace();
                }

                String res="";
                try {
                    byte[] decrypt = decrypt(decodeByte, keyByte);
                    res = new String(decrypt, StandardCharsets.UTF_8);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                return res;
            }

        }
        return value;
    }

    @FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
    public String eval(String value){

        return this.eval(value,"a8dd58076db7733c");

    }

    public static byte[] decrypt(byte[] cipherBytes, byte[] key) throws Exception {

        SecretKeySpec secKey = new SecretKeySpec(key,"AES");

        Cipher cipher = Cipher.getInstance("AES");

        cipher.init(Cipher.DECRYPT_MODE, secKey);

        return cipher.doFinal(cipherBytes);
    }
    public static String  gzipDecompress(byte[] input) {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ByteArrayInputStream in = new ByteArrayInputStream(input);
            GZIPInputStream gzipStream = new GZIPInputStream(in);
            byte[] buffer = new byte[1024];
            int n;
            while ((n = gzipStream.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
            return out.toString("utf-8");
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return new String(input,StandardCharsets.UTF_8);
    }
    public boolean isGzip(byte[] in) {
        boolean gzip = false;
        byte[] header = new byte[2];
        try {

            ByteArrayInputStream bis = new ByteArrayInputStream(in);
            bis.mark(2);
            int result = bis.read(header);
            bis.reset();
            // 判断是否是GZIP格式
            int ss = (header[0] & 0xff) | ((header[1] & 0xff) << 8);

            if(result!=-1 && ss == GZIPInputStream.GZIP_MAGIC) {

                gzip=true;

            }
        }catch (IOException e){
            e.printStackTrace();
        }
        return gzip;
    }

    @Override
    public void close() throws Exception {
    }




    public static void main(String[] args) throws Exception {
        // TODO: 2021/6/25 test

        // String str="AijQKluNfVfQvze7fm+g2MPRQIHZorwcnSwqx2NGs7BCLnFKYBHNSsCHiqazM5eqq5CTPykf6PGH";

        //测试用加密appList
        String str = "AijQKluNfVfQvze7fm+g2MPRQIHZorwcnSwqx2NGs7BCLnFKYBHNSsCHiqazM5eqq5CTPykf6PGH" +
                "Qe8v8UZh9pEAw+68vUpiM96ZAcidOf6VpoB5dqUO1dAnD7FhSK1pFEGpGyQJqMPdf0vrxPQY+7et" +
                "RvSTH0dQnmSxn8rDDCssS+5yRm5gyoIZArk5WboVYuWfxNo8cKgIXidKG0A398NP2pcuiaqfOtJ0" +
                "TLhJV0gOJFoAi3PlBGZ0SIjcGdmtJW/6Wlt/YuZpyOvNXGdzg90MhyMGgCZZXpY5ISwT2KH4s2Sp" +
                "9XFfy518fZbfIMHyS9sggyfc3WkwRODFGJNgWetzWB+0kVsvIWGWkDCA5WahRNh/RM8e75zj0Rvd" +
                "4CenAPXxXUW/a9W9UpgQ/UaRbOYaaz3rhoUl8qSsI75K1/lLate3+WMDplgyPZveTWsNbmnsPGOh" +
                "+tNsKd5zZ5ei/cHSZW0F2dGMciJj5GA7C4R7F9tLAbbVy3W4GQuFyoCsoXBujo/ffW5TLkvLFEtE" +
                "xKpvsBPjb5RiBOI7NQS9Tj5qV5VmMG8GloL89bGNn3fCdpH/Wuhy/UfKYQSwrc2ulFV9HlNBxQA/" +
                "+HshvrppGkpNeg6+4wKzVKMbgL71YcHCMU6KLGUMJ9qmxif5xHU/XM4HRTRMPz1xuLrudpCl9NCe" +
                "G7S7XHQIz/AC6uOknw16Vtt9y7kqSJF8Pjui+UqTeZs5/4qTcJtaSAP/YAup5sARzXZTcMG+Gdfa" +
                "+hgijO3KXFFfy4KN2dsxqS3eDgly39/3vrbxT9hiIk61jwVFOhb4p+5pp6L/DbRHbjfsgMpAha2Q" +
                "5/VjxEjWClKn2Ih1Mzb5pnnyH1XzjTpG2ro1MV0/6A3lZDE13rgdUaktYwMuTG1cdvPOdLgFxJaF" +
                "FsBFlf7R/w4IveTu8kRuyMz9t6EhVPWKakzeIaKV5ifcFzSEEUHmrvzfXCRsW30dcSfMHUSOrwBc" +
                "XzEYymJpToXWnM/u5zBVfyMa8TLOI3LWKmVxugREqJW3fUPc/aPA5j9xnQhNBPErCXRgX3Tiq968" +
                "DX48SEPQZZlzo9cJYui4twaKSL4+H+TL8WrTpq5yDs6KXGYdnnCQaBjBrL2k+0ulEMTi3HVHA4hg" +
                "KPhpi5EwbFmj8rUEM/6sF4GJ/3NMvbAesNgjbq+TEbPDzWtrvnUkNED1CoVSck1x78DWOYzYngy3" +
                "zMkEYEcSzSGc6vPK6UZBWUwWSohwWrpV44CI1w/Xjn2jgELnvPPGxZRP7FXRp4pnovJ7DntfPfGT" +
                "jk9wu0+ZwrWijrxanYHkco+VVbdjAeRoZwQ2CSnP9kTVtNHn/blim/5cDuxE8A05M0PJFM54D8uo" +
                "KUslHPSiTE8Nc1Mqfix6KsXpGIftClN2ifdInxIHgKMnz2uZv42VEHIs5b8DV34+d5Eb5kqrT/Q4" +
                "4wJfehRYknfBQtOcsP4PB6aA/BRAb3Iya8LtMyis2a8Rs0odC01rZDXuZHwUx1oMLD22A9HH2PbI" +
                "d31bBXeQSiQxFxtHi7dZZedq2tmoevsFICE/eD3ZeuwGWskXf/RCWblW5pr2lKegFPiNNsNeAZ3z" +
                "hYUxR6unl7oyjaQk86fOO3sciIGYmGJNnL4JSpwN3XI3fj28dK5U5w/ZfXlNcE31PDzpKVTNBcSs" +
                "NKcyUACJjcJm4cMvzYcxdV9DBsiadffc2Rr92vRPFJ8Lrlx7OeiMFKmFFokeQw6dkrAcRTd6jfLG" +
                "E9E7lWsCnSgMunGbwfJOqDN8ufQdWfzwxTqvqzyJzSRzOIWQyklvMLbsUjsEDnCcXnNfYnu+E9RC" +
                "FQlRdl4eDOJ26sImwZxcn92Ewi48cS43XnbdiCwf9Wjz2lyFtgq6K4g9XXSTAk//QNcEVU7Jz9WJ" +
                "CB7MDExhOpHVCqkStfNoTPNMB4H6toT8HG8kS4hP+JZAdzJd4VAEDrYOr6O/wnPSF16ZNH/UbKfh" +
                "3bflpt1e7SnXSDuqzjqBgVdiqN30L7dfhHLf36VMp97ptbyIdf8cSWskc9NSIpEc2dNLxeET1V9H" +
                "/6WH9hUCPmjBZR9b7ZDKqgbbHjcafLzrPE4zEu6BRipbItOpiWgpUs9LwXuYPXfAKj2qs3CsR5dw" +
                "64kBF8eYZ1tL9q2VKTIaFdHa5vzi0LxzlD4gS8IAuHViuHMJceBlYtqdiddY4IRUojoHmWjsQBTH" +
                "MSKFjwOVIbdBYBhb8ikE5qeoi75U0KLokeEqwc78K7PU85qyU2YPzlVf2zQHGOBw1Eq5gI03TcVW" +
                "ZuEfXPmAeN8VMSWSdpg43N9sheLWdBlTTrv8oj8gQ3rz/SODir/US17pZYph1Y1MSNcv/RhHbg22" +
                "m0jswGd/Nt6sAHAwmuQcrCH/xxN/KBOu8kiUAmFGM5rXslzQ33xm4B/rjdkzjNayY7gz404sSxVk" +
                "OQmZSOgxugTafJ1l7FjfiAJI+hjo8ELjz6fDxdS2eRk8PHY00C612qqMRYaGq/NAI+rovEP3tfTY" +
                "rJBwM00YzNRbxZqkQoloozhD32KKruovs1W3thRrHj5t6jaHnzUlVfl8HJa4vuPZkezNqpxAy+I2" +
                "9DB1pwj7FLALGiVoHTBcXKaxfPiF37uPKe5hBWjXDE2zaS1FXqFoh9lD9zAi3iL50Vf1v4Y0vdSX" +
                "D53d9dxUhnjHoYdw2Sc24S38kmIckROaAoqeyqeAf+qbwjZBpdYbLc2wP1KBo/i5TqkZ0LKY2T17" +
                "S06QWL2GaNbj/KEMVRx+pKOsCQi+eOff4PLKhOYaaz3rhoUl8qSsI75K1/kRThTmW5qmETCPIiJ0" +
                "19YUK3dqtP6edqJOjHPKV8t4vwAkIyfCpYQsZtGp+KWF9v9X05XILZYbrMFI6Abnqu8OnOq3n9/k" +
                "etODRRaDTDft9u/4n/8NUWY9LxbzBCkLnB+IgjYGIOx2yqdtOYY9TuJqV3HIKJar43pEZqKv7UfU" +
                "r76n1hLGiYR6zvvPPdeQHJsM9WdZOXjJ9sIEfOn59sdXhOyD7TSm02+c489fTHqc29uP7ra0ugiq" +
                "urr6OWxDqG1b6rhL+Ioy8m96TtXCW+pG1CGrlw2XzY/l14HVEyoVX+UQEH7EDorzU/4hO6cEIH2I" +
                "YkWzQxcpBKgUk6QhRYD47C3rLUXenLrYIFgrrPG4WBv77f+lGrkXaOl/IqdBM0z+7Nomsnozu0S8" +
                "a3xusdErwDfNShPtsK0Dy6Ynfcrh0uitE52DAsjVWricMFSUawfzNMyfaUi7CrMdD1aZIJAGXvZ2" +
                "D1DgWEq1goyv6miWPXQ3iFlrsCFGJ/8d9nYeHxGuP+UrHVGrXszv7Onf6369tGG79qzfTi2d0HFS" +
                "FQJV2rzqRlBjXZf1BK2Srlqh6kSWfmRByhkCYIod1r678AYBA1aiBXWEuIJ1QrfJHNmDoOZvkJ5Y" +
                "B7d9rHLF9DDXEVT2XdJujTF6SiNxP8/qZKwZtOBXqPKXxgHz6MnjwjOQL/cJu06hrFwtWsGljvLF" +
                "ZlkIhqZKrw5yc9zJA7z1kAR33+pSL52Lv34ok9Rr6sDX2863tWOqxNqsv1JnRME+dY65q3O8yTaE" +
                "TlnhiSZ1s9EGQmiehKczrovuXHYHIZdVVejunMds4Ba+9rTi0Cfwhi3UIN/dlzQBMYjO4vHKF63L" +
                "r8b84Pan4J5xKjlRiG6TfTgUyTs2DJIEwaEPEd9ISasRKsXc97hKf4nZAb0SoWzfx8JC1fVZ/Kso" +
                "tqZ0yTV4nBLzHGEJIZH3An6uny677qN6yOefv43BHAEiKS1xrLjZC0jneOLiQp8DK5RoCt6NxBK8" +
                "UvU9i2uL8NgufuLWn6HwuX3ywFBVzcB+f59BX2E1uFQb4YJnDCOhQ2zJOyZULS+XCbfA9y9nKFiQ" +
                "cycWxKxa7jIvF9LTJX5v4xJ9bxjcP6Y1/o3A8uGyJ8tiWsX8dZjuA9mQ5iAXb1nKVbr9oSsPHEW8" +
                "Z/WPy38JjkRLS6hw0AZUAp2lE53uyv79iMFFh2efB4yHvxjgsv/UfbqkQp8gAGRB1cri+ZUuc87V" +
                "7XDERLdELm6zfGgRYD0hOyUElAS5iNUioM0XJ+/ExAP9spvNa0I80h6KZo4H3oYyYQu+m2SoFWCa" +
                "2aBFlZKD28dXNeH8ySkNo/EYrHZBk9yV/mPtrqHZVJNyUx4ixhVNrNj2T+OjlPwZt/CvGMzSyapL" +
                "85tlhUSksh91Q9fflfe79P6hhWqY0OjYeq0a797bhpAWh6U/MBolSxI7Cf/RX+POYOb+FV+2uEc/" +
                "qqodeNqQgbi2HCiy0TQc6X5QQhOsFuVez5uVgPPmTdMt6H8gqUqvlu7UB0eC8kePwd9AxsavtLV7" +
                "tXcvEV6mlZeGDt0k16rBBMNIh44gjswxE3PGlYfCatUSJgsIOUp0b0rI9gSibml1zVoKwcJbGTwE" +
                "BluLdHNHhPJTHfvQ98ZvE78I3p9fBPqF05Omhnh2fnGnzLhw+Snizvuu9atOkMazQKybVTLxSQ0B" +
                "Q9UFqq3eTRY9sbdueLjuoGBW70YCcdT+jSGQMv9Y0/XTae/jl8OnNYI3DrWAvILkXyNQifPOcuDE" +
                "VWMop8L+Mavlo/s9yAAGaKBVsXFECTptBuv4rqgZasx4c82XbbZZlcSEhdrW09Oz4b/zvsfjH1N2" +
                "8XS2PAhkm140s9/ELI8Oe6cCuvsI4cZoIrpbkLbsLCS7DihMI4VMmH7NzZIT+mIwVf1K+Yw9uUW9" +
                "0fxSbkXjEgOz8080XBK2Z6sXpwXhedqmeYk+VXJPweXvfdZXxZE0WS2XeiYfz/DKnmZJx2VezsGf" +
                "iWLJt6/ELA3zv0Yn1doOGrVlk1oqQPihApCrvHzAvi+QfEsdXAux0wt0Fdo+oq1DGbvdkx9ruQL+" +
                "++6u2SmSyl058CbhYH8kFdNjMKMvPYyhZXkjE4ZNp50fuXHYtoGBtyyT5au1YojIPJa+f3olD7Nc" +
                "EflfktYe5vgbKPekZZITiFfi6qXPyBat8vUXn4yR2MbL4mAIssDeDsRsYaUZu8sBeg1P2CTkn8K7" +
                "f31ty6r/dcBEtuu/nHfd58n8UzBHN86lUUiOE8hYk5xjFH3/Fjy44I78YWkrR5hEWctSrDdexFKW" +
                "CTJnfdr0uaAOBJ4qqzdsnSxDZFyx4NWb8SbesVtapE3RNRSZUyVfB5dN8EmmM8MOXcqZaALTiSPN" +
                "dgqCZrA1DwB7QeuHY4Ld6N3/PHIV9o3lcBHmQEjZCvyUEH/xF9B0F8vr5lAJyaaxVBq0LlInierE" +
                "Wj2Hu+TMb7xwmwjfJ8YY10JoTniN8tX39MPIKO3LZxW/PTmcDIa4UjM8rsXvHYqcFxQVovJ7Dntf" +
                "PfGTjk9wu0+ZwrVWEv53xvJnhQ78CPS6VIcOWHjOjVcKWPVobMOGCMUplq902Wms5SVPlAk6mF/y" +
                "Y1Q5i3ltw/hJqdJImM7GnsBizVk1Qt+8kNog/YCUnQap2XnkU89PeOz8A5K9+PC9L6Pind2bsY1X" +
                "5ki8Dg71PQIrYXkLoQhTSnL+0ArruwRSI7gRSrNVjcVAYWj1WfzPfV/GAxp2zWX4DSO0bMMSYvOC" +
                "snIUjScw4szo89jOrLkZRR0WzXgV4HslAlr2wJUk9pEs+rbFXrtmZeD4+6rGcKSs7B2pWB8+E3pS" +
                "KWjpnJf3UZxrS4JTcEUJ4++ojViIz6gNbIM3sQfZ9KjR7pV78kelnUvDXC37Y43pIlMKglBKwCqn" +
                "hN8waJIxwDE/IHI7UMriQQ13OGrna8ol4ncyLSvVgDX6fMRoEWRCsZUuJ9eB8N+JnXX7dKRMjVbT" +
                "E4ojafJA/zlirH4caisSXUIuddUTMAh+kv+5txX4Pu4YQipJPRHCYRAsUvB7iEFVR32ToJciihQe" +
                "xOEfeDcNB20oD3DHWyYE34/iHDw9JS231iqFfqDF1SfbUl7UgRJxAJEM8EZu0OMvEijge8z6NbDs" +
                "LNcwMHLecHYqQpLlqHvfIgiRf0/jeGE423rJCHD4fhA1DBbKigF3E3HT+8n/rYjMmlZMAxZW91lP" +
                "IHLD6DpAHxqhJ36y+FN7h3+jDtNzJAPr5+xyljCpE+qtx4OrMMcXwjISPXiKX9It+6FLgwiYA/Pf" +
                "WmtjF6iBgNTmIQ3X9J8LyOIWg2Hd+QSdL3FCWcDqVNtzrP5SyQS/EqOCfmcRzyR5ih8nqnTgzL4v" +
                "f4c8uonFmSKXa6MTufX/kGVmeXXTCi+u0mJBlBy88YZoCkzOHabbRcCs9gHIrb2AtkAUy3AB7OcD" +
                "3nCnIxPmWjuaYxcjtnixhqslBYRPugCthp3UOSJCIJC9v7f54ugxn3Y2hXpgHWn4wd7AvIstK9U+" +
                "aymnNyWFLsGGzd5rQn+IEu9aS/7ucivILgTawBseI6tM0i3D/T7NI7XytPgcpOti7KRCKQK6rN79" +
                "QMNSXPWJ0iRqg2XjlYG5rpMbeBOOJWF7eAIwVePWVQRNPuMZ+spaTYrGGm/lFlzsNXdsBZFYVJaU" +
                "J6FL57Q9BSfmnHu3kENPVw/BpXOlJZANI8iK7Z6uKlPlKxWungrgwtQEFZay42UujmK/BhzxlKBt" +
                "PZeQDGEWGB8Lt13+OrgcoxGNF8wtCEMy9hLFFDLJCRBWA/mqS9bFQlld5elg5ebzVMYACtxfs2F+" +
                "3GzLId3rmvsQ7zwiZYymq2sBsE9Ecigiv9utbqDtw931RI7vQejsLvsRTKPDscSaU/6A2twE729k" +
                "XghHeFpjSW2hI2H4AW644pyGBzbhLSjvmFpDxEEBUKisKlUvd1us3eiBIg4PkkKFE9dJbRFvYtUw" +
                "O2sNKRWllGo8YuxxFIbBCk+j77q80fUxpLrJW2646CP5i6iUOMh59cYVuL0quVuE86Vwg2Vbr5iD" +
                "Q07Gvm1L5hprPeuGhSXypKwjvkrX+W04FLw3AAPGHkY+IVZf7u3Q7pgudKzgNRU6jxZ367V1sfhG" +
                "PeFOgLjVg5c3bnaoiuVQe/tifKsx5NDoJDAdJDY7n7FMK+7OFCHIRnA78aZ6lF9Gt3wmcqlk1FYv" +
                "Vi6DTy1SuofKW3BPrIRT2mqIH18Zfoa4A1n1DcBfDzm1R3yuT5TCbuU31vTFz9RXSVQS0BQjWOje" +
                "97IwqSrGxCu7VMvoN2PSd3AWeFV7e80MN26w";

        String key="dewu2021dewu2021";  //测试key
        //And V4
        String str2="H4sIAAAAAAAAAI1ZW1PbSrr9K3qcqdPbs2dPzakzj+CEhAkXBzthzn45I2xhVMiWtyQnYZ4wYLC5mYvB3AkXh0uCgQSCsTH8lyl3S3qav3C+VrdkTEhqV1EJ2FKrL2utb61P+DyHr5dwbQlnviJyOGmuVq3Tav36AwoOiBH1ra6GB3UUVmM+MR7RVDniCxu6L6HJb+gv+oAcQ/+rJkPJPqnpooSmvpF1WY37fhuIJJSk7lPfSJoiDqH6zXt7OGUXDlFrdzBorl9YqRX00q8qqobslTu8WDRvZ3H2AD1TcSlr7aXx6iGybq+tgwWcWSGXV/g8h1rYY4SgpL2Rw5IudMh9mqgNoeeSGEkMqHFJR/biLS59RTg33bjPzF9ap5t4YdrcPqnf7eCTFTxyiJ4MxcWYHBaCQ7ohxYRXiYhowABwsT2Rg4vJ8IE1WkNkcgnGIBszeHIHBaS+PkWCh3y1UnmcO4XR2DcwaZw+NKtj9epXfJYzz7+Y2QzZ+IT8L3sQ/2D9giyc4Mkjkp1CpFDEs+N8JtbdHJ48xKkirlwhXMmb+UPzcApXciR7hM+X+LP5KPu3eLry7a5HJE33xaSILKKAKv9LjQvPAq/aY2JU4itA5uyUNZy2Zvas4WHUI0X5LjpD/WbIPp397QvTQ+F/IDNzjosTVrYCB6NGFUnojktCSI5JQntcNhonMiBqUsQ7j2eSbiQ1URG6xDdyVDQAEUKrqCFruADbS/aGyeUUYvv+XFISkoaC7Z14Zgc21fxSrVcqfLG4PIdzV02LfSsqSkKEW8KamqB3+kUjPNChRtFrSdclBZnrZbJRIe8ncGkN+eU4HLIaRzCsWd02l6btzV12SvwR9coCPNUeXvOOEllrI9Za6nWgix6MNVfES+P8GzqRd7KoxmQfIEdRkxE9MugLRgZbEglFDjsLRRTI67uoXq5ih2UkN2fuV4ABK3b6yjoYt3fnUY+ajEekCLLOx83FPX7meDptlnbMuXE+wXp5kgJ4vUamZhCertrXdyhElw473SOFDYRnZuvlCr+5PII3KujlS3v7S/16ji+YAOaWzxDeX7ZHDxnarNIt3p9ALe1+VY6zw0+KCvwf8yXlmCbFVEMKK7IUh/Gzx2TlPSLlMsm4DKiX8yS9i56HOjsEsl00N6YoU/HctDV5bu9UUKs6KA24T3du4asJwAmKdFOTYSkIAuLCLwRYaVPk6ICB7PQMLhbYBNlO0LHhQPH4NF3ZSMksnSNzepaKB5nbMC92UWdMd0fCixm8dIYnKtbONMxzyqrV+H46ZIJdOKjfFMyRa+t0DHW1+YWQqirIGsvCD3LQliL503p1BZm1ebO60ZgCMBYwBX9TaXHPjkze2NuXsAfW6S7yt/glzQAYOLDKrgPMgdIooKnvhoROEZCuITgJcnYABwkIQeTLGT7Lw4MoFerlYwA9yQ7j24J5ONt8KpSbhqRIVOCGXF4ycSKHO/gGmD03Tka/kPOaVfuAyGXOOsg4s16t0GvYuZHTHGgOBeHnDdQii7puvHakmvOAXQwcRPb6MT4/t76mzcoB3kuZlTLQSAsnFSAw1yDQ04NxrmRvpHhE1ZxZym8VMY78arxfjjI51Zq4qzuMT8KFYSgRbml4JhvPk30Ilo4zANXaEiAYdhuZq7e48sG6XcTpIvmy5KnqdzBLdwaeB1QgmSuYGMUMnQrQJR6WhFc97cITWVTgks72V+339RrZxbt6Zbx+nUHuTH97I/Lp+ehT6C4tb9rpBbO4RXe2Sx6U/p16L/Qk44Jfgclbq7MkO+9qZADuaxSpflUTWnqaANl0IcgLrhSAH2FAC9ygQ0kMyjEBwOTMGKSNrr2yS8szMKNerTXvqmQYcjzq1VqQpqSMQNFMoCd7nHvpgzV1yn6qYcGheBi2vwQKCEhfxCd5Wi7spTu8NdUoonTXrItDqI0cUUznQFrwzLKr17CU7VmutWxvuyTjraoNBg0xPNjNT5xu/nkal4C9aiQJ+9ApGSLARUTWaQpWyRwAZy0bzTo9AVjgmyWyOItaFRis2Z2ImiZDTXCg51UlUnhvr22Yu1RV6FyeaZIUR6R6AT9cJrrDEkA2mBCBU70tgXY2AfQXoTVpGFC1HhawsxQDArIyx6QAFLkkh8dQJ9L2+hUXSvvjCqVd4SvOVL3i6uGVLlM3RCOpx1VD7neLBpk/wvtHVq1kFg/p5rpLYKt3C0Rp83E+wVDhAY9QzzuDgl/VJIoBq3QKjKqXZ8nUIS2lnOwzu5Rhvf2el0L47iN7CDtdsjNOtr56wKGQ8sbvBFrFvfscIpH3ZXw3yo+cHK3CH1RqhrPoqagP0etpvXKVkzsdJvS07qZOKf3N7NEfyOnCH91JLk+QnT2vxvznJtMbCEJRB8v6n5ss+rX71+5Qd28XAvBoKmi7PVqr36whCfBKFw61waycwg8eX63XNhDzfqgzFBCgNNPlQwm4V9c9jsR19IrZEbYhTd/2AfKSCQdnWoxPlF1M7obJRbXZq+WrZLKI2uNw4FFNjIEGDOPCoXmwTYX6r/8DVRPoguy1nJn/gie3oIaTs/l6ZR7hchmeDKBHvJyzceEccW7G/pBBr4cGgfNtsiLGoFI/AQVE+GYYVgUUwac3SNWiwPKIJCY1VRN9ckxH4CLgx7q4wsPb5NMtTMuAWwUYA3RaeC1pHhoboHCWYU/M08ffV8yHgmKIWlQyUGvI3x5y9ssBjAxP0OKi4hMTCR+ze/eNkn30ydoYMY83gUhAHRTkQiYEk9EouAKZHgUzyo0xDU2M64ozgI9uga72G41vw9QMSlqC/udrFXWpyZhVL+p3JV4z6VrmRsFiQQn+rl/tThqKqg42xvcYF5Eccfc4QaYn8EzBXsh8W7sjLGdEIrpbvOvlDbJ8DQjFp9SoTFjnlAJUZJu2T1Nj4JhT4NupbSKXH3BuniYPkC3Xu86b6ysPnhjWZCWmR40kcE6hHDNrJZ4zOmHU14k4+NVOMQ7J4EF1djy8bgBeohLDObK2ZvBcpl6bgUE4tO3h90BUp1DMnOH9MWo+uMVgFmLyiJYcwC2tFlDKlzLc6gvM63voAuQpoAZN7h4w6NMkXU1q93bXrVftbS0tHjS3KxCvwL3T5HSeq99totDTjo5uZ7ioU159D1Eac6qN7pPeGW7a0Xk4fFxXow4uJJ8ixyXvqNvan3QLr1ra/uz7GYq/Y5St9C0ILff29EBHa3aqSGYPkF35gGuLePILNyacvsw7cgcVlMLwkKeKRKl8H7DM+3DDTg1rrya7nqoJYn2yGgMVlMO6rx84AQTQgHvuGiEMl/DCWv36zMptNE2/JdiJngZBaIHTDd8Ihd3e3WA5Cmx34wsvKSqilw+Z0XfA4GrfXBZnjrgbdW+JJGL8FtACkKIbvL8GPw2nTZ11w2belmAzuSfhjQk2YP16il7JpszdzT9+eiLD2RgM64hbhfy+PQ5leuIYAGLdbZGpdcC/0OMPPn7UTeapVwpAjNF+hKVoTOdug8U08O8QzR/XqSEwWaIKhQrSeZskUkgBq+z1ce6h+Mm8SsQlQ3C4vkNzKtCb2z27UCP5O6hqjfH7wbE0T7rJD0mK0qepYiQMZkOTwhLdoEZaZ3Tslfpey9JbSGCiBl8kfg91Imo4SWGqU4u5cA6RFfLZAig5qlcnqVbvX5DNHbKVsoe3qVe//6njR043+alZkwtkayP0+oGXpSbeiTv046b1Mc/seRoonuSkiPywUqHVXaqnLWQja6cWqUB+yluls4b1OB23F4r24i16+cTf+VNb2++RH17dOCP8AyDMEnrlbxCDynHmHFBH8+l9K+xamfueqru7lTc9uAB6PRaycELyt050m1yG0RotivrN+6bMwKnGP2oanjFS6O1o6RKsyzFSmWtuFtE22UNh/a6VfnihX4Td7lCjUB1pffhTj0TrhURpZubPzeMCzu155kBU9YSnmj8kHR/95Uv0D6e74h6j0AauSaL5QXghSXTLyMQIqAAubyJ7b4vKwu9AzyOLVNSwqDgUTaia0ejz2NUVq7TPfZ+Z+khWtpuC8DfKy7InxDUYcbAlaQwgMKwke8s6Bo+YAeYbIADIYHDCniWw01fmVIGbvS7VUOkWyP3Cn4SgA0YhCHMVICC6oHE6C89hPTTR/l0dUnUQ4+F5XB5tRDa6Z99OgW6nQvkCKq4D4OF+YBRs5yNR22vaeNIsyhGF7XPEMZ3jkOOu6+UTcPwu2HkPhRGPfcZLY2+nV75ZoGHRl1/D5DtAS5cHgB56kiA4kWZjSsPofwmtEBvpLHmHyJz+aG7cPCrBepOhBVlwa9VVEbIBfzL4F5I+MPPb4OMR3jujDLRX7uyJabiBrcDx5qfT9nCe9niy67RE4ekqCgIINE97nOKETBgrO8F6gGBPyOS+24dyWhy8+FPyV65+Misf6rVF2hkF+EwW8fwkCrV0utP2XKDQAQbg8SLzLuY0JKkmROR34EfNyrXTJJsqPHKwZOQTuSyBA6YRXIrBPmBAYOn8D52h1j++/sX3M8LLGyDrZCprr6fhIvoMBYo338JeuU1GeOMMRmgiCO3m0S4Ya97ABXj9FqJXsX4923zmTbyN6YmkPoCCXS96ggDGHbNUcPr/mWMzd8LNATAEIgjqgEom9LpNYCEgh2mhZn0MtltQ3frAn6CQGNWdFmktR3Mhn6AryFm8mHKl2kk2dr5EzbRVO6UtfDbJNkV9KwQGgI4823OgPx7CvewAKVHzhM+qVRtuCs9W8eIxHruDyOP4+LtROp51d4z3PuKzG7pnqQna4KcIF1qVpGSoKqjK3/6KM+M0P9L25eUYEJabTkCNNfIZBX9LAkMViRuqJsfOGh71m1S9vOb2zx6U+Sa/4L4IaKq+eHbbLG4hvDWKN25JOe2UcGcGZPnMLlTRy66nIRSSwXwKIU2MANec0GEeVezCRaOVGmj3d3f7keOvIBCI9B2CqAxRLUStkLViIoh9gL/R4PEF+eM0yH0rZQAC9DLU8+0XSTnGIzLMhSKEJeT9I3KWR3Z1jHy+5idSr3yCOEbjIETvF+RkmscY5t+phcULx3hhiSsYV9+QZEDMhOEfqz+85RWQtJis0xYr64gy2lK2RaS+ZBTZH1fw7RWnSIfKPL/bNYR89RFOG/6lHpHMj4ESpcEJgxX8bJ/fALEroCYcRQ/f07mv6OyFLPwATbcf1jG6xp1pKHCwQLN4+Odf/vLzf5PJXfgVMf2DPbJ3Lt0GQKEKctioAY9t97M+0TsiWudkYJ+7lm/jMzcZepM9aL7ERZ+btPkCeVoSgB33X2DxtzJ9LmG8gSHvsiaqjrpaW4RfXvzyM/p1KB4VBWbxzeodufhMUyA+mbNHSu6bIZDluWl88sFR+Jk1BDuIry5weooHxsc6R41o4PXrUXAAqEW7a2Z29dHE60oZT9yMNfjqMxQOK18z18Zw7sp8n0Ikc0xr+g8eSI/DjZknBZwGsTteAH1oeu9DhotkaZ433VoCLwSnO6VQFwHLttZSgDU8mqPUsC428d4XamYyd3Tb/aL30oZHfd5D1ZNgCeU4XSUtad/rpoCTUXgRIOcTVMzo5KaXmVjxKQEmcTlFjU63Fh6A87ZOx62LA0QKX+2F7ab3eI3ifK8BBG600XdqQuB3TD5DDgoktQTIp6e4Qmf7kwbEcJq+krAL13QOHaGn9xLH/VbKj1TVTWEP2vTjC5T+be2hf3YFn3cHENkctVfnQHpw5qsTxp1CSlWdd3oYSyBNvzNoY89dajCmP6WfUBLWy1ByVjjVqdm5TNkT4DtmqJ/FpTXYcLetfrVtlY+RMyYMyIa634IgVyu063y/W8a8AsktOCaHls+vfNZM5TkzIsn/Y6+jm3KLU0W/FRCunG/lKGQOwxVXnwYwsBY3KXWabjHkmNRobsybs2fm51tcO/pBX0w0+t9GXKfpvG9v/l6K9cV05L2A5h0z+n5uhcZI7szxYh4vHrF/IQwlDDjRACQIUWEM4DHTeT0rxmHZ3yVDi6x1yrA+DXUmdTks+KW4847QMc8PG/0/BhLPN27E7Q0Ehe7+fidVFI7t4RTzEsEnL5wGYKvbK3J7N06vgeob9XKsygUlcMwtPail3VpdI1Pb3Ku2JCOy+rS/n/by3WLw/2kOUNBKIgAA";
        //ISO v4
        String str3="Fy+cjeDa5KP1U9gu3KOJcnqIQwbZLkTQKHyRTo5eMl6QJ6GFOJauSSKqaQH74ucIddSY8cPN7zBmM9Uuk6qaQEyZe83cCPtp3fc5WDFDMFL5JKAFoJhk+MfRLyWXg1kwRewxDtIFoSZlatSuVLEWKmgaQwyOUdoUX26XVTFmfhTy4oHsZj8a7PurLsX6aYlZl5o1WY57HBftOcmNsikmjSgDYwZkhDKF1a5IYBT2zUMtgJs1eo/Fba5KtvUujgyxxaqoqU9vDAQdeb5GK9opsL3Uqma/kFL1NsFzYpnTuxQ5lxuM0fsYf7m6oaJFY/jCI2R5RkpT3oU5gDBMAAEXelrtGLOBjym6IfcKFgdY9GM22HU5Stl0S22lI41WiNRtzW8p5RH4Wn1Rfe427Lm59p3QnuWkQf9pKPo45O2ZtQ7MjeiIT24TdoemCpXW1M7yEj66dPK/N5Pkb8P03001KGLmOsnZPRnxdQYAzBFHeVBdJatH/fGqqD4QeysWaV6q/cafqh90QX9+7vmM6hfLjw==";
        String eval = new Base64DecoderToAesDecryptUdf().eval(str2);
        System.out.println(eval);

    }
}
