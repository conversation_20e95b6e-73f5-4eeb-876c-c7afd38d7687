package com.poizon.udf.json;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.Iterator;

/**
 * <AUTHOR>
 * @Date 2021/4/13 6:04 下午
 * [{"funderType":1,"subsidyAmount":3000},{"funderType":2,"subsidyAmount":3000}]
 */
@Slf4j
public class GetSubsidyInfoValue extends ScalarFunction {
    public long eval(String str1, int type, int amount) {
        long subsidyValue = 0L;
        //json长度为0 '{}'
        if ("{}".equals(str1) && type == 1) {
                subsidyValue = amount;
        }
        //json长度不为0
        if (!"{}".equals(str1)) {
            JSONArray jsonArray = null;
            try {
                jsonArray = JSONObject.parseArray(str1);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("异常的json是:" + str1);
            }
            //数组长度为0 '[]'
            if (jsonArray != null && jsonArray.size() == 0 && type == 1) {
                    subsidyValue = amount;
            }
            if (jsonArray != null && jsonArray.size() != 0) {
                Iterator<Object> iterator = null;
                String value = "";
                iterator = jsonArray.iterator();
                while (iterator.hasNext()) {
                    value = iterator.next().toString();
                    int subsidyType =(int)JSONObject.parseObject(value).get("funderType");
                    if (subsidyType == type) {
                        subsidyValue = (int) JSONObject.parseObject(value).get("subsidyAmount");
                    }
                }
            }
        }
        return subsidyValue;
    }

    public static void main(String[] args) {
        String str1 = "[{\"funderType\":1,\"subsidyAmount\":3000},{\"funderType\":2,\"subsidyAmount\":300}]";
//        String str1 = "[]";
        int type = 2;
        int amount = 3000;
        GetSubsidyInfoValue getSubsidyValue = new GetSubsidyInfoValue();
        long eval = getSubsidyValue.eval(str1, type, amount);
        System.out.println(eval);
    }
}
