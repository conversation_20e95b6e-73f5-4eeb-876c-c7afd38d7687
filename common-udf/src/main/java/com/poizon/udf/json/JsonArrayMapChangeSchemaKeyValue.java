package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 重构ARRAY中的Map的KV键值对
 * <AUTHOR>
 * @Date 2025/8/7 下午4:40
 **/
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("ARRAY<MAP<STRING,STRING>>"))
@Slf4j
public class JsonArrayMapChangeSchemaKeyValue extends ScalarFunction {
    public Map<String, String>[]  eval(String json) {
        if (json == null || json.trim().isEmpty()) {
            log.warn("输入的JSON字符串为空或null");
            return new HashMap[0];
        }
        try {
            JSONArray jsonArray = JSON.parseArray(json);
            if (jsonArray == null || jsonArray.isEmpty()) {
                log.warn("JSON数组为空，原值 = {}", json);
                return new HashMap[0];
            }
            List<Map<String, String>> resultList = new ArrayList<>(jsonArray.size());
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject inputMap = jsonArray.getJSONObject(i);
                if (inputMap == null || inputMap.isEmpty()) {
                    continue;
                }
                // 每个MAP只有一对KV
                String firstKey = inputMap.keySet().iterator().next();
                Object firstValue = inputMap.getOrDefault(firstKey, 0);

                // 创建新的HashMap，格式为 {"a": 原始KEY, "b": 原始VALUE}
                Map<String, String> newMap = new HashMap<>(2);
                newMap.put("k", firstKey);
                newMap.put("v", String.valueOf(firstValue));
                resultList.add(newMap);
            }
            return resultList.toArray(new HashMap[0]);
        } catch (Exception throwable) {
            log.error("数组转换失败，原值 = {}; exception = {}",json,throwable.getMessage(),throwable);
        }
        return new HashMap[0];
    }
}
