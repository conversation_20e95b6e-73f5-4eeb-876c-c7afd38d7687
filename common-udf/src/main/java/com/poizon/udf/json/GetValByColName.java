package com.poizon.udf.json;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

/**
 * Created on 2022/4/15.
 *
 * <AUTHOR>
 */
public class GetValByColName  extends ScalarFunction {

    @Override
    public void open(FunctionContext context) {
    }

    public String eval(String json, String colName) {
        if(StringUtils.isEmpty(json)){
            return null;
        }
        JSONArray jsonArray = JSONArray.parseArray(json);

        for(int i=0;i<jsonArray.size();i++){
            JSONObject job = jsonArray.getJSONObject(i);
            Object res = job.get(colName);
            if(res != null ){
                return String.valueOf(res);
            }
        }
        return  null;
    }

    // 可选，close方法可以不写
    @Override
    public void close() {

    }

    public static void main (String[] args) {
        String val = "[{\"uid\":\"1552521212\",\"time\":\"1611049153831\",\"sid\":\"1\",\"bid\":\"ebf14a07-6e2a-40a9-a60e-dddec13a5de1\",\"lid\":\"0b60a7a1-8d6a-40ad-9f5c-c2ea04a08493\",\"eid\":\"99\",\"prid\":\"99\",\"traceid\":\"257bae8c1b3b046\"},{\"uid\":\"1552521212\",\"time\":\"1611049153831\",\"sid\":\"1\",\"bid\":\"ebf14a07-6e2a-40a9-a60e-dddec13a5de1\",\"lid\":\"5ccc1960-38f2-45f2-8bda-ed4d0981d2de\",\"eid\":\"139\",\"raid\":\"139\",\"traceid\":\"257bae8c1b3b046\"},{\"uid\":\"1552521212\",\"time\":\"1611049153831\",\"sid\":\"1\",\"bid\":\"ebf14a07-6e2a-40a9-a60e-dddec13a5de1\",\"lid\":\"6952321e-842b-4f22-a3ee-1290f7bd2e1a\",\"eid\":\"98\",\"mid\":\"98\",\"traceid\":\"257bae8c1b3b046\"},{\"uid\":\"1552521212\",\"time\":\"1611049153831\",\"sid\":\"1\",\"bid\":\"ebf14a07-6e2a-40a9-a60e-dddec13a5de1\",\"lid\":\"970d5b04-ff54-4c83-8189-4e0fa090cc18\",\"eid\":\"127\",\"reid\":\"127\",\"traceid\":\"257bae8c1b3b046\"},{\"uid\":\"1552521212\",\"time\":\"1611049153831\",\"sid\":\"1\",\"bid\":\"ebf14a07-6e2a-40a9-a60e-dddec13a5de1\",\"lid\":\"d3343634-7eae-440b-987b-f43224096d38\",\"eid\":\"101\",\"poid\":\"101\",\"traceid\":\"257bae8c1b3b046\"}]";
        System.out.println(new GetValByColName().eval(val, "raid"));
        System.out.println(new GetValByColName().eval(val, "mid"));
        System.out.println(new GetValByColName().eval(val, "reid"));
    }

}