package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.*;

/**
 * 校验事件属性的Schema信息，是否有多的，如果有多个事件属性，则把多个事件属性删除
 */
@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("STRING")
)
public class JsonMesageCheckUdf extends ScalarFunction {

    List<String> whiteList;

    @Override
    public void open(FunctionContext context) {
        whiteList = Arrays.asList("dw_userid", "visit_mode", "session_id",
                "app_build", "current_page", "block_type", "current_page_title", "block_type_title");
    }

    //传入两个参数，第一个为message信息，必须为string类型，第二个为该事件对应的schema信息 必须为string类型
    //如果传入为null,则直接返回messgage
    //如果传入不为null，则进行校验
    public String eval(String json, String schema) {
        JSONObject jsonObject = new JSONObject();
//        if (null == args) {
//            return JSON.toJSONString(jsonObject);
//        }
//        if (args.length != 2) {
//            jsonObject.put("message", args[0]);
//            return JSON.toJSONString(jsonObject);
//        }
        List<String> schemaList = JSONObject.parseArray(schema, String.class);
        JSONObject message = JSON.parseObject(json);
        JSONObject properties = JSON.parseObject(message.get("properties").toString());
        Set<String> keys = properties.keySet();
        Iterator<String> iterator = keys.iterator();
        ArrayList<String> errorKey = new ArrayList<>();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (Objects.nonNull(schemaList)) {
                schemaList.addAll(whiteList);
            } else {
                schemaList = whiteList;
            }
            if (!key.startsWith("$") && !schemaList.contains(key)) {
                errorKey.add(key);
                iterator.remove();
            }
        }
        message.put("properties", properties);
        jsonObject.put("message", message);
        if (!errorKey.isEmpty()) {
            jsonObject.put("errorkey", errorKey);
        }
        return JSON.toJSONString(jsonObject);
    }

    //可选，close方法可以不写
    @Override
    public void close() {
    }


    public static void main(String[] args) {
        List<String> strings = JSONObject.parseArray(null, String.class);
        System.out.println(strings);
    }
}