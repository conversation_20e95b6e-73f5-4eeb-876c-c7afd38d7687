package com.poizon.udf.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.logging.log4j.util.Strings;


@Slf4j
public class JsonValuesSumToDouble extends ScalarFunction {


    public Double eval(String jsonString, String keys) {
        double sum = 0;
        if (Strings.isBlank(jsonString) || keys == null) {
            return sum;
        }

        try {
            JSONObject originJson = JSON.parseObject(jsonString);
            String[] keyArray = keys.split(",");
            for (String key : keyArray) {
                if (originJson.containsKey(key)) {
                    sum += originJson.getDoubleValue(key);
                }
            }
        } catch (Exception e) {
            log.error("JsonValuesSumToDouble UDF 执行异常。参数为 [{}, {}] ", jsonString, keys, e);
        }
        return sum;
    }
}
