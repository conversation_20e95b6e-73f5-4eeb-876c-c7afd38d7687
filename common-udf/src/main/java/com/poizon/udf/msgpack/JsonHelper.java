package com.poizon.udf.msgpack;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.msgpack.jackson.dataformat.MessagePackFactory;
import java.io.IOException;


/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/10/25
 */
public class JsonHelper {

	private final static ObjectMapper objectMapper = new ObjectMapper();

	private final static ObjectMapper msgpackMapper = new ObjectMapper(new MessagePackFactory());

	public static <T> T readValue(byte[] src, Class<T> valueType) throws IOException {
		return objectMapper.readValue(src, valueType);
	}

	public static <T> T readValue(String content, Class<T> valueType) throws IOException {
		return objectMapper.readValue(content, valueType);
	}

	public static JsonNode readTree(String content) throws IOException {
		return objectMapper.readTree(content);
	}

	public static String writeValueAsString(Object value) throws JsonProcessingException {
		return objectMapper.writeValueAsString(value);
	}

	public static <T> T readMsgpackValue(byte[] src, Class<T> valueType) throws IOException {
		return msgpackMapper.readValue(src, valueType);
	}

	public static JsonNode readMsgpackTree(byte[] src) throws IOException {
		return msgpackMapper.readTree(src);
	}
}

