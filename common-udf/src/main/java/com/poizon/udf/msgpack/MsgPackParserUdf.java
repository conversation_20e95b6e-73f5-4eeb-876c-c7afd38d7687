package com.poizon.udf.msgpack;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.io.IOException;

/**
 * @Description:
 * @author: zhiping.lin
 * @date: 2022/10/25
 */

@FunctionHint(
	input = {@DataTypeHint("VARBINARY"), @DataTypeHint("STRING")},
	output = @DataTypeHint("STRING")
)
public class MsgPackParserUdf extends ScalarFunction {

	public String eval(byte[] input, String field) {
		EventForNewUserGap event = null;
		try {
			event = JsonHelper.readMsgpackValue(input, EventForNewUserGap.class);
			return event.get(field);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

}
