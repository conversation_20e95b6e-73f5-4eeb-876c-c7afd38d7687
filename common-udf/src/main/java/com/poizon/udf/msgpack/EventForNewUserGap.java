package com.poizon.udf.msgpack;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.poizon.utils.JsonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * @Description: XXX
 * @author: zhiping.lin
 * @date: 2022/10/25
 */
@Data
public class EventForNewUserGap implements Serializable {

	private String app;

	private String name;

	@JsonProperty("version")
	private String sdkVersion;

	private LinkedHashMap<String, Object> tags;

	private long timestamp;

	private String traceId;

	private String spanId;

	public String get(String field){
		switch (field){
			case "app" : return app;
			case "name" : return name;
			case "sdkVersion" : return  sdkVersion;
			case "tags" : return JsonUtil.writeValueAsString(tags);
			case "timestamp" : return String.valueOf(timestamp);
			case "traceId" : return traceId;
			case "spanId" : return spanId;
			default: return null;
		}
	}

}

