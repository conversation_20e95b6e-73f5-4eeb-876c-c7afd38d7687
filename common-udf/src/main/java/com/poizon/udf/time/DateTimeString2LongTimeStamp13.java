package com.poizon.udf.time;

import com.util.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 张天龙
 * @Project: du-tech-data-udf
 * @Package com.poizon.udf.time
 * @date Date : 2024年07月16日 14:20
 * 需求：
 * 逻辑：
 */
public class DateTimeString2LongTimeStamp13 extends ScalarFunction {
	public String eval(String originTime) {
		if (StringUtils.isBlank(originTime)) {
			return null;
		}
		final String[] split = originTime.split("\\.");
		final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		if (split.length > 1) {
			final String time = split[0];

			final Long timestampMillis;
			try {
				timestampMillis = sdf.parse(time).getTime();
			} catch (Exception e) {
				return null;
			}

			String milliseconds = split[1];
			milliseconds = milliseconds + "000".substring(milliseconds.length());

			return timestampMillis / 1000 + milliseconds;
		}
		final Long time;
		try {
			time = sdf.parse(originTime).getTime();
		} catch (ParseException e) {
			return null;
		}
		return time.toString();
	}


	public static void main(String[] args) {
		List<String> timestamps = new ArrayList<>();
		timestamps.add("2023-09-13 17:58:44.100");
		timestamps.add("2023-09-13 17:58:44.1");
		timestamps.add("2023-09-13 17:58:44.01");
		timestamps.add("2023-09-13 17:58:44.001");
		timestamps.add("2023-09-13 17:58:44.0");
		timestamps.add("2023-09-13 17:58:44");
		for (String timestamp : timestamps) {
			System.out.println(new DateTimeString2LongTimeStamp13().eval(timestamp));
		}

//		List<String> formattedTimestamps = new ArrayList<>();
//		for (String timestamp : timestamps) {
//			String[] parts = timestamp.split("\\.");
//			String milliseconds = parts[1];
//			milliseconds = milliseconds + "000".substring(milliseconds.length()); // Pad with 0 to ensure three digits
//			formattedTimestamps.add(parts[0] + "." + milliseconds);
//		}

//		System.out.println(formattedTimestamps);
	}

}
