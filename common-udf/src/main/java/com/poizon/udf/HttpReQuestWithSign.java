package com.poizon.udf;

import com.alibaba.fastjson.JSONObject;
import com.shizhuang.sign.SignOtherUtils;
import com.util.HttpClientUtil;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.accumulators.IntCounter;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.metrics.Meter;
import org.apache.flink.metrics.MeterView;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 带签名的Http请求udf
 * @Author: lmh
 * @date: 2022/9/1 11:31 上午
 */
public class HttpReQuestWithSign extends ScalarFunction {

    private static final Logger logger = LoggerFactory.getLogger(HttpReQuestWithSign.class);
    private String  url;
    private final Map<String,Object> tmpHeaders = new HashMap<>();
    private String apiUrl;
    private String appId;
    private transient Meter meter;
    private transient Counter numOfDate;
    private transient Counter numOfRt;
    private transient long maxRt;
    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);

        //总处理数据
        this.numOfDate=context.getMetricGroup()
                .counter("numOfDate");
        //处理数率
        this.meter=context.getMetricGroup()
                .meter("http-rps/s",new MeterView(numOfDate,1));
        //最大响应时间
        context.getMetricGroup()
                .gauge("max-rt/ms", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return maxRt;
                    }
                });
        //总响应时间
        this.numOfRt=context.getMetricGroup()
                .counter("numOfRt");
        //平均响应时间
        context.getMetricGroup()
                .gauge("avg-rt/ms", new Gauge<Long>() {
                    @Override
                    public Long getValue() {
                        return numOfRt.getCount()  / numOfDate.getCount();
                    }
                });

        //获取header
        JSONObject headersObject = JSONObject.parseObject(context.getJobParameter("http.headers", null));
        Map<String,Object> headers = headersObject == null ? null : headersObject.getInnerMap();

        //域名
        String baseUrl = context.getJobParameter("http.url.base", null);

        //api地址
        apiUrl = context.getJobParameter("http.url.api", null);

        //params
        JSONObject paramsObject = JSONObject.parseObject(context.getJobParameter("http.url.params", null));
        Map<String, Object> params = paramsObject == null ? null : paramsObject.getInnerMap();
        url = HttpClientUtil.buildUrl(baseUrl, apiUrl, params).toString();

        //签名sign 类型 默认1php 2H5 3app
        appId = context.getJobParameter("http.sign.appId", "1");
        //签名默认需要一个时间 与请求的时间不能超过5分钟 这个请求也使用这个时间就好了

        //设置一些默认参数
        tmpHeaders.put("appId",appId);
        tmpHeaders.put("Content-Type","application/json");

        if (Objects.nonNull(headers) && !headers.isEmpty()){
            tmpHeaders.putAll(headers);
        }

        logger.info("url :{};\n headers: {} \n params:{}", url,tmpHeaders, params);
    }

    @FunctionHint(input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
    public String eval(String body,String type) throws IOException {

        if (StringUtils.isEmpty(body)){
            return null;
        }

        Response response=null;

        //计数
        numOfDate.inc(1);

        //请求访问时间
        long timeStart = Calendar.getInstance().getTimeInMillis();

        if ("post".equals(type)){
            response = HttpClientUtil.postRequest(url, getHeaders(timeStart/1000), body);
        }else {
            throw new UnsupportedOperationException();
        }

        //请求结束时间
        long timeEnd = Calendar.getInstance().getTimeInMillis();

        //总响应时间 ms
        numOfRt.inc(timeEnd - timeStart);

        //最大响应时间 ms
        maxRt = Math.max (timeEnd - timeStart,maxRt);

        String res = response.body().string();

        if (!response.isSuccessful()){
            logger.warn("response is not successful response:{},body:{}",res,body);
        }

        //好像 不关也会复用
        response.close();
        return res;
    }

    @FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
    public String eval(String body) throws IOException {
        return this.eval(body,"post");
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    /**
     * time以秒为单位，共10位数，与服务器时间差不能超过10分钟
     * */
    private Map<String,Object> getHeaders(long time ){
        HashMap<String, String> headerMap = new HashMap<>(2);
        headerMap.put("appId", appId);
        headerMap.put("time",String.valueOf(time));

        HashMap<String, Object> headers = new HashMap<>();
        headers.put("time",String.valueOf(time));
        headers.put("token",SignOtherUtils.sign(apiUrl, headerMap));
        headers.putAll(tmpHeaders);

        return headers;
    }

}
