package com.poizon.udtf;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

@FunctionHint(
        input = {@DataTypeHint("STRING"), @DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<f0 STRING>")
)
public class StringSplitTableFunction extends TableFunction<Row> {

    public void eval(String content, String regex) {
        if (content != null && null != regex) {
            for(String str : content.split(regex)) {
                Row row = new Row(1);
                row.setField(0, str);
                collect(row);
            }
        } else {
            //适配MC的行为
            Row row = new Row(1);
            row.setField(0, null);
            collect(row);
        }
    }
}
