package com.poizon.udtf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.poizon.entity.CommunityContentInfoListExp;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/18
 * 神策曝光日志解析
 */
@FunctionHint(
        input = {@DataTypeHint("STRING")},
        output = @DataTypeHint("ROW<f1 STRING, f2 STRING, f3 INT>")
)
public class SensorsCommunityExpLogParserUDTF extends TableFunction<Row> {
    public void eval(String input) {
        try {
            List<CommunityContentInfoListExp> comListExp =  JSONObject.parseObject(input,new TypeReference<List<CommunityContentInfoListExp>>(){});

            for(CommunityContentInfoListExp sensorsDealLog: comListExp) {
                if (sensorsDealLog != null) {
                    Row row = new Row(3);
                    row.setField(0, sensorsDealLog.getContentId());
                    row.setField(1, sensorsDealLog.getContentType());
                    row.setField(2, sensorsDealLog.getPosition());
                    collect(row);
                }
            }
        } catch (Throwable e) {
            System.out.println("log:"+input +", msg: "+ e.getStackTrace());
        }
    }

    public static void main(String[] args) {
        List<CommunityContentInfoListExp> comListExp =  JSONObject.parseObject("[{\"content_id\":\"25735015\",\"position\":1,\"content_type\":\"1\"}]",new TypeReference<List<CommunityContentInfoListExp>>(){});

//        List<String> list = JSONObject.parseObject(jsonString, new TypeReference<List<String>>() {});
        for(CommunityContentInfoListExp sensorsDealLog: comListExp) {
            System.out.println(sensorsDealLog.getContentId());
        }
    }
}
