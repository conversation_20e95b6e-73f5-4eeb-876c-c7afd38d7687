package com;

import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Optional;

public class StringTableFunction extends TableFunction<Row> {

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                            .outputTypeStrategy(callContext -> {
                                String functionName = callContext.getName();
                                //根据函数入参情况确定返回值
                                List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                                if (argumentDataTypes.size() > 1) {
                                    DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1];
                                    for (int i = 0; i < argumentDataTypes.size(); i++) {
                                        DataType parameterDataType = argumentDataTypes.get(i);
                                        if (!parameterDataType
                                                .getLogicalType()
                                                .getTypeRoot()
                                                .getFamilies()
                                                .contains(
                                                        LogicalTypeFamily.CHARACTER_STRING)) {
                                            throw new ValidationException(String.format(
                                                    "function %s argument %s data type is not %s",
                                                    functionName,
                                                    i + 1,
                                                    DataTypes.STRING().toString()));
                                        }
                                        if (i > 0) {
                                            outputDataTypes[i - 1] = DataTypes.STRING();
                                        }
                                    }
                                    return Optional.of(DataTypes.ROW(outputDataTypes));
                                } else if (argumentDataTypes.size() == 1) {
                                    return Optional.of(DataTypes.ROW(DataTypes.STRING()));
                                } else {
                                    throw new ValidationException(String.format(
                                            "function %s argument is empty",
                                            functionName));
                                }
                            })
                            .build();
    }
}
