package com.util;

import com.shizhuang.sign.SignOtherUtils;
import lombok.SneakyThrows;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.junit.Test;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @description: OkHttpClient 的 http请求客户端
 * @Author: lmh
 * @date: 2022/9/1 11:44 上午
 */
public class HttpClientUtil {

    //单例
    private static final OkHttpClient  okHttpClient = new OkHttpClient()
            .newBuilder()
            .connectTimeout(200, TimeUnit.SECONDS)
            .readTimeout(20,TimeUnit.SECONDS)
            .writeTimeout(180,TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(5,5L,TimeUnit.MINUTES))
            .build();

    public HttpClientUtil() {
    }

    public static Response postRequest(String Url,Map<String,Object> header,String content) throws IOException {

        if (StringUtils.isEmpty(Url)){
            throw new RuntimeException("Url is empty !!");
        }

        Request.Builder builder = new Request.Builder()
                .url(Url)
                ;

        //构建requestBody 默认支持Json String
        if (StringUtils.isNotEmpty(content)){
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), content);
            builder.method("POST", body);
        }else
            builder.method("POST", null);

        //添加header
        if (Objects.nonNull(header) && !header.isEmpty()){
            header.forEach((x,y)->builder.addHeader(x,y.toString()));
        }

        return okHttpClient.newCall(builder.build()).execute();

    }


    /** 拼接 params*/
    public static URI buildUrl(String baseUrl, String api, Map<String,Object> params)  {

        String url = StringUtils.join(baseUrl, api);
        URI uri =null;
        try {
            URIBuilder uriBuilder = new URIBuilder(url);

            if (Objects.nonNull(params) && !params.isEmpty()){
                params.forEach((x,y)->{uriBuilder.addParameter(x,y.toString());});
            }
             uri = uriBuilder.build();

        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }

        return uri;
    }

    /**
     * test
     * */
    @Test
    public void test() throws IOException {

        String reqUrl1 = "https://d1-ms3.dewu.net";
        String reqUrl = "/api/v1/bigdata-duie-service/predict";
        Long time = Calendar.getInstance().getTimeInMillis() / 1000;
        System.out.println(time);
        HashMap<String, String> headerMap = new HashMap<>(3);
        headerMap.put("appId",String.valueOf(1));
        headerMap.put("time",String.valueOf(time));
        String sign = SignOtherUtils.sign(reqUrl, headerMap);
        System.out.println(sign);
        HashMap<String, Object> map = new HashMap<>();
//        map.put("noSign","true");
        URI uri = buildUrl(reqUrl1,reqUrl, map);
        String content = "{\n    \"itemIds\": [\n        \"54f493039ad5028c\"\n    ],\n    \"sceneCode\": \"1008\",\n    \"extInfo\": {\n        \"itemFeature\": [\n            {\n                \"device_active_weekday_hour_strindexer\": 0.0654,\n                \"city_strindexer\": 0.193,\n                \"device_brand_strindexer\": -0.1266,\n                \"device_model_strindexer\": -0.101,\n                \"app_version_strindexer\": 0.185386501,\n                \"device_os_version_strindexer\": 0.417,\n                \"download_first_level_channel_strindexer\": -0.0951,\n                \"download_second_level_channel_strindexer\": 0.0833,\n                \"download_third_level_channel_strindexer\": 0.1049,\n                \"download_fourth_level_channel_strindexer\": 0.0792,\n                \"download_fifth_level_channel_strindexer\": 0.0792,\n                \"pid_strindexer\": 0.0792,\n                \"app_num_of_0\": 0,\n                \"app_num_of_1\": 0,\n                \"app_num_of_2\": 0,\n                \"app_num_of_3\": 0,\n                \"app_num_of_4\": 0,\n                \"app_num_of_5\": 0,\n                \"app_num_of_6\": 0,\n                \"app_num_of_7\": 0,\n                \"app_num_of_8\": 0,\n                \"app_num_of_9\": 0,\n                \"app_num_of_10\": 0,\n                \"app_num_of_11\": 0,\n                \"app_num_of_12\": 0,\n                \"app_num_of_13\": 0,\n                \"app_num_of_14\": 0,\n                \"app_num_of_15\": 1,\n                \"app_num_of_16\": 0,\n                \"app_num_of_17\": 0,\n                \"app_num_of_18\": 0,\n                \"app_num_of_19\": 0,\n                \"app_num_of_20\": 3,\n                \"app_num_of_21\": 50,\n                \"app_num_of_22\": 39,\n                \"app_num_of_23\": 20,\n                \"app_num_of_24\": 14,\n                \"app_num_of_25\": 19,\n                \"app_num_of_26\": 16,\n                \"app_num_of_27\": 7,\n                \"app_num_of_28\": 2,\n                \"app_num_of_29\": 9,\n                \"app_num_of_30\": 3,\n                \"app_num_of_31\": 1,\n                \"app_num_of_32\": 2,\n                \"app_num_of_33\": 0,\n                \"app_num_of_34\": 3,\n                \"app_num_of_35\": 0,\n                \"app_num_of_36\": 0,\n                \"app_num_of_37\": 0,\n                \"app_num_of_38\": 1,\n                \"app_num_of_39\": 1,\n                \"app_num_of_40\": 1,\n                \"app_num_of_41\": 0,\n                \"app_num_of_42\": 0,\n                \"app_num_of_43\": 0,\n                \"app_num_of_44\": 0,\n                \"app_num_of_45\": 0,\n                \"app_num_of_46\": 0,\n                \"app_num_of_47\": 0,\n                \"app_num_of_48\": 0,\n                \"app_num_of_49\": 0,\n                \"app_num_of_50\": 0,\n                \"app_num_of_51\": 0,\n                \"app_num_of_52\": 0,\n                \"app_num_of_53\": 0,\n                \"app_num_of_54\": 0,\n                \"app_num_of_55\": 0,\n                \"app_num_of_56\": 0,\n                \"app_num_of_57\": 0,\n                \"app_num_of_58\": 0,\n                \"app_num_of_59\": 0,\n                \"app_num_of_60\": 0,\n                \"app_num_of_61\": 0,\n                \"app_num_of_62\": 0,\n                \"app_num_of_63\": 0,\n                \"app_num_of_64\": 0,\n                \"app_num_of_65\": 0\n            }\n        ]\n    }\n}";
        HashMap<String, Object> map1 = new HashMap<>();
        map1.put("appId","1");
        map1.put("time",String.valueOf(time));
        map1.put("token",sign);
        map1.put("Content-Type","application/json");

        Response request = HttpClientUtil.postRequest(uri.toString(), map1, content);

        System.out.println(request.body().string());

    }

    public static void main(String[] args) throws IOException {
        //并发测试
        Runnable runnable2 = new Runnable() {
            @SneakyThrows
            @Override
            public void run() {
                new HttpClientUtil().test();
            }
        };

        Runnable runnable1 = new Runnable() {
            @SneakyThrows
            @Override
            public void run() {
                new HttpClientUtil().test();
            }
        };

        Runnable runnable = new Runnable() {
            @SneakyThrows
            @Override
            public void run() {
                new HttpClientUtil().test();
            }
        };

        new Thread(runnable2).start();
        new Thread(runnable1).start();
        new Thread(runnable).start();

    }
}
