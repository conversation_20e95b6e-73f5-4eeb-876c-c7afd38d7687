//package com.util;
//
//import com.aliyun.odps.Instance;
//import com.aliyun.odps.Odps;
//import com.aliyun.odps.OdpsException;
//import com.aliyun.odps.account.Account;
//import com.aliyun.odps.account.AliyunAccount;
//import com.aliyun.odps.data.Record;
//import com.aliyun.odps.task.SQLTask;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.Executors;
//import java.util.concurrent.ScheduledExecutorService;
//import java.util.concurrent.TimeUnit;
//
///**
// * Author: WangLin
// * Date: 2022/10/9 下午1:57
// * Description: 返回产品给的user_tag_id枚举值
// */
//public class CategoryTransportUserTagIds {
//
//
//	//	private static final String endPoint = "http://service.cn-hangzhou.maxcompute.aliyun.com/api"; //本地测试
//	private static final String endPoint = "http://service.cn-hangzhou.maxcompute.aliyun-inc.com/api"; //生产
//	private static final String project = "du_shucang";
//	private static List<String> userTagIdList;
//
//	static {
//
//		try {
//			userTagIdList = getUserTagIds();
//		} catch (OdpsException e) {
//			e.printStackTrace();
//		}
//
//		Executors.newSingleThreadScheduledExecutor().scheduleWithFixedDelay(() -> {
//			try {
//				userTagIdList = getUserTagIds();
//			} catch (OdpsException e) {
//				e.printStackTrace();
//			}
//		}, 30, 30, TimeUnit.MINUTES);
//	}
//
//	private static List<String> getUserTagIds() throws OdpsException {
//		String sql = "SELECT  DISTINCT tagid as user_tag_id FROM du_shucang.dw_soc_user_manage_tag_detail WHERE pt = max_pt('du_shucang.dw_soc_user_manage_tag_detail') AND (mark RLIKE '测试|内容搬运' OR tagname RLIKE '测试|内容搬运');";
//
//		List<String> list = new ArrayList<>();
//
//		Account account = new AliyunAccount(accessId, accessKey);
//		Odps odps = new Odps(account);
//		odps.setEndpoint(endPoint);
//		odps.setDefaultProject(project);
//		Instance i = SQLTask.run(odps, sql);
//		i.waitForSuccess();
//
//		List<Record> records = SQLTask.getResult(i);
//
//		records.iterator().forEachRemaining(item -> list.add(item.getString("user_tag_id")));
//
//		return list;
//	}
//
//	public static List<String> getUserTagIdsByProduct() {
//		if (userTagIdList == null) {
//			userTagIdList = new ArrayList<>();
//		}
//		return userTagIdList;
//	}
//
//	public static void main(String[] args) {
//		System.out.println(getUserTagIdsByProduct());
//	}
//}
