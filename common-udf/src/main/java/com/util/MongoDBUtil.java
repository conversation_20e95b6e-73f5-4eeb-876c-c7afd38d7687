package com.util;


import com.mongodb.*;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * Created on 2022/8/15.
 *
 * <AUTHOR>
 */
public class MongoDBUtil {
    MongoClient mongoClient;

    public MongoDBUtil (List<ServerAddress> adds, String userName, String source, String password) {
        List<MongoCredential> credentials = new ArrayList<>();
        //MongoCredential.createScramSha1Credential()三个参数分别为 用户名 数据库名称 密码
        MongoCredential mongoCredential = MongoCredential.createScramSha1Credential(userName, source, password.toCharArray());
        credentials.add(mongoCredential);

        //通过连接认证获取MongoDB连接
        MongoClientOptions.Builder build = new MongoClientOptions.Builder();
        build.connectionsPerHost(5);   //与目标数据库能够建立的最大connection数量为50
        build.threadsAllowedToBlockForConnectionMultiplier(50); //如果当前所有的connection都在使用中，则每个connection上可以有50个线程排队等待
        /*
         * 一个线程访问数据库的时候，在成功获取到一个可用数据库连接之前的最长等待时间为2分钟
         * 这里比较危险，如果超过maxWaitTime都没有获取到这个连接的话，该线程就会抛出Exception
         * 故这里设置的maxWaitTime应该足够大，以免由于排队线程过多造成的数据库访问失败
         */
        build.maxWaitTime(1000*60*1);
        build.connectTimeout(1000*60*1);    //与数据库建立连接的timeout设置为1分钟

        MongoClientOptions myOptions = build.build();
        mongoClient = new MongoClient(adds, credentials, myOptions);


    }
    public MongoDBUtil(String url) {
        this.mongoClient = new MongoClient(new MongoClientURI(url));
        new MongoClientURI(url);
    }

    public <T> Set<T> findOnlyFieldValue(String dbName, String collectionName, String field, Class<T> reClass, Document whereStr) {
        HashSet<T> set = new HashSet();
        FindIterable documents;
        if (whereStr != null) {
            documents = this.getCollection(this.getDB(dbName), collectionName).find(whereStr);
        } else {
            documents = this.getCollection(this.getDB(dbName), collectionName).find();
        }

        MongoCursor var8 = documents.iterator();

        while(var8.hasNext()) {
            Document document = (Document)var8.next();
            T value = document.get(field, reClass);
            if (value != null) {
                set.add(value);
            }
        }

        return set;
    }
    public MongoDatabase getDB(String dbName) {
        return this.mongoClient.getDatabase(dbName);
    }

    public MongoCollection<Document> getCollection(MongoDatabase db, String collectionName) {
        return db.getCollection(collectionName);
    }

    public MongoClient getConnection() {
        return mongoClient;
    }

    public void close() {
        mongoClient.close();
    }

    public static void main(String[] args) {
        MongoDBUtil mongoDBUtil = new MongoDBUtil("mongodb://nvwa_root:z3bw2^<EMAIL>:3717,dds-uf606abca184fb342.mongodb.rds.aliyuncs.com:3717/db_nvwa_mongo?replicaSet=mgset-14973769");
        Set<Long> fieldValue = mongoDBUtil.findOnlyFieldValue("db_nvwa_mongo", "Brand", "brandId", Long.class, new Document("isDelete", false));
        System.out.println(fieldValue);
    }

}
