package com.util.treadpoolutils.common.base;

import java.io.Serializable;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


/**
 * Created on 2022/8/15.
 *
 * <AUTHOR>
 */
public enum ThreadPoolSingleton implements Serializable {
    INSTANCE;
    private static final ThreadPoolExecutor threadPoolExecutor=new ThreadPoolExecutor(
            4,
            20,
            600L,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>());

    public ThreadPoolExecutor getInstance(){
        return   threadPoolExecutor;
    }

}
