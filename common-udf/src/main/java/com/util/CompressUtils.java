package com.util;

//import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 * @create 2021-08-29 5:57 下午
 **/
public class CompressUtils {

    private final static ObjectMapper objectMapper = new ObjectMapper();


    public static String compress(Object obj) {
        if(obj == null) {
            return null;
        }
        if((obj instanceof Number)  || (obj instanceof String)) {
            return obj.toString();
        }
        try {
            String data = objectMapper.writeValueAsString(obj);
            // Create an output stream, and a gzip stream to wrap over.
            ByteArrayOutputStream bos = new ByteArrayOutputStream(data.length());
            GZIPOutputStream gzip = new GZIPOutputStream(bos);

            // Compress the input string
            gzip.write(data.getBytes());
            gzip.close();
            byte[] compressed = bos.toByteArray();
            bos.close();

            // Convert to base64
            compressed = Base64.getEncoder().encode(compressed);

            // return the newly created string
            return new String(compressed);
        } catch(IOException ex) {
            return null;
        }
    }



    public static Object decompress(String compressedText) throws IOException {

        // get the bytes for the compressed string
        byte[] compressed = compressedText.getBytes("UTF8");

        // convert the bytes from base64 to normal string
        Base64.Decoder d = Base64.getDecoder();
        compressed = d.decode(compressed);

        // decode.
        final int BUFFER_SIZE = 32;

        ByteArrayInputStream is = new ByteArrayInputStream(compressed);

        GZIPInputStream gis = new GZIPInputStream(is, BUFFER_SIZE);

        StringBuilder string = new StringBuilder();

        byte[] data = new byte[BUFFER_SIZE];

        int bytesRead;

        while ((bytesRead = gis.read(data)) != -1)
        {
            string.append(new String(data, 0, bytesRead));
        }
        gis.close();
        is.close();
        return objectMapper.readValue(string.toString(), Object.class);
    }

    public static void main(String[] args) throws IOException {
        Object decompress = decompress("H4sIAAAAAAAAAD2PyxGAMAhEe8nZcfgECLbm2LskZj3xWGCBu7G0i04+mmv6xtCp8kSz0bcq2iORmMXqFi8WykBBxRgDFArVfiJnw051AiIunyF1D8EnZBW9VvU6bauJgdDlnbPx++B5AQ9+wJLXAAAA");
        System.out.println(decompress.getClass());
        if (decompress instanceof LinkedHashMap){
            LinkedHashMap<String,Double> map = (LinkedHashMap) decompress;
            System.out.println(map.size());
            for (Map.Entry<String, Double> entry : map.entrySet()) {
                System.out.println(entry.getKey());
                System.out.println(entry.getValue());
            }
        }

        String compress = compress(null);
        System.out.println(compress);
        String compress1 = compress(new HashMap<>());
        String compress2 = compress("");
        System.out.println(compress1);
        System.out.println(compress2);

    }

}
