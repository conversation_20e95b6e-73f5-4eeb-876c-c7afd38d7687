package com.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class SensorsDataStringUtil {
    public static String transJsonToShenceFormat(String jsonsResult) {
        try {
            // Create an output stream, and a gzip stream to wrap over.
            ByteArrayOutputStream bos = new ByteArrayOutputStream(jsonsResult.length());
            GZIPOutputStream gzip = new GZIPOutputStream(bos);

            // Compress the input string
            gzip.write(jsonsResult.getBytes());
            gzip.close();
            byte[] compressed = bos.toByteArray();
            bos.close();

            // Convert to base64
            compressed = Base64.getEncoder().encode(compressed);
            // Convert to URLEncoder
            String result = URLEncoder.encode(new String(compressed, "UTF-8"));
            // return the newly created string
            return result;
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 解压经过GZIP压缩的消息
     *
     * @param encodeMessage 经过GZIP压缩的消息
     * @return json
     */
    public static String transShenceGzipFormatToJson(String encodeMessage) {
        try {

            String s1 = URLDecoder.decode(encodeMessage, "UTF-8");
            // return the newly created string
            // Convert to base64
            byte[] decode = Base64.getDecoder().decode(s1);

            byte[] ret = null;
            ByteArrayInputStream bis = new ByteArrayInputStream(decode);
            GZIPInputStream gzip = new GZIPInputStream(bis);

            byte[] buf = new byte[1024];
            int num = -1;
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            while ((num = gzip.read(buf, 0, buf.length)) != -1) {
                bos.write(buf, 0, num);
            }
            gzip.close();
            bis.close();
            ret = bos.toByteArray();
            bos.flush();
            bos.close();

            return new String(ret);
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 解压未经过GZIP压缩的消息
     */
    public static String transShenceFormatToJson(String encodeMessage) {
        try {

            String s1 = URLDecoder.decode(encodeMessage, "UTF-8");
            // return the newly created string
            // Convert to base64
            byte[] decode = Base64.getDecoder().decode(s1);
            return new String(decode);
        } catch (IOException e) {
            return null;
        }
    }

    public static void main(String[] args) throws Exception{
//        String str = "eyJkaXN0aW5jdF9pZCI6IjE3MzVjMjFhYjViZmM5LTBmOGUzZTA1OTQ4ZWE4OC0yZjdiMWIyOS0zNzA5NDQtMTczNWMyMWFiNWMxNGJjIiwibGliIjp7IiRsaWIiOiJqcyIsIiRsaWJfbWV0aG9kIjoiY29kZSIsIiRsaWJfdmVyc2lvbiI6IjEuMTUuMTIifSwicHJvcGVydGllcyI6eyIkdGltZXpvbmVfb2Zmc2V0IjotNDgwLCIkc2NyZWVuX2hlaWdodCI6ODk2LCIkc2NyZWVuX3dpZHRoIjo0MTQsIiRsaWIiOiJqcyIsIiRsaWJfdmVyc2lvbiI6IjEuMTUuMTIiLCIkbGF0ZXN0X3RyYWZmaWNfc291cmNlX3R5cGUiOiLnm7TmjqXmtYHph48iLCIkbGF0ZXN0X3NlYXJjaF9rZXl3b3JkIjoi5pyq5Y%2BW5Yiw5YC8X%2BebtOaOpeaJk%2BW8gCIsIiRsYXRlc3RfcmVmZXJyZXIiOiIiLCIkcmVmZXJyZXIiOiIiLCIkdXJsIjoiaHR0cDovL3QwLW0uZGV3dS5jb20vcm4tYWN0aXZpdHkvYmxpbmQtYm94LWNvcHkvMjAyMDA3MTYxMT9pZD02IiwiJHVybF9wYXRoIjoiL3JuLWFjdGl2aXR5L2JsaW5kLWJveC1jb3B5LzIwMjAwNzE2MTEiLCIkdGl0bGUiOiIiLCIkaXNfZmlyc3RfZGF5Ijp0cnVlLCIkaXNfZmlyc3RfdGltZSI6dHJ1ZSwiJHJlZmVycmVyX2hvc3QiOiIifSwiYW5vbnltb3VzX2lkIjoiMTczNWMyMWFiNWJmYzktMGY4ZTNlMDU5NDhlYTg4LTJmN2IxYjI5LTM3MDk0NC0xNzM1YzIxYWI1YzE0YmMiLCJ0eXBlIjoidHJhY2siLCJldmVudCI6IiRwYWdldmlldyIsIl90cmFja19pZCI6MTg1NzA3MjU3fQ%3D%3D";
//        String str = "eyJkaXN0aW5jdF9pZCI6IjE3NWY0NTkwOTk4M2FlLTAyMTEyOGEzMWE2MzljLTc5MzkxYTMwLTEyOTYwMDAtMTc1ZjQ1OTA5OTkyN2QiLCJsaWIiOnsiJGxpYiI6ImpzIiwiJGxpYl9tZXRob2QiOiJjb2RlIiwiJGxpYl92ZXJzaW9uIjoiMS4xNS4xMiJ9LCJwcm9wZXJ0aWVzIjp7IiR0aW1lem9uZV9vZmZzZXQiOi00ODAsIiRzY3JlZW5faGVpZ2h0Ijo5MDAsIiRzY3JlZW5fd2lkdGgiOjE0NDAsIiRsaWIiOiJqcyIsIiRsaWJfdmVyc2lvbiI6IjEuMTUuMTIiLCIkbGF0ZXN0X3RyYWZmaWNfc291cmNlX3R5cGUiOiLnm7TmjqXmtYHph48iLCIkbGF0ZXN0X3NlYXJjaF9rZXl3b3JkIjoi5pyq5Y%2BW5Yiw5YC8X%2BebtOaOpeaJk%2BW8gCIsIiRsYXRlc3RfcmVmZXJyZXIiOiIiLCJjdXJyZW50X3BhZ2UiOjYxLCJ2ZW51ZV9pZCI6IjVmYmEzMWYzNDkzYmFkMmUyMjNhNDJkYiIsInZlbnVlX3RpdGxlIjoiMTAwJeW%2Bl%2BaaluWGrOa7oeWHj%2BWIuO%2B8gSIsImNvbXBvbmVudF90eXBlIjoicHJvZHVjdEZsb3dXaXRoSWQiLCJjb21wb25lbnRfdWlkIjoiT0RrMU1qQXkiLCJjb21wb25lbnRfY29udGVudF9pbmZvX2xpc3QiOiJbe1wiY29udGVudElEXCI6MTAxNTgwNH1dIiwiJGlzX2ZpcnN0X2RheSI6dHJ1ZX0sImFub255bW91c19pZCI6IjE3NWY0NTkwOTk4M2FlLTAyMTEyOGEzMWE2MzljLTc5MzkxYTMwLTEyOTYwMDAtMTc1ZjQ1OTA5OTkyN2QiLCJ0eXBlIjoidHJhY2siLCJldmVudCI6InZlbnVlX2NvbXBvbmVudF9jb250ZW50X2V4cG9zdXJlIiwiX3RyYWNrX2lkIjozMDI2NTA3MDd9";
//        String str = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
//        String s = transShenceFormatToJson(str);
//        System.out.println(s);
//        String s = "{\"login_id\":\"71338465\",\"_track_id\":1399973147,\"lib\":{\"$lib\":\"iOS\",\"$lib_method\":\"code\",\"$lib_version\":\"2.1.0\",\"$app_version\":\"4.59.0\"},\"distinct_id\":\"71338465\",\"anonymous_id\":\"289349AC-E495-40E6-A4E6-2E419199F49D\",\"_flush_time\":1606185707406,\"time\":1606185705137,\"event\":\"community_comment_exposure\",\"type\":\"track\",\"properties\":{\"$screen_orientation\":\"portrait\",\"block_type\":\"145\",\"$os\":\"iOS\",\"$longitude\":\"\",\"$network_type\":\"WIFI\",\"$wifi\":true,\"$ip\":\"*************\",\"$screen_height\":812,\"dw_userid\":\"71338465\",\"$device_id\":\"90029E27-218F-4103-87C8-E2FE95DC5610\",\"content_type\":\"1\",\"$app_id\":\"com.siwuai.duapp\",\"$latitude\":\"\",\"current_page\":\"9\",\"community_comment_info_list\":\"[{\\\"comment_type\\\":0,\\\"comment_id\\\":2056795}]\",\"$os_version\":\"14.1\",\"$is_first_day\":false,\"content_id\":\"22947126\",\"$model\":\"iPhone10,3\",\"session_id\":\"90029E27-218F-4103-87C8-E2FE95DC56101606185579925\",\"$screen_width\":375,\"$app_version\":\"4.59.0\",\"$lib\":\"iOS\",\"app_build\":\"1615\",\"visit_mode\":\"1\",\"$timezone_offset\":-480,\"$lib_version\":\"2.1.0\",\"$manufacturer\":\"Apple\"}}";

//        String s = "H4sIAAAAAAAAE%2B2c%2FW%2FbxhnH86cQbIBtgGXcHY9Hnn%2BTbTkLWidGFHco1kFgxLNFmCI1vsT1ggAZOmBtsjRuVyxtig7LhrXd0G7D1mFZizY%2F7E9p5Jf%2FYg8lWZZskXq1TLkUgtg%2BUbzjc8%2F38zzPHamf3pFtd9NySpYpL8gYI51wxuU5ObCqQl7ADDHCONOZjvicbDius1N1Q799%2BOISz%2Bs5pOs8R3lhOcdVrOXIYmExzzWd45XoVOK2cAI4%2FHK%2BVluyrfIWtJUCzyhvNU6DNa4hwhHF0Lxhh36l1N05R4hrc3LNc2vCCyzhywt35Msu%2FJCt60U42WW%2F7AnhlLYtM6jICwpH0GbUaqXbwvMt14ED6bzK5tXoWGGLKoynFOzUoA95%2FepiGARwDLxl%2BaUNy%2FODkmnsyAsbhu0LaK26prCjvtYqriOwMqdEx5ritlUWTTss0QJfXmGFXEFjKEeRxnKcrazkCpwVGOb5gkIbn3FEsO16W0dd0ytRY9nwPEt48PeLZ1%2FUP%2Frm4JfvH957Er0TWeEX0GXJ3djwBVgwR%2FXoyratDet4eK1rrwhrswLH6JRCo20EVhCaUS8d9nGMyKzycnjTMxzfKAdgm8XQtxzh%2B6uuGdpifnk9f%2BNVS2wvuU7gubYNA4tO5zqbnedz%2FQ7TYjqPOzpx4WqcwAiab9ZcD2baChpnsW51fIzM43nUaj6eyarhhBswstBrmAQ8xhZyazYbti671Xnf2g4Na94MoVW%2BOyeblh9YTjk47cRNQzd8Df5s9HSnORBTBIZlD22NlxqvQa8mzgejz1ZFUHGj8Rph4N5sjPDu3bmB5UjZuHIEq5iiBKoyQzBd0x6lmrEJji22uyVKVKZileuKOg2JGvYmOFFQqbYH1zrvTum2YYfRjKL0ybUcel7ElciC0dEoeo0nY78WNkdMqabr8mldn9TlID7ZU7txsuur6YnrciKSKoMPDKEmHSlkEsEtQkWxYbET8qGUYk3XktSjTyzApUsWA7p%2F6PVj8VqTBcsNTp0OUYOEQU9sCK%2Fp4ePHwBhn7SmvgULc4EF6MFNMUbQDBdOEUackquqNHHQSUdUTYFpIM034DZxQmO1QJt6owalFNyAUDCdCEGPZqIQ4jpme%2BHkoQP1XG4NXFAzoUXWsY4Q1DFk2HHzLdstt%2FeKopeb6VtNR8HC0CQxvUwSldqTCOiN6emL4UectUX13b7f%2B1gcH95%2FVd3%2Bz9%2Fm33917d62QXyqsXr22Xrx%2BrSC9IV2ztoSUtzxpxfXKQsLS2up1qf7o4f6%2Fvj78%2FYP6s0%2Bk%2Bj8eHX706ODRfWnv4zfrzx8ffvuhBLSr7z586cV%2FPzj80zv7f%2Fhy799%2F3X%2Fyq%2Fqnu%2FBPSsZjL5oeG6%2FDvL3t10XRYVOQ437KFcNxhN10Gqu6WbSqx5Pbw4i7X7746pP6r5%2B27XjKbv97LPlhzYNaK%2Fq1bG5Ke7%2F74vC9v0iHX7%2B7%2F%2BE3ccieVMozNoYvfMpDx055RkId0RhVCVI5TUAdnwrqyJioQ5QrKslgd8Fht%2F%2FOgxfP7tUfPhncjq%2FLa4Zn5K65li8kMo9el1vY62td0tR%2FhsbvJRp1gmBM5PzRqIyHRg3qXZ6B8SKD8ZSJfoC0yCrH5gDiHbz9z4xm504z5UxWipv4iEDmg2VP1LMMEaQzhceCDCOUBLJuNikpWwKbTZ7EcqF7JS5uvS4T8XmKWENntPvKdFVHUMfEr033kWq2%2BTqxzdc%2B67rf113YWVg41ulkgqwvDK9cKUETmANcvPd%2BLFYZQirHSaLFp0QbP3vpCq%2FjbbyqeLLyPf8Q1obtLEUspox9g0LSlqqiaYiDB6GkFPO0BDrjVtxl91ICeIQdmbednKVPBkNutLagWmzwphu0zbYz214dMsqd%2BT7raJY4n93VIcaaltDI1AnVn63Q6Ak%2FtOP2UXVQPSGIoaGoEOcpA6ysaSZXODaIqaBbhG%2BcLF01EkMUv2p4AVjWCUtlmLKOnDhhNloW8N0QqsjGVm3%2FBTYUi6SeeBmBUyfir8J6Vshtcp7eMlx1PSGt1wJRrbnSog2TKf2kYgVC2v%2F73%2FZ334QKuWPTMJZ%2F3e5xvJQpKwlwbH1oS%2ByUwBbmScJ3oTNmnSw%2BEiRWzOCQWv%2BCucNNrKPOYuTcj0utSz0yTMkL7fb6YX%2BYXowymqmTTdOTWESgsuZUoYwksIhkLJoRFv3wSvFHo%2FGHppU%2FOOPPBeYPfJKQaCE%2Bw0%2Fq8fMq1EpS8eZrrxQkhUnF9cJyQTr47ccjpzxqKpGDFYSznOesmXMJpv0SvCKVX5oycyjRGcWIEyUBOkoGnWlDZ5jN%2BuFpw1JJm6N7UDLaTDfDUae32kOoriNV0%2BLvl8pocz4pztTvhxiNXFoqyTXgzRQZuWaWXJhpmqZyxJJ2rzJypak4C3ZsERVnq4bnV6qGbbvb0g2jbDmb0g1hSlCq7X%2F1x9EopKeSQhhRQjMKXWAKEUwYVQiMJKNQmih0KjuK1p7bD5B0VG2j4YanEjearjXv881wM1XcTOhrRAYp1xjiWhRVkhaHaIabVCQ9S%2FniVSjJHj49%2BPPzvc8%2Fla7kij%2B%2BvvQyFGj1tx5LV%2FI58JIczksHz3fr9z%2Fbe%2FvBwdPP9t%2F%2FD%2Fw%2FGpRw0hMq50cllSE1o9KUqcQpm9DXMAyyZI05JDkqxlpGpZRR6TwevB2BXKkE1%2FHD6Rm6pouu6d3tyImiYUJQRq5Ukat3%2BQYMAhJNoHwjqeSNjkASGW2mTRtterQhiGgRU5Ske6vVUXDTBRE2PERueYZjDkWSlD26kZhb9CLOYI%2Fs9qHBAI90dA%2BseTGov6oxpWMIu6d6k9SeCXu8zSiicxVplOiZsKclbDKbwuaZrGdG1lQlejSS%2BG9Vz1Q9YVXHFQgpVzWGnI5lyh5T2dO7yxaqfph9yvSkgM0yaU9S2nQ2pa03jZAJezaEzTVMEVFJUomdCXuiwlZnU9gYYY4yafeS9s%2F%2BD622xKPUaAAA";
        String s = "eyJkaXN0aW5jdF9pZCI6IjE3NGMwMjc5Y2VmM2FhLTA1MzcxNTQ5NDc4ODRlLTRkMTEyYTI5LTIzMDQwMC0xNzRjMDI3OWNmMDc2NyIsImxpYiI6eyIkbGliIjoianMiLCIkbGliX21ldGhvZCI6ImNvZGUiLCIkbGliX3ZlcnNpb24iOiIxLjE1LjIxIn0sInByb3BlcnRpZXMiOnsiJHRpbWV6b25lX29mZnNldCI6LTQ4MCwiJHNjcmVlbl9oZWlnaHQiOjcwMCwiJHNjcmVlbl93aWR0aCI6NDAwLCIkbGliIjoianMiLCIkbGliX3ZlcnNpb24iOiIxLjE1LjIxIiwiJGxhdGVzdF90cmFmZmljX3NvdXJjZV90eXBlIjoi55u05o6l5rWB6YePIiwiJGxhdGVzdF9zZWFyY2hfa2V5d29yZCI6IuacquWPluWIsOWAvF%2Fnm7TmjqXmiZPlvIAiLCIkbGF0ZXN0X3JlZmVycmVyIjoiIiwiJGxhdGVzdF91dG1fc291cmNlIjoidG91ZmFuZyIsIiRsYXRlc3RfdXRtX21lZGl1bSI6ImRvdXlpbl9LT0wiLCIkbGF0ZXN0X3V0bV9jb250ZW50IjoidGVhNjA5IiwiY3VycmVudF9wYWdlIjo2MSwiYmxvY2tfdHlwZSI6MzE3LCJ2ZW51ZV9pZCI6IjVmZGVmYmIzYjQ3ZTkwMDlmZmQxMmY2NiIsImNvbXBvbmVudF90eXBlIjoic3ViVmVudWVFbnRyeV8yIiwiY29tcG9uZW50X3VpZCI6Ik16QTJPRGMxIiwiY29tcG9uZW50X2NvbnRlbnRfaW5mb19saXN0IjoiW3tcImNvbnRlbnRUaXRsZVwiOlwi6YCB5aWz55SfXCIsXCJwb3NpdGlvblwiOjIsXCJjb250ZW50SURcIjpcIlwiLFwiY29udGVudFVybFwiOlwiaHR0cHM6Ly9mYXN0LmRld3UuY29tL25lemhhLXBsdXMvZGV0YWlsLzVmOThkZjZkZmE0YmM5NmY5NzRkZTE3MVwifV0iLCJfdGltZSI6MTYwODQ1MTM5MzU3NCwiaDV1c2VyaWQiOiIwZTFjODUzZC1jYThjLTkyYTQtZWY0NS00ZWZhZjYwNDRhNjgiLCJfdXJsIjoiaHR0cHM6Ly9mYXN0LmRld3UuY29tL25lemhhLXBsdXMvZGV0YWlsLzVmZGVmYmIzYjQ3ZTkwMDlmZmQxMmY2NiIsIiRpc19maXJzdF9kYXkiOmZhbHNlfSwiYW5vbnltb3VzX2lkIjoiMTc0YzAyNzljZWYzYWEtMDUzNzE1NDk0Nzg4NGUtNGQxMTJhMjktMjMwNDAwLTE3NGMwMjc5Y2YwNzY3IiwidHlwZSI6InRyYWNrIiwiZXZlbnQiOiJ2ZW51ZV9jb21wb25lbnRfY29udGVudF9jbGljayIsIl90cmFja19pZCI6OTAyNDM1NzV9";
//        String str = "%7B%22distinct_id%22%3A%221765523d034954-0ee0c403c2e8a18-16a7d24-304704-1765523d035a48%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%221765523d034954-0ee0c403c2e8a18-16a7d24-304704-1765523d035a48%22%7D";
        String str = "H4sIAAAAAAAAE%2B2b224TRxiAX8Xa5jKxZmbPuSMJaSO1Kmpoe1FVq8nu2B6x3nX3kJAipECVqgEaQlEPoqDSKi1ctFICiADh8DCwdniL%2FrN2SOzYbmwHoyrri8g7szPzH7%2F5Pbv54pzk%2BkXuWdyRxiUsmxrBhoakUSniZSaNYw2ZukZkhAxTHZWo53uLZT8O6%2FdPnDTliUlkjmFNJ2OKYU6NmdMnpscMgqcUMj0tG1MKTMXmmRfB7VFAHWaFjAZ2yQpYGLuRxc5WYDoGd1nQbZ9JJyaYyArCsDQ0F9w4LFkt4hADy6NSJfArLIg4C6Xxc1LIwpD73uFFq8%2BGZUVRkA4SjIhFvvY9ZvmFQshAZBiIQGu36Ac8KpVB6K9iFkbWjFhCR0zTHVWjGMZj3RQz%2BKE1zwIhhjCnkiei0aZBwFkALa8e%2FZP8%2Bqx2Zzu5dBd65nnII6vsO6CaBNcOm%2Bc2s%2BK4F%2Bs6CxYYMEiHiNUak%2FQyBdjRie3I8qgwMujLzlLPYUFugXrF3KsnN5LVb5PNzWT9Tm31cq52Zym5%2BSRXYhx8GFY3Hrz%2BYXvn4XKycTW5ulVduVy9%2FCK5siztt9vuAg2HLVrz1I1hKTBuczhUfLBIw3pYaOPyOfjOP54VF2XqxQVqR3GQWvNEpeKKyAkrcSN%2BiaLpWIamOdeHWIoWK0IfXXjBpukt%2Bhwhpqwy4iDZnEPgPkQcjBy5YGPEVFVMV6YBeIV5jVnTlcFHrhDkVAniA5NRXPf2nmwg6D7XkzzOiywaCe2AMc9a4E5UksYVrIhbacSjeNfptFKx5mLuiqWUvIbyKE%2FSoaIjFcD2y%2FmQL8SU550YWqGTOwV6eP%2FuSgEuK5YgrA1Tg0aPRQt%2B8MZKyvv77gS%2FQc7ShitciIbQphWWKup7xX3ijyzwApfGC9QNWUPoPTPU9RHWj4MAJrQqtCjGyVqznW3fi0R3Q5Sm2LFL1POYW8856U3A7EaKFcSuGFPbfpqsfbfXf4YtWqCecyCehcw8tAo8gER26OIb2RsDQz8ObJhRPg%2B5xcOIexC4B%2FFYlzRlFlymYXquYxQ0RXEHE6Vjyywq%2BXWfg4HPnx%2Ftgc%2Fm0PisIFVFhGDB4I58VjI%2BD4fPn4s%2FH%2FpFPyfge3UN4JsDJNd%2Bu1%2B7sDE4hknPGNZNpOsZhDMIHzsIm0gZGoRlTZMVGWky6gJhNYPwW4Lwy6W110sXautPq6t3d75fe7l0DVqqt%2B4mjy8ma1eqfz%2BHlhZQn4gibtNGwby8ITi982Lr1bPbucEpLfdeLCMNKcTMOJ1x%2Bhhy2hgap2FVbMqGpildOK1nnB5Osdw4vGig%2BKeb1Surab28fW1n5d7gGFb6wLBKdBllGM4wfPwwDEE5tHLZUDSiGxjhLhg2Mgy%2FmzPl0xDAELe5ZP1Gsv442by4s%2Fxj8uivvQON5M%2F7UFPv1stHhWu1d1xjRVVlJcN1hutjiGtteI8AoV5WMQRzt0eAZobroZ1utACcpgV07d7PO5c2k2t%2FVLe2IDWS5W9qD243jjaOitFafyU1yRidMfoYMpqg4ZXUmJi6bKRPHjsxWoiTMfpdlNQfnJz55OTsbNv3M15ff1699XtK6esPj4TSeu%2BUJlBLG9n5c0bp40hpeXiUNmUDqVBOd6mkCc4o%2FU5fpjsV8DJ1qyu%2F1FYeCywfOPiA24HeR1ZUG%2F0U1RC4aobrDNfHDdfYMAZ%2B93kEEmrS5alC%2B%2BFMZFWWZaXLi3SEtLJ5f24MzuluqdAuxQ6f7B29ASnqHmSj6GEuK%2B8L1k9nJmgwEUeR781ErNwv1dvHZHvwHMEO0HmPasuF1o2rJes77aOd0NaWR81YPMCZDqnVdgM%2BzK6xK0RjD5yKT9U3qdkUAx%2FBd5flm9qmWES5%2BxlnC5PAq8B3XZiwEzMrfgApxKMeYd0vdJxUtv71eC%2F9dLFz7wijceSfTkXtiWPmwM%2FbOnCMYF2TkW6oWgayDGQZyDKQvU2QEUXFRwEyod1s6pdmmhnIhA%2FSjC4wO%2FDvZ%2F8XmPUHnk7O7AFIfRGo7Q%2BpQ58BdGJOM5laSRkH9RyBuPRCYALoPBGH3AOnNmfKJI0Y%2FJRaPJDtAzEtYAUW1DE0AG%2BGTsb%2BrfXfP4yHDMc%2BVXnLePzyXzay55StOwAA\\";
//        String decode = URLDecoder.decode(str, "UTF-8");
//        String s1 = transShenceFormatToJson(s);
//        String s1 = transShenceFormatToJson(str);



        System.out.println();

    }


}
