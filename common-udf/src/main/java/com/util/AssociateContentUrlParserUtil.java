package com.util;


import java.net.URLDecoder;
import java.util.Map;

/**
 * Author: <PERSON><PERSON>
 */
public class AssociateContentUrlParserUtil {

	public static String parse(String need, String contentUrl) throws Exception {

		if (need.equals("content_id")) {
			return parseContentId(contentUrl);
		} else {
			return parseContentType(contentUrl);
		}

	}


	public static String parseContentType(String contentUrl) {

		if (contentUrl.contains("/trend/details")) { //图文、图文打卡
			return "1";
		} else if (contentUrl.contains("/trend/videoPage")) { //视屏
			return "2";
		} else if (contentUrl.contains("/trend/postsPage")) { //专栏
			return "3";
		} else if (contentUrl.contains("/live/LiveRoomPage")) { //直播
			return "5";
		} else if (contentUrl.contains("/live/LiveReplayProtratiPage")) { //直播回放
			return "10";
		} else if (contentUrl.contains("/trend/LabelGroupPagePage")) { //标签
			return "8";
		} else if (contentUrl.contains("/trend/CircleGroupPage")) { //圈子
			return "7";
		} else if (contentUrl.contains("fast.dewu.com") || contentUrl.contains("m.dewu.com")) { //站内活动
			return "12";
		} else if (!(contentUrl.contains("fast.dewu.com") || contentUrl.contains("m.dewu.com"))) { //其他（站外活动）
			return "13";
		} else {
			return "";
		}

	}


	public static String parseContentId(String contentUrl) throws Exception {

		Map<String, String> paramMap = UrlParseUtil.URLRequest(contentUrl);

		if (contentUrl.contains("/trend/details") || contentUrl.contains("/trend/videoPage") || contentUrl.contains("/trend/postsPage")) {
			return paramMap.get("id");
		} else if (contentUrl.contains("/live/LiveRoomPage")) {
			return paramMap.get("roomId");
		} else if (contentUrl.contains("/live/LiveReplayProtratiPage")) {
			return paramMap.get("liveId");
		} else if (contentUrl.contains("/trend/LabelGroupPagePage")) {
			return paramMap.get("tagId");
		} else if (contentUrl.contains("/trend/CircleGroupPage")) {
			return paramMap.get("circleId");
		} else if (contentUrl.contains("fast.dewu.com") || contentUrl.contains("m.dewu.com")) {
			String decodeUrl = URLDecoder.decode(contentUrl, "UTF-8");
			String[] split = decodeUrl.split("/");
			return split[split.length - 1];
		} else {
			return "";
		}

	}


	public static void main(String[] args) throws Exception {

//      String url = "https://m.poizon.com/router/trend/details?id=2345";
//      System.out.println(AssociateContentUrlParserUtil.parse("content_id", url));
//      System.out.println(AssociateContentUrlParserUtil.parse("content_type", url));
//
//      String url2 = "https://m.poizon.com/router/trend/postsPage?id=8765";
//      System.out.println(AssociateContentUrlParserUtil.parse("content_id", url2));
//      System.out.println(AssociateContentUrlParserUtil.parse("content_type", url2));
//
//      String url3 = "https://m.poizon.com/router/trend/videoPage?id=1111";
//      System.out.println(AssociateContentUrlParserUtil.parse("content_id", url3));
//      System.out.println(AssociateContentUrlParserUtil.parse("content_type", url3));
//
//      String url4 = "https://m.poizon.com/router/live/LiveRoomPage?roomId=2222";
//      System.out.println(AssociateContentUrlParserUtil.parse("content_id", url4));
//      System.out.println(AssociateContentUrlParserUtil.parse("content_type", url4));
//
//      String url5 = "https://m.poizon.com/router/live/LiveReplayProtratiPage?liveId=3333";
//      System.out.println(AssociateContentUrlParserUtil.parse("content_id", url5));
//      System.out.println(AssociateContentUrlParserUtil.parse("content_type", url5));

//      String url6 = "https://fast.dewu.com/nezha-plus/detail/5f994d04f518686f90488957";
		String url6 = "https://m.poizon.com/router/web/BrowserPage?loadUrl=https://m.dewu.com/nezha/detail/5f23ee3d5a17c9807aa8efdc";
//      String url6 = "https://m.poizon.com/router/web/BrowserPage?loadUrl=https%3A%2F%2Ffast.dewu.com%2Fnezha-plus%2Fpreview%2F60e6cb8a3777a5534669dc30";
		System.out.println(AssociateContentUrlParserUtil.parse("content_id", url6));
		System.out.println(AssociateContentUrlParserUtil.parse("content_type", url6));

//      String url7 = "https://m.dewu.com/router/trend/LabelGroupPagePage?tagId=1797";
//      System.out.println(AssociateContentUrlParserUtil.parse("content_id", url7));
//      System.out.println(AssociateContentUrlParserUtil.parse("content_type", url7));
//
//      String url8 = "https://m.poizon.com/router/trend/CircleGroupPage?circleId=144";
//      System.out.println(ContentUrlParserUtil.parse("content_id", url8));
//      System.out.println(ContentUrlParserUtil.parse("content_type", url8));

	}

}
