package com.udf.types;

import org.apache.flink.api.common.typeutils.SimpleTypeSerializerSnapshot;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.TypeSerializerSnapshot;
import org.apache.flink.core.memory.DataInputView;
import org.apache.flink.core.memory.DataOutputView;

import java.io.IOException;

/**
 * @description: /
 * @Author: lmh
 * @date: 2024/1/24 10:54
 */
public class OptimizedBitmapTypeSerializer extends TypeSerializer<BitmapValue> {
    /**
     * Sharable instance of the BitmapValue.
     */
    public static final OptimizedBitmapTypeSerializer INSTANCE = new OptimizedBitmapTypeSerializer();
    private static final long serialVersionUID = 4261938190508446007L;

    @Override
    public boolean isImmutableType() {
        return false;
    }

    @Override
    public TypeSerializer<BitmapValue> duplicate() {
        return this;
    }

    @Override
    public BitmapValue createInstance() {
        return new BitmapValue();
    }

    @Override
    public BitmapValue copy(BitmapValue from) {
        try {
            byte[] bitmapToBytes = BitmapValue.bitmapToBytes(from);
            return BitmapValue.bitmapFromBytes(bitmapToBytes);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public BitmapValue copy(BitmapValue from, BitmapValue reuse) {
        return copy(from);
    }

    @Override
    public int getLength() {
        return -1;
    }

    @Override
    public void serialize(BitmapValue record, DataOutputView target) throws IOException {
        record.serialize(target);
    }

    @Override
    public BitmapValue deserialize(DataInputView source) throws IOException {
        BitmapValue navigableMap = new BitmapValue();
        navigableMap.deserialize(source);
        return navigableMap;
    }

    @Override
    public BitmapValue deserialize(BitmapValue reuse, DataInputView source) throws IOException {
        reuse.deserialize(source);
        return reuse;
    }

    @Override
    public void copy(DataInputView source, DataOutputView target) throws IOException {
        BitmapValue deserialize = this.deserialize(source);
        copy(deserialize);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        } else if (obj != null && obj.getClass() == OptimizedBitmapTypeSerializer.class) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public int hashCode() {
        return this.getClass().hashCode();
    }

    @Override
    public TypeSerializerSnapshot<BitmapValue> snapshotConfiguration() {
        return new OptimizedBitmapTypeSerializer.BitmapValueSerializerSnapshot();
    }

    public static final class BitmapValueSerializerSnapshot
            extends SimpleTypeSerializerSnapshot<BitmapValue> {

        public BitmapValueSerializerSnapshot() {
            super(() -> OptimizedBitmapTypeSerializer.INSTANCE);
        }
    }
}
