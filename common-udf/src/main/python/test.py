import logging
import json
import os
# 创建一个新的 logger 对象
flink_logger = logging.getLogger('flink_logger')
flink_logger.setLevel(logging.ERROR)
# 获取环境变量 LOGS_PATH 的值
LOGS_PATH = os.getenv("LOGS_PATH")
# 创建文件处理器，将日志记录到文件中
# 根据 LOGS_PATH 的值选择日志文件路径
if LOGS_PATH is None:
    file_handler = logging.FileHandler('flink_error.log')
else:
    file_handler = logging.FileHandler(f"{LOGS_PATH}/flink_error.log")
# 定义日志等级
file_handler.setLevel(logging.ERROR)
# 定义日志格式
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
# 将处理器添加到 logger 对象
flink_logger.addHandler(file_handler)

class FlinkUdf:

    def close(self):
        pass

    def process(self, value):
        try:
            if isinstance(value, str):
                value_dict = json.loads(value)
            elif isinstance(value, dict):
                value_dict = value
            else:
                raise ValueError("Invalid value type")
            return value_dict
        except Exception as e:
            flink_logger.exception(f"udf process error.\nerror:{e}")
            raise e
