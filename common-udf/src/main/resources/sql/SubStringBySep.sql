CREATE FUNCTION subStringBySep AS 'com.alibaba.blink.udx.SubStringBySep';

create view demo_data as
select 'u_00001'  as uid,
       'shanghai' as address,
       'index'    as page
union all
select 'u_00002'  as uid,
       'shanghai' as address,
       'index'    as page
union all
select 'u_00003'  as uid,
       'shanghai' as address,
       'index'    as page
union all
select 'u_00004' as uid,
       'beijing' as address,
       'index'   as page
;


create table print_sink (
                            string_value string,
                            uid    string
) with (
      'connector'='print'
      );

insert into print_sink
select
    subStringBySep(uid,'_',-1) as string_value,
    uid
from demo_data;