create function nonnull as 'com.alibaba.blink.udx.udaf.NonNullFunction';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view demo_data as
select 'u_00001'  as uid,
       'shanghai' as address,
       1    as page
union all
select 'u_00002'  as uid,
       'shanghai' as address,
       2    as page
union all
select 'u_00003'  as uid,
       'shanghai' as address,
       1    as page
union all
select 'u_00004' as uid,
       'beijing' as address,
       2   as page
;


create table print_sink (
string_value string,
string_value2 string,
key_field    string
) with (
    'connector'='print'
);

insert into print_sink
select
    nonnull(uid) as string_value,
    nonnull(page) as string_value2,
    address as key_field
from demo_data
group by address;