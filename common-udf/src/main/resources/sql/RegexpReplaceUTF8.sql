create function regexpReplaceUTF8 as 'com.alibaba.blink.udx.udf.RegexpReplaceUTF8';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view lxh_demo_data as
select 'u_00001_lxh_1111111111'  as uids,
       '\uD83D\uDCB0580 支持放店\uD83D\uDE80\n【独家优势】\n全新G版出货‼️                       A                            Ma Maniere x Air Jordan 1 High OG \"Sail and Burgundy\" ' as address
;


create table lxh_print_sink (
                                string_value string,
                                key_field    string
) with (
      'connector'='print'
      );

insert into lxh_print_sink
select
    uids as string_value,
    regexpReplaceUTF8(address) as key_field
from lxh_demo_data;