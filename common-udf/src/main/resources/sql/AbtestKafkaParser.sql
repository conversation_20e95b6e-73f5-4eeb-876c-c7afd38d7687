create function kafka_parse as 'com.alibaba.blink.udx.udtf.AbtestKafkaParser';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view demo_data as
select '{"activityId":"779","activityLayerId":"677","activityName":"快捷分享v478","activityPercentage":100,"currentGroupParam":"quick_share_V478","expVersion":"4","groupId":"1586","groupName":"快捷分享关","groupParam":"quick_share_V478","groupParamValue":"0","groupPercentage":100,"requestFlag":"server","requestTime":1650889187648,"userId":"1202621"}'  as message
;


create table print_sink (
deviceUUID VARCHAR,
userId VARCHAR,
requestParams VARCHAR,
useUserId INTEGER,
requestTime BIGINT,
activityId INTEGER,
activityName VARCHAR,
activityVersion INTEGER,
activitySalt VARCHAR,
activityLayerId INTEGER,
groupId INTEGER,
groupName VARCHAR,
groupDistributionType INTEGER,
groupPercentage INTEGER,
groupResultSdk VARCHAR
) with (
    'connector'='print'
);

insert into print_sink
select
    T.`deviceUUID`,
    T.`userId`,
    T.`requestParams`,
    T.`useUserId`,
    T.`requestTime`,
    T.`activityId`,
    T.`activityName`,
    T.`activityVersion`,
    T.`activitySalt`,
    T.`activityLayerId`,
    T.`groupId`,
    T.`groupName`,
    T.`groupDistributionType`,
    T.`groupPercentage`,
    T.`groupResultSdk`
from demo_data,
lateral table(kafka_parse(cast(`message` as bytes))) AS T;