create function awesome_udf as 'com.alibaba.blink.udx.LogUaParse';

create view demo_data as

-- 测试用例：解析 Android UA
-- 输出 - android#10#v2057a
select 'Mozilla/5.0 (Linux; Android 10; V2057A Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like ' ||
      'Gecko) Version/4.0 Chrome/75.0.3770.156 Mobile Safari/537.36  aweme_190200 JsSdk/1.0 NetType/WIFI ' ||
      'Channel/vivo_1128_64 AppName/aweme app_version/19.2.0 ByteLocale/zh-CN Region/CN AppSkin/white ' ||
      'AppTheme/light BytedanceWebview/d8a21c6 TTWebView/0751130025426' as ua

union all

-- 测试用例：解析 iOS UA
-- 输出 - ios#15.0.2#iphone
select 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 aweme_19.0.0 JsSdk/2.0 NetType/WIFI Channel/App Store ByteLocale/zh Region/CN AppTheme/dark RevealType/Dialog WKWebView/1 BytedanceWebview/d8a21c6'
           as ua

union all

-- 测试用例：解析 华为 UA
-- 输出 - android#10#art-al00x
select 'com.ss.android.ugc.aweme/190201 (Linux; U; Android 10; zh_CN_#Hans; ART-AL00x; Build/HUAWEIART-AL00x; ' ||
       'Cronet/TTNetVersion:28eaf52b 2021-12-28 QuicVersion:68cae75d 2021-08-12)' as ua

union all

-- 测试用例：解析 一加 UA
-- 输出 - android#9#oneplus a5010
select 'Mozilla/5.0 (Linux; Android 9; ONEPLUS A5010; wv) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.101' ||
       ' Mobile Safari/537.36 JsSdk/2 NewsArticle/7.3.1 NetType/wifi' as ua

union all

-- 测试用例：解析 小米 UA
-- 输出 - android#9#redmi k20
select 'Mozilla/5.0 (Linux; Android 9; Redmi K20 Build/PKQ1.190302.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Mobile Safari/537.36 JsSdk/2 NewsArticle/7.2.3' as ua
;

create table print_sink (
    ua string
) with (
      'connector'='print'
      );

insert into print_sink
select
     awesome_udf(ua)
from demo_data;