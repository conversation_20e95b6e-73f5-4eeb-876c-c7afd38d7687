create function awesome_udf as 'com.poizon.userportrait.udtf.SetConvertSingle';

create view demo_data as
select '["1", "2", "3"]' as message

union all
select '{"contentType": 0}' as message

union all
select '[]' as message

union all
select '' as message

union all
select '{}[]' as message;


create table print_sink (
                            before varchar,
                            after varchar
) with (
      'connector'='print'
      );


insert into print_sink
select
    message, s
from demo_data, LATERAL TABLE(awesome_udf(message)) as T(s);