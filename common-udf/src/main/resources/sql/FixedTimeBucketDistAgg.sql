CREATE FUNCTION FixedTimeBucketDistAgg AS 'com.alibaba.blink.udx.udaf.FixedTimeBucketDistAgg';

create view demo_data as
select '2022-01-02 12:15:12'  as integer_time,
       'shanghai' as address,
       'index'    as page
union all
select '2022-01-02 12:15:12'  as integer_time,
       'shanghai' as address,
       'index'    as page
union all
select '2022-01-02 12:15:12'  as integer_time,
       'shanghai' as address,
       'index'    as page
union all
select '2022-01-02 12:15:12' as integer_time,
       'beijing' as address,
       'index'   as page
;


create table print_sink (
                            string_value string,
                            uid    string
) with (
      'connector'='print'
      );

insert into print_sink
select
    FixedTimeBucketDistAgg(concat(substring(integer_time,12,5),':00'),cast(1 as bigint)) as start_up_cnt ,
    -- concat(substring(integer_time,12,5),':00') as start_up_cnt,
    substring(integer_time,0,10)
from demo_data
group by substring(integer_time,0,10);