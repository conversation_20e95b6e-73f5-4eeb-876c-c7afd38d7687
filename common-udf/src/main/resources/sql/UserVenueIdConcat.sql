create function awesome_udf as 'com.alibaba.blink.udx.udaf.UserVenueIdConcat';

create view demo_data as
select 'u_00001'  as uid,
       'shanghai' as address,
       1    as page

union all
select 'u_00002'  as uid,
       'shanghai' as address,
       2    as page

union all
select 'u_00003'  as uid,
       'shanghai' as address,
       1    as page

union all
select 'u_00004' as uid,
       'beijing' as address,
       2   as page

union all
select '' as uid,
       'beijing' as address,
       2   as page

;


create table print_sink (
                            string_value2 varchar,
                            key_field    varchar
) with (
      'connector'='print'
      );

insert into print_sink
select
    address as string_value2,
    awesome_udf(uid) as key_field
from demo_data
group by address;