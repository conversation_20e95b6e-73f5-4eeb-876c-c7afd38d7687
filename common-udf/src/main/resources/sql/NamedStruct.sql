create function namedStruct as 'com.poizon.userportrait.kafkadim.NamedStruct';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view demo_data as
select 'u_00001'  as uid,
       '1'        as address,
       '1'          as page
;


create table print_sink (
                            string_value string,
                            string_value2 string,
                            key_field    string
) with (
      'connector'='print'
      );

insert into print_sink
select
    uid as string_value,
    page as string_value2,
    namedStruct(address, 'expenseType') as key_field
from demo_data
group by address;