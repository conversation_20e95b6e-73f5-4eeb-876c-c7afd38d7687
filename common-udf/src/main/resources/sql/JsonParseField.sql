create function json_parse as 'com.alibaba.blink.udx.log.JsonParseField2';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view demo_data as
select '{"appVersion":"4.65.2","blockType":"9"}'  as message
;


create table print_sink (
block_type VARCHAR,
app_version VARCHAR
) with (
    'connector'='print'
);

insert into print_sink
select
    T.`block_type`,
    T.`app_version`
from demo_data,
lateral table(json_parse(cast(`message` as varchar), 'blockType','appVersion')) AS T(
  `block_type`,
  `app_version`
)