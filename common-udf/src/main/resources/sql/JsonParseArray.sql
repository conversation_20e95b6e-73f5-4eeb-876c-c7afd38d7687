create function JsonParseArray as 'com.alibaba.blink.udx.udtf.JsonParseArray';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view demo_data as
select 'u_00001'  as uid,
       '[{\"expenseType\":1,\"expenseName\":\"技术服务费\",\"originalExpense\":12495,\"currentExpense\":12495,\"originalPercent\":500,\"currentPercent\":500,\"extendJson\":\"{\\\"currentPercent\\\":500,\\\"currentexpense\\\":12495,\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalTechnicalFeeLimit\\\":{\\\"expenseLimit\\\":{\\\"max\\\":24900,\\\"min\\\":1500},\\\"originalExpense\\\":12495,\\\"originalPercent\\\":500},\\\"salaInfoInnerVo\\\":{\\\"salaPercent\\\":10000,\\\"salaType\\\":5}}\",\"salePercent\":10000},{\"expenseType\":3,\"expenseName\":\"转账手续费\",\"originalExpense\":2499,\"currentExpense\":2499,\"originalPercent\":100,\"currentPercent\":100,\"extendJson\":\"{\\\"currentExpense\\\":2499,\\\"currentPercent\\\":100,\\\"originalExpense\\\":2499,\\\"originalPercent\\\":100}\"},{\"expenseType\":5,\"expenseName\":\"查验费\",\"originalExpense\":800,\"currentExpense\":800,\"extendJson\":\"{\\\"currentExpense\\\":800,\\\"originalExpense\\\":800}\"},{\"expenseType\":2,\"expenseName\":\"鉴别费\",\"originalExpense\":1500,\"currentExpense\":1500,\"extendJson\":\"{\\\"currentExpense\\\":1500,\\\"originalExpense\\\":1500}\"},{\"expenseType\":4,\"expenseName\":\"包装服务费\",\"originalExpense\":1000,\"currentExpense\":1000,\"extendJson\":\"{\\\"currentExpense\\\":1000,\\\"originalExpense\\\":1000}\"},{\"expenseType\":7,\"expenseName\":\"预计收入\",\"originalExpense\":231606,\"currentExpense\":231606},{\"expenseType\":6,\"expenseName\":\"总费用\",\"originalExpense\":18294,\"currentExpense\":18294}]'
           as address,
       1    as page
;


create table print_sink (
                            string_value string,
                            string_value2 string,
                            key_field    string
) with (
      'connector'='print'
      );

insert into print_sink
select
    uid as string_value,
    page as string_value2,
    JsonParseArray(address, 'expenseType', 'returnColumn', '1', '2') as key_field
from demo_data
group by address;