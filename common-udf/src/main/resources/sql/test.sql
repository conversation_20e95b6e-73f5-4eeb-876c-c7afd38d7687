create view demo_data as
select 'u_00001'  as uid,
       'shanghai' as address,
       'index'    as page
;

create table print_sink (
t1 bigint,
t2 bigint,
t3 varchar,
t4 bigint,
t5 bigint,
t6 numeric
) with (
    'connector'='print'
);

insert into print_sink
select
    unix_timestamp(cast(now() as varchar)) t1,
    cast(cast(now() as varchar) as bigint) t2,
    substring(cast(to_timestamp_ltz(1651824709685, 3) as varchar),1,10),
    cast(1651824709685 as bigint)/60000,
    cast(cast(1651830988982 as bigint)/60000 as bigint)*60000,
    COALESCE(if(uid = '' or CHARACTER_LENGTH(uid)=0, cast(NULL as NUMERIC) ,cast(uid as NUMERIC)), cast(uid as numeric)) as `entityId`
from demo_data;