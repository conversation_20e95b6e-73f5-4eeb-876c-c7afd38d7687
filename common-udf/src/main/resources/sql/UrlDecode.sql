CREATE FUNCTION UrlDecode AS 'com.alibaba.blink.udx.udf.UrlDecode';

create view demo_data as
select 'http%3A%2F%2Fwww.baidu.com'  as url,
       'shanghai' as address,
       'index'    as page
union all
select 'http%3A%2F%2Fwww.baidu.cn'  as url,
       'shanghai' as address,
       'index'    as page
union all
select 'http%3A%2F%2Fwww.baidu.org'  as url,
       'shanghai' as address,
       'index'    as page
union all
select 'http%3A%2F%2Fwww.baidu.com' as url,
       'beijing' as address,
       'index'   as page
;


create table print_sink (
                            string_value string,
                            uid    string
) with (
      'connector'='print'
      );

insert into print_sink
select
    UrlDecode(url) as string_value,
    url
from demo_data;