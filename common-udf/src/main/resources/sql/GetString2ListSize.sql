create function getString2ListSize as 'com.poizon.userportrait.udf.GetString2ListSize';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view lxh_demo_data_1 as
select 'u_00001_lxh_1111111111'  as uids,
       'A,1,2,3,4,YYYYYYYddd' as address
;


create table lxh_print_sink (
                                string_value string,
                                key_field    int
) with (
      'connector'='print'
      );

insert into lxh_print_sink
select
    uids as string_value,
    getString2ListSize(address,',') as key_field
from lxh_demo_data_1;