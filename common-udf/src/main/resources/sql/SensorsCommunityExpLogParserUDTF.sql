CREATE FUNCTION logParser as 'com.poizon.udtf.SensorsCommunityExpLogParserUDTF';

create view demo_data as
select '[{"content_id":"25735015","position":1,"content_type":"1"}]'  as message
;

create table print_sink (
block_type VARCHAR,
app_version VARCHAR,
good_name INTEGER
) with (
    'connector'='print'
);

insert into print_sink
select
    T.`block_type`,
    T.`app_version`,
    T.`good_name`
from demo_data,
LATERAL TABLE (logParser(`message`)) as T (block_type, app_version, good_name)
;