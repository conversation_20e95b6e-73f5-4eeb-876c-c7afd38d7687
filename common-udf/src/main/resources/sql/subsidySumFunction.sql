create function subsidySumFunction as 'com.alibaba.blink.udx.udf.subsidySumFunction';

create view demo_data as
select '[{"funderType":"PLATFORM","subsidyType":"RATIO","subsidyValue":80},{"funderType":"INSTALLMENT_LE","subsidyType":"RATIO","subsidyValue":20}]'  as message
;


create table print_sink (
    longvalue bigint
) with (
      'connector'='print'
      );

insert into print_sink
select
     subsidySumFunction(message)  as longvalue
from demo_data;