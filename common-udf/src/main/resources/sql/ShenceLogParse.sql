create function shenceLogParse as 'com.alibaba.blink.udx.udf.ShenceLogParse';
-- create function keyby as 'com.flink.udaf.KeyByFunction';

create view lxh_demo_data as
select 'u_00001_lxh_1111111111'  as uids,
       'https://m.poizon.com/router/trend/details?id=2345' as address
;


create table lxh_print_sink (
                            string_value string,
                            key_field    string
) with (
      'connector'='print'
      );

insert into lxh_print_sink
select
    uids as string_value,
    shenceLogParse('contentUrl','content_id', address) as key_field
from lxh_demo_data;