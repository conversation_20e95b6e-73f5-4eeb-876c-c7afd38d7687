create function awesome_udf as 'com.alibaba.blink.udx.udf.MidHour';

create view demo_data as

-- 测试用例：返回 00 时（含）之前的小时
-- 输出 - 00
select '00' as param1, true as param2

union all

-- 测试用例：返回 01 时（含）之前的小时
-- 输出 - 00,01
select '01' as param1, true as param2

union all

-- 测试用例：返回 10 时（含）之前的小时
-- 输出 - 00,01,02,03,04,05,06,07,08,09,10
select '10' as param1, true as param2

union all

-- 测试用例：返回 23 时（含）之前的小时
-- 输出 - 00,01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21,22,23
select '23' as param1, true as param2

union all

-- 测试用例：返回 24 时（含）之前的小时
-- 输出 - 00,01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24
select '24' as param1, true as param2

union all

-- 测试用例：返回 00 时（含）之后的小时
-- 输出 - 00,01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21,22,23
select '00' as param1, false as param2

union all

-- 测试用例：返回 01 时（含）之后的小时
-- 输出 - 01,02,03,04,05,06,07,08,09,10,11,12,13,14,15,16,17,18,19,20,21,22,23
select '01' as param1, false as param2

union all

-- 测试用例：返回 10 时（含）之后的小时
-- 输出 - 10,11,12,13,14,15,16,17,18,19,20,21,22,23
select '10' as param1, false as param2

union all

-- 测试用例：返回 23 时（含）之后的小时
-- 输出 - 23
select '23' as param1, false as param2;

create table print_sink (
    before string,
    param1 string
) with (
      'connector'='print'
      );

insert into print_sink
select
    param1 as before, awesome_udf(param1, param2)
from demo_data;