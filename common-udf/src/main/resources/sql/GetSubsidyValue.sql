create function getSubsidyValue as 'com.alibaba.blink.udx.GetSubsidyValue';

create view check_data as
select '[{"funderType":"PLATFORM","subsidyType":"CONSTANT","subsidyValue":1500}]' as str1,
       'PLATFORM' as str2;


create table print_sink (
                            str1 varchar,
                            str2 varchar,
                            check_result double
)with(
     'connector'='print'
     );

insert into print_sink
select str1, str2,
       s as check_result
from check_data, LATERAL TABLE(getSubsidyValue(str1, str2, 2000)) as T(s);
