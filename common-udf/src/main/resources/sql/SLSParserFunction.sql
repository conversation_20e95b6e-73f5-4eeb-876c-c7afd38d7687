CREATE FUNCTION slsParser AS 'com.alibaba.blink.udx.udtf.SLSParserFunction';

create view demo_data as
select '[algo-tags]value:map[algo:{"category_lv1_id":[],"category_lv2_id":null,"category_lv3_id":["31","33"],"brand_id":["33"],"sex":""} content: cspu: tags:],request:&{UserId:6184357 AppId:duapp Os:ios PkgChannel: Imei: Oaid: AndroidId:null Idfa:BA221151-495B-4030-BB85-D669D0583BEC Fcuuid:32c5bdad1cef48c899a2150a4ffdfd18 Uuid:BA221151-495B-4030-BB85-D669D0583BEC Clipboard: Ua:Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 Ip:*************** Source:activate EventTime:1630917196 ShumeiId: OrderNo:},isNew:false
'  as categories,
       'shanghai' as address,
       'index'    as page
;


create table print_sink (
                            category_lv1_id string,
                            category_lv2_id string,
                            category_lv3_id string,
                            brand_id string,
                            uid string,
                            is_new string
) with (
      'connector'='print'
      );

insert into print_sink
select
    category_lv1_id,
    category_lv2_id,
    category_lv3_id,
    brand_id,
    uid,
    is_new
from demo_data,
         LATERAL TABLE (slsParser (categories)) as tmp (
    category_lv1_id,
    category_lv2_id,
    category_lv3_id,
    brand_id,
    uid,
    is_new
  );