package com.alibaba.blink.udx.udf;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * StringListUnionCountUdf 性能基准测试
 * 
 * 对比不同实现方案的性能差异
 */
public class StringListUnionCountUdfBenchmark {

    private static final StringListUnionCountUdf optimizedUdf = new StringListUnionCountUdf();

    public static void main(String[] args) {
        System.out.println("=== StringListUnionCountUdf 性能基准测试 ===\n");

        // 测试不同规模的数据
        int[] testSizes = {100, 1000, 5000, 10000};
        
        for (int size : testSizes) {
            System.out.println("测试规模: " + size + " 元素");
            runBenchmark(size);
            System.out.println();
        }
    }

    private static void runBenchmark(int size) {
        // 生成测试数据
        String list1 = generateTestList(size, "item1_", 0.3); // 30%重复率
        String list2 = generateTestList(size, "item2_", 0.3);

        // 预热JVM
        for (int i = 0; i < 100; i++) {
            optimizedUdf.eval(list1, list2);
            naiveImplementation(list1, list2);
            streamImplementation(list1, list2);
        }

        // 性能测试
        int iterations = 1000;

        // 测试优化版本
        long startTime = System.nanoTime();
        int result1 = 0;
        for (int i = 0; i < iterations; i++) {
            result1 = optimizedUdf.eval(list1, list2);
        }
        long optimizedTime = System.nanoTime() - startTime;

        // 测试朴素实现
        startTime = System.nanoTime();
        int result2 = 0;
        for (int i = 0; i < iterations; i++) {
            result2 = naiveImplementation(list1, list2);
        }
        long naiveTime = System.nanoTime() - startTime;

        // 测试Stream实现
        startTime = System.nanoTime();
        int result3 = 0;
        for (int i = 0; i < iterations; i++) {
            result3 = streamImplementation(list1, list2);
        }
        long streamTime = System.nanoTime() - startTime;

        // 验证结果一致性
        assert result1 == result2 && result2 == result3 : 
            String.format("结果不一致: %d, %d, %d", result1, result2, result3);

        // 输出结果
        System.out.printf("结果: %d 个唯一元素\n", result1);
        System.out.printf("优化版本: %.2f ms (平均 %.4f ms/次)\n", 
            optimizedTime / 1_000_000.0, optimizedTime / 1_000_000.0 / iterations);
        System.out.printf("朴素实现: %.2f ms (平均 %.4f ms/次)\n", 
            naiveTime / 1_000_000.0, naiveTime / 1_000_000.0 / iterations);
        System.out.printf("Stream实现: %.2f ms (平均 %.4f ms/次)\n", 
            streamTime / 1_000_000.0, streamTime / 1_000_000.0 / iterations);
        
        System.out.printf("性能提升: 优化版本比朴素实现快 %.1fx\n", (double) naiveTime / optimizedTime);
        System.out.printf("性能提升: 优化版本比Stream实现快 %.1fx\n", (double) streamTime / optimizedTime);
    }

    /**
     * 朴素实现：直接使用split
     */
    private static int naiveImplementation(String list1, String list2) {
        Set<String> unionSet = new HashSet<>();
        
        if (list1 != null && !list1.isEmpty()) {
            String[] elements1 = list1.split(",");
            for (String element : elements1) {
                String trimmed = element.trim();
                if (!trimmed.isEmpty()) {
                    unionSet.add(trimmed);
                }
            }
        }
        
        if (list2 != null && !list2.isEmpty()) {
            String[] elements2 = list2.split(",");
            for (String element : elements2) {
                String trimmed = element.trim();
                if (!trimmed.isEmpty()) {
                    unionSet.add(trimmed);
                }
            }
        }
        
        return unionSet.size();
    }

    /**
     * Stream实现：使用Java 8 Stream API
     */
    private static int streamImplementation(String list1, String list2) {
        Set<String> unionSet = new HashSet<>();
        
        if (list1 != null && !list1.isEmpty()) {
            unionSet.addAll(Arrays.stream(list1.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet()));
        }
        
        if (list2 != null && !list2.isEmpty()) {
            unionSet.addAll(Arrays.stream(list2.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet()));
        }
        
        return unionSet.size();
    }

    /**
     * 生成测试列表
     * 
     * @param size 元素数量
     * @param prefix 元素前缀
     * @param duplicateRate 重复率
     * @return 生成的测试字符串
     */
    private static String generateTestList(int size, String prefix, double duplicateRate) {
        StringBuilder sb = new StringBuilder();
        Random random = new Random(42); // 固定种子
        
        for (int i = 0; i < size; i++) {
            if (i > 0) {
                sb.append(",");
            }
            
            // 根据重复率决定是否生成重复元素
            int elementId;
            if (random.nextDouble() < duplicateRate && i > 0) {
                // 生成重复元素
                elementId = random.nextInt(i);
            } else {
                // 生成新元素
                elementId = i;
            }
            
            sb.append(prefix).append(elementId);
        }
        
        return sb.toString();
    }
}
