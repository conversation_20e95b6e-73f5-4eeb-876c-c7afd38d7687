package com.alibaba.blink.udx.udf;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * StringListUnionCountOptimalUdf 测试类
 */
public class StringListUnionCountOptimalUdfTest {

    private StringListUnionCountOptimalUdf udf;

    @BeforeEach
    public void setUp() {
        udf = new StringListUnionCountOptimalUdf();
    }

    @Test
    public void testBothEmpty() {
        assertEquals(0, udf.eval("", ""));
        assertEquals(0, udf.eval(null, null));
        assertEquals(0, udf.eval("", null));
        assertEquals(0, udf.eval(null, ""));
    }

    @Test
    public void testOneEmpty() {
        assertEquals(3, udf.eval("a,b,c", ""));
        assertEquals(3, udf.eval("a,b,c", null));
        assertEquals(3, udf.eval("", "x,y,z"));
        assertEquals(3, udf.eval(null, "x,y,z"));
    }

    @Test
    public void testNoOverlap() {
        assertEquals(6, udf.eval("a,b,c", "x,y,z"));
        assertEquals(4, udf.eval("1,2", "3,4"));
    }

    @Test
    public void testCompleteOverlap() {
        assertEquals(3, udf.eval("a,b,c", "a,b,c"));
        assertEquals(3, udf.eval("a,b,c", "c,b,a"));
    }

    @Test
    public void testPartialOverlap() {
        assertEquals(4, udf.eval("a,b,c", "c,d,e"));
        assertEquals(5, udf.eval("1,2,3", "3,4,5,6"));
    }

    @Test
    public void testDuplicatesInSameList() {
        assertEquals(3, udf.eval("a,b,c,a,b", "x,y,z"));
        assertEquals(6, udf.eval("a,b,c,a,b", "x,y,z,x,y"));
        assertEquals(3, udf.eval("a,a,a", "b,b,b"));
    }

    @Test
    public void testWithSpaces() {
        assertEquals(3, udf.eval(" a , b , c ", "x,y,z"));
        assertEquals(4, udf.eval("a, b, c", " c , d , e "));
    }

    @Test
    public void testEmptyElements() {
        assertEquals(2, udf.eval("a,,b", "c,,d"));
        assertEquals(3, udf.eval("a,b,", ",c,d"));
    }

    @Test
    public void testSingleElements() {
        assertEquals(1, udf.eval("a", "a"));
        assertEquals(2, udf.eval("a", "b"));
        assertEquals(1, udf.eval("single", ""));
    }

    @Test
    public void testPerformanceComparison() {
        // 创建大数据集进行性能测试
        StringBuilder list1 = new StringBuilder();
        StringBuilder list2 = new StringBuilder();
        
        for (int i = 0; i < 10000; i++) {
            if (i > 0) {
                list1.append(",");
                list2.append(",");
            }
            list1.append("item").append(i);
            list2.append("item").append(i + 5000); // 5000个重叠
        }
        
        long startTime = System.nanoTime();
        int result = udf.eval(list1.toString(), list2.toString());
        long endTime = System.nanoTime();
        
        assertEquals(15000, result); // 10000 + 10000 - 5000 = 15000
        System.out.println("最优版本大数据集测试耗时: " + (endTime - startTime) / 1_000_000.0 + "ms");
    }

    @Test
    public void testEdgeCases() {
        assertEquals(0, udf.eval(",,,", ",,,"));
        assertEquals(1, udf.eval("a", "a,a,a,a"));
        assertEquals(1, udf.eval("same,same,same", "same"));
        assertEquals(2, udf.eval("a,b,a,b,a,b", "b,a,b,a"));
    }

    @Test
    public void testComplexSpacing() {
        assertEquals(3, udf.eval("  a  ,  b  ,  c  ", "  x  ,  y  ,  z  "));
        assertEquals(4, udf.eval(" a , b , c ", " c , d , e "));
        assertEquals(2, udf.eval("a,   ,b", "c,   ,d"));
    }
}
