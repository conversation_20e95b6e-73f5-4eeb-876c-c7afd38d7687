package com.alibaba.blink.udx.udf;

import java.util.Random;

/**
 * 针对10000元素规模的专门性能测试
 */
public class StringListUnionCount10KBenchmark {

    public static void main(String[] args) {
        System.out.println("=== 10000元素规模专门性能测试 ===\n");

        StringListUnionCountOptimalUdf optimalUdf = new StringListUnionCountOptimalUdf();
        StringListUnionCountUltraOptimizedUdf ultraUdf = new StringListUnionCountUltraOptimizedUdf();

        // 测试不同的重叠率
        double[] overlapRates = {0.0, 0.1, 0.3, 0.5, 0.7, 0.9};
        
        for (double overlapRate : overlapRates) {
            System.out.printf("测试重叠率: %.1f%%\n", overlapRate * 100);
            runBenchmarkWithOverlap(optimalUdf, ultraUdf, 10000, overlapRate);
            System.out.println();
        }
        
        // 测试不同规模
        System.out.println("=== 不同规模性能测试 ===");
        int[] sizes = {1000, 3000, 5000, 7000, 10000};
        for (int size : sizes) {
            System.out.printf("测试规模: %d 元素\n", size);
            runBenchmarkWithOverlap(optimalUdf, ultraUdf, size, 0.3);
            System.out.println();
        }
    }

    private static void runBenchmarkWithOverlap(StringListUnionCountOptimalUdf optimal, 
                                              StringListUnionCountUltraOptimizedUdf ultra,
                                              int size, double overlapRate) {
        // 生成测试数据
        TestData testData = generateTestDataWithOverlap(size, overlapRate);
        String list1 = testData.list1;
        String list2 = testData.list2;
        int expectedResult = testData.expectedUnionSize;

        // 预热JVM
        for (int i = 0; i < 100; i++) {
            optimal.eval(list1, list2);
            ultra.eval(list1, list2);
        }

        // 性能测试
        int iterations = 1000;

        // 测试最优版本
        long startTime = System.nanoTime();
        int result1 = 0;
        for (int i = 0; i < iterations; i++) {
            result1 = optimal.eval(list1, list2);
        }
        long optimalTime = System.nanoTime() - startTime;

        // 测试超级优化版本
        startTime = System.nanoTime();
        int result2 = 0;
        for (int i = 0; i < iterations; i++) {
            result2 = ultra.eval(list1, list2);
        }
        long ultraTime = System.nanoTime() - startTime;

        // 验证结果
        if (result1 != result2) {
            System.err.printf("错误：结果不一致! 最优版本: %d, 超级优化版本: %d, 期望: %d\n", 
                result1, result2, expectedResult);
            return;
        }

        // 输出结果
        System.out.printf("结果: %d 个唯一元素\n", result1);
        System.out.printf("最优版本: %.2f ms (平均 %.4f ms/次)\n", 
            optimalTime / 1_000_000.0, optimalTime / 1_000_000.0 / iterations);
        System.out.printf("超级优化版本: %.2f ms (平均 %.4f ms/次)\n", 
            ultraTime / 1_000_000.0, ultraTime / 1_000_000.0 / iterations);
        
        double speedup = (double) optimalTime / ultraTime;
        System.out.printf("性能提升: %.2fx", speedup);
        
        if (speedup > 1.0) {
            System.out.printf(" (超级优化版本快 %.1f%%)\n", (speedup - 1) * 100);
        } else {
            System.out.printf(" (最优版本快 %.1f%%)\n", (1 / speedup - 1) * 100);
        }
    }

    /**
     * 生成具有指定重叠率的测试数据
     */
    private static TestData generateTestDataWithOverlap(int size, double overlapRate) {
        Random random = new Random(42);
        StringBuilder list1 = new StringBuilder();
        StringBuilder list2 = new StringBuilder();
        
        // 计算重叠元素数量
        int overlapCount = (int) (size * overlapRate);
        int uniqueInList1 = size - overlapCount;
        int uniqueInList2 = size - overlapCount;
        
        // 生成第一个列表
        for (int i = 0; i < size; i++) {
            if (i > 0) list1.append(",");
            
            if (i < uniqueInList1) {
                // 第一个列表独有的元素
                list1.append("list1_").append(i);
            } else {
                // 重叠元素
                list1.append("common_").append(i - uniqueInList1);
            }
        }
        
        // 生成第二个列表
        for (int i = 0; i < size; i++) {
            if (i > 0) list2.append(",");
            
            if (i < uniqueInList2) {
                // 第二个列表独有的元素
                list2.append("list2_").append(i);
            } else {
                // 重叠元素
                list2.append("common_").append(i - uniqueInList2);
            }
        }
        
        // 计算期望的并集大小
        int expectedUnionSize = uniqueInList1 + uniqueInList2 + overlapCount;
        
        return new TestData(list1.toString(), list2.toString(), expectedUnionSize);
    }
    
    /**
     * 测试数据类
     */
    private static class TestData {
        final String list1;
        final String list2;
        final int expectedUnionSize;
        
        TestData(String list1, String list2, int expectedUnionSize) {
            this.list1 = list1;
            this.list2 = list2;
            this.expectedUnionSize = expectedUnionSize;
        }
    }
    
    /**
     * 内存使用测试
     */
    public static void memoryUsageTest() {
        System.out.println("=== 内存使用测试 ===");
        
        StringListUnionCountUltraOptimizedUdf ultraUdf = new StringListUnionCountUltraOptimizedUdf();
        
        // 生成大数据集
        TestData testData = generateTestDataWithOverlap(10000, 0.3);
        
        // 记录内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行多次操作
        for (int i = 0; i < 1000; i++) {
            ultraUdf.eval(testData.list1, testData.list2);
        }
        
        runtime.gc(); // 强制垃圾回收
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        
        System.out.printf("内存使用变化: %d KB\n", (memoryAfter - memoryBefore) / 1024);
        System.out.printf("平均每次操作内存开销: %d bytes\n", (memoryAfter - memoryBefore) / 1000);
    }
}
