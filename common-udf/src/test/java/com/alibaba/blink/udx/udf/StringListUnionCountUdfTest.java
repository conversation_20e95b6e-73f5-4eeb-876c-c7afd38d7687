package com.alibaba.blink.udx.udf;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * StringListUnionCountUdf 测试类
 */
public class StringListUnionCountUdfTest {

    private StringListUnionCountUdf udf;

    @BeforeEach
    public void setUp() {
        udf = new StringListUnionCountUdf();
    }

    @Test
    public void testBothEmpty() {
        // 测试两个列表都为空
        assertEquals(0, udf.eval("", ""));
        assertEquals(0, udf.eval(null, null));
        assertEquals(0, udf.eval("", null));
        assertEquals(0, udf.eval(null, ""));
    }

    @Test
    public void testOneEmpty() {
        // 测试一个列表为空
        assertEquals(3, udf.eval("a,b,c", ""));
        assertEquals(3, udf.eval("a,b,c", null));
        assertEquals(3, udf.eval("", "x,y,z"));
        assertEquals(3, udf.eval(null, "x,y,z"));
    }

    @Test
    public void testNoOverlap() {
        // 测试无重叠元素
        assertEquals(6, udf.eval("a,b,c", "x,y,z"));
        assertEquals(4, udf.eval("1,2", "3,4"));
    }

    @Test
    public void testCompleteOverlap() {
        // 测试完全重叠
        assertEquals(3, udf.eval("a,b,c", "a,b,c"));
        assertEquals(3, udf.eval("a,b,c", "c,b,a")); // 顺序不同
    }

    @Test
    public void testPartialOverlap() {
        // 测试部分重叠
        assertEquals(4, udf.eval("a,b,c", "c,d,e")); // {a,b,c,d,e} - c重复
        assertEquals(5, udf.eval("1,2,3", "3,4,5,6")); // {1,2,3,4,5,6} - 3重复
    }

    @Test
    public void testDuplicatesInSameList() {
        // 测试同一列表内有重复元素
        assertEquals(3, udf.eval("a,b,c,a,b", "x,y,z"));
        assertEquals(6, udf.eval("a,b,c,a,b", "x,y,z,x,y"));
        assertEquals(3, udf.eval("a,a,a", "b,b,b"));
    }

    @Test
    public void testWithSpaces() {
        // 测试包含空格的情况
        assertEquals(3, udf.eval(" a , b , c ", "x,y,z"));
        assertEquals(4, udf.eval("a, b, c", " c , d , e "));
    }

    @Test
    public void testEmptyElements() {
        // 测试包含空元素的情况
        assertEquals(2, udf.eval("a,,b", "c,,d")); // 空字符串被忽略
        assertEquals(3, udf.eval("a,b,", ",c,d")); // 开头和结尾的空元素
    }

    @Test
    public void testSingleElements() {
        // 测试单个元素
        assertEquals(1, udf.eval("a", "a"));
        assertEquals(2, udf.eval("a", "b"));
        assertEquals(1, udf.eval("single", ""));
    }

    @Test
    public void testLargeNumbers() {
        // 测试数字字符串
        assertEquals(4, udf.eval("100,200,300", "300,400,500"));
        assertEquals(6, udf.eval("1,2,3", "4,5,6"));
    }

    @Test
    public void testSpecialCharacters() {
        // 测试特殊字符
        assertEquals(4, udf.eval("a@b,c#d", "e$f,g%h"));
        assertEquals(3, udf.eval("hello_world,test-case", "hello_world,another-test"));
    }

    @Test
    public void testPerformanceWithLargeLists() {
        // 性能测试：大列表
        StringBuilder list1 = new StringBuilder();
        StringBuilder list2 = new StringBuilder();
        
        // 创建1000个元素的列表
        for (int i = 0; i < 1000; i++) {
            if (i > 0) {
                list1.append(",");
                list2.append(",");
            }
            list1.append("item").append(i);
            list2.append("item").append(i + 500); // 500个重叠元素
        }
        
        long startTime = System.currentTimeMillis();
        int result = udf.eval(list1.toString(), list2.toString());
        long endTime = System.currentTimeMillis();
        
        assertEquals(1500, result); // 1000 + 1000 - 500 = 1500
        System.out.println("大列表性能测试耗时: " + (endTime - startTime) + "ms");
    }

    @Test
    public void testPerformanceComparison() {
        // 性能对比测试
        String list1 = generateRandomList(5000, "prefix1_");
        String list2 = generateRandomList(5000, "prefix2_");
        
        // 预热
        for (int i = 0; i < 10; i++) {
            udf.eval(list1, list2);
        }
        
        // 性能测试
        long startTime = System.nanoTime();
        int result = udf.eval(list1, list2);
        long endTime = System.nanoTime();
        
        System.out.println("10000元素性能测试结果: " + result);
        System.out.println("耗时: " + (endTime - startTime) / 1_000_000.0 + "ms");
        
        // 验证结果合理性
        assert result > 0 && result <= 10000;
    }

    @Test
    public void testEdgeCases() {
        // 边界情况测试
        assertEquals(0, udf.eval(",,,", ",,,"));
        assertEquals(1, udf.eval("a", "a,a,a,a"));
        assertEquals(1, udf.eval("same,same,same", "same"));
        assertEquals(2, udf.eval("a,b,a,b,a,b", "b,a,b,a"));
    }

    @Test
    public void testVeryLongStrings() {
        // 测试很长的字符串元素
        String longElement1 = "very_long_element_name_that_contains_many_characters_1";
        String longElement2 = "very_long_element_name_that_contains_many_characters_2";
        String longElement3 = "very_long_element_name_that_contains_many_characters_3";
        
        String list1 = longElement1 + "," + longElement2;
        String list2 = longElement2 + "," + longElement3;
        
        assertEquals(3, udf.eval(list1, list2));
    }

    /**
     * 生成随机测试列表
     */
    private String generateRandomList(int size, String prefix) {
        StringBuilder sb = new StringBuilder();
        Random random = new Random(42); // 固定种子保证可重复性
        
        for (int i = 0; i < size; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(prefix).append(random.nextInt(size / 2)); // 制造一些重复
        }
        
        return sb.toString();
    }
}
