package com.alibaba.blink.udx.udf;

import java.util.Random;

/**
 * 性能对比测试：原版本 vs 最优版本
 */
public class PerformanceComparisonTest {

    public static void main(String[] args) {
        System.out.println("=== 字符串列表并集计数UDF性能对比 ===\n");

        StringListUnionCountUdf originalUdf = new StringListUnionCountUdf();
        StringListUnionCountOptimalUdf optimalUdf = new StringListUnionCountOptimalUdf();

        // 测试不同规模的数据
        int[] testSizes = {1000, 5000, 10000, 20000};
        
        for (int size : testSizes) {
            System.out.println("测试规模: " + size + " 元素");
            runComparison(originalUdf, optimalUdf, size);
            System.out.println();
        }
    }

    private static void runComparison(StringListUnionCountUdf original, 
                                    StringListUnionCountOptimalUdf optimal, 
                                    int size) {
        // 生成测试数据
        String list1 = generateTestData(size, "item1_", 0.3);
        String list2 = generateTestData(size, "item2_", 0.3);

        // 预热JVM
        for (int i = 0; i < 50; i++) {
            original.eval(list1, list2);
            optimal.eval(list1, list2);
        }

        // 性能测试
        int iterations = 500;

        // 测试原版本
        long startTime = System.nanoTime();
        int result1 = 0;
        for (int i = 0; i < iterations; i++) {
            result1 = original.eval(list1, list2);
        }
        long originalTime = System.nanoTime() - startTime;

        // 测试最优版本
        startTime = System.nanoTime();
        int result2 = 0;
        for (int i = 0; i < iterations; i++) {
            result2 = optimal.eval(list1, list2);
        }
        long optimalTime = System.nanoTime() - startTime;

        // 验证结果一致性
        if (result1 != result2) {
            System.err.printf("错误：结果不一致! 原版本: %d, 最优版本: %d\n", result1, result2);
            return;
        }

        // 输出结果
        System.out.printf("结果: %d 个唯一元素\n", result1);
        System.out.printf("原版本总耗时: %.2f ms (平均 %.4f ms/次)\n", 
            originalTime / 1_000_000.0, originalTime / 1_000_000.0 / iterations);
        System.out.printf("最优版本总耗时: %.2f ms (平均 %.4f ms/次)\n", 
            optimalTime / 1_000_000.0, optimalTime / 1_000_000.0 / iterations);
        
        double speedup = (double) originalTime / optimalTime;
        System.out.printf("性能提升: %.2fx\n", speedup);
        
        if (speedup > 1.0) {
            System.out.printf("最优版本比原版本快 %.1f%%\n", (speedup - 1) * 100);
        } else {
            System.out.printf("原版本比最优版本快 %.1f%%\n", (1 / speedup - 1) * 100);
        }
    }

    /**
     * 生成测试数据
     */
    private static String generateTestData(int size, String prefix, double duplicateRate) {
        StringBuilder sb = new StringBuilder();
        Random random = new Random(42); // 固定种子保证可重复性
        
        for (int i = 0; i < size; i++) {
            if (i > 0) {
                sb.append(",");
            }
            
            // 根据重复率决定是否生成重复元素
            int elementId;
            if (random.nextDouble() < duplicateRate && i > 0) {
                elementId = random.nextInt(i);
            } else {
                elementId = i;
            }
            
            sb.append(prefix).append(elementId);
            
            // 随机添加一些空格，测试trim性能
            if (random.nextDouble() < 0.1) {
                sb.append(" ");
            }
        }
        
        return sb.toString();
    }
}
