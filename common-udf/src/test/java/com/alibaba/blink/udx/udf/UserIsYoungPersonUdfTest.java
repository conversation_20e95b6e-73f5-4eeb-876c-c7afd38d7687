//package com.alibaba.blink.udx.udf;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
///**
// * UserIsYoungPersonUdf 测试类
// */
//public class UserIsYoungPersonUdfTest {
//
//    private UserIsYoungPersonUdf udf;
//
//    @BeforeEach
//    public void setUp() {
//        udf = new UserIsYoungPersonUdf();
//    }
//
//    @Test
//    public void testDeviceDimensionMatch() {
//        // 测试设备维度匹配的情况
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "[\"uuid1\",\"uuid2\",\"uuid3\"]";
//        String uuidList = "uuid2,uuid4";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(1, result, "设备ID匹配时应该返回1");
//    }
//
//    @Test
//    public void testDeviceDimensionNoMatch() {
//        // 测试设备维度不匹配的情况
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "[\"uuid1\",\"uuid2\",\"uuid3\"]";
//        String uuidList = "uuid4,uuid5";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "设备ID不匹配时应该返回0");
//    }
//
//    @Test
//    public void testDeviceDimensionEmptyUuidList() {
//        // 测试用户设备ID列表为空的情况
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "[\"uuid1\",\"uuid2\",\"uuid3\"]";
//        String uuidList = "";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "用户设备ID列表为空时应该返回0");
//    }
//
//    @Test
//    public void testDeviceDimensionEmptyArray() {
//        // 测试itemValue为空数组的情况（大部分数据的情况）
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "[]";
//        String uuidList = "uuid1,uuid2";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "itemValue为空数组时应该直接返回0");
//    }
//
//    @Test
//    public void testDeviceDimensionInvalidJson() {
//        // 测试无效JSON格式的情况
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "invalid_json";
//        String uuidList = "uuid1,uuid2";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "无效JSON格式时应该返回0");
//    }
//
//    @Test
//    public void testAppSettingDimensionWithPassword() {
//        // 测试App设置维度有密码的情况
//        String itemKey = "young_person_password";
//        String itemValue = "123456";
//        String uuidList = "uuid1,uuid2";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(1, result, "有密码时应该返回1");
//    }
//
//    @Test
//    public void testAppSettingDimensionEmptyPassword() {
//        // 测试App设置维度密码为空的情况
//        String itemKey = "young_person_password";
//        String itemValue = "";
//        String uuidList = "uuid1,uuid2";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "密码为空时应该返回0");
//    }
//
//    @Test
//    public void testAppSettingDimensionBlankPassword() {
//        // 测试App设置维度密码为空白字符的情况
//        String itemKey = "young_person_password";
//        String itemValue = "   ";
//        String uuidList = "uuid1,uuid2";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "密码为空白字符时应该返回0");
//    }
//
//    @Test
//    public void testOtherItemKey() {
//        // 测试其他itemKey的情况
//        String itemKey = "other_config";
//        String itemValue = "some_value";
//        String uuidList = "uuid1,uuid2";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(0, result, "其他itemKey时应该返回0");
//    }
//
//    @Test
//    public void testNullParameters() {
//        // 测试空参数的情况
//        int result1 = udf.eval(null, "value", "uuid1");
//        assertEquals(0, result1, "itemKey为null时应该返回0");
//
//        int result2 = udf.eval("key", null, "uuid1");
//        assertEquals(0, result2, "itemValue为null时应该返回0");
//    }
//
//    @Test
//    public void testDeviceDimensionWithSpaces() {
//        // 测试设备ID包含空格的情况
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "[\"uuid1\",\"uuid2\",\"uuid3\"]";
//        String uuidList = " uuid2 , uuid4 ";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(1, result, "设备ID包含空格时应该正确匹配");
//    }
//
//    @Test
//    public void testSingleDeviceId() {
//        // 测试单个设备ID的情况
//        String itemKey = "sys_young_person_device_config";
//        String itemValue = "[\"uuid1\"]";
//        String uuidList = "uuid1";
//
//        int result = udf.eval(itemKey, itemValue, uuidList);
//        assertEquals(1, result, "单个设备ID匹配时应该返回1");
//    }
//}
