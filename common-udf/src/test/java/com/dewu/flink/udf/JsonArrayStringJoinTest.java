package com.dewu.flink.udf;

import org.junit.Test;

import static org.junit.Assert.*;

public class JsonArrayStringJoinTest {

    @Test
    public void eval() {
        String json1 = "[\"a\",\"b\",\"c\"]";
        String json2 = "[1,2,3]";
        String json3 = "";
        String json4 = "{}";
        String json5 = "1";
        JsonArrayStringJoin func = new JsonArrayStringJoin();
        System.out.println(func.eval(json1,","));
        System.out.println(func.eval(json2,","));
        System.out.println(func.eval(json3,","));
        System.out.println(func.eval(json4,","));
        System.out.println(func.eval(json5,","));
    }
}