package com.poizon.udf.json;

import org.junit.Test;

import static org.junit.Assert.*;

public class JsonValuesSumToDoubleTest {

    @Test
    public void eval() {
        String jsonString = "{\"766427027\":0.040892,\"766427028\":0.048327,\"766427029\":0.04461,\"766427030\":0.111524,\"766427031\":0.118959,\"766427032\":0.063197,\"766427033\":0.096654,\"766427034\":0.063197,\"766427035\":0.02974,\"766427036\":0.01487,\"766427037\":0.011152,\"790628199\":0.007435,\"790628296\":0.141264,\"790628298\":0.037175,\"790628316\":0.018587,\"790628318\":0.003717,\"790628338\":0.003717,\"790628383\":0.092937,\"887630798\":0.052045}";
        String keys = "790628338,766427036,766427027,790628318,790628383";
        JsonValuesSumToDouble jsonValuesSumToDouble = new JsonValuesSumToDouble();
        System.out.println(jsonValuesSumToDouble.eval(jsonString, keys));
    }
}