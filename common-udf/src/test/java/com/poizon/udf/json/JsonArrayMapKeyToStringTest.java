package com.poizon.udf.json;

import org.junit.Test;

/**
 * @Description 短标签类测试
 * <AUTHOR>
 * @Date 2025/8/7 上午10:15
 **/
public class JsonArrayMapKeyToStringTest {

    @Test
    public void eval() {
        String jsonArray = null;
        jsonArray = "[{\"奥迪\":0.999999},{\"宝马\":0.899999}, {\"奔驰\":0.799999}, {\"理想\":0.56999}, {\"蔚来\":0.499999}]";
        JsonArrayMapKeyToString jsonArrayMapKeyToString = new JsonArrayMapKeyToString();
        String eval = jsonArrayMapKeyToString.eval(jsonArray);
        System.out.println(eval);
    }
}
