package com.poizon.udf.json;

import org.junit.Test;

import static org.junit.Assert.*;

public class JsonValuesConcatTest {

    @Test
    public void eval() {
        String jsonString = "{\"93529\":\"766427033\",\"1176580\":\"790628296\",\"2545054\":\"887630798,790628298\",\"5280728\":\"766427034,766427032,766427030,766427031,766427028,766427029\",\"6245487\":\"790628338,766427036,766427027,790628318\",\"6590418\":\"790628383\",\"10553762\":\"790628199,766427035\",\"10859475\":\"766427037\",\"11422712\":\"790628316\"}";
        String keys = "6245487,6590418,60000000";
        JsonValuesConcat jsonValuesConcat = new JsonValuesConcat();
        System.out.println(jsonValuesConcat.eval(jsonString, keys));
    }
}