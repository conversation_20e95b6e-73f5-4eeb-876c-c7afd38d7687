package com.poizon.udf.json;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/8/9 下午4:38
 **/
public class JsonStringUdfV2NotWriteNulltTest {

    @Test
    public void eval() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sss",11);
        jsonObject.put("sss2",11);
        jsonObject.put("ssws",11);
        jsonObject.put("ssws3",11);
        jsonObject.put("ssws4",null);
        jsonObject.put("ssws5",null);
        String eval1 = new JsonStringUdfV2NotWriteNull().eval("kkkk",null,"ke", 1,"kee", "2", "keee",jsonObject.toJSONString());
        System.out.println(eval1);
    }
}
