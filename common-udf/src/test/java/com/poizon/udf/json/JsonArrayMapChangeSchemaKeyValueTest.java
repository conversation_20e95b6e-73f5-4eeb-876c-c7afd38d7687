package com.poizon.udf.json;

import org.junit.Test;
import java.util.Arrays;
import java.util.Map;

/**
 * @Description 兴趣标签类测试
 * <AUTHOR>
 * @Date 2025/8/7 上午11:27
 **/
public class JsonArrayMapChangeSchemaKeyValueTest {

    @Test
    public void eval() {
        String jsonArray = null;
        jsonArray = "[{\"社交通讯\":0.839949}, {\"影音娱乐\":0.683113}, {\"聊天通讯\":0.965475}]";
        JsonArrayMapChangeSchemaKeyValue jsonArrayMapChangeSchemaKeyValue = new JsonArrayMapChangeSchemaKeyValue();
        Map<String, String>[] eval = jsonArrayMapChangeSchemaKeyValue.eval(jsonArray);
        System.out.println(Arrays.toString(eval));
    }
}
