package com.poizon.userportrait.udf;

import org.junit.Test;

import static org.junit.Assert.*;

public class JsonArrayGetTest {

    @Test
    public void eval() {
        JsonArrayGet jsonArrayGet = new JsonArrayGet();

        String s = "[{\"brandId\":1011748,\"pools\":\"5281\",\"eventId\":5281,\"ratio\":\"0.875\",\"investmentNo\":\"DLY2024122615225195260\",\"laoyueid\":734462,\"expectRoi\":\"\",\"roiVersion\":\"\",\"merchanId\":7457453}," +
                "{\"brandId\":1011748,\"pools\":\"3621\",\"eventId\":3621,\"ratio\":\"1.0\",\"investmentNo\":\"DLY2024120410391636507\",\"laoyueid\":734462,\"expectRoi\":\"4.0\",\"roiVersion\":\"1956\",\"merchanId\":7457466}," +
                "{\"brandId\":1011807,\"pools\":\"5283\",\"eventId\":5283,\"ratio\":\"0.9\",\"investmentNo\":\"DLY2024122615534387321\",\"laoyueid\":734462,\"expectRoi\":\"\",\"roiVersion\":\"\",\"merchanId\":8400831}," +
                "{\"brandId\":1011989,\"pools\":\"4998\",\"eventId\":4998,\"ratio\":\"1.0\",\"investmentNo\":\"DLY2024122922535035130\",\"laoyueid\":734462,\"expectRoi\":\"4.0\",\"roiVersion\":\"1827\",\"merchanId\":7562859}," +
                "{\"brandId\":1012022,\"pools\":\"5311\",\"eventId\":5311,\"ratio\":\"1.0\",\"investmentNo\":\"DLY2025010311482378396\",\"laoyueid\":734462,\"expectRoi\":\"\",\"roiVersion\":\"\",\"merchanId\":7614005}]";

        // eval(String input, String k, String v)
        assertNotNull(jsonArrayGet.eval(s, "brandId", "1011748"));
        assertNull(jsonArrayGet.eval(s, "brandId", "-1"));

        // eval(String input, Integer index)
        assertNotNull(jsonArrayGet.eval(s, 0));
        assertNull(jsonArrayGet.eval(s, -1));


        // eval(String input, String... kvs)
        assertNotNull(jsonArrayGet.eval(s, "brandId", "1011748","merchanId","7457453"));
        assertNull(jsonArrayGet.eval(s, "brandId", "1011748","merchanId","0000000"));

    }

}