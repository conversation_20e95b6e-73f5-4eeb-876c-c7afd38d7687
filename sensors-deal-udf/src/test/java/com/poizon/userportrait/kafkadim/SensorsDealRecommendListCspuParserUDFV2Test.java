package com.poizon.userportrait.kafkadim;

import com.poizon.userportrait.kafkadim.entity.RecommendContentInfoPortrait;
import com.poizon.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.List;

import static org.junit.Assert.*;
@Slf4j
public class SensorsDealRecommendListCspuParserUDFV2Test {

    @Test
    public void reInfo() {
        String recommendInfoList = "[]";
        try {
            if (StringUtils.isBlank(recommendInfoList)) {
                System.out.println("list is " + recommendInfoList);
            } else {
                List<RecommendContentInfoPortrait> recommendContentInfoList = JsonUtil.readValue(recommendInfoList, JsonUtil.getCollectionType(List.class, RecommendContentInfoPortrait.class));
            }
        } catch (IOException e) {
            log.error("recommendInfoList解析异常: input:{}, msg:{}", recommendInfoList, e.getStackTrace());
        }
    }
}