package com.poizon;

import com.poizon.userportrait.kafkadim.JavaConfUtil;
import com.poizon.userportrait.kafkadim.dao.CommodityCspuDao;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * @Author: wpp
 * @Date: 2021/6/24 21:46
 */
public class Jdbc {

    public static void main(String[] args) {
        InputStream fileInputStream =  CommodityCspuDao.class.getClassLoader().getResourceAsStream("config/config-prod.properties");
        if(!JavaConfUtil.getIsProd()) {
            fileInputStream = CommodityCspuDao.class.getClassLoader().getResourceAsStream("config/config-dev.properties");
        }
        Properties properties = new Properties();
        try {
            properties.load(fileInputStream);
        }catch (IOException e){
            System.out.println("读取配置文件异常" + e.getMessage());
        }

        System.out.println(properties.getProperty("jdbc.username"));
        System.out.println(properties.getProperty("jdbc.password"));
        CommodityCspuDao commodityCspuDao = new CommodityCspuDao();
        System.out.println(commodityCspuDao.loadAllCspu().size());

    }
}
