package com.poizon.userportrait.kafkadim.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.poizon.userportrait.kafkadim.entity.CommodityCspuModel;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/3/18
 */
@Getter
@Setter
public class DealMemoryPool {
    private DealMemoryPool() {

    }
    private static DealMemoryPool dealMemoryPool;

    public static DealMemoryPool getInstance() {
        if (dealMemoryPool == null) {
            synchronized (DealMemoryPool.class) {
                if (dealMemoryPool == null) {
                    dealMemoryPool = new DealMemoryPool();
                }
            }
        }
        return dealMemoryPool;
    }

    /**
     * sku和cspu  mapping关系
     */
    private Cache<String, String> skuCspuMappingPool = CacheBuilder.newBuilder()
            .maximumSize(100000) // 设置缓存的最大容量
            .expireAfterWrite(7, TimeUnit.DAYS)
            .concurrencyLevel(10) // 设置并发级别为10
            .build();
    /**
     * spu+propertyvalueId和cspu  mapping关系
     */
    private Map<String, String> spuCspuMappingPool = new HashMap<>(200000);

    /**
     * 未命中cache
     */
    private Cache<String, String> invalidSpuCspuMappingPool = CacheBuilder.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .concurrencyLevel(10)
            .build();

    /**
     * cspu 基础信息
     */
    private Cache<String, CommodityCspuModel> cspuInfoPool = CacheBuilder.newBuilder()
            .maximumSize(200000) // 设置缓存的最大容量
            .expireAfterWrite(3, TimeUnit.DAYS)
            .concurrencyLevel(10) // 设置并发级别为10
            .build();
    /**
     *
     * @return
     */
    public String getCspuBySpu(String spuPid) {
        return spuCspuMappingPool.get(spuPid);
    }

    /**
     *
     * @param spuKey
     * @param cspuId
     */
    public void updateSpuCspuMappingPool(String spuKey, String cspuId) {
        spuCspuMappingPool.put(spuKey, cspuId);
    }

    /**
     * 查询未命中缓存
     * @param spuPid
     * @return
     */
    public String getInvalidCspuBySpu(String spuPid) {
        return invalidSpuCspuMappingPool.getIfPresent(spuPid);
    }

    public void updateInvalidSpuCspuMappingPool(String spuKey, String cspuId) {
        invalidSpuCspuMappingPool.put(spuKey, cspuId);
    }



    /**
     *
     * @param sku
     * @return
     */
    public String getCspuBySku(String sku) {
        return skuCspuMappingPool.getIfPresent(sku);
    }

    /**
     *
     * @param sku
     * @param cspu
     */
    public void updateSkuCspuMappingPoo(String sku, String cspu) {
        skuCspuMappingPool.put(sku, cspu);
    }

    public CommodityCspuModel getCspuModelByCspu(String cspuId) {
        return cspuInfoPool.getIfPresent(cspuId);
    }

    public void updateCspuModelPool(String cspuId, CommodityCspuModel commodityCspuModel) {
        cspuInfoPool.put(cspuId, commodityCspuModel);
    }

}
