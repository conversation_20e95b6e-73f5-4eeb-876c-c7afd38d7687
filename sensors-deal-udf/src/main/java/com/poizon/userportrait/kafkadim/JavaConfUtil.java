package com.poizon.userportrait.kafkadim;

import java.io.InputStream;
import java.util.HashMap;
import java.util.Properties;

/**
 * @Author: wpp
 * @Date: 2020/4/17 15:05
 */
public  class JavaConfUtil {
    /**
     *cache缓存
     */
    static  HashMap<String, Properties> cache   = new HashMap<String,Properties>();

    /**
     * 加载配置文件，默认先读取缓存中配置
     * @param filePath
     * @return
     */

    public  static Properties loadProp(String filePath  ){

        if (filePath != null && filePath != ""){

            Properties props  = new Properties();
            InputStream inputStream  = JavaConfUtil.class.getResourceAsStream(filePath);

            try {
                if (inputStream == null) {
                    return null;
                }
                props.load(inputStream);
                return props;
            }catch (Exception e){
                    e.printStackTrace();
            }
        }
        return null;
    }

    public static Boolean getIsProd(){
        String res = loadProp("/config/config.properties").getProperty("isProd");
        if("true".equals(res)){
            return true;
        }
        return  false;
    }

    public static void main(String[] args) {
//        System.out.println(JavaConfUtil.class.getResource("/config/config.properties"));
        System.out.println("是否是生产:" +getIsProd());
    }


    /**
     * 获取key对应的String类型值
     * @param key
     * @return 值
     */
    public static String getString(String key  )  {
        if (key == null){
            return null;
        }
        String res = loadProp("/config/config-dev.properties").getProperty(key);
        if(getIsProd()){
            res = loadProp("/config/config-prod.properties").getProperty(key);
        }

        return  res;
    }

    /**
     * 获取key对应的String类型值
     * @param key
     * @return 值
     */
    public static Integer getInt(String key  )  {
        if (key == null){
            return null;
        }
        String res = loadProp("/config/config-dev.properties").getProperty(key);
        if(getIsProd()){
            res = loadProp("/config/config-prod.properties").getProperty(key);
        }
        return  Integer.valueOf(res);
    }
}
