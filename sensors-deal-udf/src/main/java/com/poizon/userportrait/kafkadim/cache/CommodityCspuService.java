package com.poizon.userportrait.kafkadim.cache;

import com.poizon.userportrait.kafkadim.dao.CommodityCspuDao;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/19
 */
@Slf4j
public class CommodityCspuService {
    private static volatile CommodityCspuService instance;

    private CommodityCspuDao commodityCspuDao;

    private CommodityCspuService() {
        commodityCspuDao = CommodityCspuDao.getInstance();
    }

    public static CommodityCspuService getInstance() {
        if (instance == null) {
            synchronized (CommodityCspuService.class) {
                if (instance == null) {
                    instance = new CommodityCspuService();
                }
            }
        }
        return instance;
    }

    public Map<String, String> loadAllCspu() {
        return commodityCspuDao.loadAllCspu();
    }

    public Map<String, String> selectOnesBySpuAndPropertyValueId(Map<String, String> spuMap) {
        Map<String, String> selectOnesBySpuAndPropertyValueId = commodityCspuDao.selectOnesBySpuAndPropertyValueId(spuMap);
        for (Map.Entry<String, String> entry : spuMap.entrySet()) {
            String key = entry.getKey()+"_"+entry.getValue();
            if (!selectOnesBySpuAndPropertyValueId.containsKey(key)) {
                //不存在的key也保留
                selectOnesBySpuAndPropertyValueId.put(key, null);
            }
        }
        return selectOnesBySpuAndPropertyValueId;
    }
}
