package com.poizon.userportrait.kafkadim.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Description 神策日志解析后的数据
 * <AUTHOR>
 * @Date 2022/11/4 下午4:25
 **/
@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SensorsDealLog {
    private String userId;
    private String time;
    private String event;
    private String currentPage;
    private String currentPageTitle;
    private String blockType;
    private String blockTypeTitle;
    private String jumpType;
    private String jumpContentId;
    private String os;
    private String wifi;
    private String networkType;
    private String ip;
    private String appVersion;
    private String spuId;
    private String algorithmProductPropertyValue;
    private String targetSpuId;
    private String algorithmTargetSpuPropertyValue;
    private String algorithmChannelId;
    private String algorithmRequestId;
    private String skuId;
    private String cspuId;
    private String targetCspuId;
    private String categoryId;
    private String brandId;
    private String articleNum;
    private String color;
    private String SeriesId;
    private String fitId;
    private String city;
    private String province;
    private String country;
    private String searchKeyWord;
    private String recommendContentInfoList;
    private String jumpContentUrl;
    private String deviceId;
    private String contentId;
    private String contentType;
    private String contentUrl;
    private String feedbackContentType;
    private String feedbackType;
    private String blockContentId;
    private String blockContentPosition;
    private String position;
    private String searchFilterInfoList;
    private String searchKeyWordPosition;
    private String searchKeyWordSource;
    private String searchKeyWordType;
    private String searchPositionRule;
    private String searchResultPosition;
    private String searchResultType;
    private String searchSource;
    private String tradeTabId;
    private String tradeTabTitle;
    private String tradeType;
    private String venueId;
    private String paymentMethod;
    private String ifSuccess;
    private String properties;
    private String appList;
    private String smartMenuId;
    private String smartMenuContentType;
    private String minPrice;
    private String level1CategoryId;
    private Long flushTime;
    private Long receiveTime;
    private Long isUp;
    private Long isBack;
    private String eventPageBlock;
    private String acm;
}
