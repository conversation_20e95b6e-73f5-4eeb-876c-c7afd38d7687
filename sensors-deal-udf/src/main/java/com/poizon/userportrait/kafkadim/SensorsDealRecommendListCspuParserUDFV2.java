package com.poizon.userportrait.kafkadim;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.poizon.userportrait.kafkadim.entity.RecommendContentInfoPortrait;
import com.poizon.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
/**
 * @Description 解析瀑布流曝光的recommendContentInfoListV2,弃用SensorsDealRecommendListCspuParserUDF
 * <AUTHOR>
 * @Date 2022/7/5 下午5:41
 **/
@Slf4j
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
public class SensorsDealRecommendListCspuParserUDFV2 extends ScalarFunction {

    /**spu+ pid 和 cspu的映射关系*/
    private Cache<String, String> spuPidMappingCspuCache;

    private HbaseUtil hbaseUtil;

    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        hbaseUtil = new HbaseUtil();
        initCache();
    }

    public void initCache() {
        spuPidMappingCspuCache = CacheBuilder.newBuilder()
                .maximumSize(4000000) //202207,商品表约370w支持全量缓存
                .expireAfterWrite(6, TimeUnit.HOURS) //6小时缓存，spu+pid->cspuid关系完全稳定，可适当放大
                .concurrencyLevel(8)
                .build();
    }

    @Override
    public void close() throws Exception {
        hbaseUtil.close();
    }

    public String eval(String recommendInfoList) {
        try {
            // null、"", " "直接返回
            if (StringUtils.isBlank(recommendInfoList)) {
                return null;
            }
            List<RecommendContentInfoPortrait> recommendContentInfoList = JsonUtil.readValue(recommendInfoList, JsonUtil.getCollectionType(List.class, RecommendContentInfoPortrait.class));
            // key: spu_pid value: cspuId
            Map<String, String> spuMap = new HashMap<>(16);
            if (recommendContentInfoList != null && !recommendContentInfoList.isEmpty()) {
                for (int i=0; i < recommendContentInfoList.size(); i++) {
                    RecommendContentInfoPortrait recommendContentInfo = recommendContentInfoList.get(i);
                    // contentType为0的商品时,contentId为spuId
                    if ("0".equals(recommendContentInfo.getContentType()) && !"".equals(recommendContentInfo.getContentID()) && !"".equals(recommendContentInfo.getPropertyValueId())) {
                        String spuAndPropertyValueKey = recommendContentInfo.getContentID()+"_"+recommendContentInfo.getPropertyValueId();
                        // 缓存中查询cspuId
                        String cspuId = spuPidMappingCspuCache.getIfPresent(spuAndPropertyValueKey);
                        if (null != cspuId) {
                            recommendContentInfo.setCspuId(cspuId);
                        } else {
                            // 等待HBASE批量查询
                            spuMap.put(spuAndPropertyValueKey, null);
                        }
                    }
                }
            }
            // 批量查询HBASE
            if (MapUtils.isNotEmpty(spuMap)) {
                List<String> rowKeys = Lists.newArrayList(spuMap.keySet());
                List<String> columns = Lists.newArrayList("id");
                // 获取结果
                Map<String, Map<String, Object>> reslutMap = hbaseUtil.batchLoadCommodityInfo(rowKeys, columns);
                if (MapUtils.isNotEmpty(reslutMap)) {
                    for (String rowKey : rowKeys) {
                        String cspuId = (String) reslutMap.get(rowKey).get("id");
                        if (null != cspuId) {
                            spuMap.put(rowKey, cspuId);
                            spuPidMappingCspuCache.put(rowKey, cspuId);
                        }
                    }
                }
            }
            // 设置每条数据的cspuId
            for (RecommendContentInfoPortrait recommendContentInfo : recommendContentInfoList) {
                String key = recommendContentInfo.getContentID()+"_"+recommendContentInfo.getPropertyValueId();
                if (null == recommendContentInfo.getCspuId()) {
                    recommendContentInfo.setCspuId(spuMap.get(key));
                }
            }
            return JsonUtil.writeValueAsString(recommendContentInfoList);
        }
        catch (Throwable e) {
            log.error("recommendInfoList解析异常: input:{}, msg:{}", recommendInfoList, e.getStackTrace());
        }
        return null;
    }
}
