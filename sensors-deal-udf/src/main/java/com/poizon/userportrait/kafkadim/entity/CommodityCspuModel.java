package com.poizon.userportrait.kafkadim.entity;

import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommodityCspuModel {

    //    @JsonProperty("id")
//    private Long id;
    @JsonProperty("id")
    private Long cspuId;
    @JsonProperty("spu_pid")
    private Long spuPid;
    @JsonProperty("spu_id")
    private Long spuId;
    @JsonProperty("property_value_id")
    private Long propertyValueId;
    @JsonProperty("sku_ids")
    private String skuIds;
    @JsonProperty("level1_category_id")
    private Long level1CategoryId;
    @JsonProperty("level2_category_id")
    private Long level2CategoryId;
    @JsonProperty("brand_id")
    private Long brandId;
    @JsonProperty("relation_brand_ids")
    private String relationBrandIds;
    @JsonProperty("category_id")
    private Long categoryId;
    @JsonProperty("fit_id")
    private Long fitId;
    @JsonProperty("title")
    private String title;
    @JsonProperty("series_ids")
    private String seriesIds;
    @JsonProperty("min_sale_price")
    private Long minSalePrice;
    @JsonProperty("img_url")
    private String imgUrl;
    @JsonProperty("color")
    private String color;
    @JsonProperty("base_color")
    private String baseColor;
    @JsonProperty("luxuryLevel")
    private Long luxuryLevel;
    @JsonProperty("has_price")
    private Long hasPrice;
    @JsonProperty("sold_num")
    private Long soldNum;
    @JsonProperty("is_show")
    private Long isShow;
    @JsonProperty("article_number")
    private String articleNumber;
    @JsonProperty("item_type")
    private String item_type = "product";
    @JsonProperty("is_new")
    private Long isNew;
    @JsonProperty("first_sale_time")
    private String firstSaleTime;
    @JsonProperty("update_time")
    private String updateTime;
    @JsonProperty("description")
    private String description;

    public CommodityCspuModel() {
    }

    public CommodityCspuModel(Long cspuId, Long spuId, Long propertyValueId) {
        this.cspuId = cspuId;
        this.spuId = spuId;
        this.propertyValueId = propertyValueId;
    }
}
