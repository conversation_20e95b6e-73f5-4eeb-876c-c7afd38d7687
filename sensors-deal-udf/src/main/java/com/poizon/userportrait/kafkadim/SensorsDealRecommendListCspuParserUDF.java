package com.poizon.userportrait.kafkadim;

import com.poizon.userportrait.kafkadim.cache.CacheProvider;
import com.poizon.userportrait.kafkadim.entity.RecommendContentInfoPortrait;
import com.poizon.utils.JsonUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Deprecated
@FunctionHint(input = {@DataTypeHint("STRING")}, output = @DataTypeHint("STRING"))
public class SensorsDealRecommendListCspuParserUDF extends ScalarFunction {
    static {
//        Properties properties = PropertyLoader.getPropertiesFromClasspath("dewu-flink-udf-application.properties");
//        System.setProperty("env", properties.getProperty("app.apollo.env", "pro"));
//        // 初始化Apollo配置
//        Apollo.init(properties.getProperty("app.apollo.cluster"), properties.getProperty("app.apollo.namespace"), properties.getProperty("apollo.recsys.db.parameters"));
    }
    private CacheProvider cacheProvider;

    @Override
    public void open(FunctionContext ignore) {
        cacheProvider = CacheProvider.getInstance();
//        第一次加载是，加载所有的cspuid
        cacheProvider.loadAllCspu();
    }

    public String eval(String recommendInfoList) {

        try {
            List<RecommendContentInfoPortrait> recommendContentInfoList = JsonUtil.readValue(recommendInfoList, JsonUtil.getCollectionType(List.class, RecommendContentInfoPortrait.class));
            Map<String, String> spuMap = new HashMap<>(16);
            if (recommendContentInfoList != null && !recommendContentInfoList.isEmpty()) {
                for (int i=0; i<recommendContentInfoList.size(); i++) {
                    RecommendContentInfoPortrait recommendContentInfo = recommendContentInfoList.get(i);
                    if ("0".equals(recommendContentInfo.getContentType()) && !"".equals(recommendContentInfo.getContentID()) && !"".equals(recommendContentInfo.getPropertyValueId())) {
                        String spuAndPropertyValueKey = recommendContentInfo.getContentID()+"_"+recommendContentInfo.getPropertyValueId();
                        spuMap.put(spuAndPropertyValueKey, recommendContentInfo.getPropertyValueId());
                    }
                }
            }
            Map<String, String> cspuBySpuPid = cacheProvider.getCspuBySpuPid(spuMap);
            for (RecommendContentInfoPortrait recommendContentInfo : recommendContentInfoList) {
                String key = recommendContentInfo.getContentID()+"_"+recommendContentInfo.getPropertyValueId();
                recommendContentInfo.setCspuId(cspuBySpuPid.get(key));
            }
            return JsonUtil.writeValueAsString(recommendContentInfoList);

        } catch (Throwable e) {
            log.error("input:{}, msg:{}", recommendInfoList, e.getStackTrace());
        }
        return null;
    }
}
