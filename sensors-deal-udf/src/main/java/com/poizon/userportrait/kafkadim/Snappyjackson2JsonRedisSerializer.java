package com.poizon.userportrait.kafkadim;

import org.apache.commons.lang3.SerializationException;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JavaType;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.type.TypeFactory;
import org.xerial.snappy.Snappy;

import java.io.Serializable;

public class Snappyjackson2JsonRedisSerializer<T> implements Serializable {
    private final JavaType javaType;

    static final byte[] EMPTY_ARRAY = new byte[0];


    private ObjectMapper objectMapper = new ObjectMapper();


    public Snappyjackson2JsonRedisSerializer(Class<T> type) {
        this.javaType = getJavaType(type);
    }


    public Snappyjackson2JsonRedisSerializer(JavaType javaType) {
        this.javaType = javaType;
    }

    public T deserialize(byte[] bytes) throws SerializationException {

        if (bytes == null || bytes.length == 0) {
            return null;
        }
        try {
            // 通过Snappy 解压缩
            bytes = Snappy.uncompress(bytes);
            return (T) this.objectMapper.readValue(bytes, 0, bytes.length, javaType);
        } catch (Exception ex) {
            throw new SerializationException("Could not read JSON: " + ex.getMessage(), ex);
        }
    }

    public byte[] serialize(Object t) throws SerializationException {
        if (t == null) {
            return EMPTY_ARRAY;
        }
        try {
            //先转成json字符串
            String value = this.objectMapper.writeValueAsString(t);
            //然后通过Snappy压缩
            return StringUtils.isNotBlank(value) ? Snappy.compress(value) : null;
        } catch (Exception ex) {
            throw new SerializationException("Could not write JSON: " + ex.getMessage(), ex);
        }
    }


    public void setObjectMapper(ObjectMapper objectMapper) {
        if (objectMapper == null) {
            throw new SecurityException("'objectMapper' must not be null");
        }
        this.objectMapper = objectMapper;
    }


    protected JavaType getJavaType() {
        return getJavaType();
    }


    protected JavaType getJavaType(Class<?> clazz) {
        return TypeFactory.defaultInstance().constructType(clazz);
    }
}
