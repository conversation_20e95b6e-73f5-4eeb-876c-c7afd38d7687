package com.poizon.userportrait.kafkadim;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.Closeable;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * @Description HBASE查询
 * <AUTHOR>
 * @Date 2022/7/5 下午5:32
 **/
@Slf4j
public class HbaseUtil implements Closeable {
    private final static String HBASE_ZK_ADDR = "ld-bp1aq02r0wmr187yj-proxy-hbaseue.hbaseue.rds.aliyuncs.com:30020";
    private final static String HBASE_USERNAME = "root";
    private final static String HBASE_PASSWORD = "root";
    private final static String HBASE_OFFLINE_TABLE_NAME = "commodity_cspu";
    private final static String HBASE_OFFLINE_TABLE_FAMILY = "cf";

    private Connection connection;

    public HbaseUtil() throws IOException {
        Configuration config = HBaseConfiguration.create();
        config.set(HConstants.ZOOKEEPER_QUORUM, HBASE_ZK_ADDR);
        config.set(HConstants.USERNAME, HBASE_USERNAME);
        config.set(HConstants.PASSWORD, HBASE_PASSWORD);

        connection = ConnectionFactory.createConnection(config);
    }

    @Override
    public void close() throws IOException {
        if(connection != null && !connection.isClosed()) {
            connection.close();
        }
    }

    /**
     * 批量查询HBASE
     * @param ids 批量查询的rowkey
     * @param columns 列名
     * @return
     * @throws Exception
     */
    public Map<String, Map<String, Object>> batchLoadCommodityInfo(List<String> ids, List<String> columns) throws Exception {

        byte[] familyName = Bytes.toBytes(HBASE_OFFLINE_TABLE_FAMILY);
        Table table = null;
        // 查询结果
        Map<String, Map<String, Object>> resultMap = new HashMap<>(ids.size());
        try {
            table = connection.getTable(TableName.valueOf(HBASE_OFFLINE_TABLE_NAME));
            List<Get> getList = new ArrayList<>();
            for (String id : ids) {
                byte[] rowKey = Bytes.toBytes(id);
                Get get = new Get(rowKey);
                // 要查询的列
                for (String column : columns) {
                    get.addColumn(familyName, Bytes.toBytes(column));
                }
                getList.add(get);
            }
            // 批量获取结果
            Result[] results = table.get(getList);
            for (int i = 0; i < results.length; i++) {
                Result result = results[i];
                // 返回的就是id
                String rowKey = Bytes.toString(result.getRow());
                // 获取rowKey查询后对应列的值
                Map<String, Object> valueMap = new HashMap<>(columns.size());
                for (String column : columns) {
                    String value = Bytes.toString(result.getValue(familyName, Bytes.toBytes(column)));
                    valueMap.put(column, value);
                }
                resultMap.put(rowKey, valueMap);
            }
            return resultMap;
        } catch (Exception e) {
            log.error("查询HBASE异常......, ids = {}, e = {}", ids, e.getMessage());
            return null;
        } finally {
            if (table != null) {
                try {
                    table.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
