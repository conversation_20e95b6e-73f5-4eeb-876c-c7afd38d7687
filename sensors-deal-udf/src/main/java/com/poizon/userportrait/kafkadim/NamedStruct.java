package com.poizon.userportrait.kafkadim;

import com.poizon.utils.JsonUtil;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/7/23
 * (name1, val1, name2, val2, ...)
 * Creates a struct with the given field names and values")
 */
public class NamedStruct extends ScalarFunction {
    public String eval(Object... arguments) {
        int numFields = arguments.length;
        if (numFields % 2 == 1) {
            throw new IllegalArgumentException("NamedStruct expects an even number of arguments.");
        }
        Map<String, Object> map = new HashMap<>(16);
        ArrayList<String> fname = new ArrayList<String>(numFields / 2);
        for (int f = 0; f < numFields; f += 2) {
            fname.add(arguments[f].toString());
        }

        for (int i = 0; i < arguments.length / 2; i++) {
            map.put(fname.get(i), arguments[2 * i + 1]);
        }
        return JsonUtil.writeValueAsString(map);
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING()))
                .build();
    }
}
