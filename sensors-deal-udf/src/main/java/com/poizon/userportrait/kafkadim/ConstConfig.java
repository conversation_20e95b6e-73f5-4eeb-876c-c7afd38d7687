package com.poizon.userportrait.kafkadim;

/**
 * @Author: wpp
 * @Date: 2021/7/1 21:07
 */
public class ConstConfig {
//    public static String devRedisHost ="r-uf6w4jeitf13wkzgj8.redis.rds.aliyuncs.com";
//    public static String devRedisPassWord ="_3lM)sue)ijdMW!";

    public static String devRedisHost ="r-uf6tutnxnqfid1y71j.redis.rds.aliyuncs.com";
    public static String devRedisPassWord ="Im@0T5wXYS#@uSb3";

    public static String getDimRedisHost() {
        if(JavaConfUtil.getIsProd()){
            return "r-bp1ef2ea217e5db4.redis.rds.aliyuncs.com";
        }else{
            return devRedisHost;
        }
    }

    public static String getDimRedisPassWord() {
        if(JavaConfUtil.getIsProd()){
            return "FUJy5ZLmUeC8Fyj7";
        }else{
            return devRedisPassWord;
        }
    }

    public static Integer getDimRedisDb() {
        if(JavaConfUtil.getIsProd()){
            return 3;
        }else{
            return 10;
        }
    }


    /**
     *
     * @Fields SPU_PID_CSPUID_MAP : SPU_PID 映射 CSPUID
     */
    public static final String SPU_PID_CSPUID_MAP = "recsys:spu:pid:cspu:map:";
}
