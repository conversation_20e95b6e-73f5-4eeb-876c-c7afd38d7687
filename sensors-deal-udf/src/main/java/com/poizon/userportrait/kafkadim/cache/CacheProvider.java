package com.poizon.userportrait.kafkadim.cache;

import com.poizon.userportrait.kafkadim.RedisUtil;
import com.poizon.userportrait.kafkadim.Snappyjackson2JsonRedisSerializer;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import static com.poizon.userportrait.kafkadim.ConstConfig.SPU_PID_CSPUID_MAP;
import static com.poizon.userportrait.kafkadim.ConstConfig.getDimRedisDb;

/**
 * <AUTHOR>
 * @date 2021/3/18
 */
@Slf4j
public class CacheProvider {
    private DealMemoryPool dealMemoryPool = DealMemoryPool.getInstance();
    private static CacheProvider cacheProvider;
    private CommodityCspuService commodityCspuService = CommodityCspuService.getInstance();

    Snappyjackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Snappyjackson2JsonRedisSerializer(Object.class);

    public static CacheProvider getInstance() {
        if (cacheProvider == null) {
            synchronized (CacheProvider.class) {
                if (cacheProvider == null) {
                    cacheProvider = new CacheProvider();
                }
            }
        }
        return cacheProvider;
    }

    /**
     * 加载所有spu和cspu的映射关系
     */
    public void loadAllCspu() {
        dealMemoryPool.setSpuCspuMappingPool(commodityCspuService.loadAllCspu());
        log.info("初始化 cspu 映射池，大小: {}", dealMemoryPool.getSpuCspuMappingPool().size());
    }

    /**
     * 查询spu+pid对应的cspuId
     * @param spuMap
     * @return
     */
//    public Map<String, String> getCspuBySpuPid(Map<String, String> spuMap) {
//        Map<String, String> returnCspuMapping = new HashMap<>(16);
//        Map<String, String> queryDBSpuMap = new HashMap<>(16);
//
//        for (Map.Entry<String, String> entry : spuMap.entrySet()) {
//            String spuKey = entry.getKey()+"_"+entry.getValue();
//            String cspuId = dealMemoryPool.getCspuBySpu(spuKey);
//            if (cspuId == null) {
//                //查询未命中cache，可能是异常的spu和pid组合或者没有值，则需要去查db
//                String invalidCspu= dealMemoryPool.getInvalidCspuBySpu(spuKey);
//                if (invalidCspu == null) {
//                    //查询数据库
//                    queryDBSpuMap.put(entry.getKey(), entry.getValue());
//                } else {
//                    //错误的spu+pid组合， 不需返回
//                    System.out.println("spuKey:{} is invalid pair."+spuKey);
//                }
//            } else {
//                returnCspuMapping.put(spuKey, cspuId);
//            }
//        }
//        if(!queryDBSpuMap.isEmpty()) {
//            Map<String, String> map = commodityCspuService.selectOnesBySpuAndPropertyValueId(queryDBSpuMap);
//            for (Map.Entry<String, String> entry : map.entrySet()) {
//                if (entry.getValue() == null) {
//                    //不存在pair更新到未命中cache中,不做异常存储
//                    dealMemoryPool.updateInvalidSpuCspuMappingPool(entry.getKey(), "-1");
//                } else {
//                    //更新内存cache
//                    dealMemoryPool.updateSpuCspuMappingPool(entry.getKey(), entry.getValue());
////                    wpp 添加命中的key也返回到数据库
//                    returnCspuMapping.put(entry.getKey(), entry.getValue());
//                }
//
//            }
//
//        }
//        return returnCspuMapping;
//    }

    public Map<String, String> getCspuBySpuPid(Map<String, String> spuMap) {
        Map<String, String> returnCspuMapping = new HashMap<>(16);

        for (Map.Entry<String, String> entry : spuMap.entrySet()) {
            String spuKey = entry.getKey();
            String cspuId = dealMemoryPool.getCspuBySpu(spuKey);

            if (cspuId == null) {
                String redisCspuId = null;
                try{
                    byte[] redisCspuIdByte = RedisUtil.getInstance().hash().hget(SPU_PID_CSPUID_MAP.getBytes(), spuKey.getBytes(), getDimRedisDb());
                    redisCspuId = jackson2JsonRedisSerializer.deserialize(redisCspuIdByte) == null? null:String.valueOf(jackson2JsonRedisSerializer.deserialize(redisCspuIdByte));
                }catch (Exception ex){
                    System.out.println("redis里没有相应的cspuid："+spuKey);
                }
                if (redisCspuId == null) {
                    System.out.println(spuKey+"为空");
                } else {
                    dealMemoryPool.updateSpuCspuMappingPool(spuKey, redisCspuId.trim());
                    returnCspuMapping.put(spuKey, redisCspuId.trim());
                }
            } else {
                returnCspuMapping.put(spuKey, cspuId);
            }
        }

        return returnCspuMapping;
    }
}
