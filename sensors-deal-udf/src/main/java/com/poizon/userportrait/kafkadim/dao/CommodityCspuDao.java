package com.poizon.userportrait.kafkadim.dao;

import com.alibaba.fastjson.JSON;
import com.du.blink.common.factory.ConnectionPool;
import com.poizon.userportrait.kafkadim.JavaConfUtil;
import com.poizon.userportrait.kafkadim.entity.CommodityCspuModel;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
//import com.google.byteair.common.base.Throwables;

/**
 * <AUTHOR>
 * @date 2021/3/19
 */
@Slf4j
public class CommodityCspuDao {
    private static volatile CommodityCspuDao commodityCspuDao;
    private static final String DATA_BASE = "algo_search";
    public static Map<String, String> hashMap1 = new HashMap<>();

    static {
        InputStream fileInputStream =  CommodityCspuDao.class.getClassLoader().getResourceAsStream("config/config-prod.properties");
        if(!JavaConfUtil.getIsProd()) {
            fileInputStream = CommodityCspuDao.class.getClassLoader().getResourceAsStream("config/config-dev.properties");
        }
        Properties properties = new Properties();
        try {
            properties.load(fileInputStream);
        }catch (IOException e){
            System.out.println("读取配置文件异常" + e.getMessage());
        }
//        Map<String, String> hashMap1 = new HashMap<>();
        hashMap1.put("db.alias","algo_search");
        hashMap1.put("algo_search.mysql.driverClassName",properties.getProperty("jdbc.driverClassName"));
        hashMap1.put("algo_search.mysql.url",properties.getProperty("jdbc.url"));
        hashMap1.put("algo_search.mysql.username",properties.getProperty("jdbc.username"));
        hashMap1.put("algo_search.mysql.password",properties.getProperty("jdbc.password"));
        hashMap1.put("algo_search.maxActive","20");
        System.out.println(JSON.toJSON(hashMap1));
        ConnectionPool.updateDataSourceProperties(hashMap1);
    }

    public static CommodityCspuDao getInstance() {
        if (commodityCspuDao == null) {
            synchronized (CommodityCspuDao.class) {
                if (commodityCspuDao == null) {
                    commodityCspuDao = new CommodityCspuDao();
                }
            }
        }
        return commodityCspuDao;
    }


    /**
     * 获取数据库链接
     *
     * @return
     */
    private Connection getConnection() {
//        ConnectionPool.updateDataSourceProperties(hashMap1);
        return ConnectionPool.getConnection(DATA_BASE);
//        return DBConnectionPool.getInstance().getConnection();
    }

    /**
     * 加载所有cspu id
     * @return
     */
    public Map<String, String> loadAllCspu() {
        long currentTimeMillis = System.currentTimeMillis();
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet rstSet = null;
        String sql = "";
        Map<String, String> allCspuMapping = new HashMap<>(300000);
        try {
            connection = getConnection();
            //TODO 切换新的redis
            sql = "select id, spu_id, property_value_id from commodity_cspu";
            statement = connection.prepareStatement(sql);
            rstSet = statement.executeQuery();
            while (rstSet.next()) {
                CommodityCspuModel commodityCspuModel = new CommodityCspuModel(rstSet.getLong("id"), rstSet.getLong("spu_id"), rstSet.getLong("property_value_id"));
                String key = commodityCspuModel.getSpuId()+"_"+commodityCspuModel.getPropertyValueId();
                allCspuMapping.put(key, commodityCspuModel.getCspuId().toString());
            }
            System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(System.currentTimeMillis())+"加载全部画像的时间：" +(System.currentTimeMillis()-currentTimeMillis) +",画像数据大小:"+allCspuMapping.size() );
        } catch (Throwable e) {
            log.error("[LoadAllCspu] 执行SQL:\n{}\n出现异常: {}",  sql, e.getMessage());
            return null;
        } finally {
            ConnectionPool.close(connection, statement, rstSet);
        }
        return allCspuMapping;
    }

    /**
     * 批量查询cspuid，因为一个曝光会有列表格式数据
     * @return
     */
    public Map<String, String> selectOnesBySpuAndPropertyValueId(Map<String, String> spuMap) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet rstSet = null;
        String sql = "";
        Map<String, String> cspuMap = new HashMap<>();
        try {
            connection = getConnection();
            //TODO
            sql = "select id, spu_id, property_value_id from commodity_cspu where spu_id=? and property_value_id=?";
            statement = connection.prepareStatement(sql);
            for (Map.Entry<String, String> entry : spuMap.entrySet()) {
                statement.setString(1,entry.getKey());
                statement.setString(2,entry.getValue());
                rstSet = statement.executeQuery();
                while (rstSet.next()) {
                    CommodityCspuModel commodityCspuModel = new CommodityCspuModel(rstSet.getLong("id"), rstSet.getLong("spu_id"), rstSet.getLong("property_value_id"));
                    String key = commodityCspuModel.getSpuId()+"_"+commodityCspuModel.getPropertyValueId();
                    cspuMap.put(key, commodityCspuModel.getCspuId().toString());
                }
            }
        } catch (Throwable e) {
            log.error("[LoadAllCspu] 执行SQL:\n{}\n出现异常: {}",  sql, e.getMessage());
            return null;
        } finally {
            ConnectionPool.close(connection, statement, rstSet);
        }
        return cspuMap;
    }


}
