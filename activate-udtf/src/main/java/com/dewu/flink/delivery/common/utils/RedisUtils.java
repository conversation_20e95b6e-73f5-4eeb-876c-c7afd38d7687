package com.dewu.flink.delivery.common.utils;

import com.dewu.flink.delivery.common.bean.RetainResult;
import com.dewu.flink.delivery.common.bean.SearchClickResult;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.consts.RedisConsts;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.List;
import java.util.Map;


public class RedisUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisUtils.class);


    /**
     * 用户行为topic处理进行3秒内消息去重逻辑
     * 功能描述 当uuid+eventtype(TypeActivate,TypeRegister,TypeOrder) 在3秒钟内出现重复数据时，只处理一条。插入成功返回true,否则再重试2次插入，元素仍存在则返回false
     * 元素插入成功返回true,元素已存在则返回false（表示最近3秒内后续重复消息不处理返回false）
     * <AUTHOR>
     * @date 2022/2/17
     */
    public static boolean lock(JedisSington jedisSington, String redisKey)  {
        for (int i = 0; i < 1; i++) {
            //替代go redis的rds.client.SetNX(redisKey, "lock", 3*time.Second)
            Jedis jedis = null;
            try {
                jedis = jedisSington.getJedis();
                String boolCmd = jedis.set(redisKey, "lock", "nx", "ex", 3);
                if (boolCmd != null) {
                    return true;
                }
//            Thread.sleep(10);
            } catch (Exception e) {
                throw e;
            } finally {
                jedisSington.returnResource(jedis);
            } }
        return false;
    }


    /**
     * 功能描述  存储激活数据，设置ttl
     * <AUTHOR>
     * @date 2022/2/17
     * @param
     * @return
     */
    public static void mSetRetainData(JedisSington jedisSington, String[] keys, Map<String, String> data, Integer ex) {
        String recall = data.get(Consts.Recall);
        String prefix = RedisConsts.getRetainKey(recall);
        for (String k : keys) {
            if (StringUtils.isNotBlank(k)) {
                Jedis jedis = null;
                try {
                    jedis = jedisSington.getJedis();
                    jedis.hmset(prefix + k, data);
                    jedis.expire(prefix + k, ex);
                } catch (Exception e) {
                    throw e;
                } finally {
                    jedisSington.returnResource(jedis);
                }
            }
        }
    }

    /**
     *功能描述
     * hset 集合，1级key keys 2级key eventType 对应value设置为1
     * 二级key TypeRegister,TypeOrder,TypeRetain,TypeRetain7
     * <AUTHOR>
     * @date 2022/2/17
     * @param
     * @return
     */
    public static void setRetainData(JedisSington jedisSington, String[] keys, String eventType, String recall) {
        String prefix = RedisConsts.getRetainKey(recall);
        for (int i = 0; i < keys.length; i++) {
            String k = keys[i];
            if (StringUtils.isBlank(k)) {
                continue;
            }

            Jedis jedis = null;
            try {
                jedis = jedisSington.getJedis();
                jedis.hset(prefix + k, eventType, 1 + "");
            } catch (Exception e) {
                //go项目redis连接异常了，不会上抛数据，仍进行后续逻辑处理
//                logs.Error(err, "[redis]设置回传标志失败, %s", eventType)
                throw e;
            } finally {
                jedisSington.returnResource(jedis);
            }
        }
    }

    /**
     * 功能描述
     * keys 设备唯一ID
     * recall data.get(Consts.Recall) ( 点击消息类型 1：召回投放 2：拉新&180天沉默用户混投), "", Consts.TypeRecallDefault= "1"
     *
     * 二级key包含 TypeRegister,TypeOrder,TypeRetain,TypeRetain7，TypeActivate
     * <AUTHOR>
     * @date 2022/2/17
     * @param
     * @return
     */
    public static RetainResult getRetainData(JedisSington jedisSington, String[] keys, String recall,
                                             long exDebug,String retainKey) {
        RetainResult retainResult=new RetainResult();
        String prefix = RedisConsts.getRetainKey(recall);

        for (int i = 0; i < keys.length; i++) {
            String k = keys[i];
            if (StringUtils.isBlank(k)) {
                continue;
            }

            Jedis jedis = null;
            try {
                jedis = jedisSington.getJedis();
                Map<String, String> mdata = jedis.hgetAll(prefix + k);
                if (mdata.size() > 0) {
                    //和投放侧ttl对齐
                    if(exDebug>0) {
                        if((prefix + k).equals(retainKey))
                        {
                            jedis.expire(prefix + k, (int) exDebug);
                        }
                    }
                    Long ttl = jedis.ttl(prefix + k);
                    if(RedisConsts.RedisRetainRecall.equals(prefix)) {
                        retainResult.setTtlFlag("存储2天"+prefix+ k);
                    }else {
                        retainResult.setTtlFlag("存储8天"+prefix+ k);
                    }
                    retainResult.setData(mdata);
                    retainResult.setRetainKey(prefix+ k);
                    retainResult.setTtl(ttl);
                    return retainResult;
                }
            } catch (Exception e) {
                //go项目redis连接异常了，不会上抛数据，仍进行后续逻辑处理
                throw e;
            } finally {
                jedisSington.returnResource(jedis);
            }
        }
        return retainResult;
    }


    public static RetainResult getDeliveryRetainData(JedisSington jedisSington, String[] keys, String recall) {
        RetainResult retainResult=new RetainResult();
        String prefix = RedisConsts.getDeliveryRetainKey(recall);

        for (int i = 0; i < keys.length; i++) {
            String k = keys[i];
            if (StringUtils.isBlank(k)) {
                continue;
            }

            Jedis jedis = null;
            try {
                jedis = jedisSington.getJedis();
                Map<String, String> mdata = jedis.hgetAll(prefix + k);
                if (mdata.size() > 0) {
                    Long ttl = jedis.ttl(prefix + k);
                    retainResult.setData(mdata);
                    retainResult.setTtl(ttl);
                    retainResult.setRetainKey(prefix + k);
                    return retainResult;
                }
            } catch (Exception e) {
                //go项目redis连接异常了，不会上抛数据，仍进行后续逻辑处理
//                logs.Error(err, "[redis]失败,)
                throw e;
            } finally {
                jedisSington.returnResource(jedis);
            }
        }
        return retainResult;
    }


    /**
     *功能描述 注意，后期对接要切换投放库，双跑阶段不能使用投放库，否则事故
     * <AUTHOR>
     * @date 2022/4/22
     * @param
     * @return
     */
    public static void hsetDeliveryClickUsed(JedisSington clickJedis, String key) {
        Jedis jedis = null;
        try {
            jedis = clickJedis.getJedis();
            jedis.hset(key,  Consts.Used, "1");
        } catch (Exception e) {
            throw e;
        } finally {
            clickJedis.returnResource(jedis);
        }
    }

    public static void hsetDeliveryClickUsedDebug(JedisSington jedisSington, String key,long exDebug) {
        Jedis jedis = null;
        try {
            jedis = jedisSington.getJedis();
            jedis.hset(key,  Consts.Used, "1");
            if(exDebug>0)
            {
                jedis.expire(key, (int) exDebug);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            jedisSington.returnResource(jedis);
        }
    }

    /**
     *功能描述 注意，使用自己库，双跑阶段不能使用投放库，否则事故，且上线要下掉该功能
     * <AUTHOR>
     * @date 2022/4/22
     * @param
     * @return
     */
    public static void setClickData(JedisSington jedisSington, List<String> keys, Map<String, String> data, String prefix, Integer ex) {
        Jedis jedis = null;
        try {
            jedis = jedisSington.getJedis();
            for (String k : keys) {
                jedis.hmset(prefix + k, data);
                jedis.expire(prefix + k, ex);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            jedisSington.returnResource(jedis);
        }
    }


    public static String hget(JedisSington jedisSington,String key,String field){
        Jedis jedis = null;
        try{
            jedis = jedisSington.getJedis();
            String value = jedis.hget(key,field);
            return value;
        }catch (Exception e){
            throw e;
        }finally {
            jedisSington.returnResource(jedis);
        }
    }


    public static String get(JedisSington jedisSington, final String key) {
        Jedis jedis = null;
        try {
            jedis = jedisSington.getJedis();
            String orderInfo = jedis.get(key);
            return orderInfo;
        } catch (Exception e) {
            throw e;
        } finally {
            jedisSington.returnResource(jedis);
        }
    }

    public static String set(JedisSington jedisSington, final String key, final String value, final String nxxx, final String expx,
                             final int time) {
        Jedis jedis = null;
        try {
            jedis = jedisSington.getJedis();
            String set = jedis.set(key, value, nxxx, expx, time);
            return set;
        } catch (Exception e) {
            throw e;
        } finally {
            jedisSington.returnResource(jedis);
        }
    }


    public static SearchClickResult getClickData(JedisSington jedisSington, List<String> keys, String prefix, long sleep, int count) throws Exception {
        Jedis jedis = null;
        SearchClickResult searchClickResult = new SearchClickResult();
        try {
            jedis = jedisSington.getJedis();
            for (String k : keys) {
                if(StringUtils.isBlank(k))
                {
                    continue;
                }
                for (int i = 0; i < count; i++) {
                    Map<String, String> data = jedis.hgetAll(prefix + k);
                    if (data.size() != 0) {
                        Long ttl = jedis.ttl(prefix + k);
                        searchClickResult.setData(data);
                        searchClickResult.setTtl(ttl);
                        searchClickResult.setClickKey(prefix + k);
                        return searchClickResult;
                    }
                    if(sleep!=0) {
                        Thread.sleep(sleep);
                    }
                }
            }
            return searchClickResult;
        } catch (Exception e) {
            throw e;
        } finally {
            jedisSington.returnResource(jedis);
        }
    }

    public static SearchClickResult getClickDataDebug(JedisSington jedisSington, String keys) throws Exception {
        Jedis jedis = null;
        SearchClickResult searchClickResult = new SearchClickResult();
        try {
            jedis = jedisSington.getJedis();
            Map<String, String> data = jedis.hgetAll(keys);
            if (data.size() != 0) {
                Long ttl = jedis.ttl(keys);
                searchClickResult.setData(data);
                searchClickResult.setTtl(ttl);
                searchClickResult.setClickKey(keys);
                return searchClickResult;
            }
            return searchClickResult;
        } catch (Exception e) {
            throw e;
        } finally {
            jedisSington.returnResource(jedis);
        }
    }

}