package com.dewu.flink.delivery.common.api;

import com.dewu.flink.delivery.common.bean.QueryReq;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.service.AttributeServiceBackUp;
import com.dewu.flink.delivery.common.utils.LongUtils;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


public class Activate {

    private AttributeServiceBackUp attributeService;

    private Gson gson = new Gson();

    private static volatile Activate singleton;

    public static Activate getInstance() throws IOException {
        if (singleton == null) {
            synchronized (Activate.class) {
                if (singleton == null) {
                    singleton = new Activate();
                }
            }
        }
        return singleton;
    }

    public static Activate getInstance(String clickIp, int clickPort, String clickPassword, Integer clickDB,
                                       String tagIp, int tagPort, String tagPassword, Integer tagDB,
                                       String retainIp, int retainPort, String retainPassword, Integer retainDB) throws IOException {
        if (singleton == null) {
            synchronized (Activate.class) {
                if (singleton == null) {
                    singleton = new Activate(clickIp,clickPort,clickPassword,clickDB,
                            tagIp,tagPort,tagPassword,tagDB,
                            retainIp,retainPort,retainPassword,retainDB );
                }
            }
        }
        return singleton;
    }


//    public static Activate getHbaseInstance(String hbaseUsername,String hbasePassword,String hbaseZookeeperQuorum,
//                                            String tagIp, int tagPort, String tagPassword, Integer tagDB) throws IOException {
//        if (singleton == null) {
//            synchronized (Activate.class) {
//                if (singleton == null) {
//                    singleton = new Activate(hbaseUsername,hbasePassword,hbaseZookeeperQuorum,
//                            tagIp,tagPort,tagPassword,tagDB);
//                }
//            }
//        }
//        return singleton;
//    }

    public Activate() throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().build();
    }

//    public Activate(String hbaseUsername,String hbasePassword,String hbaseZookeeperQuorum,
//                    String tagIp, int tagPort, String tagPassword, Integer tagDB) throws IOException {
//        this.attributeService = new AttributeServiceBackUp.Builder()
//                .hbaseUsername(hbaseUsername)
//                .hbasePassword(hbasePassword)
//                .hbaseZookeeperQuorum(hbaseZookeeperQuorum)
//                .tagIp(tagIp).tagPort(tagPort).tagPassword(tagPassword).tagDB(tagDB).
//                build();
//    }


    public Activate(String clickIp, int clickPort, String clickPassword, Integer clickDB,
                    String tagIp, int tagPort, String tagPassword, Integer tagDB,
                    String retainIp, int retainPort, String retainPassword, Integer retainDB) throws IOException {
        this.attributeService = new AttributeServiceBackUp.Builder().
                clickIp(clickIp).clickPort(clickPort).clickPassword(clickPassword).clickDB(clickDB)
                .tagIp(tagIp).tagPort(tagPort).tagPassword(tagPassword).tagDB(tagDB)
                .retainIp(retainIp).retainPort(retainPort).retainPassword(retainPassword).retainDB(retainDB).
                        build();
    }

    public List<SinkMessage> deal(String message,boolean delay) throws Exception {
//        HashMap<String, String> dataSource;
//        try {
//            dataSource = gson.fromJson(message, HashMap.class);
//        } catch (Exception e) {
//            throw e;
//        }
//
//        Iterator<Map.Entry<String, String>> it = dataSource.entrySet().iterator();
//        while (it.hasNext()) {
//            Map.Entry<String, String> entry = it.next();
//            String key = entry.getKey();
//            String v = entry.getValue();
//            if ("null".equals(v)) {
//                dataSource.put(key, "");
//            }
//        }
//
//        QueryReq param = new QueryReq();
//        param.setAppId(Consts.APP_DEWU);
//        param.setOs(dataSource.get("lib"));
//        param.setPkgChannel(dataSource.get("android_channel"));
//        param.setOaid(dataSource.get("oaid"));
//        param.setImei(dataSource.get("imei"));
//        param.setAndroidId(dataSource.get("android_id"));
//        param.setIdfa(dataSource.get("idfa"));
//        param.setUa(dataSource.get("user_agent"));
//        param.setIp(dataSource.get("ip"));
//        param.setIpvx(dataSource.get("ipvx"));
//        param.setUuid(dataSource.get("device_uuid"));
//        param.setFcuuid(dataSource.get("fcuuid"));
//        param.setSource(Consts.SourceActivate);
//
//
//        if ("iOS".equalsIgnoreCase(dataSource.get("lib")) && StringUtils.isBlank(param.getFcuuid())
//                && !dataSource.get("device_uuid").equals(dataSource.get("idfa"))) {
//            param.setFcuuid(dataSource.get("device_uuid"));
//        }
//
//        param.setUserId(LongUtils.valueOf(dataSource.get("dw_userid")));
//        param.setEventTime(LongUtils.valueOf(dataSource.get("receive_time")));
//
//        attributeService.formatParams(param);
        QueryReq param = formatParams(message);
        List<SinkMessage> sinkMessageList= attributeService.activate(param,message,delay);
        return sinkMessageList;
    }



    public QueryReq formatParams(String message)  {
        HashMap<String, String> dataSource;
        try {
            dataSource = gson.fromJson(message, HashMap.class);
        } catch (Exception e) {
            throw e;
        }

        Iterator<Map.Entry<String, String>> it = dataSource.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, String> entry = it.next();
            String key = entry.getKey();
            String v = entry.getValue();
            if ("null".equals(v)) {
                dataSource.put(key, "");
            }
        }

        QueryReq param = new QueryReq();
        param.setAppId(Consts.APP_DEWU);
        param.setOs(dataSource.get("lib"));
        param.setPkgChannel(dataSource.get("android_channel"));
        param.setOaid(dataSource.get("oaid"));
        param.setImei(dataSource.get("imei"));
        param.setAndroidId(dataSource.get("android_id"));
        param.setIdfa(dataSource.get("idfa"));
        param.setUa(dataSource.get("user_agent"));
        param.setIp(dataSource.get("ip"));
        param.setIpvx(dataSource.get("ipvx"));
        param.setUuid(dataSource.get("device_uuid"));
        param.setFcuuid(dataSource.get("fcuuid"));
        param.setSource(Consts.SourceActivate);

        if ("iOS".equalsIgnoreCase(dataSource.get("lib")) && StringUtils.isBlank(param.getFcuuid())
                && !dataSource.get("device_uuid").equals(dataSource.get("idfa"))) {
            param.setFcuuid(dataSource.get("device_uuid"));
        }

        param.setUserId(LongUtils.valueOf(dataSource.get("dw_userid")));
        param.setEventTime(LongUtils.valueOf(dataSource.get("receive_time")));

        attributeService.formatParams(param);
        return param;
    }

    public AttributeServiceBackUp getAttributeService() {
        return attributeService;
    }

    public void setAttributeService(AttributeServiceBackUp attributeService) {
        this.attributeService = attributeService;
    }
}