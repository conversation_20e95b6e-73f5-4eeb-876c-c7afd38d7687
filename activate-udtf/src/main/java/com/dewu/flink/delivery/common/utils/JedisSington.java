package com.dewu.flink.delivery.common.utils;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCommands;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

public class JedisSington {

    private JedisCommands jedisCommands;

    private JedisPool jedisPool;

    private  JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();

    private int timeout = 60000;

    public JedisSington(String ip, int port) {
        // 初始化jedis
        // 设置配置
        jedisPoolConfig.setMaxTotal(100);
        jedisPoolConfig.setMaxIdle(100);
        jedisPoolConfig.setMinIdle(10);//最小的闲置连接数
        jedisPoolConfig.setMaxWaitMillis(15000);
        jedisPoolConfig.setTestOnBorrow(false);
        jedisPoolConfig.setTestOnReturn(false);

        // 初始化JedisPool
        jedisPool = new JedisPool(jedisPoolConfig, ip, port, timeout);

    }

    public JedisSington(String ip, int port,String password,int DB) {
        // 初始化jedis
        // 设置配置
        jedisPoolConfig.setMaxTotal(50);
        jedisPoolConfig.setMaxIdle(50);
        jedisPoolConfig.setMinIdle(10);//最小的闲置连接数
        jedisPoolConfig.setMaxWaitMillis(5000);
        jedisPoolConfig.setTestOnBorrow(false);
        jedisPoolConfig.setTestOnReturn(false);
        jedisPoolConfig.setBlockWhenExhausted(true);
        // 初始化JedisPool
        jedisPool = new JedisPool(jedisPoolConfig, ip, port, timeout,password,DB);

    }


    public JedisPool getJedisPool() {
        return jedisPool;
    }

    public void setJedisPool(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }


    public Jedis getJedis() {

        return jedisPool.getResource();
    }

    public void returnResource(Jedis jedis) {
        if (jedis != null) {
            jedis.close();
        }
    }

}