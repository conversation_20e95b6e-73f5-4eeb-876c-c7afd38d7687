//package com.dewu.flink.delivery.sink;
//
//import com.dewu.flink.delivery.common.sink.RocketMQPorducerUtils;
//import org.apache.flink.types.Row;
//import org.apache.rocketmq.common.message.Message;
//import org.apache.rocketmq.flink.RocketMQConfig;
//import org.apache.rocketmq.remoting.common.RemotingHelper;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.UnsupportedEncodingException;
//import java.util.Properties;
//
//public class RocketMqDelaySink extends CustomSinkBase {
//
//    private static Logger LOG = LoggerFactory.getLogger(RocketMqDelaySink.class);
//
//    private String topic;
//    private String address;
//    private String instanceName;
//
//    @Override
//    public void open(int taskNumber, int numTasks)  {
//        topic = userParamsMap.get("topic");
//        address = userParamsMap.get("address");
//        instanceName = userParamsMap.get("instance_name");
//    }
//
//    @Override
//    public void close()  {
//
//    }
//
//    @Override
//    public void writeAddRecord(Row row) throws UnsupportedEncodingException {
//        Properties producerProps = new Properties();
//        producerProps.setProperty(RocketMQConfig.NAME_SERVER_ADDR, address);
//        producerProps.setProperty(RocketMQConfig.INSTANCE_NAME, instanceName);
//
//        Long startDeliverTime = Long.valueOf(row.getField(2).toString());
//        RocketMQPorducerUtils rocketMqSink = null;
//        try {
//            rocketMqSink = RocketMQPorducerUtils.getInstance(producerProps, startDeliverTime);
//        } catch (Exception e) {
//            throw new RuntimeException(" RocketMQPorducerUtils.getInstance error" , e);
//        }
//
//        String message = row.getField(0).toString();
//        String tag = row.getField(1).toString();
//        String keys = row.getField(3).toString();
//        Message msg = new Message(topic,
//                tag,
//                keys,
//                message.getBytes(RemotingHelper.DEFAULT_CHARSET));
//        try {
//            rocketMqSink.invoke(msg);
//        } catch (Exception e) {
//            throw new RuntimeException(" RocketMQ send error" , e);
//        }
//    }
//
//    @Override
//    public void writeDeleteRecord(Row row)  {
//    }
//
//    @Override
//    public void sync() {
//
//    }
//
//    @Override
//    public String getName() {
//        return "RocketMqDelaySink";
//    }
//
//}
//
