package com.dewu.flink.delivery.udf.v1;

import com.dewu.flink.delivery.common.api.UserAction;
import com.dewu.flink.delivery.common.bean.*;
import com.dewu.flink.delivery.common.utils.LongUtils;
import com.dewu.flink.delivery.common.utils.TimeUtils;
import com.google.gson.Gson;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

@Deprecated
public class SelectClickRedis extends ScalarFunction {

    private static final Logger logger = LoggerFactory.getLogger(SelectClickRedis.class);

    UserAction userAction;

    public void eval(String message, String redisIp, int redisPort, int db, String password) throws Exception {
        userAction = UserAction.getInstance(redisIp, redisPort, db, password);
        commonDeal(message);
    }

    public String eval(String message,
                       String clickIp, int clickPort, String clickPassword, int clickDB,
                       String tagIp, int tagPort, String tagPassword, int tagDB,
                       String retainIp, int retainPort, String retainPassword, int retainDB) throws Exception {
        userAction = UserAction.getInstance(clickIp, clickPort, clickPassword, clickDB,
                tagIp, tagPort, tagPassword, tagDB,
                retainIp, retainPort, retainPassword, retainDB);
        return commonDeal(message);
    }

    private String commonDeal(String message) throws Exception {
        try {
            Gson gson = new Gson();
            QueryReqBack param = gson.fromJson(message, QueryReqBack.class);

            QueryReq queryReq=new QueryReq();
            queryReq.setOs(param.getOs());
            queryReq.setIp(param.getIp());
            queryReq.setUa(param.getUa());
            queryReq.setIpua(param.getIpua());
            queryReq.setIdfa(param.getIdfa());
            queryReq.setAndroidId(param.getAndroid_id());
            queryReq.setImei(param.getImei());
            queryReq.setOaid(param.getOaid());
            queryReq.setFcuuid(param.getFcuuid());
            queryReq.setUuid(param.getUuid());

            QueryResult clickData = userAction.queryDebug(queryReq);
            if(clickData.getData()!=null)
            {
                String event_time = clickData.getData().get("event_time");
                String click_time = clickData.getData().get("click_time");
                String process_time = clickData.getData().get("process_time");
                Long etime = LongUtils.valueOf(event_time);
                String event_time_format = TimeUtils.getyyyyMMddHHmmss(etime);

                Long ckt = LongUtils.valueOf(click_time);
                String click_time_format = TimeUtils.getyyyyMMddHHmmss(ckt);

                Long prt = LongUtils.valueOf(process_time);
                String process_time_format = TimeUtils.getyyyyMMddHHmmss(prt);


                long timeStampSecond = TimeUtils.getTimeStampSecond(System.currentTimeMillis());
                long ttl = clickData.getTtl();
                long originttl = timeStampSecond - etime + ttl;


                String M="";
                long log_time = param.getLog_time();
                if(log_time<etime)
                {
                    M="查询时间早与点击时间";
                }else
                {
                    M="查询时间晚与点击时间";
                }
                long userEventTime = param.getEvent_time();
                System.out.println("uuid:"+param.getUuid()+
                        " 查询到点击数据了:"+ clickData+
                        " 【原始数据】："+message+
                        "点击流中点击的时间:"+click_time_format+
                        "点击流中处理的时间:"+process_time_format+
                        "点击流中的事件时间:"+event_time_format+

                        "查询点击时间:"+TimeUtils.getyyyyMMddHHmmss(log_time)+
                        "行为时间:"+TimeUtils.getyyyyMMddHHmmss(userEventTime)+

                        "--"+M+"---"+
                        "当前时间-点击时间+ttl得到时间："+originttl);
                return "sucess";
            }
            return "fail";
        } catch (Exception e) {
            e.printStackTrace();
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            e.printStackTrace(new PrintStream(stream));
            String exception = stream.toString();
            logger.error("message:{}", message, e);
            throw e;
        }
    }


}