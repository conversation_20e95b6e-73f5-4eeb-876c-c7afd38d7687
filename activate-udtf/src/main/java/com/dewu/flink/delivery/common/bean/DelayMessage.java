package com.dewu.flink.delivery.common.bean;

import java.util.Map;

public class DelayMessage {

    QueryReq active;
    Map<String, String> click;//点击数据

    Map<String, String> retainClick;//留存点击数据标志 debug
    long ttl;//点击数据ttl，用于设置留存点击数据ttl，对齐过期时间

    public DelayMessage() {
    }

    public DelayMessage(QueryReq active, Map<String, String> click) {
        this.active = active;
        this.click = click;
    }

    public DelayMessage(QueryReq active, Map<String, String> click, Map<String, String> retainClick) {
        this.active = active;
        this.click = click;
        this.retainClick = retainClick;
    }

    public DelayMessage(QueryReq active, Map<String, String> click, Map<String, String> retainClick,long ttl) {
        this.active = active;
        this.click = click;
        this.retainClick = retainClick;
        this.ttl=ttl;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }

    public Map<String, String> getRetainClick() {
        return retainClick;
    }

    public void setRetainClick(Map<String, String> retainClick) {
        this.retainClick = retainClick;
    }

    public QueryReq getActive() {
        return active;
    }

    public void setActive(QueryReq active) {
        this.active = active;
    }

    public Map<String, String> getClick() {
        return click;
    }

    public void setClick(Map<String, String> click) {
        this.click = click;
    }


}