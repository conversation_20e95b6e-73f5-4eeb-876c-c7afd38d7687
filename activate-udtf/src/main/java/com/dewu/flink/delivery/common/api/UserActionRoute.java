package com.dewu.flink.delivery.common.api;

import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.sink.KafkaPorducerUtilsTemp;
import com.dewu.flink.delivery.common.sink.RocketMQPorducerUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.flink.RocketMQConfig;
import org.apache.rocketmq.remoting.common.RemotingHelper;

import java.util.List;
import java.util.Properties;

@Deprecated
public class UserActionRoute {

    RocketMQPorducerUtils rocketMQSink;

    KafkaPorducerUtilsTemp kafkaPorducerUtilsTemp;

    public void init() throws Exception {
        Properties producerProps = new Properties();
        producerProps.setProperty(RocketMQConfig.NAME_SERVER_ADDR, "***********:9876");
        producerProps.setProperty(RocketMQConfig.INSTANCE_NAME, "RocketMQPorducerUtils");

        rocketMQSink = new RocketMQPorducerUtils(producerProps, 0L);

        kafkaPorducerUtilsTemp = new KafkaPorducerUtilsTemp(producerProps);

    }

    public void deal() throws Exception {
        Gson gson = new Gson();
        UserAction userAction = new UserAction("127.0.0.1", 6379);
        String deal = userAction.deal("{\"create_time\":\"2022-02-11 15:34:31\",\"ip\":\"2409:8915:7030:162e:c37f:216b:2515:3869\",\"modify_time\":\"2022-02-11 15:34:31\",\"channel\":\"guagndiantong244\",\"type\":0,\"uuid\":\"2822e221e5045fc9\",\"version\":\"4.85.6\",\"operator\":\"CMCC\",\"platform\":\"android\",\"user_id\":1822689631,\"is_del\":0,\"id\":10342385070,\"event\":\"activity_signin_is_sucessful_click\"}");

        List<SinkMessage> messages = gson.fromJson(deal, new TypeToken<List<SinkMessage>>() {
        }.getType());
        for (int i = 0; i < messages.size(); i++) {
            SinkMessage sinkMessage = messages.get(i);
            String type = sinkMessage.getType();
            if (Consts.KAFKA.equals(type)) {

                kafkaPorducerUtilsTemp.invoke(sinkMessage.getData());

            } else if (Consts.ROCKETMQ.equals(type)) {
                Message msg = new Message(sinkMessage.getDesc(),
                        sinkMessage.getMessageTag(),
                        sinkMessage.getMessageKey(),
                        sinkMessage.getData().getBytes(RemotingHelper.DEFAULT_CHARSET));
                msg.setStartDeliverTime(sinkMessage.getDeliverTime());
                rocketMQSink.invoke(msg);
            }
        }
    }

    public static void main(String[] args) throws Exception {


    }
}