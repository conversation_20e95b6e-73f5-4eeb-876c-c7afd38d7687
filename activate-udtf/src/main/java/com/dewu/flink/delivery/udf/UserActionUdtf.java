package com.dewu.flink.delivery.udf;

import com.dewu.flink.delivery.common.api.UserAction;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.List;

public class UserActionUdtf extends CollectSinkMessageUdtfBase {

    private static final Logger logger = LoggerFactory.getLogger(UserActionUdtf.class);

    private UserAction userAction;

    public void eval(String message,String channel,
                     String clickIp, int clickPort, String clickPassword, int clickDB,
                     String tagIp, int tagPort, String tagPassword, int tagDB,
                     String retainIp, int retainPort, String retainPassword, int retainDB
    ) throws Exception {
        userAction = UserAction.getInstance(clickIp, clickPort, clickPassword, clickDB,
                tagIp, tagPort, tagPassword, tagDB,
                retainIp, retainPort, retainPassword, retainDB);
        commonDeal(message,channel);
    }

    private void commonDeal(String message,String channel) throws Exception {
        String deal="";
        try {
            Gson gson = new Gson();
            deal = userAction.deal(message);
            List<SinkMessage> messages = gson.fromJson(deal, new TypeToken<List<SinkMessage>>() {
            }.getType());
            collectSinkMessage(messages,channel);
        } catch (Exception e) {
            logger.error("message:{},deal:{}", message,deal, e);
            throw new Exception("message---->>>>>:"+message,e);
        }
    }


}