package com.dewu.flink.delivery.common.sink;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 *功能描述 仅模拟主干流程，实际不用该类
 * <AUTHOR>
 * @date 2022/2/16
 * @param
 * @return
 */
public class KafkaPorducerUtilsTemp {

    private static final Logger LOG = LoggerFactory.getLogger(KafkaPorducerUtilsTemp.class);

    private Long startDeliverTime=0L;

    public KafkaPorducerUtilsTemp(Properties props) throws Exception {
    }

    public void invoke(String message) throws Exception {
    }


    public static void main(String[] args) throws Exception {
    }





}