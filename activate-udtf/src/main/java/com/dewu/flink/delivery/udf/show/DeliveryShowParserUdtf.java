package com.dewu.flink.delivery.udf.show;

import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.consts.RedisConsts;
import com.dewu.flink.delivery.common.utils.BaseUtils;
import com.google.gson.Gson;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class DeliveryShowParserUdtf extends TableFunction<Row> {

    public static Gson gson = new Gson();

    public void eval(String message) throws Exception {
        Map<String, String> data = gson.fromJson(message, Map.class);

        List<String> deviceIds = BaseUtils.processDeviceId(data);

        //存储48小时，其实存很久也无所谓的
        Integer expires = RedisConsts.RedisClickEx;
        if (Consts.TypeRecallDefault.equals(data.get(Consts.Recall))) {
            for (String k : deviceIds) {
                Row row = new Row(3);
                row.setField(0, RedisConsts.HbaseShow+k);
                row.setField(1, gson.toJson(data));
                row.setField(2, Consts.NO_USE_FLAG);//废弃
                collect(row);
            }
        }
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(
                callContext ->
                        Optional.of(
                                DataTypes.ROW(
                                        DataTypes.STRING(),
                                        DataTypes.STRING(),
                                        DataTypes.STRING()
                                )
                        )).build();
    }
}