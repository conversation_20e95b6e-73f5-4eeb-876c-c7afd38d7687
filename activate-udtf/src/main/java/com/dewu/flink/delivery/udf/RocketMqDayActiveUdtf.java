package com.dewu.flink.delivery.udf;

import com.dewu.flink.delivery.common.api.RocketMqAction;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class RocketMqDayActiveUdtf extends CollectSinkMessageUdtfBase {

    private static final Logger logger = LoggerFactory.getLogger(RocketMqDayActiveUdtf.class);

    RocketMqAction rocketMqAction;


    public void eval(String message,String channel,
                     String clickIp, int clickPort, String clickPassword, int clickDB,
                     String tagIp, int tagPort, String tagPassword, int tagDB,
                     String retainIp, int retainPort, String retainPassword, int retainDB) throws Exception {
        rocketMqAction = RocketMqAction.getInstance(clickIp, clickPort, clickPassword, clickDB,
                tagIp, tagPort, tagPassword, tagDB,
                retainIp, retainPort, retainPassword, retainDB,
                "");
        commonDeal(message,channel);
    }


    private void commonDeal(String message,String channel) throws Exception {
        try {
            List<SinkMessage> sinkMessageList = rocketMqAction.processActive(message);
            collectSinkMessage(sinkMessageList,channel);
        } catch (Exception e) {
            throw new Exception("message---->>>>>:"+message,e);
        }
    }

}