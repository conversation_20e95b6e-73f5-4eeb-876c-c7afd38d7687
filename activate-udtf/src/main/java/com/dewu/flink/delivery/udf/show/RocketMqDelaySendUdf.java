package com.dewu.flink.delivery.udf.show;

import com.dewu.flink.delivery.common.sink.RocketMQPorducerUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.flink.RocketMQConfig;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.Properties;

public class RocketMqDelaySendUdf extends ScalarFunction {

    private static Logger LOG = LoggerFactory.getLogger(RocketMqDelaySendUdf.class);


    //处理毫秒
    @FunctionHint(
            input = {@DataTypeHint("STRING"),@DataTypeHint("STRING"),@DataTypeHint("STRING"),
                    @DataTypeHint("STRING"),@DataTypeHint("STRING"),@DataTypeHint("BIGINT"),@DataTypeHint("STRING")},
            output = @DataTypeHint("BIGINT")
    )
    public long eval(String topic,String address,String instanceName,
            String message,String tag,Long startDeliverTime,String keys) throws ParseException, UnsupportedEncodingException {

        Properties producerProps = new Properties();
        producerProps.setProperty(RocketMQConfig.NAME_SERVER_ADDR, address);
        producerProps.setProperty(RocketMQConfig.INSTANCE_NAME, instanceName);

        RocketMQPorducerUtils rocketMqSink = null;
        try {
            rocketMqSink = RocketMQPorducerUtils.getInstance(producerProps, startDeliverTime);
        } catch (Exception e) {
            throw new RuntimeException(" RocketMQPorducerUtils.getInstance error" , e);
        }

        Message msg = new Message(topic,
                tag,
                keys,
                message.getBytes(RemotingHelper.DEFAULT_CHARSET));
        try {
            rocketMqSink.invoke(msg);
        } catch (Exception e) {
            throw new RuntimeException(" RocketMQ send error" , e);
        }
        return 0;
    }

}

