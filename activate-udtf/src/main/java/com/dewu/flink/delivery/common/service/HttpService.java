package com.dewu.flink.delivery.common.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dewu.flink.delivery.common.utils.PostHttpUtils;
import com.shizhuang.sign.SignH5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.HashMap;
import java.util.Map;

import static com.alibaba.fastjson.parser.Feature.OrderedField;

public class HttpService {


    private static final Logger logger = LoggerFactory.getLogger(HttpService.class);


    public static String getRiskLevel(String url,Map<String,String> riskParam)
    {

        PostHttpUtils getHttpFunction = PostHttpUtils.getInstance();
//        Map body = new HashMap<String,String>();
//        body.put("genericDeviceId","76ef0fd5-d4f0-4cea-9c13-6092d87b143c");
//        body.put("seqNo","IOS");
//        body.put("duUuid","IOS");
//        body.put("action","E7_01");

        String jsonData = JSON.toJSONString(riskParam);

        Map<String,String> headerMap = new HashMap<>(2);
        headerMap.put("appId","h5");
        headerMap.put("Content-Type","application/json");
        Object requestBody = JSONObject.parseObject(jsonData, OrderedField);
        String sign = SignH5Utils.sign(requestBody, null, headerMap, "POST");

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("sign",sign);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("appId","h5");

        String eval =null;
        try {
            logger.info("=====================>>>>>>>>>>>>>>>>>>>>>>jsonData:{},map:{}",jsonData,map);
//             eval = getHttpFunction.eval("http://pre-ms3.dewu.com/api/v1/h5/risk-ng-route/ng/ec/call?sign={sign}", jsonData, map, headers);
//             eval = getHttpFunction.eval("http://ms3.dewu.com/api/v1/h5/risk-ng-route/ng/ec/call?sign={sign}", jsonData, map, headers);
            eval = getHttpFunction.eval(url, jsonData, map, headers);
        }catch (Exception e)
        {
            logger.error("=====================jsonData:{},map:{}",jsonData,map,e);
            throw e;
        }
        return eval;
    }

}