package com.dewu.flink.delivery.udf;

import com.dewu.flink.delivery.common.api.Activate;
import com.google.gson.Gson;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.types.inference.TypeInference;

import java.util.Optional;

public class ActivateUdtf extends CollectSinkMessageUdtfBase {

    private Activate activate ;
    private Gson gson;

//    @Override
//    public void open(FunctionContext context){
//
//        String clickIp = "r-bp19f1a27d0e5034.redis.rds.aliyuncs.com";
//        int clickPort = 6379;
//        String clickPassword = "cTpi6EV4Bm7oBc8W";
//        Integer clickDB = 0;
//
//        String tagIp = "r-bp133fd71b2b7c04.redis.rds.aliyuncs.com";
//        int tagPort = 6379;
//        String tagPassword = "gU0kcojSNd8Asn61";
//        Integer tagDB = 0;
//
//        //暂时使用测试环境的
////        String retainIp = "r-uf6w4jeitf13wkzgj8.redis.rds.aliyuncs.com";
////        int retainPort = 6379;
////        String retainPassword = "_3lM)sue)ijdMW!";
////        Integer retainDB = 229;
//
//        String retainIp = "r-bp19f1a27d0e5034.redis.rds.aliyuncs.com";
//        int retainPort = 6379;
//        String retainPassword = "cTpi6EV4Bm7oBc8W";
//        Integer retainDB = 70;
//
//        activate = Activate.getInstance(clickIp,clickPort,clickPassword,clickDB,
//                tagIp,tagPort,tagPassword,tagDB,
//                retainIp,retainPort,retainPassword,retainDB);
//        gson = new Gson();
//    }


    /**
     *功能描述
     * rocketmq  sinkMessage.setMessageTag(Consts.MsgTagLtvQuery)
     * Consts.MsgTagActiveRisk(激活兜底)
     *
     * kafka
     *
     * <AUTHOR>
     * @date 2022/4/7
     * @param
     * @return
     */
    public void eval(String message,String channel,
                     String clickIp, int clickPort, String clickPassword, int clickDB,
                     String tagIp, int tagPort, String tagPassword, int tagDB,
                     String retainIp, int retainPort, String retainPassword, int retainDB) throws Exception {
        activate = Activate.getInstance(clickIp, clickPort, clickPassword, clickDB,
                tagIp, tagPort, tagPassword, tagDB,
                retainIp, retainPort, retainPassword, retainDB);
        collectSinkMessage(activate.deal(message,true),channel);
    }


//    public void eval(String str) throws Exception {
//
//    }

    // the automatic, reflection-based type inference is disabled and
    // replaced by the following logic
    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .typedArguments(DataTypes.STRING(), DataTypes.STRING(),
                        DataTypes.STRING(), DataTypes.INT(), DataTypes.STRING(), DataTypes.INT(),
                        DataTypes.STRING(), DataTypes.INT(), DataTypes.STRING(), DataTypes.INT(),
                        DataTypes.STRING(), DataTypes.INT(), DataTypes.STRING(), DataTypes.INT())
                .outputTypeStrategy(callContext -> Optional.of(DataTypes.ROW(DataTypes.STRING(), DataTypes.STRING(),
                        DataTypes.STRING(), DataTypes.STRING(), DataTypes.BIGINT(), DataTypes.STRING(),
                        DataTypes.BIGINT(), DataTypes.STRING())))
                .build();
    }


}
