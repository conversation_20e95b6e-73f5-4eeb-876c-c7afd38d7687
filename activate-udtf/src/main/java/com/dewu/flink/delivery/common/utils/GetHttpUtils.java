package com.dewu.flink.delivery.common.utils;


import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedWriter;
import java.net.SocketTimeoutException;
import java.util.HashMap;


/**
 * Created by 刘洋 on 2018/9/10.
 */
public class GetHttpUtils extends HttpBase {

    protected BufferedWriter out;

    public void open() throws Exception {
        restTemplate = createSimpleRestTemplate();
    }

    public String eval(String url, String... key) throws InterruptedException {
        HashMap<String, Object> urlVariables = new HashMap();
        for (int i = 0; i < key.length; ) {
            urlVariables.put(key[i], key[i + 1]);
            i++;
            i++;
        }

        String body = "";
        try {
//          body = restTemplate.getForObject(url, String.class, urlVariables);
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class, urlVariables);
            body = responseEntity.getBody();// 获取响应体

//        //以下是getForEntity比getForObject多出来的内容
            HttpStatus statusCode = responseEntity.getStatusCode(); // 获取响应码
            int statusCodeValue = responseEntity.getStatusCodeValue(); // 获取响应码值
            HttpHeaders headers = responseEntity.getHeaders(); // 获取响应头
            System.out.println("statusCode:" + statusCode + " statusCodeValue:" + statusCodeValue + " headers:" + headers);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof SocketTimeoutException) {

            } else {
                Thread.sleep(1000);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class, urlVariables);
            body = responseEntity.getBody();// 获取响应体

        }
        return body;
    }


    public static void main(String[] args) throws Exception {
        GetHttpUtils getHttpFunction = new GetHttpUtils();
        getHttpFunction.open();
        System.out.println(getHttpFunction.eval("http://127.0.0.1:8080/user/detail?id=1", "1", "2", "3", "4"));
        System.out.println(getHttpFunction.eval("http://127.0.0.1:8080/user/detail?id=1", "1", "2", "3", "4"));
        System.out.println(getHttpFunction.eval("http://127.0.0.1:8080/user/detail?id=1", "1", "2", "3", "4"));
        System.out.println(getHttpFunction.eval("http://127.0.0.1:8080/user/detail?id=1", "1", "2", "3", "4"));
        System.out.println(getHttpFunction.eval("http://127.0.0.1:8080/user/detail?id=1", "1", "2", "3", "4"));
        System.out.println(getHttpFunction.eval("http://127.0.0.1:8080/user/detail?id=1", "1", "2", "3", "4"));
    }
}