package com.dewu.flink.delivery.udf.show;


import com.dewu.flink.delivery.common.api.RocketMqAction;
import com.dewu.flink.delivery.common.bean.RocketMqMess;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.udf.CollectSinkMessageUdtfBase;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class RocketMqShowActiveDelayUdtf extends CollectSinkMessageUdtfBase {

    private static final Logger logger = LoggerFactory.getLogger(RocketMqShowActiveDelayUdtf.class);

    private RocketMqAction rocketMqAction;

    public void eval(String message,String hbaseUsername,String hbasePassword,String hbaseZookeeperQuorum,String hbaseTableName,
                     String tagIp, int tagPort, String tagPassword, int tagDB,
                     String url) throws Exception {
        rocketMqAction = RocketMqAction.getHbaseInstance(hbaseUsername, hbasePassword, hbaseZookeeperQuorum,
                tagIp,tagPort,tagPassword,tagDB,
                url);

        try {
            Gson gson = new Gson();
            RocketMqMess rocketMqMess = gson.fromJson(message, RocketMqMess.class);
            if (rocketMqMess == null) {
                List<SinkMessage> sinkMessageList =new ArrayList<>();
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "延迟消费用户关键行为数据失败，解析异常", "",-1L);
                temp.setMessageLog(message);
                sinkMessageList.add(temp);
                collectSinkMessage(sinkMessageList,"channel");
                return ;
            }

            Message mess = new Message();
            mess.setTags(rocketMqMess.getTag());
            mess.setBody(rocketMqMess.getData().getBytes());
            String deal = rocketMqAction.dealDelayShowActivate(mess,hbaseTableName);

            List<SinkMessage> sinkMessageList = gson.fromJson(deal, new TypeToken<List<SinkMessage>>() {
            }.getType());
            collectSinkMessage(sinkMessageList,"channel");
        } catch (Exception e) {
            logger.error("message:{}", message, e);
            throw new Exception("message---->>>>>:"+message,e);
        }
    }


}


