package com.dewu.flink.delivery.common.api;

import com.dewu.flink.delivery.common.bean.ClickBean;
import com.dewu.flink.delivery.common.bean.SearchClickResult;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.consts.RedisConsts;
import com.dewu.flink.delivery.common.utils.BaseUtils;
import com.dewu.flink.delivery.common.utils.JedisSington;
import com.dewu.flink.delivery.common.utils.RedisUtils;
import com.dewu.flink.delivery.common.utils.TimeUtils;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetClick {

    public Gson gson = new Gson();

    private static volatile GetClick singleton;

    public GetClick() {
    }

    public static GetClick getInstance(String retainIp, int retainPort, String retainPassword, int retainDB) {
        if (singleton == null) {
            synchronized (GetClick.class) {
                if (singleton == null) {
                    singleton = new GetClick(retainIp, retainPort, retainPassword, retainDB);
                }
            }
        }
        return singleton;
    }


    public GetClick(String retainIp, Integer retainPort, String retainPassword, Integer retainDB) {
        if (retainDB == null && retainPassword == null && retainIp != null && retainPort != null) {
            this.retainJedisSington = new JedisSington(retainIp, retainPort);
        } else if (retainDB != null && retainPassword != null && retainIp != null && retainPort != null) {
            this.retainJedisSington = new JedisSington(retainIp, retainPort, retainPassword, retainDB);
        }
    }

    public JedisSington retainJedisSington;

    public List<ClickBean> dealV2(String value) throws Exception {
        List<ClickBean> list = new ArrayList<>();
        Map<String, String> data = gson.fromJson(value, Map.class);
        List<String> deviceIds = processDeviceId(data);
        // 展示上报和软广不需要下面的流程
        if ("1".equals(data.get(Consts.Inview))) {
            return list;
        }
        // 商店流量优先级低于信息流，如果当前设备号已存在点击，则不存入redis
        //此处逻辑会影响落odps的点击数据比投放落redis的多，此处必须注释掉
        if("1".equals(data.get(Consts.Market)) && processMarketClick(data)){
            return list;
        }

        Integer expires = RedisConsts.RedisClickEx;
        if (Consts.TypeRecallDefault.equals(data.get(Consts.Recall))) {
            expires = RedisConsts.RedisClickRecallEx;
        }

        //此处逻辑会影响落odps的点击数据比投放落redis的多
//        if svc.data[consts.Soft] == "1" {
//            prefix = consts.RedisSoftClick
//            redisClient = svc.reportRedis
//            softClick := config.GetConfig().SoftClick
//            // 软广缓存时间走配置
//            expires = time.Duration(softClick.CacheTTL.Click) * time.Hour
//            if svc.data[consts.Recall] == consts.TypeRecallDefault {
//                expires = time.Duration(softClick.CacheTTL.Recall) * time.Hour
//            }
//
//            // 不在配置中的广告商，不放入缓存
//            if !softClick.CacheMedias[svc.data[consts.Media]] {
//                return
//            }
//        }

        Map<String, String> data1=new HashMap<>();
        data1.put(Consts.Market,data.get(Consts.Market));
        data1.put(Consts.Used,"");
        RedisUtils.setClickData(retainJedisSington,deviceIds,data1, RedisConsts.RedisClick,expires);
        return list;
    }


    public List<ClickBean> deal(String value) throws Exception {
        List<ClickBean> list = new ArrayList<>();
        Map<String, String> data = gson.fromJson(value, Map.class);
        List<String> deviceIds = processDeviceId(data);

        Integer expires = RedisConsts.RedisClickEx;
        if (Consts.TypeRecallDefault.equals(data.get(Consts.Recall))) {
            expires = RedisConsts.RedisClickRecallEx;
        }

        for (int i = 0; i < deviceIds.size(); i++) {
            String key = deviceIds.get(i);
            ClickBean clickBean = new ClickBean();
            clickBean.setKey(key);
            clickBean.setValue(value);
            clickBean.setExpires(expires);
            clickBean.setPrefix(RedisConsts.RedisClick);
            clickBean.setSave_time(TimeUtils.getTimeStampSecond());
            list.add(clickBean);
        }
        return list;
    }


    //若当前设备号已有点击存在，则不处理,返回true
    private Boolean processMarketClick(Map<String, String> params) throws Exception {
        List<String> keys;
        if (params.get(Consts.Os).equals(Consts.Ios)) {
            keys = BaseUtils.getDeviceKeys(params.get(Consts.Idfa));
            keys.add(params.get(Consts.IpUa));
        } else {
            keys = BaseUtils.getDeviceKeys(params.get(Consts.Oaid));
        }


        SearchClickResult clickData = RedisUtils.getClickData(retainJedisSington, keys, RedisConsts.RedisClick, 0, 1);
        //若当前设备号已有点击存在，则不处理
        if(clickData.getData().size() > 0 && StringUtils.isBlank(clickData.getData().get(Consts.Market))){
            return true;
        }
        return false;
    }

    /**
     * 功能描述 处理设备ID，这一块投放已经处理好了并发送到了Kafka，所以直接从kafka中获取对应值即可，不需要在进行处理了
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/3/21
     */
    private List<String> processDeviceId(Map<String, String> data) {
        String[] fields = {Consts.Imei, Consts.Oaid, Consts.Idfa, Consts.AndroidId};
        List<String> deviceIds = new ArrayList();
        for (String v : fields) {
            //但是需要加上这个条件，否则deviceIds集合里面会有空串或者null等值进行影响
            String value = data.get(v);
            if (StringUtils.isBlank(value) || StringUtils.contains(value,Consts.InvalidDeviceId) || "null".equals(value)) {
                continue;
            }
            deviceIds.add(value);
        }

        if (Consts.Ios.equals(data.get(Consts.Os)) || "1".equals(data.get(Consts.Soft))) {
            if (StringUtils.isNotBlank(data.get(Consts.IpUa))) {
                deviceIds.add(data.get(Consts.IpUa));
            }
            if (StringUtils.isNotBlank(data.get(Consts.IpvxUa)) &&
                    !data.get(Consts.IpvxUa).equals(data.get(Consts.IpUa))) {
                deviceIds.add(data.get(Consts.IpvxUa));
            }
        }
        return deviceIds;
    }


}