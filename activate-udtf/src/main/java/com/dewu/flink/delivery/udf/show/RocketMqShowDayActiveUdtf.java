package com.dewu.flink.delivery.udf.show;


import com.dewu.flink.delivery.common.api.RocketMqAction;
import com.dewu.flink.delivery.common.bean.ActiveMessage;
import com.dewu.flink.delivery.common.bean.QueryReq;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.udf.CollectSinkMessageUdtfBase;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.flink.table.functions.FunctionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class RocketMqShowDayActiveUdtf extends CollectSinkMessageUdtfBase {

    private static final Logger logger = LoggerFactory.getLogger(RocketMqShowDayActiveUdtf.class);

    private RocketMqAction rocketMqAction;

    @Override
    public void open(FunctionContext context) throws Exception {
//        rocketMqAction = new RocketMqAction("root","root","ld-bp1v180d8v08by55e-proxy-hbaseue.hbaseue.rds.aliyuncs.com:30020",
//                "r-bp133fd71b2b7c04.redis.rds.aliyuncs.com",6379,"gU0kcojSNd8Asn61",0,
//                "url");
    }

    public void eval(String message,
                     String hbaseUsername,String hbasePassword,String hbaseZookeeperQuorum,String hbaseTableName,
                     String tagIp, int tagPort, String tagPassword, int tagDB) throws Exception {
        rocketMqAction = RocketMqAction.getHbaseInstance(hbaseUsername, hbasePassword, hbaseZookeeperQuorum,
                tagIp,tagPort,tagPassword,tagDB,"url");


        commonDeal(message,hbaseTableName);
    }

    public void eval(String message,String hbaseTableName) throws Exception {
        commonDeal(message,hbaseTableName);
    }


    private void commonDeal(String message,String hbaseTableName) throws Exception {
        try {
            Gson gson = new Gson();
            String log =message;
            ActiveMessage data;
            try {
                data = gson.fromJson(message, ActiveMessage.class);
            } catch (Exception e) {
                logger.error("rocketmq日活，解析异常:{}", message, e);
                List<SinkMessage> sinkMessageList =new ArrayList<>();
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "rocketmq日活，解析异常", "",-1L);
                temp.setMessageLog(log);
                sinkMessageList.add(temp);
                collectSinkMessage(sinkMessageList,"channel");
                return;
            }

            QueryReq params = rocketMqAction.formatDayActive(data);
            String mess = rocketMqAction.getRocketMqAttributeService().showActive(params,log,hbaseTableName);

            List<SinkMessage> sinkMessageList = gson.fromJson(mess, new TypeToken<List<SinkMessage>>() {
            }.getType());
            collectSinkMessage(sinkMessageList,"channel");
        } catch (Exception e) {
            logger.error("message:{}", message, e);
            throw new Exception("message---->>>>>:"+message,e);
        }
    }

}

