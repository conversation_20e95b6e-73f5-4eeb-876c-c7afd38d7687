package com.dewu.flink.delivery.common.bean;

import java.util.HashMap;
import java.util.Map;

public class RetainResult {
    private Map<String,String> data;
    private String retainKey;
    private String ttlFlag;
    private long ttl=-998L;

    public RetainResult() {
        data=new HashMap<>();
    }

    public void setData(Map<String, String> data) {
        this.data = data;
    }

    public String getRetainKey() {
        return retainKey;
    }

    public void setRetainKey(String retainKey) {
        this.retainKey = retainKey;
    }

    public String getTtlFlag() {
        return ttlFlag;
    }

    public void setTtlFlag(String ttlFlag) {
        this.ttlFlag = ttlFlag;
    }

    public Map<String, String> getData() {
        return data;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }
}
