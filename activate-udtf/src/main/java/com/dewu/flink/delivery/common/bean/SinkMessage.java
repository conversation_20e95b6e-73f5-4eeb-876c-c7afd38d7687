package com.dewu.flink.delivery.common.bean;


public class SinkMessage {
    private String type;//标志发送渠道类型，kafka,debug,rocketmq table-> mq_type
    private String desc;//debug 消息中文描述   table-> mess_topic
    private String data;//发出的数据 table-> mess_result
    private Long deliverTime;//debug消息标志,rocketmq消息则标志延迟时间 table-> deliver_time
    private String messageKey;//table-> mess_key
    private String messageTag;//log信息,ROCKETMQ类型下，tag table-> mess_tag
    private String messageLog;//log信息 table-> mess_tag

    private Long ttl=-100L;//table-> mess_ttl
    private String redisKey;//查询到消息的key table-> mess_click_key

    public SinkMessage(String type, String desc, String data) {
        this.type = type;
        this.desc = desc;
        this.data = data;
    }

    public SinkMessage(String type, String desc, String data,String messageLog) {
        this.type = type;
        this.desc = desc;
        this.data = data;
        this.messageLog=messageLog;
    }

    public SinkMessage(String type, String desc, String data,Long deliverTime) {
        this.type = type;
        this.desc = desc;
        this.data = data;
        this.deliverTime=deliverTime;
    }

    public String getMessageLog() {
        return messageLog;
    }

    public void setMessageLog(String messageLog) {
        this.messageLog = messageLog;
    }

    public String getRedisKey() {
        return redisKey;
    }

    public void setRedisKey(String redisKey) {
        this.redisKey = redisKey;
    }

    public void setDeliverTime(Long deliverTime) {
        this.deliverTime = deliverTime;
    }

    public void setMessageKey(String messageKey) {
        this.messageKey = messageKey;
    }

    public void setMessageTag(String messageTag) {
        this.messageTag = messageTag;
    }

    public Long getTtl() {
        return ttl;
    }

    public void setTtl(Long ttl) {
        this.ttl = ttl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Long getDeliverTime() {
        return deliverTime;
    }

    public String getMessageKey() {
        return messageKey;
    }

    public String getMessageTag() {
        return messageTag;
    }
}