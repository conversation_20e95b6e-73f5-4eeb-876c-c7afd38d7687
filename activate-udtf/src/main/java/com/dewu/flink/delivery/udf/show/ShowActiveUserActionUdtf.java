package com.dewu.flink.delivery.udf.show;

import com.dewu.flink.delivery.common.api.UserAction;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.dewu.flink.delivery.udf.CollectSinkMessageUdtfBase;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class ShowActiveUserActionUdtf extends CollectSinkMessageUdtfBase {

    private static final Logger logger = LoggerFactory.getLogger(ShowActiveUserActionUdtf.class);

    private UserAction userAction;

    public void eval(String message,String hbaseUsername,String hbasePassword,String hbaseZookeeperQuorum,String hbaseTableName,
                     String tagIp, int tagPort, String tagPassword, int tagDB
                    ) throws Exception {
        userAction = UserAction.getHbaseInstance(hbaseUsername,hbasePassword,hbaseZookeeperQuorum, tagIp,tagPort,tagPassword,tagDB);

        try {
            Gson gson = new Gson();
            String deal = userAction.dealShowUserAction(message,hbaseTableName);
            List<SinkMessage> messages = gson.fromJson(deal, new TypeToken<List<SinkMessage>>() {
            }.getType());
            collectSinkMessage(messages,"channel");
        } catch (Exception e) {
            logger.error("message:{}", message, e);
            throw new Exception("message---->>>>>:"+message,e);
        }
    }

}
