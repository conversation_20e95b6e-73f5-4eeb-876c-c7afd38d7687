package com.dewu.flink.delivery.common.bean;

public class QueryResp {

    private long userId;//             int64  `json:"userId" form:"userId"`                         // APP用户ID
    private String os;//            string `json:"os" form:"os"`                                 // 操作系统：android|iphone|ios
    private String deviceType;//         string `json:"deviceType"`   // 匹配到的设备类型：imei|oaid|idfa|ipua|android_id，未匹配到为空
    private  String moduleType;//         json:"moduleType"`   // 跳转类型，0：社区, 1：交易，没有为空
    private String projectId;//         `json:"projectId"`    // 广告点击里的package_name
    private String contentId;//         `json:"contentId"`    // 内容ID，例：contentId:51153498
    private  String contentTagId;//         `json:"contentTagId"` // 内容标签ID
    private  String tags;//         `json:"tags"`         // 标签，例：李宁鞋子+球鞋
    private Boolean isNew;//  `json:"isNew"`        // 是否新用户

    public long getUserId() {
        return userId;
    }

    public String getOs() {
        return os;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public String getModuleType() {
        return moduleType;
    }

    public String getProjectId() {
        return projectId;
    }

    public String getContentId() {
        return contentId;
    }

    public String getContentTagId() {
        return contentTagId;
    }

    public String getTags() {
        return tags;
    }

    public Boolean getNew() {
        return isNew;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public void setContentTagId(String contentTagId) {
        this.contentTagId = contentTagId;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public void setNew(Boolean aNew) {
        isNew = aNew;
    }
}