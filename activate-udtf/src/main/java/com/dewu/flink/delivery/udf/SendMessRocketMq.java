package com.dewu.flink.delivery.udf;

import com.dewu.flink.delivery.common.sink.RocketMQPorducerUtils;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.flink.RocketMQConfig;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

@Deprecated
public class SendMessRocketMq extends ScalarFunction {

  public static Logger LOG = LoggerFactory.getLogger(SendMessRocketMq.class);

  public String eval(String message, String topic, String tag,String address, String instanceName, Long startDeliverTime) throws Exception {
    Properties producerProps = new Properties();
    producerProps.setProperty(RocketMQConfig.NAME_SERVER_ADDR, address);
    producerProps.setProperty(RocketMQConfig.INSTANCE_NAME, instanceName);
    RocketMQPorducerUtils rocketMqSink = RocketMQPorducerUtils.getInstance(producerProps,startDeliverTime);

    Message msg = new Message(topic,
            tag,
            message.getBytes(RemotingHelper.DEFAULT_CHARSET));
    rocketMqSink.invoke(msg);
    return "success";
  }
}