package com.dewu.flink.delivery.common.utils;

import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtils {

    public static String getCurrentDate(){
        Date curDate = new Date();
        SimpleDateFormat dateFormat=new SimpleDateFormat("yyyyMMdd");
        return dateFormat.format(curDate);
    }

    public static long getTimeStampSecond(long timestamp)
    {
        String t = String.valueOf(timestamp);
        int length = t.length();
        return Integer.valueOf(t.substring(0, length - 3));
    }
    public static long getTimeStampSecond()
    {
        String t = String.valueOf(System.currentTimeMillis());
        int length = t.length();
        return Integer.valueOf(t.substring(0, length - 3));
    }

    public static int getyyyyMMdd(long et)
    {
        Date date = new Date(et*1000);
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        String format = f.format(date);
        String substring = StringUtils.substring(format, 0, 8);
        return Integer.valueOf(substring);
    }

    public static String getyyyyMMddHHmmss(long et)
    {
        Date date = new Date(et*1000);
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        String format = f.format(date);
        return format;
    }

    public static int getyyyyMMddByAddDay(long et,int days)
    {
        Date date = new Date(et*1000);
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date);
        calendar1.add(Calendar.DATE, days);
        String format =f.format(calendar1.getTime());
        String substring = StringUtils.substring(format, 0, 8);
        return Integer.valueOf(substring);
    }

    public static int getHourOfDay(){
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    public static int getMinute(){
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MINUTE);
    }

    public static int getHourOfDay(Calendar calendar){
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    public static int getMinute(Calendar calendar){
        return calendar.get(Calendar.MINUTE);
    }

    public static void main(String[] args) {
        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        String format = f.format(date);
        System.out.println(StringUtils.substring(format,0,8));

        System.out.println(TimeUtils.getyyyyMMddByAddDay(1644564871,-7));

        Calendar calendar1 = Calendar.getInstance();
        System.out.println(calendar1.get(Calendar.HOUR_OF_DAY));
        System.out.println(calendar1.get(Calendar.MINUTE));


    }


}