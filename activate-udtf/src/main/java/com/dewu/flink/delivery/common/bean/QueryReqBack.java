package com.dewu.flink.delivery.common.bean;

public class QueryReqBack {


//    ,log_time STRING
//    ,recall STRING
//    ,media STRING
//    ,device_type STRING
//    ,user_type STRING
//    ,inactive STRING
//    ,anti_spam STRING
//    ,used STRING
//    ,rid STRING
//    ,tid STRING
//    ,app_id STRING
//    ,spam_level STRING


    private String device_type="";//              string `json:"appId" form:"appId"`                           // APP标识
    private String appId="";//              string `json:"appId" form:"appId"`                           // APP标识
    private String appVersion="";//         string `json:"appVersion" form:"appVersion"`                 // APP版本号
    private String os="";//            string `json:"os" form:"os"`                                 // 操作系统：android|iphone|ios

    private String imei="";//         string `json:"imei" form:"imei"`                             // 安卓imei
    private  String oaid="";//         string `json:"oaid" form:"oaid"`                             // 安卓oaid

    private String idfa="";//         string `json:"idfa" form:"idfa"`                             // 苹果idfa
    private  String fcuuid="";//         string `json:"fcuuid" form:"fcuuid"`                         // 客户端生成的uuid，iOS有
    private  String uuid="";//         string `json:"uuid" form:"uuid"`                             // 设备号
    private  String clipboard="";//         string `json:"clipboard" form:"clipboard"`                   // 系统剪贴板，没有不传
    private String ua="";//         string `json:"ua" form:"ua"`                                 // 用户user_agent
    private String ip="";//         string `json:"ip" form:"ip"`                                 // 用户ip
    private String ipvx="";//         string `json:"ipvx" form:"ipvx"`                             // 用户ipvx
    private String source="";//        string `json:"source" form:"source"`                         // 来源，init|active

    private  String shumeiId="";//        string `json:"shumeiId" form:"shumeiId"`                     // 数美ID
    private  String orderNo="";//        string `json:"orderNo" form:"orderNo"`                       // 订单号
    private String ipua="";//        string `json:"ipua" form:"ipua"`                             // md5(lower(ip)+lower(trim_all_space(ua)))
    private long lastActiveTime;//    int64  `json:"lastActiveTime" form:"lastActiveTime"`         // 上次活跃时间
    private long dayFirstActiveTime;//  int64  `json:"dayFirstActiveTime" form:"dayFirstActiveTime"` // 当日首次活跃时间


    private String pkg_channel="";//       string `json:"pkgChannel" form:"pkgChannel"`                 // 包渠道号
    private long event_time;//        int64  `json:"eventTime" form:"eventTime"`                   // 发生时间
    private String user_id;//             int64  `json:"userId" form:"userId"`                         // APP用户ID
    private String android_id="";//         string `json:"androidId" form:"androidId"`                   // 安卓ID
    private String type="";
    private String prefix="";
    private String inactive="";


    private long ttl;
    private long log_time;
    private String clickKey;
    private String rediskeys;

    public String getDevice_type() {
        return device_type;
    }

    public void setDevice_type(String device_type) {
        this.device_type = device_type;
    }

    public String getRediskeys() {
        return rediskeys;
    }

    public void setRediskeys(String rediskeys) {
        this.rediskeys = rediskeys;
    }

    public long getLog_time() {
        return log_time;
    }

    public void setLog_time(long log_time) {
        this.log_time = log_time;
    }

    public String getClickKey() {
        return clickKey;
    }

    public void setClickKey(String clickKey) {
        this.clickKey = clickKey;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }

    public String getInactive() {
        return inactive;
    }

    public void setInactive(String inactive) {
        this.inactive = inactive;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getPkg_channel() {
        return pkg_channel;
    }

    public void setPkg_channel(String pkg_channel) {
        this.pkg_channel = pkg_channel;
    }

    public long getEvent_time() {
        return event_time;
    }

    public void setEvent_time(long event_time) {
        this.event_time = event_time;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getAndroid_id() {
        return android_id;
    }

    public void setAndroid_id(String android_id) {
        this.android_id = android_id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        if(appId ==null)
        {
            this.appId="";
            return;
        }
        this.appId = appId;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        if(appVersion ==null)
        {
            this.appVersion="";
            return;
        }
        this.appVersion = appVersion;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        if(os ==null)
        {
            this.os="";
            return;
        }
        this.os = os;
    }


    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        if(imei ==null)
        {
            this.imei="";
            return;
        }
        this.imei = imei;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        if(oaid ==null)
        {
            this.oaid="";
            return;
        }
        this.oaid = oaid;
    }


    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        if(idfa ==null)
        {
            this.idfa="";
            return;
        }
        this.idfa = idfa;
    }

    public String getFcuuid() {
        return fcuuid;
    }

    public void setFcuuid(String fcuuid) {
        if(fcuuid ==null)
        {
            this.fcuuid="";
            return;
        }
        this.fcuuid = fcuuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        if(uuid ==null)
        {
            this.uuid="";
            return;
        }
        this.uuid = uuid;
    }

    public String getClipboard() {
        return clipboard;
    }

    public void setClipboard(String clipboard) {
        if(clipboard ==null)
        {
            this.clipboard="";
            return;
        }
        this.clipboard = clipboard;
    }

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        if(ua ==null)
        {
            this.ua="";
            return;
        }
        this.ua = ua;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        if(ip ==null)
        {
            this.ip="";
            return;
        }
        this.ip = ip;
    }

    public String getIpvx() {
        return ipvx;
    }

    public void setIpvx(String ipvx) {
        if(ipvx ==null)
        {
            this.ipvx="";
            return;
        }
        this.ipvx = ipvx;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        if(source ==null)
        {
            this.source="";
            return;
        }
        this.source = source;
    }

    public String getShumeiId() {
        return shumeiId;
    }

    public void setShumeiId(String shumeiId) {
        if(shumeiId ==null)
        {
            this.shumeiId="";
            return;
        }
        this.shumeiId = shumeiId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        if(orderNo ==null)
        {
            this.orderNo="";
            return;
        }
        this.orderNo = orderNo;
    }

    public String getIpua() {
        return ipua;
    }

    public void setIpua(String ipua) {
        if(ipua ==null)
        {
            this.ipua="";
            return;
        }
        this.ipua = ipua;
    }

    public long getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(long lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public long getDayFirstActiveTime() {
        return dayFirstActiveTime;
    }

    public void setDayFirstActiveTime(long dayFirstActiveTime) {
        this.dayFirstActiveTime = dayFirstActiveTime;
    }

    @Override
    public String toString() {
        return "QueryReqBack{" +
                "appId='" + appId + '\'' +
                ", appVersion='" + appVersion + '\'' +
                ", os='" + os + '\'' +
                ", imei='" + imei + '\'' +
                ", oaid='" + oaid + '\'' +
                ", idfa='" + idfa + '\'' +
                ", fcuuid='" + fcuuid + '\'' +
                ", uuid='" + uuid + '\'' +
                ", clipboard='" + clipboard + '\'' +
                ", ua='" + ua + '\'' +
                ", ip='" + ip + '\'' +
                ", ipvx='" + ipvx + '\'' +
                ", source='" + source + '\'' +
                ", shumeiId='" + shumeiId + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", ipua='" + ipua + '\'' +
                ", lastActiveTime=" + lastActiveTime +
                ", dayFirstActiveTime=" + dayFirstActiveTime +
                ", pkg_channel='" + pkg_channel + '\'' +
                ", event_time=" + event_time +
                ", user_id='" + user_id + '\'' +
                ", android_id='" + android_id + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}