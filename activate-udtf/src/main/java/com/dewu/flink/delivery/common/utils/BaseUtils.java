package com.dewu.flink.delivery.common.utils;

import com.dewu.flink.delivery.common.bean.QueryReq;
import com.dewu.flink.delivery.common.bean.QueryReqBack;
import com.dewu.flink.delivery.common.consts.Consts;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BaseUtils {
    public static String calIpUa(String ip, String ua) {
        ua = StringUtils.lowerCase(ua);
        ip=StringUtils.replace(ip, " ", "").toLowerCase();
        // 统一ua格式，去除后面自己加的部分
        String pattern = "(.*mobile/[a-z0-9]+)(.*)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(ua);
        if (m.find( )) {
            ua = m.group(1);
        }

        ua = StringUtils.replace(ua," ", "");
        if(!"".equals(ua)&&!"".equals(ip)) {
            return md5(ip+ua);
        }
        return "";
    }


    // CalBigDataIpUa 与大数据保持一致的ua获取方式
//    func CalBigDataIpUa(ip, ua, os, model string) string {
    public static String calBigDataIpUa(String ip,String ua,String os,String model) {
        ip = StringUtils.lowerCase(StringUtils.replace(ip, " ", "", -1));
        ua = StringUtils.lowerCase(ua);

        String osVersion = "";
        os = StringUtils.lowerCase(StringUtils.trim(os));
        model = StringUtils.lowerCase(StringUtils.trim(model));

        String pattern1 = "(linux);\\s?([a-z]+)\\s?(.*\\d);\\s?(.*)\\s?(build)/";
        String pattern2 = "(ipad|iphone);\\s?\\b(cpu.*os)\\b\\s?(.*\\d)\\b\\s(like)";
        // 统一ua格式，去除后面自己加的部分
        Pattern r = Pattern.compile(pattern1);
        Matcher m = r.matcher(ua);
        if (m.find( )) {
            int i = m.groupCount();
            if(i>=5)
            {
                if("".equals(os) || "0".equals(os)) {
                    os = StringUtils.trim(m.group(2));
                }
                osVersion = StringUtils.trim(m.group(3));
                if("".equals(model)) {
                    model = StringUtils.trim(m.group(4));
                }
            }
        }

        r = Pattern.compile(pattern2);
        m = r.matcher(ua);
        if (m.find( )) {
            int i = m.groupCount();
            if(i>=5)
            {
                os = "ios";
                osVersion = StringUtils.trim(StringUtils.replace(m.group(3), "-", ".", -1));
                if("".equals(model)){
                    model = StringUtils.trim(m.group(1));
                }
            }
        }

        ua = os + "#" + osVersion + "#" + model;
        if(!"".equals(ua) && !"".equals(ip)) {
            return md5(ip + ua);
        }

        return "";
    }




    public static List<String> getDeviceKeys(String deviceId){
        List<String> keys = new ArrayList<>();
        if("".equals(deviceId)  || StringUtils.contains(deviceId,Consts.InvalidDeviceId)|| "null".equals(deviceId)){
            return keys;
        }
        String upperKey = deviceId.toUpperCase();
        String lowerKey = deviceId.toLowerCase();
        keys.add(upperKey);
        keys.add(md5(deviceId));
        if(!upperKey.equals(deviceId)){
            keys.add(md5(upperKey));
        }
        if(!lowerKey.equals(deviceId)){
            keys.add(md5(lowerKey));
        }
        if(deviceId.length()==32&&!lowerKey.equals(upperKey))
        {
            keys.add(lowerKey);
        }
        return keys;
    }

    public static String md5(String text){
        return DigestUtils.md5Hex(text);
    }

    public static String uuid(){
        return UUID.randomUUID().toString();
    }




    public static String replaceLast(String text, String strToReplace, String replaceWithThis) {
        return text.replaceFirst("(?s)" + strToReplace + "(?!.*?" + strToReplace + ")", replaceWithThis);
    }


//    static
//    {
//        URL url = BaseUtils.class.getClassLoader().getResource("config.yaml");
//        InputStream input = null;
//        try {
//            input = new FileInputStream(url.getFile());
//        } catch (FileNotFoundException e) {
//            e.printStackTrace();
//        }
//        Yaml yaml = new Yaml();
//        conf = (Map<String, Object>) yaml.load(input);
//    }

    static Map<String, Object> conf;

    //    func (svc *AttributeService) VirtualCallback(media, pid, eventType string) bool {
    public static boolean virtualCallback(String media,String pid,String eventType )  {
        boolean isCallback = true;
        Map<String, Object> map1 = (Map<String, Object>) conf.get("callbackTest");
        Map<String, Object> c = (Map<String, Object>) map1.get(media);
        if(c!=null)
        {
            Map<String, Object> virtual = (Map<String, Object>) c.get("virtual");
            Map<String, Object> ids = (Map<String, Object>) virtual.get(Integer.valueOf(eventType));
            if(ids!=null)
            {
                Map<String, Object> pidMap = (Map<String, Object>) ids.get("pid");
                if(pidMap!=null)
                {
                    Integer pidValue = (Integer) pidMap.get(Long.valueOf(pid));
                    if(pidValue!=null)
                    {
                        // 随机数，随机回传配置的比例
                        int n=new Random().nextInt(1000);
                        if(n>=pidValue)
                        {
                            isCallback = false;
                        }
                    }
                }
            }
        }




//        conf := config.GetConfig().CallbackTest
//        if c, ok := conf[media]; ok {
//            i:=c.Virtual[eventType]
//            fmt.Printf("", i)
//            pid1:=i.Pid[pid]
//            fmt.Printf("", pid1)
//            if ids, ok := c.Virtual[eventType]; ok && ids.Pid[pid] != 0 {
//                // 随机数，随机回传配置的比例
//                if n := rand.Intn(1000); n >= ids.Pid[pid] {
//                    isCallback = false
//                }
//            }
//        }

        return isCallback;
    }


    public static QueryReqBack copyQueryReq(QueryReq params) throws InvocationTargetException, IllegalAccessException {
        QueryReqBack logBean = new QueryReqBack();
        BeanUtils.copyProperties(logBean, params);
        logBean.setPkg_channel(params.getPkgChannel());
        logBean.setEvent_time(params.getEventTime());
        logBean.setUser_id(params.getUserId() + "");
        logBean.setAndroid_id(params.getAndroidId());
        logBean.setLog_time(params.getLogTime());
        return logBean;
    }


    public static List<String> processDeviceId(Map<String, String> data) {
        String[] fields = {Consts.Imei, Consts.Oaid, Consts.Idfa, Consts.AndroidId};
        List<String> deviceIds = new ArrayList();
        for (String v : fields) {
            //但是需要加上这个条件，否则deviceIds集合里面会有空串或者null等值进行影响
            String value = data.get(v);
            if (StringUtils.isBlank(value) || StringUtils.contains(value,Consts.InvalidDeviceId) || "null".equals(value)) {
                continue;
            }
            deviceIds.add(data.get(v));
        }

        if (Consts.Ios.equals(data.get(Consts.Os)) || "1".equals(data.get(Consts.Soft))) {
            if (StringUtils.isNotBlank(data.get(Consts.IpUa))) {
                deviceIds.add(data.get(Consts.IpUa));
            }
//            if (StringUtils.isNotBlank(data.get(Consts.IpvxUa)) &&
//                    !data.get(Consts.IpvxUa).equals(data.get(Consts.IpUa))) {
//                deviceIds.add(data.get(Consts.IpvxUa));
//            }
        }
        return deviceIds;
    }

    public static String trimPrefix(String str,String prex)
    {
        int i = str.indexOf(prex);
        if(i==0)
        {
            String s = StringUtils.removeStart(str, prex);
            return s;
        }
        return str;
    }
}
