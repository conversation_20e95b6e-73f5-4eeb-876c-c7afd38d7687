package com.dewu.flink.delivery.common.bean;

import java.sql.Timestamp;

public class DpActivityRecallModel {

    private long id;
    private long uuid;
    private long user_id;
    private long user_type;
    private String algo_tags;
    private String ab_type;
    private long calendar_status;
    private long push_status;
    private long risk_level;
    private long award_status;
    private long is_del;
    private Timestamp create_time;
    private Timestamp modify_time;
//    Id             uint64    `gorm:"column:id;primary_key;"                       json:"id"`              // 主键ID
//    Uuid           string    `gorm:"column:uuid;default:"                         json:"uuid"`            // 用户设备号
//    UserId         uint64    `gorm:"column:user_id;default:0"                     json:"user_id"`         // 用户ID
//    UserType       int       `gorm:"column:user_type;default:0"                   json:"user_type"`       // 用户类型，1：mdpa实验组，2：非mdpa实验组
//    AlgoTags       string    `gorm:"column:algo_tags;default:"                    json:"algo_tags"`       // 算法承接信息
//    AbType         string    `gorm:"column:ab_type;default:"                      json:"ab_type"`         // AB分组，0：默认组，1：直塞津贴，2：浏览任务
//    CalendarStatus int       `gorm:"column:calendar_status;default:0"             json:"calendar_status"` // 日历状态，0：无订阅，1：已订阅
//    PushStatus     int       `gorm:"column:push_status;default:0"                 json:"push_status"`     // push状态，0：未发送，1：第一天已发送，2：第二天已发送
//    RiskLevel      int       `gorm:"column:risk_level;default:0"                  json:"risk_level"`      // 风控等级，10000：通过，展示任务，10002：疑似，发放兜底奖励，10001：拒绝，不展示任务
//    AwardStatus    int       `gorm:"column:award_status;default:0"                json:"award_status"`    // 奖励领取状态，0：未领取，1：第一天领1次，2：第一天领2次，3：第二天领1次
//    CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP" json:"create_time"`     // 创建时间
//    ModifyTime     time.Time `gorm:"column:modify_time;default:CURRENT_TIMESTAMP" json:"modify_time"`     // 修改时间
//    IsDel          int       `gorm:"column:is_del;default:0"                      json:"is_del"`          // 是否删除


    public Timestamp getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Timestamp create_time) {
        this.create_time = create_time;
    }

    public Timestamp getModify_time() {
        return modify_time;
    }

    public void setModify_time(Timestamp modify_time) {
        this.modify_time = modify_time;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUuid() {
        return uuid;
    }

    public void setUuid(long uuid) {
        this.uuid = uuid;
    }

    public long getUser_id() {
        return user_id;
    }

    public void setUser_id(long user_id) {
        this.user_id = user_id;
    }

    public long getUser_type() {
        return user_type;
    }

    public void setUser_type(long user_type) {
        this.user_type = user_type;
    }

    public String getAlgo_tags() {
        return algo_tags;
    }

    public void setAlgo_tags(String algo_tags) {
        this.algo_tags = algo_tags;
    }

    public String getAb_type() {
        return ab_type;
    }

    public void setAb_type(String ab_type) {
        this.ab_type = ab_type;
    }

    public long getCalendar_status() {
        return calendar_status;
    }

    public void setCalendar_status(long calendar_status) {
        this.calendar_status = calendar_status;
    }

    public long getPush_status() {
        return push_status;
    }

    public void setPush_status(long push_status) {
        this.push_status = push_status;
    }

    public long getRisk_level() {
        return risk_level;
    }

    public void setRisk_level(long risk_level) {
        this.risk_level = risk_level;
    }

    public long getAward_status() {
        return award_status;
    }

    public void setAward_status(long award_status) {
        this.award_status = award_status;
    }

    public long getIs_del() {
        return is_del;
    }

    public void setIs_del(long is_del) {
        this.is_del = is_del;
    }
}
