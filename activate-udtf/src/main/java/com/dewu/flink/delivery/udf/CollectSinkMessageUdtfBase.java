package com.dewu.flink.delivery.udf;

import com.dewu.flink.delivery.common.bean.RocketMqMess;
import com.dewu.flink.delivery.common.bean.SinkMessage;
import com.dewu.flink.delivery.common.consts.Consts;
import com.google.gson.Gson;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Optional;

public class CollectSinkMessageUdtfBase  extends TableFunction<Row> {

    public void collectSinkMessage(List<SinkMessage> sinkMessageList,String channel)  {
        if (sinkMessageList != null) {
            Gson gson = new Gson();
            for (int i = 0; i < sinkMessageList.size(); i++) {
                SinkMessage sinkMessage = sinkMessageList.get(i);
                String type = sinkMessage.getType();
                if (Consts.KAFKA.equals(type) || Consts.CALL_BACK_KAFKA.equals(type)|| Consts.DEBUG.equals(type)) {
                    Row row = new Row(8);
                    row.setField(0, type);
                    row.setField(1, sinkMessage.getData());
                    if(sinkMessage.getMessageTag()!=null)
                    {
                        row.setField(2, sinkMessage.getMessageTag());
                    }else
                    {
                        row.setField(2, sinkMessage.getMessageLog());
                    }
                    row.setField(3, sinkMessage.getMessageKey());
                    row.setField(4, sinkMessage.getDeliverTime());
                    row.setField(5, sinkMessage.getDesc());
                    row.setField(6, sinkMessage.getTtl());
                    row.setField(7, sinkMessage.getRedisKey());
                    collect(row);
                } else if (Consts.ROCKETMQ.equals(type)) {
                    RocketMqMess rocketMqMess = new RocketMqMess(sinkMessage.getData(), sinkMessage.getMessageTag(),channel);
                    Row row = new Row(8);
                    row.setField(0, Consts.ROCKETMQ);
                    row.setField(1, gson.toJson(rocketMqMess));
                    if(sinkMessage.getMessageTag()!=null)
                    {
                        row.setField(2, sinkMessage.getMessageTag());
                    }else
                    {
                        row.setField(2, sinkMessage.getMessageLog());
                    }
                    row.setField(3, sinkMessage.getMessageKey());
                    row.setField(4, sinkMessage.getDeliverTime());
                    row.setField(5, sinkMessage.getDesc());
                    row.setField(6, sinkMessage.getTtl());
                    row.setField(7, sinkMessage.getRedisKey());
                    collect(row);
                }
            }
        }
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(
                callContext ->
                        Optional.of(
                                DataTypes.ROW(
                                        DataTypes.STRING(),
                                        DataTypes.STRING(),
                                        DataTypes.STRING(),
                                        DataTypes.STRING(),
                                        DataTypes.BIGINT(),
                                        DataTypes.STRING(),
                                        DataTypes.BIGINT(),
                                        DataTypes.STRING()
                                )
                        )).build();
    }
}