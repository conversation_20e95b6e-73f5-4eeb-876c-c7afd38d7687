package com.dewu.flink.delivery.common.bean;

import java.util.Map;

public class QueryResult {
    private QueryResp queryResp;
    private Map<String,String> data;
    private String log;
    private long ttl;
    private String clickKey;
    public QueryResult(QueryResp queryResp, Map<String, String> data) {
        this.queryResp = queryResp;
        this.data = data;
    }

    public QueryResult(QueryResp queryResp, Map<String, String> data, String log) {
        this.queryResp = queryResp;
        this.data = data;
        this.log = log;
    }

    public QueryResult(QueryResp queryResp, Map<String, String> data, String log, long ttl) {
        this.queryResp = queryResp;
        this.data = data;
        this.log = log;
        this.ttl = ttl;
    }

    public QueryResult(QueryResp queryResp, Map<String, String> data, String log, long ttl, String clickKey) {
        this.queryResp = queryResp;
        this.data = data;
        this.log = log;
        this.ttl = ttl;
        this.clickKey = clickKey;
    }

    public String getClickKey() {
        return clickKey;
    }

    public void setClickKey(String clickKey) {
        this.clickKey = clickKey;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }

    public QueryResp getQueryResp() {
        return queryResp;
    }

    public Map<String, String> getData() {
        return data;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    @Override
    public String toString() {
        return "QueryResult{" +
                ", data=" + data +
                ", ttl=" + ttl +
                ", clickKey='" + clickKey + '\'' +
                '}';
    }
}