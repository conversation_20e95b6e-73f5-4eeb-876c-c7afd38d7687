package com.dewu.flink.delivery.common.service;

import com.dewu.flink.delivery.common.bean.*;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.consts.RedisConsts;
import com.dewu.flink.delivery.common.utils.*;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

public class AttributeServiceBackUp {

    public static Logger LOG = LoggerFactory.getLogger(AttributeServiceBackUp.class);
    private Gson gson = new Gson();

    public AttributeServiceBackUp() {

    }

    public static String[] types = {
            "", Consts.TypeRecallDefault
    };

    public JedisSington clickJedis;
    public JedisSington tagJedisSington;
    public JedisSington retainJedisSington;
    public HbaseUtil hbaseUtil;

    private String clickIp;
    private Integer clickPort;
    private String clickPassword;
    private Integer clickDB;
    private String tagIp;
    private Integer tagPort;
    private String tagPassword;
    private Integer tagDB;


    private String retainIp;
    private Integer retainPort;
    private String retainPassword;
    private Integer retainDB;

    private String hbaseZookeeperQuorum;
    private String hbasePassword;
    private String hbaseUsername;

    private AttributeServiceBackUp(Builder builder) throws IOException {
        this.clickIp = builder.clickIp;
        this.clickPort = builder.clickPort;
        this.clickPassword = builder.clickPassword;
        this.clickDB = builder.clickDB;
        this.tagIp = builder.tagIp;
        this.tagPort = builder.tagPort;
        this.tagPassword = builder.tagPassword;
        this.tagDB = builder.tagDB;

        this.retainIp = builder.retainIp;
        this.retainPort = builder.retainPort;
        this.retainPassword = builder.retainPassword;
        this.retainDB = builder.retainDB;

        this.hbaseZookeeperQuorum = builder.hbaseZookeeperQuorum;
        this.hbasePassword = builder.hbasePassword;
        this.hbaseUsername = builder.hbaseUsername;

        if (clickDB == null && clickPassword == null && clickIp != null && clickPort != null) {
            this.clickJedis = new JedisSington(clickIp, clickPort);
        } else if (clickDB != null && clickPassword != null && clickIp != null && clickPort != null) {
            this.clickJedis = new JedisSington(clickIp, clickPort, clickPassword, clickDB);
        }

        if (tagDB == null && tagPassword == null && tagIp != null && tagPort != null) {
            this.tagJedisSington = new JedisSington(tagIp, tagPort);
        } else if (tagDB != null && tagPassword != null && tagIp != null && tagPort != null) {
            this.tagJedisSington = new JedisSington(tagIp, tagPort, tagPassword, tagDB);
        }

        if (retainDB == null && retainPassword == null && retainIp != null && retainPort != null) {
            this.retainJedisSington = new JedisSington(retainIp, retainPort);
        } else if (retainDB != null && retainPassword != null && retainIp != null && retainPort != null) {
            this.retainJedisSington = new JedisSington(retainIp, retainPort, retainPassword, retainDB);
        }

        if (hbaseZookeeperQuorum != null && hbasePassword != null &&hbaseUsername!=null) {
            this.hbaseUtil = new HbaseUtil(hbaseUsername,hbasePassword,hbaseZookeeperQuorum);
        }
    }


    public static class Builder {

        private String clickIp;
        private Integer clickPort;
        private String clickPassword;
        private Integer clickDB;

        private String tagIp;
        private Integer tagPort;
        private String tagPassword;
        private Integer tagDB;

        private String retainIp;
        private Integer retainPort;
        private String retainPassword;
        private Integer retainDB;

        private String hbaseZookeeperQuorum;
        private String hbasePassword;
        private String hbaseUsername;

        public Builder hbaseZookeeperQuorum(String hbaseZookeeperQuorum) {
            this.hbaseZookeeperQuorum = hbaseZookeeperQuorum;
            return this;
        }

        public Builder hbasePassword(String hbasePassword) {
            this.hbasePassword = hbasePassword;
            return this;
        }

        public Builder hbaseUsername(String hbaseUsername) {
            this.hbaseUsername = hbaseUsername;
            return this;
        }
        public Builder retainIp(String retainIp) {
            this.retainIp = retainIp;
            return this;
        }

        public Builder retainPort(Integer retainPort) {
            this.retainPort = retainPort;
            return this;
        }

        public Builder retainPassword(String retainPassword) {
            this.retainPassword = retainPassword;
            return this;
        }

        public Builder retainDB(Integer retainDB) {
            this.retainDB = retainDB;
            return this;
        }

        public Builder clickIp(String clickIp) {
            this.clickIp = clickIp;
            return this;
        }

        public Builder clickPort(Integer clickPort) {
            this.clickPort = clickPort;
            return this;
        }

        public Builder clickPassword(String clickPassword) {
            this.clickPassword = clickPassword;
            return this;
        }

        public Builder clickDB(Integer clickDB) {
            this.clickDB = clickDB;
            return this;
        }

        public Builder tagIp(String tagIp) {
            this.tagIp = tagIp;
            return this;
        }

        public Builder tagPort(Integer tagPort) {
            this.tagPort = tagPort;
            return this;
        }

        public Builder tagPassword(String tagPassword) {
            this.tagPassword = tagPassword;
            return this;
        }

        public Builder tagDB(Integer tagDB) {
            this.tagDB = tagDB;
            return this;
        }

        public AttributeServiceBackUp build() throws IOException {
            return new AttributeServiceBackUp(this);
        }
    }



    public List<SinkMessage> startOrder(QueryReq params,String log) throws  InvocationTargetException, IllegalAccessException {
        List<SinkMessage> sinkMessageList = new ArrayList();
        callbackTodayAction(params, Consts.TypeStartOrder,sinkMessageList,log);
        SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeStartOrder, Consts.MqDelayTimeUserAction);
        sinkMessageList.add(sinkMessage);
        return sinkMessageList;
    }
    /**
     *功能描述
     * startPay 则返回sinkMessageList集合为0
     * pay 根据是否uuid是否有值，返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    public List<SinkMessage> order(QueryReq params,String log) throws  InvocationTargetException, IllegalAccessException {
        List<SinkMessage> sinkMessageList = new ArrayList();

        //该if逻辑，从Kafka消费消息不会走该处理逻辑，该处理逻辑仅在 下单的web接口中进行处理
        //---------------------------------------------------------
        if (Consts.SourceStartPay.equals(params.getSource())) {
            // 发起下单，以orderNo为key存入redis
            try {
                RedisUtils.set(retainJedisSington, RedisConsts.getOrderNoKey(params.getOrderNo()), gson.toJson(params), "nx", "ex", 48 * 60 * 60);
            } catch (Exception e) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单兜底，[redis]存储下单信息失败", gson.toJson(params));
                temp.setMessageLog(log);
                temp.setDeliverTime(124L);
                sinkMessageList.add(temp);
                LOG.error("[redis]存储下单信息失败:{}", params);
                return sinkMessageList;
            }
            return sinkMessageList;
        } else if (Consts.SourcePay.equals(params.getSource())) {
            String orderInfo = "";
            try {
                orderInfo = RedisUtils.get(retainJedisSington, RedisConsts.getOrderNoKey(params.getOrderNo()));
                if(StringUtils.isBlank(orderInfo))
                {
                    SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单兜底，没有查询到下单数据", gson.toJson(params));
                    temp.setMessageLog(log);
                    temp.setDeliverTime(123L);
                    sinkMessageList.add(temp);
                    return sinkMessageList;
                }
            } catch (Exception e) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单兜底，[redis]获取发起下单信息失败", gson.toJson(params));
                temp.setMessageLog(log);
                temp.setDeliverTime(123L);
                sinkMessageList.add(temp);
                LOG.error("[redis]获取发起下单信息失败:{}", params);
                return sinkMessageList;
            }
            // 赋值
            QueryReq queryReq1 = gson.fromJson(orderInfo, QueryReq.class);
            queryReq1.setEventTime(params.getEventTime());
            queryReq1.setSource(params.getSource());
            params = queryReq1;
        }
        //---------------------------------------------------------

        // 匹配回传媒体
        if (StringUtils.isNotBlank(params.getUuid())) {
            callbackTodayAction(params, Consts.TypeOrder,sinkMessageList,log);
            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeOrder, Consts.MqDelayTimeUserAction);
            sinkMessageList.add(sinkMessage);
            return sinkMessageList;
        }
        return sinkMessageList;
    }


    /**
     *功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     * <AUTHOR>
     * @date 2022/2/23
     * @param
     * @return
     */
    public List<SinkMessage> register(QueryReq params,String log) throws  InvocationTargetException, IllegalAccessException {
        List<SinkMessage> sinkMessageList = new ArrayList();
        callbackTodayAction(params, Consts.TypeRegister,sinkMessageList,log);
        SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeRegister, Consts.MqDelayTimeUserAction);
        sinkMessageList.add(sinkMessage);
        return sinkMessageList;
    }


    public List<SinkMessage> activate(QueryReq param,String message,boolean delay) throws Exception {
        List<SinkMessage> sinkMessageList = new ArrayList();
        param.setLogTime(TimeUtils.getTimeStampSecond());

        SearchClickResult deliveryClick = searchClickData(param, clickJedis,100,3);

        //-----debug------
        //-----debug------
        //-----debug------
        SearchClickResult retainClickFlag = searchClickDataDebug( retainJedisSington,deliveryClick.getClickKey());
        //-----debug------
        //-----debug------
        //-----debug------

        String log = " 【原始数据】:" + message+"【查询点击数据】"+deliveryClick.getLog();

        QueryReqBack logBean = BaseUtils.copyQueryReq(param);
        logBean.setType("激活归因");
        logBean.setTtl(deliveryClick.getTtl());
        logBean.setClickKey(deliveryClick.getClickKey());
        logBean.setRediskeys(gson.toJson(deliveryClick.getKeys()));

        //-----debug------
        //-----debug------
        //-----debug------
        if(deliveryClick.getData().size()>0&&retainClickFlag.getData().size()==0)
        {
            SinkMessage logMessage = new SinkMessage(Consts.DEBUG, "投放和实时点击数据不一致", gson.toJson(logBean),log);
            logMessage.setDeliverTime(72L);
            sinkMessageList.add(logMessage);
        }
        //-----debug------
        //-----debug------
        //-----debug------

        //如果上游延迟，那么此处将无法查询到点击数据，也不会延迟再处理一次。
        Map<String, String> clickData = deliveryClick.getData();
        if (!deliveryClick.getNew()) {
            //激活逻辑中，没有查询到点击数据，但是没有查询到该用户，则也会下发数据
            if(clickData == null || clickData.size() == 0)
            {
                SinkMessage logMessage = new SinkMessage(Consts.DEBUG, "激活归因中no click data", gson.toJson(logBean),log);
                logMessage.setDeliverTime(70L);
                sinkMessageList.add(logMessage);
            }
            SinkMessage logMessage = new SinkMessage(Consts.DEBUG, "激活归因中 非当天新用户", gson.toJson(logBean),log);
            logMessage.setDeliverTime(70L);
            sinkMessageList.add(logMessage);
            return sinkMessageList;
        }

        // 赋值用户类型
        clickData.put(Consts.UserType, Consts.UserTypeDeliveryNew);
        if ("1".equals(clickData.get(Consts.AntiSpam))) {
//            SinkMessage temp = new SinkMessage(Consts.DEBUG, "激活归因，反作弊标识,延迟处理", gson.toJson(logBean));
//            temp.setMessageLog(log + " 【click data】" + gson.toJson(clickData));
//            temp.setDeliverTime(71L);
//            sinkMessageList.add(temp);

            sinkMessageList.add(SinkMessageUtils.sendActiveDelayMsgDebug(param, clickData,retainClickFlag.getData(),0));
        } else {
            // 媒体回传
//            callbackActivate(param, clickData, Consts.RiskNo, sinkMessageList, log, logBean,retainClickFlag.getData(),deliveryClick.getTtl());
            log=log+"【实时侧点击数据】"+gson.toJson(retainClickFlag);
            callbackActivate(param, clickData, Consts.RiskNo, sinkMessageList, log, logBean,retainClickFlag.getData(),
                    deliveryClick.getTtl(),retainClickFlag.getTtl(),retainClickFlag.getClickKey());
        }

        return sinkMessageList;
    }


    /**
     * 功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     *
     * @param
     * @return
     * @date 2022/2/23
     */
    public String active(QueryReq params,long sleep,int count, String log) throws Exception {
        List<SinkMessage> sinkMessageList = new ArrayList();
        if (!recall(params, sinkMessageList,sleep,count,log)) {
            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeActivate, Consts.MqDelayTimeRecall);
            sinkMessageList.add(sinkMessage);
        }

        // 留存回传
        callbackRetain(params, sinkMessageList);

        Calendar calendar = Calendar.getInstance();
        int hourOfDay = TimeUtils.getHourOfDay(calendar);
        int minute = TimeUtils.getMinute(calendar);
        if (hourOfDay == 23 && minute >= 54) {
            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeRetain, Consts.MqDelayTimeUserAction);
            sinkMessageList.add(sinkMessage);
        }

        return gson.toJson(sinkMessageList);
    }


    /**
     * 功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合  回调kafka消息,kafka消息，rocketmq消息(tag MsgTagActiveRisk)
     *
     * @param
     * @return
     * @date 2022/2/23
     */
    public Boolean recall(QueryReq params, List<SinkMessage> sinkMessageList,long sleep,int count, String log) throws Exception {
        try {
            Boolean success = false;

            formatParams(params);
            //查询点击流数据
            SearchClickResult deliveryClick = searchClickData(params,clickJedis,sleep,count);
            //debug----
            //debug---- 查询实时侧点击数据的使用标志
            //debug----
            SearchClickResult clickDebug = searchClickDataDebug(retainJedisSington,deliveryClick.getClickKey());
            //debug----
            //debug----
            //debug----


            log=log+deliveryClick.getLog();

            params.setLogTime(TimeUtils.getTimeStampSecond());
            QueryReqBack logBean =BaseUtils.copyQueryReq(params);
            logBean.setType("recall");
            logBean.setTtl(deliveryClick.getTtl());//后面会赋值给SinkMessage带走
            logBean.setClickKey(deliveryClick.getClickKey());//后面会赋值给SinkMessage带走
            logBean.setRediskeys(gson.toJson(deliveryClick.getKeys()));//投放点击keys--对应table ->redis_keys

            Map<String, String> clickData = deliveryClick.getData();
            if (clickData == null || clickData.size() == 0) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理，没有查询点击数据", gson.toJson(logBean),log);
                temp.setDeliverTime(50L);
                sinkMessageList.add(temp);
                return success;
            }

            //debug----
            //debug----
            //debug----
//            if(deliveryClick.getData().size()>0&&clickDebug.getData().size()==0)
            if(clickDebug.getData().size()==0)
            {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "投放和实时点击数据不一致", gson.toJson(logBean),log);
                temp.setDeliverTime(72L);
                temp.setMessageKey(deliveryClick.getTtl()+"");
                temp.setRedisKey(deliveryClick.getClickKey());//对应表字段mess_click_key
                sinkMessageList.add(temp);
            }
//            //实时存储的点击数据应该充当辅助功能，不能因为没有查询到就不执行
//            if(clickDebug.getData().size()==0)
//            {
//                SinkMessage temp = new SinkMessage(Consts.DEBUG, "实时侧没有查询到点击数据", gson.toJson(logBean),log);
//                temp.setDeliverTime(86L);
//                temp.setMessageKey(deliveryClick.getTtl()+"");
//                temp.setRedisKey(deliveryClick.getClickKey());//对应表字段mess_click_key
//                sinkMessageList.add(temp);
//                return success;
//            }
            //debug----
            //debug----
            //debug----


            success = true;

            String switchTmp = clickData.get(Consts.Recall);
            if(StringUtils.isBlank(switchTmp))
            {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理中，点击流数据中recall为null", gson.toJson(logBean));
                temp.setMessageLog(log + " 【click redis data】:" + gson.toJson(clickData));
                temp.setDeliverTime(52L);
                sinkMessageList.add(temp);
                return success;
            }

            Integer inactive = 0;
            // 获取投放的用户沉默时间
            if (StringUtils.isNotBlank(clickData.get(Consts.Inactive))) {
                Integer t = IntegerUtils.valueOf(clickData.get(Consts.Inactive));
                if (t > 0) {
                    inactive = t;
                }
            }
//            判断点击时间，点击时间在首次日活时间-6小时到2分钟以内
            switch (switchTmp) {
                case Consts.TypeRecallDefault: {
                    Long et = LongUtils.valueOf(clickData.get(Consts.EventTime));
                    if (params.getDayFirstActiveTime() - et > 21600) {
                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "首次日活时间大于投放点击时间21600", gson.toJson(logBean));
                        temp.setMessageLog(log + " 【click redis data】:" + gson.toJson(clickData));
                        temp.setDeliverTime(53L);
                        temp.setTtl(deliveryClick.getTtl());
                        temp.setRedisKey(deliveryClick.getClickKey());
                        sinkMessageList.add(temp);
                        return success;
                    }
                    if ( et - params.getDayFirstActiveTime() > 120) {
                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "首次日活时间小于投放点击时间120", gson.toJson(logBean));
                        temp.setMessageLog(log + " 【click redis data】:" + gson.toJson(clickData));
                        temp.setDeliverTime(54L);
                        temp.setTtl(deliveryClick.getTtl());
                        temp.setRedisKey(deliveryClick.getClickKey());
                        sinkMessageList.add(temp);
                        return success;
                    }
                    clickData.put(Consts.UserType, Consts.UserTypeDeliveryOld);
                    break;
                }


                case Consts.TypeRecallSleepy: {
                    if (params.getLastActiveTime() == 0) {

                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "沉默时间最近激活时间为0", gson.toJson(logBean));
                        temp.setMessageLog(log + " 【click redis data】:" + gson.toJson(clickData));
                        temp.setDeliverTime(55L);
                        temp.setTtl(deliveryClick.getTtl());
                        temp.setRedisKey(deliveryClick.getClickKey());
                        sinkMessageList.add(temp);

                        return success;
                    }

                    if (params.getLastActiveTime() > 10000000000L) {
                        params.setLastActiveTime(params.getLastActiveTime() / 1000);
                    }

                    // 获取投放的用户沉默时间
                    if(inactive ==0) {
                        inactive = 180;
                    }

                    if (!(params.getLastActiveTime() + inactive * 24 * 3600 < TimeUtils.getTimeStampSecond(System.currentTimeMillis()))) {
                        logBean.setInactive(inactive + "");

                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "用户沉默时间小于当前时间", gson.toJson(logBean));
                        temp.setMessageLog(log + " 【click redis data】:" + gson.toJson(clickData));
                        temp.setDeliverTime(56L);
                        temp.setTtl(deliveryClick.getTtl());
                        temp.setRedisKey(deliveryClick.getClickKey());
                        sinkMessageList.add(temp);

                        return success;
                    }
                    clickData.put(Consts.UserType, Consts.UserTypeDeliverySleepy);
                    break;
                }
                default: {
                    SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活数据召回类型不匹配", gson.toJson(logBean));
                    temp.setMessageLog(log + " 【click redis data】:" + gson.toJson(clickData));
                    temp.setDeliverTime(57L);
                    temp.setTtl(deliveryClick.getTtl());
                    temp.setRedisKey(deliveryClick.getClickKey());
                    sinkMessageList.add(temp);
                    return success;
                }
            }

            // 超过180天的不用ipua归因
            if(inactive >= 180 && Consts.IpUa.equals(clickData.get(Consts.DeviceType))) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理，超过180天的不用ipua归因", gson.toJson(logBean));
                temp.setMessageLog(log + " 【click data】" + gson.toJson(clickData));
                temp.setDeliverTime(58L);
                temp.setTtl(deliveryClick.getTtl());
                temp.setRedisKey(deliveryClick.getClickKey());
                sinkMessageList.add(temp);
                return success;
            }

            if ("1".equals(clickData.get(Consts.AntiSpam))) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理，反作弊标识,延迟处理", gson.toJson(logBean));
                temp.setMessageLog(" 【click data】" + gson.toJson(clickData));
                temp.setDeliverTime(59L);
                sinkMessageList.add(temp);

                sinkMessageList.add(SinkMessageUtils.sendActiveDelayMsgDebug(params, clickData,clickDebug.getData(),deliveryClick.getTtl()));
                return success;
            }

            log=log+"【实时侧点击数据】"+gson.toJson(clickDebug);
            // 媒体回传
            callbackActivate(params, clickData, Consts.RiskNo, sinkMessageList, log, logBean,
                    clickDebug.getData(),deliveryClick.getTtl(),clickDebug.getTtl(),clickDebug.getClickKey());

            return success;
        }catch (Exception e)
        {
            LOG.error(log,e);
            throw e;
        }
    }

    /**
     * 功能描述 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息
     * params  uuid, Ios则 Idfa 和 Fcuuid，Android 则 AndroidId
     * redisdata:recall，event_time,media
     *
     * @param
     * @return
     * @date 2022/2/23
     */
    public void callbackTodayAction(QueryReq params, String eventType,
                                    List<SinkMessage> sinkMessageList, String log) throws InvocationTargetException, IllegalAccessException {
        // 参数格式处理
        formatParams(params);
        params.setLogTime(TimeUtils.getTimeStampSecond());

        QueryReqBack logBean= BaseUtils.copyQueryReq(params);
        logBean.setType("下单和注册:"+eventType);

        log = log + "【格式化后数据】:" + gson.toJson(params);
        // 加锁
        if (!RedisUtils.lock(retainJedisSington, RedisConsts.getCallbackLockKey(params.getUuid(), eventType))) {

            SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单和注册 lock"+eventType, gson.toJson(logBean),log);
            temp.setDeliverTime(30L);
            sinkMessageList.add(temp);
            return;
        }

        // 获取查找key
        String[] keys = getCallbackRedisKeys(params);
        logBean.setRediskeys(gson.toJson(keys));
        for (int i = 0; i < types.length; i++) {
            String logTemp="";
            String t = types[i];

            //-------------------------------
            //-------------------------------查询投放侧激活数据
            //-------------------------------
            RetainResult deliveryRetainRedis =RedisUtils.getDeliveryRetainData(clickJedis, keys, t);
            long flag = logCallbackTodayAction(deliveryRetainRedis.getData(), params, eventType);
            logTemp=log+"【"+flag+"】查询投放侧留存数据"+gson.toJson(deliveryRetainRedis.getData());
            logBean.setClickKey(deliveryRetainRedis.getRetainKey()+""+flag+" ttl:"+deliveryRetainRedis.getTtl());
            logBean.setTtl(deliveryRetainRedis.getTtl());
            //-------------------------------
            //-------------------------------
            //-------------------------------


            RetainResult retainResult = RedisUtils.getRetainData(retainJedisSington, keys, t,deliveryRetainRedis.getTtl(),deliveryRetainRedis.getRetainKey());
            Map<String, String> retainRedisData =retainResult.getData();
            if (retainRedisData.size() == 0) {
                logTemp = logTemp +  " 【循环第" + i + "次,未查询激活数据】 ";
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单和注册 未查询激活数据", gson.toJson(logBean),logTemp);
                temp.setDeliverTime(31L);
                sinkMessageList.add(temp);
                continue;
            }
            if ( "1".equals(retainRedisData.get(eventType))) {
                logTemp = logTemp +  " 【循环第" + i + "次， 下单，注册 已使用激活数据】 ";
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单和注册  已使用激活数据", gson.toJson(logBean),logTemp);
                temp.setDeliverTime(31L);
                sinkMessageList.add(temp);
                continue;
            }

            logTemp = logTemp +" 【redis激活数据：】"+gson.toJson(retainRedisData);
            if (Consts.TypeRecallDefault.equals(retainRedisData.get(Consts.Recall))
                    && eventType.equals(Consts.TypeOrder)) {
                // 普通用户召回回传激活后24小时内下单
                String eventTime = retainRedisData.get(Consts.EventTime);
                Long et = LongUtils.valueOf(eventTime);
                if (params.getEventTime() - et > 86400) {//24小时后下单不归因

                    logTemp = logTemp +" 【循环第" + i + "次，下单和注册 24小时后下单不归因】";
                    SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单和注册 24小时后下单不归因 ", gson.toJson(logBean),logTemp);
                    temp.setDeliverTime(32L);
                    sinkMessageList.add(temp);

                    continue;
                }
            } else {
                // 沉默用户召回回传激活后24小时内下单
                String eventTime = retainRedisData.get(Consts.EventTime);
                Long et = LongUtils.valueOf(eventTime);

                int etDay = TimeUtils.getyyyyMMdd(et);//激活留存时间
                int etBefore7Day = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -7);//下单时间前7天时间
                int etNowDay = TimeUtils.getyyyyMMdd(params.getEventTime());//下单时间
                if (Consts.KuaiShou.equals(retainRedisData.get(Consts.Media)) && eventType.equals(Consts.TypeOrder)) {
                    //快手回传激活后7日内下单数据
                    if (etDay < etBefore7Day) {
                        logTemp = logTemp + " 【循环第" + i + "次，下单和注册 快手回传激活后非7日内下单数据】 ";
                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单和注册 快手回传激活后非7日内下单数据", gson.toJson(logBean),logTemp);
                        temp.setDeliverTime(33L);
                        sinkMessageList.add(temp);

                        continue;
                    }
                } else {
                    // 只回传激活当天下单和注册
                    if (etDay != etNowDay) {

                        logTemp = logTemp + " 【循环第" + i + "次，下单和注册 非当天激活数据】 ";
                        SinkMessage temp = new SinkMessage(Consts.DEBUG,"下单和注册 非当天激活数据", gson.toJson(logBean),logTemp);
                        temp.setDeliverTime(34L);
                        sinkMessageList.add(temp);

                        continue;
                    }
                }
            }

            logTemp = logTemp + " 循环第" + i + "次，数据成功发送到Kafka";
            // 存入redis，标识该类型用户行为已回传，后续不需要再处理了。
            RedisUtils.setRetainData(retainJedisSington, keys, eventType, t);

            // 回传媒体
            sinkMessageList.add(SinkMessageUtils.sendCallbackMediaKafka(params, retainRedisData, eventType));
            //归因
            SinkMessage sinkMessage = SinkMessageUtils.sendKafka(params, retainRedisData, eventType);
            sinkMessage.setRedisKey(retainResult.getTtlFlag());//标志数据存储时长
            sinkMessage.setMessageLog(logTemp);
            sinkMessage.setTtl(retainResult.getTtl());
            sinkMessage.setDeliverTime(flag);
            sinkMessageList.add(sinkMessage);
        }
        return;
    }


    public long logCallbackTodayAction(Map<String, String> deliveryRetainRedis,QueryReq params,String eventType)
    {
        if (deliveryRetainRedis.size() == 0) {
//            return "投放侧未查询到激活数据";
            return 1L;
        }
        if ( "1".equals(deliveryRetainRedis.get(eventType))) {
//            return "投放侧激活数据已经使用="+eventType;
            return 2L;
        }

        if (Consts.TypeRecallDefault.equals(deliveryRetainRedis.get(Consts.Recall))
                && eventType.equals(Consts.TypeOrder)) {
            // 普通用户召回回传激活后24小时内下单
            String eventTime = deliveryRetainRedis.get(Consts.EventTime);
            Long et = LongUtils.valueOf(eventTime);
            if (params.getEventTime() - et > 86400) {
                return 3L;
//              return "24小时后下单不归因";
            }
        } else {
            String eventTime = deliveryRetainRedis.get(Consts.EventTime);
            Long et = LongUtils.valueOf(eventTime);

            int etDay = TimeUtils.getyyyyMMdd(et);
            int etBefore7Day = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -7);
            int etNowDay = TimeUtils.getyyyyMMdd(params.getEventTime());
            if (Consts.KuaiShou.equals(deliveryRetainRedis.get(Consts.Media)) && eventType.equals(Consts.TypeOrder)) {
                //快手回传激活后7日内下单数据
                if (etDay < etBefore7Day) {
                    return 4L;
//                    return "非激活后7日内快手下单数据";
                }
            } else  if (etDay != etNowDay) {
                // 只回传激活当天下单和注册
//                    return "非激活当天下单和注册";
                return 5L;
            }
        }
        return 6l;//代表归因
    }


    /**
     * 功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息
     *
     * @param
     * @return
     * @date 2022/2/23
     */
    public void callbackRetain(QueryReq params, List<SinkMessage> sinkMessageList) throws Exception {
        String log = " 【原始请求数据】:" + gson.toJson(params);
        params.setLogTime(TimeUtils.getTimeStampSecond());
        QueryReqBack logBean =BaseUtils.copyQueryReq(params);
        logBean.setType("次留或7留数据");

        if (!RedisUtils.lock(retainJedisSington, RedisConsts.getCallbackLockKey(params.getUuid(), Consts.TypeRetain))) {
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活留存数据lock", gson.toJson(logBean),log);
            temp.setDeliverTime(90L);
            sinkMessageList.add(temp);
            return;
        }

        // 获取查找key
        String[] keys = getCallbackRedisKeys(params);
        logBean.setRediskeys(gson.toJson(keys));

        for (String t : types) {
            //-------------------------------
            //-------------------------------查询投放侧激活数据
            //-------------------------------
            RetainResult deliveryRetainRedis =RedisUtils.getDeliveryRetainData(clickJedis, keys, t);
            long flag = logCallbackRetain(deliveryRetainRedis.getData(), params);
            logBean.setClickKey(deliveryRetainRedis.getRetainKey()+""+flag+" ttl:"+deliveryRetainRedis.getTtl());
            if(flag!=0)
            {
                log=log+"【"+flag+"】查询投放侧留存数据"+gson.toJson(deliveryRetainRedis.getData());
            }
            //-------------------------------
            //-------------------------------
            //-------------------------------
            RetainResult retainResult = RedisUtils.getRetainData(retainJedisSington, keys, t,deliveryRetainRedis.getTtl(),deliveryRetainRedis.getRetainKey());
            Map<String, String> data =retainResult.getData();
            if (data.size() == 0) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活留存数据 redis中没有查询留存数据", gson.toJson(logBean),log);
                temp.setDeliverTime(91L);
                sinkMessageList.add(temp);
                continue;
            }
            String retainType = "";
            Long et = LongUtils.valueOf(data.get(Consts.EventTime));
            int etDay = TimeUtils.getyyyyMMdd(et);
            int day1 = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -1);
            int day7 = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -7);
            if (etDay == day1) {
                retainType = Consts.TypeRetain;
            } else if (etDay == day7) {
                retainType = Consts.TypeRetain7;
            }
            log = log + " 【callbackRetain redis data:】" + gson.toJson(data);
            // 时间不对或者已回传过，不回传
            if (StringUtils.isBlank(retainType)) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "非次流或7留，不存储", gson.toJson(logBean),log);
                temp.setDeliverTime(93L);
                sinkMessageList.add(temp);
                continue;
            }
            if ( "1".equals(data.get(retainType))) {
                logBean.setType("次留或7留数据："+retainType);
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活留存数据已经存储，不再存储", gson.toJson(logBean),log);
                temp.setDeliverTime(92L);
                sinkMessageList.add(temp);
                continue;
            }

            RedisUtils.setRetainData(retainJedisSington, keys, retainType, t);

            sinkMessageList.add(SinkMessageUtils.sendCallbackMediaKafka(params, data, retainType));
            SinkMessage sinkMessage = SinkMessageUtils.sendKafka(params, data, retainType);
            sinkMessage.setMessageLog(log);
            sinkMessage.setTtl(retainResult.getTtl());
            sinkMessage.setRedisKey(retainResult.getTtlFlag());
            sinkMessage.setDeliverTime(flag);
            sinkMessageList.add(sinkMessage);
        }
    }


    public long logCallbackRetain(Map<String, String> deliveryRetainRedis,QueryReq params) throws Exception {
        if (deliveryRetainRedis.size() == 0) {
//           return "未查询到激活数据"；
            return 7L;
        }

        String retainType = "";
        Long et = LongUtils.valueOf(deliveryRetainRedis.get(Consts.EventTime));
        int etDay = TimeUtils.getyyyyMMdd(et);
        int day1 = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -1);
        int day7 = TimeUtils.getyyyyMMddByAddDay(params.getEventTime(), -7);
        if (etDay == day1) {
            retainType = Consts.TypeRetain;
        } else if (etDay == day7) {
            retainType = Consts.TypeRetain7;
        }
        if (StringUtils.isBlank(retainType)) {
            //return "非次流或7留，不存储"；
            return 8L;
        }

        if ( "1".equals(deliveryRetainRedis.get(retainType))) {
            //return "日活留存数据已经存储，不再存储"；
            return 9L;
        }
        return 0L;//旧值10
    }
    /**
     * 功能描述 待
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/14
     */
    // 格式化处理参数
    public QueryReq formatParams(QueryReq params) {
        // 去除空格

        params.setImei(StringUtils.trim(params.getImei()));
        params.setAndroidId(StringUtils.trim(params.getAndroidId()));
        params.setOaid(StringUtils.trim(params.getOaid()));
        params.setIdfa(StringUtils.trim(params.getIdfa()));
        params.setFcuuid(StringUtils.trim(params.getFcuuid()));
        params.setUuid(StringUtils.trim(params.getUuid()));
        params.setOs(StringUtils.lowerCase(params.getOs()));
        params.setIpua(BaseUtils.calIpUa(params.getIp(), params.getUa()));
        params.setIpvxua(BaseUtils.calIpUa(params.getIpvx(), params.getUa()));


        // os统一处理
        if ("iphone".equals(params.getOs())) {
            params.setOs(Consts.Ios);
        }

        if (params.getOs().equals(Consts.Ios)) {
            // 老版本只有uuid，进行赋值
            if ("".equals(params.getIdfa())&&params.getUuid().contains("-")) {
                params.setIdfa(params.getUuid());
            }
//            if params.Fcuuid == "" && strings.HasPrefix(params.Uuid, "UUID") {
//                params.Fcuuid = params.Uuid
//            }
            if("".equals(params.getFcuuid())&&params.getUuid().indexOf("UUID")==0){
                params.setFcuuid(params.getUuid());
            }
            params.setFcuuid(BaseUtils.trimPrefix(params.getFcuuid(),"UUID"));
            params.setUuid(BaseUtils.trimPrefix(params.getUuid(),"UUID"));
            params.setIdfa(BaseUtils.trimPrefix(params.getIdfa(),"UUID"));
//        params.Fcuuid = strings.TrimPrefix(params.Fcuuid, "UUID")
//        params.Uuid = strings.TrimPrefix(params.Uuid, "UUID")
//        params.Idfa = strings.TrimPrefix(params.Idfa, "UUID")


        } else {
            if ("".equals(params.getAndroidId()) && !"".equals(params.getUuid())) {
                params.setAndroidId(params.getUuid());
            }
        }

        if (params.getEventTime() > 10000000000L) {
            params.setEventTime(params.getEventTime() / 1000);
        } else if (params.getEventTime() == 0) {
//           params.EventTime = time.Now().Unix();// 时间戳到秒
            params.setEventTime(TimeUtils.getTimeStampSecond(System.currentTimeMillis()));
        }

        if (params.getDayFirstActiveTime() > 10000000000L) {
            params.setDayFirstActiveTime(params.getDayFirstActiveTime() / 1000);
        }
        return params;
    }


    /**
     * 功能描述 根据操作系统获取redis健
     * Ios 返回 Idfa 和 Fcuuid
     * Android 返回 AndroidId
     * 获取设备的唯一编号，ios和Android 获取方式不同
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/14
     */
    public String[] getCallbackRedisKeys(QueryReq params) {
        // 根据操作系统获取redis key
        String[] keys;
        if (params.getOs().equals(Consts.Ios)) {
            keys = new String[]{params.getIdfa(), params.getFcuuid()};
        } else {
            keys = new String[]{params.getAndroidId()};
        }

        return keys;
    }


    /**
     * 激活数据使用设备 id 匹配点击上报数据。
     *
     * @param params
     * @return
     */
    public SearchClickResult searchClickData(QueryReq params,JedisSington jedisSington,long sleep,int count) throws Exception {
        String deviceType = "";
        String log = "";
        List<String> keys;
        List<String> logkeys=new ArrayList<>();
        SearchClickResult searchClickResult=new SearchClickResult();
        Boolean isNew = false;
        if (params.getOs().equals(Consts.Ios)) {
            keys = BaseUtils.getDeviceKeys(params.getIdfa());
            log = log + " 【ios idfa keys】" +params.getIdfa()+" ==== "+ gson.toJson(keys);
            searchClickResult = RedisUtils.getClickData(jedisSington, keys, RedisConsts.RedisClick,sleep,count);
            logkeys.addAll(keys);
            if (searchClickResult.getData() != null && searchClickResult.getData().size() > 0) {
                deviceType = Consts.Idfa;
            } else if (StringUtils.isNotBlank(params.getIpua())) {
                keys = new ArrayList();
                log = log + "【ios Ipua key】=" + params.getIpua();
                keys.add(params.getIpua());
                if(StringUtils.isNotBlank(params.getIpvxua())) {
                    if(!params.getIpvxua().equals(params.getIpua())) {
                        keys.add(params.getIpvxua());
                    }
                }
                logkeys.addAll(keys);
                searchClickResult = RedisUtils.getClickData(jedisSington, keys, RedisConsts.RedisClick,sleep,count);
                if (searchClickResult.getData()!= null && searchClickResult.getData().size() > 0) {
                    // aso只能通过idfa归因
                    if (searchClickResult.getData().get(Consts.Media).equals(Consts.Aso)) {
                        searchClickResult.setData( new HashMap());
                        log = log + " iaso只能通过idfa归因 data重置为null";
                    } else {
                        deviceType = Consts.Ipua;
                    }
                }
            }

            isNew = validateNewUser(params.getFcuuid());
            if (isNew && !validateNewUser(params.getIdfa())) {
                isNew = false;
            }
        } else {
            Map<String, String> deviceIds = new LinkedHashMap();
            deviceIds.put(Consts.Oaid, params.getOaid());
            deviceIds.put(Consts.Imei, params.getImei());
            deviceIds.put(Consts.AndroidId, params.getAndroidId());
            for (Iterator it = deviceIds.entrySet().iterator(); it.hasNext(); ) {
                Map.Entry<String, String> obj = (Map.Entry) it.next();
                keys = BaseUtils.getDeviceKeys(obj.getValue());
                if (keys.size() == 0) {
                    continue;
                }
                log = log + " 【android key】"+ obj.getValue()+"==="+ gson.toJson(keys);
                logkeys.addAll(keys);
                searchClickResult = RedisUtils.getClickData(jedisSington, keys, RedisConsts.RedisClick,sleep,count);
                if (searchClickResult.getData()!= null && searchClickResult.getData().size() > 0) {
                    deviceType = obj.getKey();
                    break;
                }
            }
            isNew = validateNewUser(params.getAndroidId());
        }
        if (!"".equals(deviceType)) {
            searchClickResult.getData().put(Consts.DeviceType, deviceType);
        }

        searchClickResult.setNew(isNew);
        searchClickResult.setLog(log);
        searchClickResult.setKeys(logkeys);
        return searchClickResult;
    }


    public SearchClickResult searchClickDataDebug(JedisSington jedisSington,String clickKey) throws Exception {
        SearchClickResult searchClickResult=new SearchClickResult();

        if(StringUtils.isNotBlank(clickKey)) {
            searchClickResult = RedisUtils.getClickDataDebug(jedisSington, clickKey);
        }
        return searchClickResult;
    }


    /**
     * 如果没查到或者 date 是今天，则为新用户。
     * @param deviceId
     * @return
     */
    private Boolean validateNewUser(String deviceId) {
        if ("".equals(deviceId) || "null".equals(deviceId)) {
            return true;
        }
        String md5 = BaseUtils.md5(deviceId);
        String date = RedisUtils.hget(tagJedisSington, md5.substring(0, 5), deviceId);
        if (date == null || date.equals(TimeUtils.getCurrentDate())) {
            return true;
        }
        return false;
    }

    public void callbackActivate(QueryReq params, Map<String, String> deliveryClickData, int riskLevel, List<SinkMessage> sinkMessageList,
                                 String log, QueryReqBack logBean,Map<String, String> retainClickFlag,long clickTtl,long realtimeTtl,
                                 String realtimeClickKey){
        log = log + " 【click redis data】:" + gson.toJson(deliveryClickData);

        //--------debug
        //--------debug 后期注释掉，放开下面的逻辑
        //--------debug
        if("1".equals(retainClickFlag.get(Consts.Used)))
        {
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "点击数据已使用，不进行激活归因", gson.toJson(logBean),log);
            temp.setDeliverTime(85L);
            temp.setMessageKey(clickTtl+"");
            temp.setTtl(realtimeTtl);//对应ttl
            temp.setRedisKey(realtimeClickKey);//对应表字段mess_click_key
            sinkMessageList.add(temp);
            return;
        }
        //--------debug
        //--------debug
        //--------debug


        //测试阶段，不操作实际的redis，所以这里逻辑先注释掉，这样测试验收阶段，实时数据会比投放的数据多
        //Used字段前面存储点击数据时会存储，只是为""值，然后这里设置了1，所以当前面更新点击流数据的时候，会重置这里设置的1
//        if ("1".equals(deliveryClickData.get(Consts.Used))) {
//            SinkMessage logMessage = new SinkMessage(Consts.DEBUG, "", gson.toJson(logBean),log);
//            logMessage.setDeliverTime(11L);
//            sinkMessageList.add(logMessage);
//            return;
//        }


        deliveryClickData = pickActivateParam(deliveryClickData, params);
        Map<String, String> redisData = new HashMap();
        redisData.put(Consts.TypeActivate, "1");
        for (String k : deliveryClickData.keySet()) {
            redisData.put(k, deliveryClickData.get(k));
        }
        String[] keys = getCallbackRedisKeys(params);


        //--------debug
        //--------debug 激活数据，思考后期是新库还是投放库，要保证回传媒体数据不能重复化，那么必须使用投放库。
        //--------debug
        //点击数据的Consts.DeviceType 会为nul，首次激活查询不到点击数据时就会为null
        long deliveryFlag=0;
        RetainResult deliveryDebug = RedisUtils.getDeliveryRetainData(clickJedis, keys, deliveryClickData.get(Consts.Recall));
        if ("1".equals(deliveryDebug.getData().get(Consts.TypeActivate))
                && (StringUtils.isBlank(deliveryClickData.get(Consts.DeviceType)) ||
                StringUtils.isNotBlank(deliveryDebug.getData().get(Consts.DeviceType)))) {
            deliveryFlag=83L;
        }
        //--------debug
        //--------debug
        //--------debug


        //--------同步了投放侧ttl，后期放开，不同步ttl
        //--------同步了投放侧ttl，后期放开，不同步ttl
        RetainResult retainResult = RedisUtils.getRetainData(retainJedisSington, keys,  deliveryClickData.get(Consts.Recall),
                deliveryDebug.getTtl(),deliveryDebug.getRetainKey());
        //--------同步了投放侧ttl，后期放开，不同步ttl
        //--------同步了投放侧ttl，后期放开，不同步ttl


        Map<String, String> retainData =retainResult.getData();

        log = log + "【get redis mdata】:" + gson.toJson(retainResult);


        if ("1".equals(retainData.get(Consts.TypeActivate)) &&
                (StringUtils.isBlank(deliveryClickData.get(Consts.DeviceType))
                        || StringUtils.isNotBlank(retainData.get(Consts.DeviceType)))) {

            SinkMessage temp = new SinkMessage(Consts.DEBUG, "激活数据已经使用，不更新存储", gson.toJson(logBean),log);
            temp.setDeliverTime(80L);
            sinkMessageList.add(temp);

            return;
        }

        if (riskLevel!=Consts.RiskHigh) {
            log = log + " 【set redisData】:" + gson.toJson(redisData);
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "存储激活数据", gson.toJson(logBean),log);
            temp.setDeliverTime(81L);
            sinkMessageList.add(temp);

            if (Consts.TypeRecallDefault.equals(deliveryClickData.get(Consts.Recall))) {
                RedisUtils.mSetRetainData(retainJedisSington, keys, redisData, 48 * 3600);
            } else {
                RedisUtils.mSetRetainData(retainJedisSington, keys, redisData, 8 * 24 * 3600);
            }

            SinkMessage callbackMedia = SinkMessageUtils.sendCallbackMediaKafka(params, deliveryClickData, Consts.TypeActivate);
            sinkMessageList.add(callbackMedia);
        }


        // 标记点击已使用，测试阶段，不操作实际的redis，所以这里逻辑先注释掉
        //首次激活存在查询不到点击数据后进行激活归因，此时DeviceType未null,下一次查询到点击数据后再标记
        if(StringUtils.isNotBlank(deliveryClickData.get(Consts.DeviceType))){
            try {

                SinkMessage temp = new SinkMessage(Consts.DEBUG, "激活归因成功", gson.toJson(logBean),log);
                temp.setDeliverTime(84L);
                temp.setMessageKey(clickTtl+"");
                temp.setTtl(realtimeTtl);
                temp.setRedisKey(realtimeClickKey);//对应表字段mess_click_key
                sinkMessageList.add(temp);
                //--------debug
                //--------debug 后期换成点击库的redis,clickjedis,ex要为0
                //--------debug
                String key = RedisConsts.RedisClick + deliveryClickData.get(deliveryClickData.get(Consts.DeviceType));
                RedisUtils.hsetDeliveryClickUsedDebug(retainJedisSington,key ,0);
                //--------debug
                //--------debug
                //--------debug
            }catch (Exception e)
            {
                throw e;
            }
        }

        deliveryClickData.put(Consts.SpamLevel, String.valueOf(riskLevel));
        SinkMessage sinkMessage = SinkMessageUtils.sendKafka(params, deliveryClickData, Consts.TypeActivate);
        sinkMessage.setMessageLog(log);
        sinkMessage.setDeliverTime(deliveryFlag);
        sinkMessage.setTtl(retainResult.getTtl());
        sinkMessage.setRedisKey(retainResult.getTtlFlag());
        sinkMessageList.add(sinkMessage);

    }

    /**
     * 功能描述 -- java uuid 现分配，比对不上的
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/3/2
     */
    private Map<String, String> pickActivateParam(Map<String, String> data, QueryReq params) {
        data.put(Consts.UserId, String.valueOf(params.getUserId()));
        data.put(Consts.Fcuuid, params.getFcuuid());
        data.put(Consts.Uuid, params.getUuid());
        data.put(Consts.PkgChannel, params.getPkgChannel());
        data.put(Consts.EventTime, String.valueOf(params.getEventTime()));
        if (StringUtils.isBlank(data.get(Consts.DeviceType))) {
            data.put(Consts.UserType, Consts.UserTypeNature);
            data.put(Consts.Rid, BaseUtils.uuid());//分配uudi
            data.put(Consts.Tid, data.get(Consts.Rid));
            data.put(Consts.AppId, Consts.APP_DEWU);
        }
        return data;
    }



    /**
     * 功能描述
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息，rocketmq消息
     *
     * @param
     * @return
     * @date 2022/2/23
     */
    public String showActive(QueryReq params,long sleep,int count, String log,String hbaseTableName) throws Exception {
        List<SinkMessage> sinkMessageList = new ArrayList();
        params=formatParams(params);
        SearchShowResult showResult = searchShowData(params,hbaseTableName);
        if(showResult.getNew())
        {
            QueryReqBack logBean =BaseUtils.copyQueryReq(params);
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "新设备，不处理", gson.toJson(logBean));
            temp.setMessageLog(" 【log】:" + log);
            temp.setDeliverTime(51L);
            sinkMessageList.add(temp);
            //新设备，不处理
            return gson.toJson(sinkMessageList);
        }

        if (!showRecall(showResult,params, sinkMessageList,log,hbaseTableName)) {
            SinkMessage sinkMessage = SinkMessageUtils.sendUserActionDelayMsg(params, Consts.TypeActivate, Consts.MqDelayTimeRecall);
            sinkMessageList.add(sinkMessage);
        }
        return gson.toJson(sinkMessageList);
    }

    public Boolean showRecall(SearchShowResult showResult,QueryReq params, List<SinkMessage> sinkMessageList,String log,String hbaseTableName) throws Exception {
        try {
            Boolean success = false;
            log=log+" 【show data】"+gson.toJson(showResult)+"【params】"+gson.toJson(params);
            params.setLogTime(TimeUtils.getTimeStampSecond());
            QueryReqBack logBean =BaseUtils.copyQueryReq(params);
            logBean.setType("recall");

            Map<String, String> showData = showResult.getData();
            if (showData == null || showData.size() == 0) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理，没有查询曝光数据", gson.toJson(logBean),log);
                temp.setDeliverTime(50L);
                sinkMessageList.add(temp);
                return success;
            }

            success = true;
            String switchTmp = showData.get(Consts.Recall);
            if(switchTmp==null)
            {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理中，曝光数据中recall为null", gson.toJson(logBean));
                temp.setMessageLog(" 【show hbase data】:" + gson.toJson(showData));
                temp.setDeliverTime(52L);
                sinkMessageList.add(temp);
                return success;
            }

            Integer inactive = 0;
            // 获取投放的用户沉默时间
            if (StringUtils.isNotBlank(showData.get(Consts.Inactive))) {
                Integer t = IntegerUtils.valueOf(showData.get(Consts.Inactive));
                if (t > 0) {
                    inactive = t;
                }
            }


//           判断点击时间，点击时间在首次日活时间-6小时到2分钟以内
            switch (switchTmp) {
                case Consts.TypeRecallDefault: {
                    Long et = LongUtils.valueOf(showData.get(Consts.EventTime));
                    if (params.getDayFirstActiveTime() - et > 21600) {
                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "首次日活时间大于投放点击时间21600", gson.toJson(logBean));
                        temp.setMessageLog(log + " 【show hbase data】:" + gson.toJson(showData));
                        temp.setDeliverTime(53L);
                        sinkMessageList.add(temp);
                        return success;
                    }
                    if (et - params.getDayFirstActiveTime() > 120) {
                        SinkMessage temp = new SinkMessage(Consts.DEBUG, "首次日活时间小于投放点击时间120", gson.toJson(logBean));
                        temp.setMessageLog(log + " 【show hbase data】:" + gson.toJson(showData));
                        temp.setDeliverTime(54L);
                        sinkMessageList.add(temp);
                        return success;
                    }
                    showData.put(Consts.UserType, Consts.UserTypeDeliveryOld);
                    break;
                }

                default: {
                    SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活数据召回类型不匹配", gson.toJson(logBean));
                    temp.setMessageLog(" 【show hbase data】:" + gson.toJson(showData));
                    temp.setDeliverTime(57L);
                    sinkMessageList.add(temp);
                    return success;
                }
            }

            // 超过180天的不用ipua归因
            if(inactive >= 180 && Consts.IpUa.equals(showData.get(Consts.DeviceType))) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理，超过180天的不用ipua归因", gson.toJson(logBean));
                temp.setMessageLog(log + " 【show data】" + gson.toJson(showData));
                temp.setDeliverTime(58L);
                sinkMessageList.add(temp);
                return success;
            }

            if ("1".equals(showData.get(Consts.AntiSpam))) {
                SinkMessage temp = new SinkMessage(Consts.DEBUG, "日活处理，反作弊标识,延迟处理", gson.toJson(logBean));
                temp.setMessageLog(log + " 【show data】" + gson.toJson(showData));
                temp.setDeliverTime(59L);
                sinkMessageList.add(temp);

                sinkMessageList.add(SinkMessageUtils.sendActiveDelayMsgShow(params, showResult,null,0));
                return success;
            }

            // 媒体回传
            callbackShowActivate(params, showResult, Consts.RiskNo, sinkMessageList, log,logBean,hbaseTableName);
            return success;
        }catch (Exception e)
        {
            LOG.error(log,e);
            throw e;
        }
    }


    public void callbackShowActivate(QueryReq params, SearchShowResult showResult, int riskLevel, List<SinkMessage> sinkMessageList,
                                     String log, QueryReqBack logBean,String hbaseTableName) throws Exception {
        Map<String, String> showData = showResult.getData();
        if (Consts.USE_FLAG.equals(showData.get(Consts.Used))) {
            SinkMessage logMessage = new SinkMessage(Consts.DEBUG, "曝光数据已使用", gson.toJson(logBean),log);
            logMessage.setDeliverTime(11L);
            sinkMessageList.add(logMessage);
            return;
        }

        showData = pickActivateParam(showData, params);

        Map<String, String> saveData = new HashMap();
        saveData.put(Consts.TypeActivate,Consts.USE_FLAG);
        for (String k : showData.keySet()) {
            saveData.put(k, showData.get(k));
        }

        String[] keys = getCallbackRedisKeys(params);
        SearchShowResult result = hbaseUtil.getActivateData(keys, showData.get(Consts.Recall),hbaseTableName);
        Map<String, String> activeData =result.getData();

        log=log+"【ActivateData】"+gson.toJson(activeData);
        if (Consts.USE_FLAG.equals(activeData.get(Consts.TypeActivate)) &&
                (StringUtils.isBlank(showData.get(Consts.DeviceType))
                        || StringUtils.isNotBlank(activeData.get(Consts.DeviceType)))) {

            SinkMessage temp = new SinkMessage(Consts.DEBUG, "激活数据已经使用，不更新存储", gson.toJson(logBean),log);
            temp.setDeliverTime(80L);
            sinkMessageList.add(temp);

            return;
        }

        if (riskLevel!=Consts.RiskHigh) {
            if (Consts.TypeRecallDefault.equals(showData.get(Consts.Recall))) {
                hbaseUtil.setActivateData(keys,saveData,hbaseTableName);
//                SinkMessage sinkMessage = SinkMessageUtils.sendCallbackMediaKafka(params, showData, Consts.TypeActivate);
//                sinkMessageList.add(sinkMessage);
            }
        }

        //表示查询到曝光数据的方式
        if(StringUtils.isNotBlank(showData.get(Consts.DeviceType))){
            try {
                String keytemp = showData.get(showData.get(Consts.DeviceType));
                String key = showResult.getKey();
                hbaseUtil.setShowUsed(RedisConsts.HbaseShow+key,showData,hbaseTableName);
                if(!key.equals(keytemp))
                {
                    SinkMessage temp = new SinkMessage(Consts.DEBUG, "key查询问题导致", gson.toJson(logBean),log);
                    temp.setDeliverTime(112L);
                    sinkMessageList.add(temp);
                }
                if(StringUtils.isBlank(key))
                {
                    SinkMessage temp = new SinkMessage(Consts.DEBUG, "key为null，有问题啊", gson.toJson(logBean),log);
                    temp.setDeliverTime(111L);
                    sinkMessageList.add(temp);
                    throw new Exception(log);
                }
            }catch (Exception e)
            {
                throw new Exception(log,e);
            }
        }

        showData.put(Consts.SpamLevel, String.valueOf(riskLevel));
        SinkMessage sinkMessage = SinkMessageUtils.sendKafka(params, showData, Consts.TypeActivate);
        sinkMessage.setMessageLog(log);
        sinkMessageList.add(sinkMessage);

    }

    public List<SinkMessage> showActivate(QueryReq param,String message,boolean delay,String hbaseTableName) throws Exception {
        List<SinkMessage> sinkMessageList = new ArrayList();
        param.setLogTime(TimeUtils.getTimeStampSecond());

        SearchShowResult deliveryClick = searchShowData(param,hbaseTableName);

        Map<String, String> clickData = deliveryClick.getData();
//        if (!deliveryClick.getNew()) {
//            return sinkMessageList;
//        }

        // 赋值用户类型
        clickData.put(Consts.UserType, Consts.UserTypeDeliveryNew);
        if ("1".equals(clickData.get(Consts.AntiSpam))) {
//            sinkMessageList.add(SinkMessageUtils.sendActiveDelayMsgDebug(param, clickData,"",0));
        } else {
//            callbackActivate(param, clickData, Consts.RiskNo, sinkMessageList, log, logBean,"retainClickFlag.getData()",0);
        }

        return sinkMessageList;
    }



    public SearchShowResult searchShowData(QueryReq params,String hbaseTableName) throws Exception {
        String deviceType = "";
        List<String> keys;
        SearchShowResult showResult=new SearchShowResult();
        Boolean isNew = false;
        if (params.getOs().equals(Consts.Ios)) {
            keys = BaseUtils.getDeviceKeys(params.getIdfa());
            showResult = hbaseUtil.getShowData(keys,hbaseTableName);
            if (showResult.getData().size() > 0) {
                deviceType = Consts.Idfa;
            } else if (StringUtils.isNotBlank(params.getIpua())) {
                keys = new ArrayList();
                keys.add(params.getIpua());
                if(StringUtils.isNotBlank(params.getIpvxua())) {
                    if(!params.getIpvxua().equals(params.getIpua())) {
                        keys.add(params.getIpvxua());
                    }
                }
                showResult = hbaseUtil.getShowData(keys,hbaseTableName);
                if (showResult.getData().size() > 0) {
                    // aso只能通过idfa归因
                    if (showResult.getData().get(Consts.Media).equals(Consts.Aso)) {
                        showResult.setData(new HashMap());
                    } else {
                        deviceType = Consts.Ipua;
                    }
                }
            }

            isNew = validateNewUser(params.getFcuuid());
            if (isNew && !validateNewUser(params.getIdfa())) {
                isNew = false;
            }
        } else {
            Map<String, String> deviceIds = new LinkedHashMap();
            deviceIds.put(Consts.Oaid, params.getOaid());
            deviceIds.put(Consts.Imei, params.getImei());
            deviceIds.put(Consts.AndroidId, params.getAndroidId());
            for (Iterator it = deviceIds.entrySet().iterator(); it.hasNext(); ) {
                Map.Entry<String, String> obj = (Map.Entry) it.next();
                keys = BaseUtils.getDeviceKeys(obj.getValue());
                if (keys.size() == 0) {
                    continue;
                }
                showResult = hbaseUtil.getShowData(keys,hbaseTableName);
                if (showResult.getData().size() > 0) {
                    deviceType = obj.getKey();
                    break;
                }
            }
            isNew = validateNewUser(params.getAndroidId());
        }
        if (!"".equals(deviceType)) {
            //请求参数通过Consts.DeviceType查询到曝光数据，但是在曝光数据中key并不一定是Consts.DeviceType
            //比如请求参数oaid通过md5后查询到曝光数据，但是曝光数据oaid为空，imei和请求参数oaid通过md5后相同
            //这也就解释了点击数据为何会莫名其妙被使用了
            showResult.getData().put(Consts.DeviceType, deviceType);
        }
        showResult.setNew(isNew);
        return showResult;
    }



}