package com.dewu.flink.delivery.common.bean;

public class UserActionMsg {

    private long userid;//             int64       `json:"userid"`
    private Object user_id     ;//         interface{} `json:"user_id"`

    private String device_uuid=""   ;//       string      `json:"device_uuid"`
    private String uuid=""      ;//          string      `json:"uuid"`
    private String idfa=""        ;//        string      `json:"idfa"`
    private String fcuuid =""      ;//       string      `json:"fcuuid"`
    private String caid  =""       ;//       string      `json:"caid"`
    private String shumei_id =""       ;//    string      `json:"shumei_id"`
    private String event    =""          ;// string      `json:"event"`
    private String oaid    =""    ;//        string      `json:"oaid"`
    private String imei    =""    ;//        string      `json:"imei"`
    private String android_id =""   ;//       string      `json:"android_id"`
    private String user_agent =""   ;//       string      `json:"user_agent"`
    private String os       =""    ;//       string      `json:"os"`
    private  String lib     =""     ;//       string      `json:"lib"`
    private  String platform   =""  ;//       string      `json:"platform"`
    private String android_channel="" ;//     string      `json:"android_channel"`
    private String channel    =""    ;//     string      `json:"channel"`
    private String event_time  =""    ;//     string      `json:"event_time"`
    private String create_time  =""   ;//     string      `json:"create_time"`
    private String ip         =""    ;//     string      `json:"ip"`
    private String ipvx     =""       ;//    string      `json:"ipvx"`
    private String last_active_time=""  ;//    string      `json:"last_active_time"`
    private int type            ;//    int         `json:"type"` // 类型（0:注册，1:登录）
    private String version  =""       ;//    string      `json:"version"`
    private String app_version  =""    ;//    string      `json:"app_version"`
    private String day_first_active_time="";//  string      `json:"day_first_active_time"` // 用户当天第一次活跃时间
    private String receive_time=""     ;//    string      `json:"receive_time"`          // 接收时间

    public long getUserid() {
        return userid;
    }

    public void setUserid(long userid) {
        this.userid = userid;
    }

    public Object getUser_id() {
        return user_id;
    }

    public void setUser_id(Object user_id) {
        this.user_id = user_id;
    }

    public String getDevice_uuid() {
        return device_uuid;
    }

    public void setDevice_uuid(String device_uuid) {
        this.device_uuid = device_uuid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    public String getFcuuid() {
        return fcuuid;
    }

    public void setFcuuid(String fcuuid) {
        this.fcuuid = fcuuid;
    }

    public String getCaid() {
        return caid;
    }

    public void setCaid(String caid) {
        this.caid = caid;
    }

    public String getShumei_id() {
        return shumei_id;
    }

    public void setShumei_id(String shumei_id) {
        this.shumei_id = shumei_id;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getAndroid_id() {
        return android_id;
    }

    public void setAndroid_id(String android_id) {
        this.android_id = android_id;
    }

    public String getUser_agent() {
        return user_agent;
    }

    public void setUser_agent(String user_agent) {
        this.user_agent = user_agent;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getLib() {
        return lib;
    }

    public void setLib(String lib) {
        this.lib = lib;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getAndroid_channel() {
        return android_channel;
    }

    public void setAndroid_channel(String android_channel) {
        this.android_channel = android_channel;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getEvent_time() {
        return event_time;
    }

    public void setEvent_time(String event_time) {
        this.event_time = event_time;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getIpvx() {
        return ipvx;
    }

    public void setIpvx(String ipvx) {
        this.ipvx = ipvx;
    }

    public String getLast_active_time() {
        return last_active_time;
    }

    public void setLast_active_time(String last_active_time) {
        this.last_active_time = last_active_time;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getApp_version() {
        return app_version;
    }

    public void setApp_version(String app_version) {
        this.app_version = app_version;
    }

    public String getDay_first_active_time() {
        return day_first_active_time;
    }

    public void setDay_first_active_time(String day_first_active_time) {
        this.day_first_active_time = day_first_active_time;
    }

    public String getReceive_time() {
        return receive_time;
    }

    public void setReceive_time(String receive_time) {
        this.receive_time = receive_time;
    }
}