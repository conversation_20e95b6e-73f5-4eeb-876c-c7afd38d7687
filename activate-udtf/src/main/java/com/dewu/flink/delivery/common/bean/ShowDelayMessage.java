package com.dewu.flink.delivery.common.bean;

import java.util.Map;

public class ShowDelayMessage {

    QueryReq active;
    SearchShowResult showResult;//曝光数据

    Map<String, String> retainClick;//留存点击数据标志 debug
    long ttl;//曝光数据ttl，用于设置留存点击数据ttl，对齐过期时间

    public ShowDelayMessage() {
    }

    public ShowDelayMessage(QueryReq active, SearchShowResult showResult) {
        this.active = active;
        this.showResult = showResult;
    }

    public ShowDelayMessage(QueryReq active, SearchShowResult showResult, Map<String, String> retainClick) {
        this.active = active;
        this.showResult = showResult;
        this.retainClick = retainClick;
    }

    public ShowDelayMessage(QueryReq active, SearchShowResult showResult, Map<String, String> retainClick, long ttl) {
        this.active = active;
        this.showResult = showResult;
        this.retainClick = retainClick;
        this.ttl=ttl;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }

    public Map<String, String> getRetainClick() {
        return retainClick;
    }

    public void setRetainClick(Map<String, String> retainClick) {
        this.retainClick = retainClick;
    }

    public QueryReq getActive() {
        return active;
    }

    public void setActive(QueryReq active) {
        this.active = active;
    }

    public SearchShowResult getShowResult() {
        return showResult;
    }

    public void setShowResult(SearchShowResult showResult) {
        this.showResult = showResult;
    }
}
