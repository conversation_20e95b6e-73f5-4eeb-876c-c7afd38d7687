package com.dewu.flink.delivery.common.bean;

import com.google.gson.Gson;

import java.util.HashMap;
import java.util.Map;

public class SearchShowResult {
    private String mess;
    private String key;
    private Map<String,String> data;
    private Boolean isNew;

    public SearchShowResult() {
        data=new HashMap<>();
    }

    public SearchShowResult(String mess) {
        this.mess = mess;
        this.data=new Gson().fromJson(mess, HashMap.class);
    }

    public SearchShowResult(String mess, String key) {
        this.mess = mess;
        this.key = key;
        this.data=new Gson().fromJson(mess, HashMap.class);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getMess() {
        return mess;
    }

    public void setMess(String mess) {
        this.mess = mess;
    }

    public Map<String, String> getData() {
        return data;
    }

    public void setData(Map<String, String> data) {
        this.data = data;
    }

    public Boolean getNew() {
        return isNew;
    }

    public void setNew(Boolean aNew) {
        isNew = aNew;
    }
}
