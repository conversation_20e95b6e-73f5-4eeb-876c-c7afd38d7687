package com.dewu.flink.delivery.common.api;

import com.dewu.flink.delivery.common.bean.*;
import com.dewu.flink.delivery.common.consts.Consts;
import com.dewu.flink.delivery.common.service.RocketMqAttributeService;
import com.dewu.flink.delivery.common.utils.BaseUtils;
import com.dewu.flink.delivery.common.utils.TimeUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


public class RocketMqAction {

    public static Logger LOG = LoggerFactory.getLogger(RocketMqAction.class);
    private RocketMqAttributeService rocketMqAttributeService;
    public Gson gson = new Gson();


    public RocketMqAction(String ip, int port) throws IOException {
        this.rocketMqAttributeService = new RocketMqAttributeService(ip,port);
    }

    public RocketMqAction(String ip, int port,int db) throws IOException {
        this.rocketMqAttributeService = new RocketMqAttributeService(ip,port,db);
    }

    public RocketMqAction(String ip, int port,int db,String password) throws IOException {
        this.rocketMqAttributeService = new RocketMqAttributeService(ip,port,db,password);
    }

    public RocketMqAction(String ip, int port,int db,String password,String url) throws IOException {
        this.rocketMqAttributeService = new RocketMqAttributeService(ip,port,db,password,url);
    }

    public RocketMqAction(String clickIp, int clickPort, String clickPassword, int clickDB,
                          String tagIp, int tagPort, String tagPassword, int tagDB,
                          String retainIp, int retainPort, String retainPassword, int retainDB,
                          String url) throws IOException {
        this.rocketMqAttributeService = new RocketMqAttributeService(clickIp, clickPort, clickPassword, clickDB,
                tagIp, tagPort, tagPassword, tagDB,
                retainIp, retainPort, retainPassword, retainDB, url);
    }


    public RocketMqAction(String hbaseUserName,String hbasePassword,String hbaseZookeeperQuorum,
                          String tagIp, int tagPort, String tagPassword, int tagDB,String url) throws IOException {
        this.rocketMqAttributeService = new RocketMqAttributeService(hbaseUserName,hbasePassword,hbaseZookeeperQuorum,
                tagIp,tagPort,tagPassword,tagDB,url);
    }


    private static volatile RocketMqAction singleton;

    public RocketMqAction() {
    }

    public static RocketMqAction getInstance(String ip, int port) throws IOException {
        if (singleton == null) {
            synchronized (RocketMqAction.class) {
                if (singleton == null) {
                    singleton = new RocketMqAction(ip,port);
                }
            }
        }
        return singleton;
    }

    public static RocketMqAction getInstance(String ip, int port,int db) throws IOException {
        if (singleton == null) {
            synchronized (RocketMqAction.class) {
                if (singleton == null) {
                    singleton = new RocketMqAction(ip,port,db);
                }
            }
        }
        return singleton;
    }

    public static RocketMqAction getInstance(String ip, int port,int db,String password) throws IOException {
        if (singleton == null) {
            synchronized (RocketMqAction.class) {
                if (singleton == null) {
                    singleton = new RocketMqAction(ip,port,db,password);
                }
            }
        }
        return singleton;
    }

    public static RocketMqAction getInstance(String ip, int port,int db,String password,String url) throws IOException {
        if (singleton == null) {
            synchronized (RocketMqAction.class) {
                if (singleton == null) {
                    singleton = new RocketMqAction(ip,port,db,password,url);
                }
            }
        }
        return singleton;
    }

    public static RocketMqAction getInstance(String clickIp, int clickPort, String clickPassword, int clickDB,
                                             String tagIp, int tagPort, String tagPassword, int tagDB,
                                             String retainIp, int retainPort, String retainPassword, int retainDB,
                                             String url) throws IOException {
        if (singleton == null) {
            synchronized (RocketMqAction.class) {
                if (singleton == null) {
                    singleton = new RocketMqAction(clickIp, clickPort, clickPassword, clickDB,
                            tagIp, tagPort, tagPassword, tagDB,
                            retainIp, retainPort, retainPassword, retainDB,
                            url);
                }
            }
        }
        return singleton;
    }


    public static RocketMqAction getHbaseInstance(String hbaseUserName,String hbasePassword,String hbaseZookeeperQuorum,
                                                  String tagIp, int tagPort, String tagPassword, int tagDB,
                                                  String url) throws IOException {
        if (singleton == null) {
            synchronized (RocketMqAction.class) {
                if (singleton == null) {
                    singleton = new RocketMqAction(hbaseUserName,hbasePassword,hbaseZookeeperQuorum,
                            tagIp,tagPort,tagPassword,tagDB,
                            url);
                }
            }
        }
        return singleton;
    }


    /**
     * 功能描述 处理延迟数据
     * 返回sinkMessageList集合为0或者返回sinkMessageList集合 回调kafka消息，kafka消息,
     * rocketmq消息(tag MsgTagActiveRisk)
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/18
     */
    public String dealActivate(Message message) throws Exception {
        // 丢弃错误,避免重复消费
        switch (message.getTags()) {
            case Consts.MsgTagActiveRisk: {
                return gson.toJson(rocketMqAttributeService.riskActivate(message));
            }
            //承接做
//            case  Consts.MsgTagLtvQuery: {
//            }
            case Consts.MsgTagUserAction: {
                return gson.toJson(rocketMqAttributeService.userAction(message));
            }
            //承接做
//            case Consts.MsgTagActivity: {
//            }
            default:
                //do nothing
                break;
        }
        return null;
    }

    public String dealDelayShowActivate(Message message,String hbaseTableName) throws Exception {
        // 丢弃错误,避免重复消费
        switch (message.getTags()) {
            case Consts.MsgTagActiveRisk: {
                return gson.toJson(rocketMqAttributeService.riskShowActivate(message,hbaseTableName));
            }
            case Consts.MsgTagUserAction: {
                return gson.toJson(rocketMqAttributeService.showUserAction(message,hbaseTableName));
            }
            default:
                //do nothing
                break;
        }
        return null;
    }


    public List<SinkMessage> processActive(String message) throws Exception {
        Gson gson = new Gson();
        String log ="";
        ActiveMessage data;
        try {
            data = gson.fromJson(message, ActiveMessage.class);
            log = message;
        } catch (Exception e) {
            LOG.error( "[rocketMq][userAction]Unmarshal {}",message,e);
            List<SinkMessage> sinkMessageList =new ArrayList<>();
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "rocketmq日活，解析异常", "",-1L);
            temp.setMessageLog(message);
            sinkMessageList.add(temp);
            return sinkMessageList;
        }
        QueryReq params = formatDayActive(data);

        String mess = rocketMqAttributeService.active(params,0,1,log);
        List<SinkMessage> sinkMessageList = gson.fromJson(mess, new TypeToken<List<SinkMessage>>() {
        }.getType());
        return sinkMessageList;

    }

    public QueryReq formatDayActive(ActiveMessage data) {
        QueryReq params=new QueryReq();
        params.setImei(data.getImei());
        params.setOaid(data.getOaid());
        params.setUuid(data.getDevice_id());
        params.setOs(data.getPlatform());

        if(StringUtils.isNotBlank(data.getAdvUserAgent()))
        {
            params.setUa(data.getAdvUserAgent());
        }else  if(StringUtils.isNotBlank(data.getAdv_user_agent()))
        {
            params.setUa(data.getAdv_user_agent());
        }

        params.setIp(data.getIp());
        params.setEventTime(data.getEvent_time());
        params.setUserId(data.getUser_id());
        params.setAppVersion(data.getVersion());
        params.setSource(Consts.SourceActive);

        params.setDayFirstActiveTime(data.getEvent_time());
        if (data.getHeaders() != null) {
            params.setIdfa(data.getHeaders().getIdfa());
            params.setFcuuid(data.getHeaders().getFcuuid());
            params.setAndroidId(data.getHeaders().getDuuuid());
            params.setAppId(data.getHeaders().getAppid());
            params.setShumeiId(data.getHeaders().getShumeiid());
        }
        return params;
    }

    /**
     * 功能描述 兜底kafka用户行为消费逻辑，需要回传数据到Kafka和mq
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/18
     */
    public List<SinkMessage> processOrder(String message) throws Exception {
        Gson gson = new Gson();
        OrderMessage data;
        try {
            data = gson.fromJson(message, OrderMessage.class);
            if(data==null)
            {
                return null;
            }
        } catch (Exception e) {
            //解析失败则调用http接口获取不到正确数据，则riskLevel = Consts.RiskNo组装发送到Kafka
            LOG.error("[jsoniter][ProcessOrder]解析mq数据失败 {}", message, e);
            List<SinkMessage> sinkMessageList=new ArrayList<>();
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单兜底，解析mq数据失败，默认为0", "",-1L);
            temp.setMessageLog(message);
            sinkMessageList.add(temp);
            return sinkMessageList;
        }

        if (data.getOpenApiTerminalCode() != 0) {
            List<SinkMessage> sinkMessageList=new ArrayList<>();
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "下单兜底，openApiTerminalCode不为0", gson.toJson(data),message);
            temp.setDeliverTime(37L);
            sinkMessageList.add(temp);
            return sinkMessageList;
        }

        SinkMessage sinkMessageLog=null;
        QueryReq params = new QueryReq();
        switch (data.getOrderStatus()) {
            case 1000: {
                // 生成订单
                params.setOs(data.getPlatform());
                params.setImei(data.getRequestInfo().getImei());
                params.setOaid(data.getRequestInfo().getOaid());
                params.setUuid(data.getRequestInfo().getDeviceId());
                params.setOrderNo(data.getOrderNo());
                params.setUserId(data.getRequestInfo().getUid());
                params.setSource(Consts.SourceStartPay);
                break;
            }
            case 2000: {
                // 支付订单
                params.setOrderNo(data.getOrderNo());
                params.setSource(Consts.SourcePay);
                try {
                    long timeStampSecond = TimeUtils.getTimeStampSecond(
                            new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                    .parse(data.getPayTime()).getTime());
                    params.setEventTime(timeStampSecond);
                } catch (Exception e) {
                    //nothing to do
                    sinkMessageLog = new SinkMessage(Consts.DEBUG, "下单兜底，EventTime提取失败，默认为0", gson.toJson(params),message);
                    sinkMessageLog.setDeliverTime(36L);
                }
                break;
            }
            default:
                break;
        }
        List<SinkMessage> sinkMessageList = rocketMqAttributeService.getAttributeService().order(params, "【原始数据】" + message);
        if(sinkMessageLog!=null)
        {
            sinkMessageList.add(sinkMessageLog);
        }
        return sinkMessageList;
    }


    /**
     * 功能描述 兜底kafka用户行为消费逻辑，需要回传数据到Kafka和mq
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/18
     */
    public List<SinkMessage> processRegister(String message) throws Exception {
        Gson gson = new Gson();
        RegisterMessage data;
        try {
            String trim = StringUtils.trim(StringUtils.replace(message, "\\", ""));
            String str = BaseUtils.replaceLast(trim.replaceFirst("\"", ""), "\"", "");
            data = gson.fromJson(str, RegisterMessage.class);
            if (data == null) {
                return null;
            }
        } catch (Exception e) {
            LOG.error("[jsoniter][ProcessRegister]解析mq数据失败 {}", message, e);
            List<SinkMessage> sinkMessageList=new ArrayList<>();
            SinkMessage temp = new SinkMessage(Consts.DEBUG, "注册兜底，解析mq数据失败，默认为0", "",-1L);
            temp.setMessageLog(message);
            sinkMessageList.add(temp);
            return sinkMessageList;
        }
        QueryReq params = new QueryReq();
        params.setOs(data.getPlatform());
        params.setImei(data.getImei());
        params.setOaid(data.getOaid());
        params.setUuid(data.getDeviceId());
        params.setSource(Consts.SourceRegister);

        SinkMessage sinkMessageLog=null;
        try {
            long timeStampSecond = TimeUtils.getTimeStampSecond(
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .parse(data.getInvokeTime()).getTime());
            params.setEventTime(timeStampSecond);
        } catch (Exception e) {
            //nothing to do
            sinkMessageLog = new SinkMessage(Consts.DEBUG, "注册兜底，EventTime提取失败，默认为0", gson.toJson(params));
            sinkMessageLog.setMessageLog(message);
            sinkMessageLog.setDeliverTime(35L);
        }
//        if pt, err := time.ParseInLocation("2006-01-02 15:04:05.000", data.InvokeTime, consts.Location); err == nil {
//            params.EventTime = pt.Unix()
//        }
        List<SinkMessage> sinkMessageList = rocketMqAttributeService.getAttributeService().register(params, "【原始数据】" + message);
        if(sinkMessageLog!=null)
        {
            sinkMessageList.add(sinkMessageLog);
        }
        return sinkMessageList;
    }

    public RocketMqAttributeService getRocketMqAttributeService() {
        return rocketMqAttributeService;
    }

    public void setRocketMqAttributeService(RocketMqAttributeService rocketMqAttributeService) {
        this.rocketMqAttributeService = rocketMqAttributeService;
    }
}