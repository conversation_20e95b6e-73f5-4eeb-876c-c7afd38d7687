package com.dewu.flink.delivery.common.sink;

import org.apache.commons.lang.Validate;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.flink.RocketMQConfig;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class RocketMQPorducerUtils {

    private static final Logger LOG = LoggerFactory.getLogger(RocketMQPorducerUtils.class);

    private transient DefaultMQProducer producer;

    private Long startDeliverTime=0L;


    private static volatile RocketMQPorducerUtils singleton;

    private RocketMQPorducerUtils() {
    }

    public static RocketMQPorducerUtils getInstance(Properties props,Long startDeliverTime) throws Exception {
        if (singleton == null) {
            synchronized (RocketMQPorducerUtils.class) {
                if (singleton == null) {
                    singleton = new RocketMQPorducerUtils(props,startDeliverTime);
                }
            }
        }
        return singleton;
    }


    public RocketMQPorducerUtils(Properties props,Long startDeliverTime) throws Exception {
        this.startDeliverTime=startDeliverTime;
        Validate.notEmpty(props, "Producer properties can not be empty");
        // with authentication hook
        producer = new DefaultMQProducer(RocketMQConfig.buildAclRPCHook(props));

//        producer.setInstanceName(getRuntimeContext().getIndexOfThisSubtask() + "_" + UUID.randomUUID());
        producer.setInstanceName(props.getProperty(RocketMQConfig.INSTANCE_NAME));
        RocketMQConfig.buildProducerConfigs(props, producer);

        try {
            producer.start();
        } catch (MQClientException e) {
            LOG.error("Flink sink init failed, due to the producer cannot be initialized.");
            throw new RuntimeException(e);
        }
    }

    public DefaultMQProducer getProducer() {
        return producer;
    }

    public void setProducer(DefaultMQProducer producer) {
        this.producer = producer;
    }

    public void invoke(final Message input) throws Exception {
        try {
            if(this.startDeliverTime>0)
            {
                input.setStartDeliverTime(System.currentTimeMillis()+startDeliverTime);
            }
            SendResult result = producer.send(input,30000);
            LOG.debug("Sync send message result: {}", result);
            if (result.getSendStatus() != SendStatus.SEND_OK) {
                throw new RemotingException(result.toString());
            }
        } catch (Exception e) {
            LOG.error("Sync send message exception: ", e);
            throw e;
        }
    }


    public static void main(String[] args) throws Exception {
        Properties producerProps = new Properties();
        producerProps.setProperty(RocketMQConfig.NAME_SERVER_ADDR, "10.88.11.58:9876");
        producerProps.setProperty(RocketMQConfig.INSTANCE_NAME, "RocketMQPorducerUtils");

        RocketMQPorducerUtils rocketMQSink = new RocketMQPorducerUtils(producerProps,6000L);
        Message msg = new Message("active_useraction_delay_message",
                /*消息内容。*/
                "Hello world".getBytes(RemotingHelper.DEFAULT_CHARSET));
        while(true)
        {
            rocketMQSink.invoke(msg);
            Thread.sleep(1000);
        }

//        rocketMQSink.invoke(msg,6000L);
//        rocketMQSink.invoke(msg,6000L);
//        rocketMQSink.invoke(msg,60000L);
//        rocketMQSink.invoke(msg,60000L);
//        rocketMQSink.invoke(msg,60000L);
    }


    public void close() {
        if (this.producer != null) {
            this.producer.shutdown();
        }
    }


}