create function userActionUdtf as 'com.dewu.flink.delivery.udf.UserActionUdtf';

create view check_data as select '' as param1;
create table print_sink (
                            param1 string,
                            param2 string,
                            param3 string,
                            param4 string,
                            param5 bigint,
                            param6 string,
                            param7 bigint,
                            param8 string
)with(
     'connector'='print'
     );

insert into print_sink
select p1, p2, p3, p4, p5, p6, p7, p8
from check_data,LATERAL TABLE(userActionUdtf('{"fcuuid":null,"day_first_active_time":"1648625400432","userid":null,"device_uuid":"68729257ebe92280","manufacturer":"HUAWEI","scene":null,"last_active_time":null,"caid":null,"ipvx":null,"model":null,"event":"activate_app","current_page":"61","oaid":"00000000-0000-0000-0000-000000000000","user_agent":"/duapp/(android;10)","os":"Android","device_id":"68729257ebe92280","os_version":"10","ip":"************","idfa":null,"receive_time":"1648625400432","android_channel":"douyin_ad33","shumei_id":"202203301529581829c37f98855bc2b0b4cb68d8ed836700f6857dbddc723c","imei":"","android_id":"68729257ebe92280","current_page_title":null,"properties":null,"event_time":"1648625399048"}', 'channel',
        '127.0.0.1', 1000, '1', 0,
        '*********', 1001, '1', 1,
        '*********', 1002, '1', 2)) as T(p1, p2, p3, p4, p5, p6, p7, p8)
