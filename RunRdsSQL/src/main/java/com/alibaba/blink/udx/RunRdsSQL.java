package com.alibaba.blink.udx;



import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;

import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

@FunctionHint(output = @DataTypeHint("ROW<" +
        "a BIGINT," +
        " b BIGINT," +
        " c INT," +
        " d INT," +
        " f STRING," +
        " g STRING," +
        " h STRING," +
        " i STRING," +
        " j BIGINT," +
        " k STRING," +
        " l STRING," +
        " m STRING," +
        " n INT," +
        " o INT," +
        " p STRING," +
        " q STRING," +
        " r INT," +
        " s STRING," +
        " t STRING" +
        ">"))
public class RunRdsSQL extends TableFunction<Row> {
    private static Logger LOG = LoggerFactory.getLogger(RunRdsSQL.class);

    private JDBCUtils jdbcUtils;
    private PreparedStatement pstmt = null;
    private Connection conn = null;
    private ArrayList<TouchStationEvent> cacheAllList;
    long lastCacheAllTime = 0;

    @Override
    public void open(FunctionContext context) throws SQLException {
        //数据库连接
        Properties pro = new Properties();
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "**************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "user_0101");
        String password = context.getJobParameter("jdbc.password", "hwCr219hAo0YlD58");
        String initialSize = context.getJobParameter("jdbc.initialsize", "5");
        String maxActive = context.getJobParameter("jdbc.maxactive", "1000");
        String maxWait = context.getJobParameter("jdbc.maxwait", "3000");
        String sql = context.getJobParameter("jdbc.sql", "select id,population_id,population_type,type,title,content,event_type,jump_name,jump_url,img_url,start_time,end_time,task_status,task_name,priority,department_1,business_scene,department_2,population_detail from touch_station_event\n" +
                "where task_status=4 order by priority desc, create_time");
//        String sql = context.getJobParameter("jdbc.sql", "select id,population_id,type,title,content,event_type,jump_name,jump_url,img_url,start_time,end_time,task_status,task_name,priority from touch_station_event order by priority desc, create_time");

        System.out.println("mysql info here : ");
        System.out.println("driverClassName is: " + driverClassName);
        System.out.println("url is: " + url);
        System.out.println("username is: " + username);
        System.out.println("password is: " + password);
        System.out.println("initialSize is: " + initialSize);
        System.out.println("sql is: " + sql);

        pro.put("driverClassName", driverClassName);
        pro.put("url", url);
        pro.put("username", username);
        pro.put("password", password);
        pro.put("initialSize", initialSize);
        pro.put("maxActive", maxActive);
        pro.put("maxWait", maxWait);

        jdbcUtils = new JDBCUtils(pro);
        conn = jdbcUtils.getConnection();

        pstmt = conn.prepareStatement(sql);
        cacheAllList = new ArrayList<>();
        cacheAll();
        lastCacheAllTime = System.currentTimeMillis();
    }

    private void cacheAll() throws SQLException {
        LOG.info("begin to cache all mail configs...");
        cacheAllList.clear();

        ResultSet resultSet = pstmt.executeQuery();
        while (resultSet.next()) {
            long id = resultSet.getLong("id");
            long populationId = resultSet.getLong("population_id");
            int populationType = resultSet.getInt("population_type");
            int type = resultSet.getInt("type");
            String title = resultSet.getString("title");
            String content = resultSet.getString("content");
            String eventType = resultSet.getString("event_type");
            String jumpName = resultSet.getString("jump_name");
            String jumpUrl = resultSet.getString("jump_url");
            String imgUrl = resultSet.getString("img_url");
            long startTime = resultSet.getLong("start_time");
            long endTime = resultSet.getLong("end_time");
            int taskStatus = resultSet.getInt("task_status");
            String taskName = resultSet.getString("task_name");
            String department1 = resultSet.getString("department_1");
            String businessScene = resultSet.getString("business_scene");
            String department2 = resultSet.getString("department_2");
            String populationDetail = resultSet.getString("population_detail");

            TouchStationEvent row = new TouchStationEvent(id, populationId, populationType, type, title, content, eventType,
                    jumpName, jumpUrl, imgUrl, startTime, endTime, taskStatus, taskName, department1, businessScene, department2, populationDetail);
            cacheAllList.add(row);
        }
        lastCacheAllTime = System.currentTimeMillis();
        LOG.info("finish to cache all mail configs at lastCacheAllTime.");
    }

    public void eval(Long time, String eventType) throws SQLException {
        if (System.currentTimeMillis() - lastCacheAllTime > 180000) {
            cacheAll();
        }
        Row row = new Row(19);
//        long time = (long) args[0];
//        String eventType = (String) args[1];

        for (TouchStationEvent event : cacheAllList) {
            List<String> temp = Arrays.asList(event.eventType.split(","));
            if (time >= event.startTime && time < event.endTime && temp.contains(eventType)) {
                row.setField(0, event.id);
                row.setField(1, event.populationId);
                row.setField(2, event.populationType);
                row.setField(3, event.type);
                row.setField(4, event.title);
                row.setField(5, event.content);
                row.setField(6, event.jumpUrl);
                row.setField(7, event.imgUrl);
                row.setField(8, event.endTime);
                row.setField(9, event.department1);
                row.setField(10, event.businessScene);
                row.setField(11, event.department2);
                row.setField(12, event.populationDetail == null ? null : event.populationDetail.getAutoRefreshFlag());
                row.setField(13, event.populationDetail == null ? null : event.populationDetail.getCreateType());
                row.setField(14, event.populationDetail == null ? null : event.populationDetail.getHbaseExpression());
                row.setField(15, event.populationDetail == null ? null : event.populationDetail.getName());
                row.setField(16, event.populationDetail == null ? null : event.populationDetail.getNum());
                row.setField(17, event.populationDetail == null ? null : event.populationDetail.getSearchCodes());
                row.setField(18, event.populationDetail == null ? null : event.populationDetail.getSourceSystem());
                collect(row);
            }
        }
    }

    // 如果返回值是Row，则必须重载实现getResultType方法，显式地声明返回的字段类型。
//    @Override
//    public DataType getResultType(Object[] arguments, Class[] argTypes) {
//        return DataTypes.createRowType(DataTypes.LONG, DataTypes.LONG, DataTypes.INT, DataTypes.INT, DataTypes.STRING, DataTypes.STRING, DataTypes.STRING, DataTypes.STRING, DataTypes.LONG, DataTypes.STRING, DataTypes.STRING, DataTypes.STRING);
//    }

    @Override
    public void close() {
        if (jdbcUtils != null) {
            jdbcUtils.close(pstmt, conn);
        }
    }

    private class TouchStationEvent {
        public long id;
        public long populationId;
        public int populationType;
        public int type;
        public String title;
        public String content;
        public String eventType;
        public String jumpName;
        public String jumpUrl;
        public String imgUrl;
        public long startTime;
        public long endTime;
        public int taskStatus;
        public String taskName;
        public int priority;
        public String department1;
        public String businessScene;
        public String department2;
        public PopulationDetail populationDetail;

        public TouchStationEvent(long id, long populationId, int populationType, int type, String title, String content,
                                 String eventType, String jumpName, String jumpUrl, String imgUrl, long startTime,
                                 long endTime, int taskStatus, String taskName, String department1, String businessScene,
                                 String department2, String populationDetail) {
            this.id = id;
            this.populationId = populationId;
            this.populationType = populationType;
            this.type = type;
            this.title = title;
            this.content = content;
            this.eventType = eventType;
            this.jumpName = jumpName;
            this.jumpUrl = jumpUrl;
            this.imgUrl = imgUrl;
            this.startTime = startTime;
            this.endTime = endTime;
            this.taskStatus = taskStatus;
            this.taskName = taskName;
            this.department1 = department1;
            this.businessScene = businessScene;
            this.department2 = department2;
            if (StringUtils.isEmpty(populationDetail)) {
                populationDetail = null;
            } else {
                try {
                    this.populationDetail = new PopulationDetail();
                    Map details = JsonUtil.readValue(populationDetail, Map.class);
                    this.populationDetail.strictAccept(details);
                } catch (Exception e) {
                    this.populationDetail = null;
                    LOG.error("Event populationDetail load failed for : " + id, e);
                }
            }
            if (populationDetail == null && populationType == 6) {
                LOG.warn("found empty populationDetail for xingyun config : " + this);
            }
        }

        @Override
        public String toString() {
            return "TouchStationEvent{" +
                    "id=" + id +
                    ", populationId=" + populationId +
                    ", type=" + type +
                    ", title='" + title + '\'' +
                    ", content='" + content + '\'' +
                    ", eventType='" + eventType + '\'' +
                    ", jumpName='" + jumpName + '\'' +
                    ", jumpUrl='" + jumpUrl + '\'' +
                    ", imgUrl='" + imgUrl + '\'' +
                    ", startTime=" + startTime +
                    ", endTime=" + endTime +
                    ", taskStatus=" + taskStatus +
                    ", taskName='" + taskName + '\'' +
                    ", priority=" + priority +
                    ", department1='" + department1 + '\'' +
                    ", businessScene='" + businessScene + '\'' +
                    ", department2='" + department2 + '\'' +
                    ", populationDetail='" + populationDetail + '\'' +
                    '}';
        }
    }

    @Data
    @NoArgsConstructor
    public class PopulationDetail {
        private int autoRefreshFlag;
        private int createType;
        private String hbaseExpression;
        private String name;
        private int num;
        private String searchCodes;
        private String sourceSystem;
        private long populationId;
        private String snapshotTable;

        public void strictAccept(Map details) {
            autoRefreshFlag = Integer.parseInt(details.get("autoRefreshFlag").toString());
            createType = Integer.parseInt(details.get("createType").toString());
            hbaseExpression = (String) details.get("hbaseExpression");
            name = (String) details.get("name");
            num = Integer.parseInt(details.get("num").toString());
            searchCodes = (String) details.get("searchCodes");
            sourceSystem = (String) details.get("sourceSystem");
            populationId = Long.parseLong(details.get("populationId").toString());
            snapshotTable = (String) details.get("snapshotTable");
        }
    }

    public static void main(String[] args) {
    }
}
