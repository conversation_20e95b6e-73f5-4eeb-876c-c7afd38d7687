package com.alibaba.blink.udx;


import org.apache.flink.shaded.guava30.com.google.common.cache.Cache;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 根据传入的population_id来查询指定的adb表.
 */
@FunctionHint(
		input = {@DataTypeHint("BIGINT"), @DataTypeHint("BIGINT")},
		output = @DataTypeHint("STRING")
)
public class RunRdsSQLADB extends ScalarFunction {

	private static Logger LOG = LoggerFactory.getLogger(RunRdsSQLADB.class);
	private static Connection conn = null;
	/**
	 * Long: populationId
	 * List<Long>: populationId对应的uidList
	 */
	private static Cache<Long, List<Long>> cache = null;

	@Override
	public void open(FunctionContext context) throws Exception {
		super.open(context);

		cache = CacheBuilder.newBuilder().maximumSize(20000000L).expireAfterWrite(5L, TimeUnit.DAYS).build();


		Properties pro = new Properties();
		String driverClassName = "com.mysql.jdbc.Driver";
		String url = "*********************************************************************************************************************************";
		String username = "du_bigdata";
		String password = "Bj^WUxeelvtFt%Cu";
		String initialSize = "5";
		String maxActive = "1000";
		String maxWait = "3000";

		pro.put("driverClassName", driverClassName);
		pro.put("url", url);
		pro.put("username", username);
		pro.put("password", password);
		pro.put("initialSize", initialSize);
		pro.put("maxActive", maxActive);
		pro.put("maxWait", maxWait);

		JDBCUtils jdbcUtils = new JDBCUtils(pro);
		conn = jdbcUtils.getConnection();
	}

	public String eval(Long uid, Long populationId) throws SQLException {
		try {
			List<Long> uidList = cache.getIfPresent(populationId);

			if (uidList == null) {
				List<Long> uList = getUidListFromNewAdbTable(populationId);
				cache.put(populationId, uList);
				return uList.contains(uid) ? "1" : "0";
			} else {
				return uidList.contains(uid) ? "1" : "0";
			}
		} catch (SQLException e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(),e);
		}
	}


	private static List<Long> getUidListFromNewAdbTable(Long suffix) throws SQLException {

		String tableName = "trice_circle_5_" + suffix;

		LOG.info("开始加载表 {} 的数据...", tableName);

		List<Long> uidList = new ArrayList<>();

		String querySql = "select id from " + tableName;

		try (PreparedStatement pst = conn.prepareStatement(querySql);
			 ResultSet resultSet = pst.executeQuery()) {

			while (resultSet.next()) {
				long uid = resultSet.getLong("id");
				uidList.add(uid);
			}
		}

		LOG.info("表{}的数据加载完成，共{}条", tableName, uidList.size());
		return uidList;
	}

}
