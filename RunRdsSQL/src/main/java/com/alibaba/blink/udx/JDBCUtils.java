package com.alibaba.blink.udx;

import com.alibaba.druid.pool.DruidDataSourceFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

public class JDBCUtils {
    /**
     * 静态代码：
     * 加载配置：Properties->pro
     * 创建druid连接池：DruidDataSourceFactory.createDataSource(pro)
     * 方法：
     * getConnection():返回druid连接池的连接并忽略SQL异常
     * close():关闭Statement和Connection
     * close():关闭ResultSet,Statement和Connection
     * getDataSource():返回druid连接池对象
     */

    private DataSource ds;

    public JDBCUtils(Properties pro) {
        try {
            ds = DruidDataSourceFactory.createDataSource(pro);
        } catch (Exception e) {
            System.out.println("Create datasource failed!: " + e);
        }
    }


    public Connection getConnection() throws SQLException {
        return ds.getConnection();
    }

    public void close(Statement stmt, Connection conn) {
        close(null, stmt, conn);
    }

    public void close(ResultSet rs, Statement stmt, Connection conn) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

    }

    public DataSource getDataSource() {
        return ds;
    }

    public static void main (String[] args) {
        String text = "aa$mpclcik";
        String[] arr = text.split("\\$");
        for (String a : arr) {
            System.out.println(a);
        }
    }
}
