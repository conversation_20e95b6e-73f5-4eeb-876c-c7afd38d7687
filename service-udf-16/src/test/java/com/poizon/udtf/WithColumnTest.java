package com.poizon.udtf;

import org.apache.flink.api.common.functions.util.ListCollector;
import org.apache.flink.types.Row;
import org.junit.Test;

import java.util.ArrayList;

public class WithColumnTest {

    @Test
    public void eval() {
        ArrayList<Row> result = new ArrayList<>();

        WithColumn withColumn = new WithColumn();
        withColumn.setCollector(new ListCollector<>(result));
        withColumn.eval("123");
        String a = null;
        withColumn.eval(a);
        withColumn.eval(123);

        result.forEach(System.out::println);
    }
}