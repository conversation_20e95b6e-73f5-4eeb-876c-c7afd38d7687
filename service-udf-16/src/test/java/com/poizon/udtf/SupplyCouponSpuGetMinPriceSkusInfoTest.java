package com.poizon.udtf;

import org.apache.flink.api.common.functions.util.ListCollector;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.types.Row;
import org.junit.Test;

import java.util.ArrayList;

import static org.junit.Assert.*;

public class SupplyCouponSpuGetMinPriceSkusInfoTest {

    @Test
    public void eval() throws Exception {
        SupplyCouponSpuGetMinPriceSkusInfo udtf = new SupplyCouponSpuGetMinPriceSkusInfo();
        ArrayList<Row> result = new ArrayList<>();
        udtf.setCollector(new ListCollector<>(result));

        Configuration configuration = new Configuration();
        configuration.setString("jdbc.url", "********************************");
        configuration.setString("jdbc.username", "root");
        configuration.setString("jdbc.password", "12345678");
        configuration.setString("jdbc2.url", "********************************");
        configuration.setString("jdbc2.username", "root");
        configuration.setString("jdbc2.password", "12345678");

        udtf.open(new FunctionContext(null, null, configuration));
        udtf.eval("2714883");

        result.forEach(System.out::println);

    }
}