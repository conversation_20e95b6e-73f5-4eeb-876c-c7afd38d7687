package com.poizon.udtf;

import org.apache.flink.api.common.functions.util.ListCollector;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.types.Row;

import java.util.ArrayList;

import static org.junit.Assert.*;

public class SupplyCouponGetStrategyBudgetTest {


    @org.junit.Test
    public void eval() throws Exception {
        SupplyCouponGetStrategyBudget supplyCouponGetStrategyBudget = new SupplyCouponGetStrategyBudget();

        ArrayList<Row> result = new ArrayList<>();
        supplyCouponGetStrategyBudget.setCollector(new ListCollector<>(result));

        Configuration configuration = new Configuration();
        configuration.setString("redis.host", "t-sh-redis.shizhuang-inc.net");
        configuration.setString("redis.port","1000");
        configuration.setString("redis.password","fAUmh4ZZYMeY");
        configuration.setString("redis.dbNum","0");

        supplyCouponGetStrategyBudget.open(new FunctionContext(null, null, configuration));
        supplyCouponGetStrategyBudget.eval("10654", "older");

        for (Row row : result) {
            // assertNotNull(row.getField(0));
            System.out.println(row);
        }
    }
}