package com.poizon.udtf;

import org.apache.flink.api.common.functions.util.ListCollector;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.types.Row;
import org.junit.Test;

import java.util.ArrayList;

import static org.junit.Assert.*;

public class SupplyCouponAlgoGetBrandMercStatusWithBizTest {
    @Test
    public void eval() throws Exception {
        SupplyCouponAlgoGetBrandMercStatusWithBiz supplyCouponAlgoGetBrandMercStatusWithBiz = new SupplyCouponAlgoGetBrandMercStatusWithBiz();

        ArrayList<Row> result = new ArrayList<>();
        supplyCouponAlgoGetBrandMercStatusWithBiz.setCollector(new ListCollector<>(result));

        Configuration configuration = new Configuration();
        configuration.setString("jdbc.url", "*************************************");
        configuration.setString("jdbc.username", "root");
        configuration.setString("jdbc.password", "12345678");
        configuration.setString("redis.host", "t-sh-redis.shizhuang-inc.net");
        configuration.setString("redis.port","1000");
        configuration.setString("redis.password","fAUmh4ZZYMeY");
        configuration.setString("redis.dbNum","0");

        supplyCouponAlgoGetBrandMercStatusWithBiz.open(new FunctionContext(null, null, configuration));
        supplyCouponAlgoGetBrandMercStatusWithBiz.eval("1310", "8515005", "alla","1,2,3,5");
        supplyCouponAlgoGetBrandMercStatusWithBiz.eval("1011145", "9680489", "older","1,3");

        for (Row row : result) {
            // assertNotNull(row.getField(0));
            System.out.println(row);
        }
    }

    @Test
    public void bizTypeTest() {
        System.out.println(BizType.isBudgetType2(1));
        System.out.println(BizType.isBudgetType2(2));
        System.out.println(BizType.isBudgetType2(3));
        System.out.println(BizType.isBudgetType2(4));
        System.out.println(BizType.isBudgetType2(5));
        System.out.println(BizType.isBudgetType2(6));
    }
}