package com.poizon.udf;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.functions.FunctionContext;
import org.junit.Test;

import static org.junit.Assert.*;

public class RedisHGetTest {

    @Test
    public void eval() throws Exception {
        RedisHGet redisHGet = new RedisHGet();

        Configuration configuration = new Configuration();
        configuration.setString("redis.hash.host", "t-sh-redis.shizhuang-inc.net");
        configuration.setString("redis.hash.port", "1000");
        configuration.setString("redis.hash.password", "fAUmh4ZZYMeY");
        configuration.setString("redis.hash.dbNum", "0");

        redisHGet.open(new FunctionContext(null, null, configuration));

        System.out.println(redisHGet.eval(null, "a"));
        // hset test a aaa
        System.out.println(redisHGet.eval("test", "a"));
        System.out.println(redisHGet.eval("test", "aaa"));
        // set test1 ssss
        System.out.println(redisHGet.eval("test1", "b"));
    }
}