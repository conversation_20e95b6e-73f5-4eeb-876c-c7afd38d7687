package com.poizon.udf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import redis.clients.jedis.*;
import redis.clients.jedis.exceptions.JedisBusyException;
import redis.clients.jedis.exceptions.JedisConnectionException;
import redis.clients.jedis.exceptions.JedisException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

@Slf4j
public class RedisHGet extends ScalarFunction {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private transient JedisPool jedisPool;
    private static final int MAXATTEMPTS = 4; // 最大重试次数

    @Override
    public void open(FunctionContext context) throws Exception {
        // jedisPool init
        String host = context.getJobParameter("redis.hash.host", "");
        String port = context.getJobParameter("redis.hash.port", "");
        String redisPassword = context.getJobParameter("redis.hash.password", "");
        String dbNum = context.getJobParameter("redis.hash.dbNum", "0");

        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setTestOnBorrow(true);
        jedisPool = new JedisPool(jedisPoolConfig, host, Integer.parseInt(port), 60000, redisPassword, Integer.parseInt(dbNum));
    }

    public String eval(String key, String field) throws JedisException {
        // 参数校验
        if (StringUtils.isEmpty(key) || StringUtils.isEmpty(field)) {
            log.error("RedisHGet 入参 为空 key:{}，field:{}", key, field);
            return null;
        }

        int attempt = 0;
        boolean success = false;
        JedisException unsuccessE = null;
        try {
            while (attempt < MAXATTEMPTS && !success) {
                attempt++;
                try (Jedis jedis = jedisPool.getResource()) {
                    String value = jedis.hget(key, field);

                    success = true;
                    return value;
                } catch (JedisConnectionException | JedisBusyException e) {
                    unsuccessE = e;
                    log.error("RedisHGet 第{}次查询失败, key:{},field:{}", attempt, key, field, e);
                    Thread.sleep(1000); // 等待1秒后重试
                }
            }
        } catch (Exception e) {
            log.error("RedisHGet 查询失败, key:{},field:{}", key, field, e);
        }

        if (!success && unsuccessE != null) {
            throw unsuccessE;    // 抛出 JedisException
        }
        return null;
    }


}
