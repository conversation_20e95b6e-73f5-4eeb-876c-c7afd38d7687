package com.poizon.udf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.table.functions.ScalarFunction;

import java.util.HashMap;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR> wangkang
 * @Project: du-tech-data-udf-dev
 * @Package com.poizon.userportrait.udf
 * @date Date  2024-04-18
 * @Description :TODO
 */

@Slf4j
public class SupplyCouponInvestmentFilter extends ScalarFunction {

    /**
     * @param investment_info
     * @param eids
     * @return 出现在 investment_info 中的第一个 eid
     */
    public String eval(String investment_info, String eids) {
        try {
            if (investment_info == null || investment_info.isEmpty()) {
                return null;
            }

            HashMap<String, String> hashMap = new HashMap<>();
            JSONArray jsonArray = JSONArray.parseArray(investment_info);
            jsonArray.forEach(o -> {
                JSONObject jsonObject = (JSONObject) o;
                hashMap.put(jsonObject.getString("eventId"), jsonObject.toString());
            });
            String[] eidArray = eids.split(",");
            for (String eid : eidArray) {
                eid = eid.split("_")[0].trim();
                if (hashMap.containsKey(eid)) {
                    return hashMap.get(eid);
                }
            }

            return null;
        } catch (Exception e) {
            log.error("SupplyCouponInvestmentFilter Exception. input:[{},{}]", investment_info, eids, e);
        }
        return null;
    }


}
