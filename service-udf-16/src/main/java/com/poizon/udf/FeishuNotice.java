package com.poizon.udf;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.apache.flink.table.types.inference.TypeInference;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.*;

@Slf4j
public class FeishuNotice extends ScalarFunction {

    private final ObjectMapper objectMapper = new ObjectMapper();

    public static final String format = "{\"msg_type\":\"post\",\"content\":{\"post\":{\"zh_cn\":{\"title\":\"%s\",\"content\":%s}}}}";
    public static MediaType JSON;
    public static OkHttpClient client;
    @Override
    public void open(FunctionContext context) {
        JSON = MediaType.parse("application/json; charset=utf-8");
        client = new OkHttpClient();
    }

    public String eval(String apiUrl, String title, String... texts) {
        // 参数校验
        if (StringUtils.isEmpty(apiUrl)) {
            log.error("FeishuNotice 入参 为空 apiUrl:{}", apiUrl);
            return "fail";
        }

        try {
            String jsonStr = toJsonStr(title, texts);

            return httpPost(apiUrl, jsonStr);
        } catch (Throwable e) {
            log.error("FeishuNotice 执行失败，title:{}, texts:{}", title, Arrays.toString(texts), e);
        }

        return "fail";
    }

    private String toJsonStr(String title, String... texts) throws JsonProcessingException {
        ArrayList<Object> contents = new ArrayList<>();
        for (String text : texts) {
            if (!StringUtils.isEmpty(text)) {
                HashMap<String, String> tag = new HashMap<>();
                tag.put("tag", "text");
                tag.put("text", text);
                contents.add(Collections.singletonList(tag));
            }
        }
        return String.format(format, title, objectMapper.writeValueAsString(contents));
    }

    @NotNull
    private static String httpPost(String apiUrl, String jsonStr) throws IOException {
        RequestBody body = RequestBody.create(JSON, jsonStr);
        Request request = new Request.Builder()
                .url(apiUrl)
                .post(body)
                .build();
        Response response = client.newCall(request).execute();
        return response.body().string();
    }


    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(callContext -> Optional.of(DataTypes.STRING())).build();
    }
}
