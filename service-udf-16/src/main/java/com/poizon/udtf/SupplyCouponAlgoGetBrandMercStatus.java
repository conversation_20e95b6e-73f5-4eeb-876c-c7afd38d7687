package com.poizon.udtf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@FunctionHint(input = {@DataTypeHint("String"), @DataTypeHint("String"), @DataTypeHint("String")}, output = @DataTypeHint("ROW<status STRING, algos_info STRING>"))
public class SupplyCouponAlgoGetBrandMercStatus extends TableFunction<Row> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private Connection conn;
    private transient JedisPool jedisPool;
    private static final String redisKeyPrefix = "coupon:investment:budget:";
    private static final String OFF = "off";
    private static final String ON = "on";
    private static final int MAXATTEMPTS = 4; // 最大重试次数

    @Override
    public void open(FunctionContext context) throws Exception {
        // jdbc init
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "**************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_etl");
        String password = context.getJobParameter("jdbc.password", "");

        log.info("driverClassName:{}, url:{}, username:{}, password:{}", driverClassName, url, username, password);
        conn = getConnection(driverClassName, url, username, password);

        // jedisPool init
        String host = context.getJobParameter("redis.host", "kvredis-1dc95006c4.kvredis.shizhuang-inc.com");
        String port = context.getJobParameter("redis.port", "1371");
        String redisPassword = context.getJobParameter("redis.password", "");
        String dbNum = context.getJobParameter("redis.dbNum", "0");

        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setTestOnBorrow(true);
        jedisPool = new JedisPool(jedisPoolConfig, host, Integer.parseInt(port), 60000, redisPassword, Integer.parseInt(dbNum));
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            log.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }


    public void eval(String brand_id, String merc_id, String biz_channel) {
        try {
            if (StringUtils.isEmpty(brand_id) || StringUtils.isEmpty(merc_id)) {
                log.error("SupplyCouponAlgoGetBrandMercStatus 入参 为空 brand_id:{}，merc_id:{}", brand_id, merc_id);
                collect(new Row(2));
                return;
            }

            int attempt = 0;
            boolean success = false;
            while (attempt < MAXATTEMPTS && !success) {
                try {
                    String status = OFF;
                    List<SupplyCouponAlgo> supplyCouponAlgos = querySupplyCouponAlgo(brand_id, merc_id);
                    try (Jedis jedis = jedisPool.getResource()) {
                        for (SupplyCouponAlgo algo : supplyCouponAlgos) {
                            String redisKey = getRedisKey(algo, biz_channel);

                            Long brandSubsidyAmount = 0L;
                            String brand_subsidy_amount = jedis.hget(redisKey, "brand_subsidy_amount");
                            if (!StringUtils.isEmpty(brand_subsidy_amount)) {
                                brandSubsidyAmount = Long.parseLong(brand_subsidy_amount);
                            }

                            String algoStatus = checkBudget(algo, biz_channel, brandSubsidyAmount);
                            if (ON.equals(algoStatus)) {
                                status = ON;
                            }
                        }
                    }

                    // collect
                    Row row = new Row(2);
                    row.setField(0, status);
                    row.setField(1, objectMapper.writeValueAsString(supplyCouponAlgos));
                    collect(row);

                    success = true;
                } catch (SQLException | JedisException e) {
                    log.error("SupplyCouponAlgoGetBrandMercStatus 第{}次查询失败, brand_id:{},merc_id:{},biz_channel:{}", ++attempt, brand_id, merc_id, biz_channel, e);
                    Thread.sleep(1000); // 等待1秒后重试
                }
            }

            if (attempt > 1) {
                if (success) {
                    log.warn("SupplyCouponAlgoGetBrandMercStatus 重试成功，重试{}次, brand_id:{},merc_id:{},biz_channel:{}", attempt, brand_id, merc_id, biz_channel);
                } else {
                    log.error("SupplyCouponAlgoGetBrandMercStatus 查询失败，重试{}次, brand_id:{},merc_id:{},biz_channel:{}", attempt, brand_id, merc_id, biz_channel);
                }
            }

        } catch (Exception e) {
            log.error("SupplyCouponAlgoGetBrandMercStatus 执行失败, brand_id:{},merc_id:{},biz_channel:{}", brand_id, merc_id, biz_channel, e);
        }
    }

    private static String getRedisKey(SupplyCouponAlgo algo, String biz_channel) {
        String redisKey = redisKeyPrefix;
        if (!"newbie".equals(biz_channel)) {
            redisKey = redisKeyPrefix.concat(biz_channel).concat(":");
        }
        Long pool = algo.getBiz_type() == 2 ? algo.getInvestment_activity_id() : algo.getId();
        redisKey = redisKey.concat(algo.getBiz_type() + ":" + pool);
        return redisKey;
    }

    private static String checkBudget(SupplyCouponAlgo algo, String biz_channel, Long brandSubsidyAmount) {
        algo.setBiz_channel(biz_channel);
        algo.setBrand_subsidy_amount(brandSubsidyAmount);
        String status = OFF;
        Long budget = 0L;

        switch (biz_channel) {
            case "newbie":
                budget = algo.getInvestment_amount();
                break;
            case "recall":
                budget = algo.getRecall_amount();
                break;
            case "older":
                budget = algo.getOlder_budget();
                break;
        }
        if (budget > brandSubsidyAmount) {
            algo.setStatus(ON);
            status = ON;
        } else {
            algo.setStatus(OFF);
        }
        return status;
    }

    public List<SupplyCouponAlgo> querySupplyCouponAlgo(String brand_id, String merc_id) throws SQLException {
        List<SupplyCouponAlgo> list = new ArrayList<>();
        String sql = "SELECT id, brand_id, merchant_id, start_time, end_time, investment_activity_id, biz_type, investment_amount, recall_amount, older_budget \n" +
                "FROM supply_coupon_algo \n" +
                "WHERE brand_id = ? AND merchant_id = ? \n" +   //  品牌*商户，任一存续期内的活动是有预算的，都算可以投放
                "AND online_status = 1 AND is_del = 0 \n" +
                "AND start_time <= now() AND now() < end_time";

        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, brand_id);
            ps.setString(2, merc_id);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                SupplyCouponAlgo supplyCouponAlgo = SupplyCouponAlgo.builder().id(rs.getLong("id")).brand_id(rs.getLong("brand_id")).merchant_id(rs.getString("merchant_id")).start_time(rs.getString("start_time")).end_time(rs.getString("end_time")).investment_activity_id(rs.getLong("investment_activity_id")).biz_type(rs.getInt("biz_type")).investment_amount(rs.getLong("investment_amount")).recall_amount(rs.getLong("recall_amount")).older_budget(rs.getLong("older_budget")).build();
                list.add(supplyCouponAlgo);
            }
        }

        return list;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SupplyCouponAlgo {
        Long id;                               // eventId
        Long brand_id;                         // 品牌id
        String merchant_id;                    // 商家id

        String start_time;
        String end_time;

        Long investment_activity_id;           // 招商活动id
        Integer biz_type;                      // 0 -- 普通，1 -- 星桥，2 -- 游戏化
        Long investment_amount;                // 出资金额 单位分
        Long recall_amount;                    // 召回分配金额1
        Long older_budget;                     // 老客预算，单位分

        String biz_channel;                    // 渠道
        Long brand_subsidy_amount;             // 预算消耗金额
        String status;                         // 活动状态
    }

}
