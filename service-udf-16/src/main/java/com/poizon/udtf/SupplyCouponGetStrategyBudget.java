package com.poizon.udtf;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import redis.clients.jedis.*;
import redis.clients.jedis.exceptions.JedisException;

import java.io.IOException;
import java.util.*;

@Slf4j
@FunctionHint(output = @DataTypeHint("ROW<strategy_budget_entries STRING, slot_info STRING>"))
public class SupplyCouponGetStrategyBudget extends TableFunction<Row> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private transient JedisPool jedisPool;
    private static final int MAXATTEMPTS = 4; // 最大重试次数

    @Override
    public void open(FunctionContext context) throws Exception {
        // jedisPool init
        String host = context.getJobParameter("redis.host", "kvredis-1dc95006c4.kvredis.shizhuang-inc.com");
        String port = context.getJobParameter("redis.port", "1371");
        String redisPassword = context.getJobParameter("redis.password", "");
        String dbNum = context.getJobParameter("redis.dbNum", "0");

        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setTestOnBorrow(true);
        jedisPool = new JedisPool(jedisPoolConfig, host, Integer.parseInt(port), 60000, redisPassword, Integer.parseInt(dbNum));
    }

    public void eval(String pool, String biz_channel) {
        try {
            if (StringUtils.isEmpty(pool) || StringUtils.isEmpty(biz_channel)) {
                log.error("SupplyCouponGetStrategyBudget 入参 为空 pool:{}，biz_channel:{}", pool, biz_channel);
                collect(new Row(2));
                return;
            }

            int attempt = 0;
            boolean success = false;
            while (attempt < MAXATTEMPTS && !success) {
                attempt++;
                try (Jedis jedis = jedisPool.getResource()) {
                    Pipeline pipelined = jedis.pipelined();

                    HashMap<String, Response<String>> slotStrategyResponseMap = new HashMap<>();
                    HashMap<String, Response<String>> slotBudgetResponseMap = new HashMap<>();
                    for (int i = 0; i < 100; i++) {
                        String slot = String.valueOf(i);

                        slotStrategyResponseMap.put(slot, pipelined.get(biz_channel + ":slotStrategy:" + slot));
                        slotBudgetResponseMap.put(slot, pipelined.hget(String.format("coupon:investment:slot:budget:%s:%s:%s", biz_channel, slot, pool), "brand_subsidy_amount"));
                    }
                    pipelined.sync();
                    Map<String, String> slotStrategyMap = responseMap2stringMap(slotStrategyResponseMap);
                    Map<String, String> slotBudgetMap = responseMap2stringMap(slotBudgetResponseMap);

                    HashSet<String> strategySet = new HashSet<>(slotStrategyMap.values());
                    HashMap<String, Response<String>> strategySlotResponseMap = new HashMap<>();
                    for (String strategy : strategySet) {
                        Response<String> response = pipelined.get(biz_channel + ":strategySlot:" + strategy);
                        strategySlotResponseMap.put(strategy, response);
                    }
                    pipelined.sync();
                    Map<String, String> strategySlotMap = responseMap2stringMap(strategySlotResponseMap);

                    HashMap<String, Long> strategyBudgetMap = new HashMap<>();
                    for (Map.Entry<String, String> strategySlotEntry : strategySlotMap.entrySet()) {
                        String strategy = strategySlotEntry.getKey();
                        String slots = strategySlotEntry.getValue();
                        long budget = 0;

                        for (String slot : slots.split(",")) {
                            String slotBudget = slotBudgetMap.get(slot);
                            if (slotBudget != null)
                                budget += Long.parseLong(slotBudget);
                        }
                        strategyBudgetMap.put(strategy, budget);
                    }

                    ArrayList<Object> strategyBudgetEntries = strategyBudgetMap2Entries(strategyBudgetMap);  // 2jsonArray
                    HashMap<String, Object> slotInfoMap = new HashMap<>();
                    slotInfoMap.put("slotStrategyMap", slotStrategyMap);
                    slotInfoMap.put("slotBudgetMap", slotBudgetMap);
                    slotInfoMap.put("strategySlotMap", strategySlotMap);
                    slotInfoMap.put("strategyBudgetMap", strategyBudgetMap);

                    // collect
                    Row row = new Row(2);
                    row.setField(0, objectMapper.writeValueAsString(strategyBudgetEntries));
                    row.setField(1, objectMapper.writeValueAsString(slotInfoMap));
                    collect(row);
                    success = true;
                } catch (IOException | JedisException e) {
                    log.error("SupplyCouponGetStrategyBudget 第{}次查询失败, biz_channel:{},pool:{}", attempt, biz_channel, pool, e);
                    Thread.sleep(1000); // 等待1秒后重试
                }
            }

            if (attempt > 1) {
                if (success) {
                    log.warn("SupplyCouponGetStrategyBudget 重试成功, 重试{}次, biz_channel:{},pool:{}", attempt - 1, biz_channel, pool);
                } else {
                    log.error("SupplyCouponGetStrategyBudget 执行失败, 重试{}次, biz_channel:{},pool:{}", attempt - 1, biz_channel, pool);
                    collect(new Row(2));
                }
            }
        } catch (Exception e) {
            log.error("SupplyCouponGetStrategyBudget 执行失败, biz_channel:{},pool:{}", biz_channel, pool, e);
            collect(new Row(2));
        }
    }

    private static ArrayList<Object> strategyBudgetMap2Entries(HashMap<String, Long> strategyBudgetMap) {
        ArrayList<Object> strategyBudgetEntries = new ArrayList<>();
        for (Map.Entry<String, Long> strategyBudgetEntry : strategyBudgetMap.entrySet()) {
            String strategy = strategyBudgetEntry.getKey();
            Long budget = strategyBudgetEntry.getValue();

            HashMap<String, Object> hashMap = new HashMap<>(2);
            hashMap.put("strategy", strategy);
            hashMap.put("budget", budget);
            strategyBudgetEntries.add(hashMap);
        }
        return strategyBudgetEntries;
    }

    private static Map<String, String> responseMap2stringMap(Map<String, Response<String>> responseMap) {
        Map<String, String> stringMap = new HashMap<>();
        for (Map.Entry<String, Response<String>> responseEntry : responseMap.entrySet()) {
            String key = responseEntry.getKey();
            String value = responseEntry.getValue().get();

            if (!StringUtils.isEmpty(value))
                stringMap.put(key, value);
        }
        return stringMap;
    }
}
