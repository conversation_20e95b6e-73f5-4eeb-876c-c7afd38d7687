package com.poizon.udtf;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.guava30.com.google.common.cache.Cache;
import org.apache.flink.shaded.guava30.com.google.common.cache.CacheBuilder;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.sql.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@FunctionHint(input = {@DataTypeHint("String")}, output = @DataTypeHint("ROW<min_price_skus_info STRING, merc_sale_skus STRING>"))
public class SupplyCouponSpuGetMinPriceSkusInfo extends TableFunction<Row> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private Connection conn;
    private Connection conn2;
    private transient Cache<String, Long> cache2;
    private static final int MAXATTEMPTS = 4; // 最大重试次数

    @Override
    public void open(FunctionContext context) throws Exception {
        // jdbc init
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "**********************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_bigdata_flink");
        String password = context.getJobParameter("jdbc.password", "");
        conn = getConnection(driverClassName, url, username, password);

        String url2 = context.getJobParameter("jdbc2.url", "****************************************************************************************************************************************************************************");
        String username2 = context.getJobParameter("jdbc2.username", "du_bigdata_flink");
        String password2 = context.getJobParameter("jdbc2.password", "");
        String catchSize2 = context.getJobParameter("jdbc2.catch.size", "10000");
        String catchTtl2 = context.getJobParameter("jdbc2.catch.ttl", "60");
        conn2 = getConnection(driverClassName, url2, username2, password2);
        cache2 = CacheBuilder.newBuilder()
                .maximumSize(Long.parseLong(catchSize2))
                .expireAfterWrite(Long.parseLong(catchTtl2), TimeUnit.MINUTES)
                .build();
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException, ClassNotFoundException {
//        Class.forName(driverClassName);
        log.info("driverClassName:{}, url:{}, username:{}, password:{}", driverClassName, url, username, password);
        log.info("getConnection");
        return DriverManager.getConnection(url, username, password);
    }


    public void eval(String spu_id) {
        try {
            if (StringUtils.isEmpty(spu_id)) {
                log.error("SupplyCouponSpuGetMinPriceSkusInfo 入参 为空 spu_id:{}", spu_id);
                collect(new Row(2));
                return;
            }

            int attempt = 0;
            boolean success = false;
            while (attempt < MAXATTEMPTS && !success) {
                try {
                    List<SkuInfo> skuInfos = queryMinPriceSkusInfo(spu_id);

                    for (SkuInfo skuInfo : skuInfos) {
                        String uid = objectMapper.readTree(skuInfo.getFeatures()).get("uid").toString();
                        Long merchant_id = getDimMerchantId(uid);
                        skuInfo.setMerchant_id(merchant_id);
                    }

                    Map<Long, List<SkuInfo>> skuGrouped = skuInfos.stream()
                            .collect(Collectors.groupingBy(SkuInfo::getSku_id));

                    HashMap<Long, String> mercSaleSkus = new HashMap<>();
                    for (List<SkuInfo> sameSkuInfos : skuGrouped.values()) {
                        SkuInfo skuInfoTop1 = sameSkuInfos.stream().min(Comparator.comparing(SkuInfo::getPrice)
                                .thenComparing(skuInfo -> skuInfo.getBid_type() == 5 || skuInfo.getBid_type() == 14 ? 0 : 1) // CASE WHEN逻辑
                                .thenComparing(SkuInfo::getCreate_time) // 按 `create_time` ASC
                                .thenComparing(SkuInfo::getId)) // 按 `id` ASC
                                .get();

                        Long merchant_id = skuInfoTop1.getMerchant_id();
                        Long sku_id = skuInfoTop1.getSku_id();
                        String skus = mercSaleSkus.get(merchant_id);
                        skus = skus == null ? "" + sku_id : skus + "," + sku_id;
                        mercSaleSkus.put(merchant_id, skus);
                    }


                    // collect
                    collect(Row.of(objectMapper.writeValueAsString(skuInfos), objectMapper.writeValueAsString(mercSaleSkus)));
                    success = true;
                } catch (SQLException e) {
                    log.error("SupplyCouponSpuGetMinPriceSkusInfo 第{}次查询失败, spu_id:{}", ++attempt, spu_id, e);
                    Thread.sleep(1000); // 等待1秒后重试
                }
            }

            if (attempt > 1) {
                if (success) {
                    log.warn("SupplyCouponSpuGetMinPriceSkusInfo 重试成功，重试{}次, spu_id:{}", attempt, spu_id);
                } else {
                    log.error("SupplyCouponSpuGetMinPriceSkusInfo 查询失败，重试{}次, spu_id:{}", attempt, spu_id);
                }
            }

        } catch (Exception e) {
            log.error("SupplyCouponSpuGetMinPriceSkusInfo 执行失败, spu_id:{}", spu_id, e);
        }
    }

    private Long getDimMerchantId(String uid) throws SQLException {
        Long merchant_id = cache2.getIfPresent(uid);
        if (merchant_id == null) {
            merchant_id = queryDimMerchantId(uid);
            if (merchant_id != null) cache2.put(uid, merchant_id);
        }

        return merchant_id;
    }

    private Long queryDimMerchantId(String uid) throws SQLException {
        String sql = "SELECT `merchantId`, `userId`, `status` " +
                "FROM `merchant`  " +
                "WHERE `userId`=? AND `status`=1";

        try (PreparedStatement ps = conn2.prepareStatement(sql)) {
            ps.setString(1, uid);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                return rs.getLong("merchantId");
            }
        }
        return null;
    }

    public List<SkuInfo> queryMinPriceSkusInfo(String spu_id) throws SQLException {
        List<SkuInfo> list = new ArrayList<>();
        String sql = "SELECT `id`, `spu_id`, `sku_id`, `bid_type`, `status`, `price`, `create_time`, `features` " +
                "FROM `sell_sku_min_price`  " +
                "WHERE `spu_id`=? AND `status`=0";

        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, spu_id);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                SkuInfo skuInfo = SkuInfo.builder()
                        .id(rs.getLong("id"))
                        .spu_id(rs.getLong("spu_id"))
                        .sku_id(rs.getLong("sku_id"))
                        .bid_type(rs.getInt("bid_type"))
                        .status(rs.getInt("status"))
                        .price(rs.getLong("price"))
                        .create_time(rs.getString("create_time"))
                        .features(rs.getString("features"))
                        .build();
                list.add(skuInfo);
            }
        }

        return list;
    }


    @Data
    @Builder
    public static class SkuInfo {
        Long id;
        Long spu_id;
        Long sku_id;
        Integer bid_type;
        Integer status;
        Long price;
        String create_time;
        String features;

        Long merchant_id;
    }

}
