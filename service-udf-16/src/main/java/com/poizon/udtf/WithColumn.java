package com.poizon.udtf;

import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Optional;

public class WithColumn extends TableFunction<Row> {

    public void eval(Object... message) {
        collect(Row.of(message));
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder().outputTypeStrategy(
                callContext -> {
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    DataType[] outputDataTypes = new DataType[argumentDataTypes.size()];
                    for (int i = 0; i < argumentDataTypes.size(); i++) {
                        outputDataTypes[i] = argumentDataTypes.get(i);
                    }
                    return Optional.of(DataTypes.ROW(outputDataTypes));
                }
        ).build();
    }
}
