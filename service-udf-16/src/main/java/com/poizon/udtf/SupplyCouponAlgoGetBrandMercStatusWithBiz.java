package com.poizon.udtf;

import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.exceptions.JedisException;

import java.sql.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@FunctionHint(output = @DataTypeHint("ROW<status STRING, algos_info STRING>"))
public class SupplyCouponAlgoGetBrandMercStatusWithBiz extends TableFunction<Row> {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private Connection conn;
    private transient JedisPool jedisPool;
    private static final String redisKeyPrefix = "coupon:investment:budget:";
    private static final String OFF = "off";
    private static final String ON = "on";
    private static final int MAXATTEMPTS = 4; // 最大重试次数

    @Override
    public void open(FunctionContext context) throws Exception {
        // jdbc init
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "**************************************************************************************************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "du_etl");
        String password = context.getJobParameter("jdbc.password", "");

        log.info("driverClassName:{}, url:{}, username:{}, password:{}", driverClassName, url, username, password);
        conn = getConnection(driverClassName, url, username, password);

        // jedisPool init
        String host = context.getJobParameter("redis.host", "kvredis-1dc95006c4.kvredis.shizhuang-inc.com");
        String port = context.getJobParameter("redis.port", "1371");
        String redisPassword = context.getJobParameter("redis.password", "");
        String dbNum = context.getJobParameter("redis.dbNum", "0");

        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setTestOnBorrow(true);
        jedisPool = new JedisPool(jedisPoolConfig, host, Integer.parseInt(port), 60000, redisPassword, Integer.parseInt(dbNum));
    }

    private Connection getConnection(String driverClassName, String url, String username, String password) throws SQLException {
        try {
            Class.forName(driverClassName);
        } catch (ClassNotFoundException e) {
            log.info(e.getMessage());
        }
        return DriverManager.getConnection(url, username, password);
    }


    public void eval(String brand_id, String merc_id, String biz_channel, String laoyue_biz_type) {
        try {
            if (StringUtils.isEmpty(brand_id) || StringUtils.isEmpty(merc_id)) {
                log.error("SupplyCouponAlgoGetBrandMercStatus 入参 为空 brand_id:{}，merc_id:{}", brand_id, merc_id);
                collect(new Row(2));
                return;
            }

            int attempt = 0;
            boolean success = false;
            while (attempt < MAXATTEMPTS && !success) {
                try {
                    String status = OFF;
                    List<SupplyCouponAlgo> supplyCouponAlgos = querySupplyCouponAlgo(brand_id, merc_id, laoyue_biz_type);
                    try (Jedis jedis = jedisPool.getResource()) {
                        for (SupplyCouponAlgo algo : supplyCouponAlgos) {
                            String redisKey = getRedisKey(algo, biz_channel);

                            Long brandSubsidyAmount = 0L;
                            Long brandSubsidyCount = 0L;
                            List<String> hmget = jedis.hmget(redisKey, "brand_subsidy_amount", "brand_subsidy_count");
                            if (!StringUtils.isEmpty(hmget.get(0))) {
                                brandSubsidyAmount = Long.parseLong(hmget.get(0));
                            }
                            if (!StringUtils.isEmpty(hmget.get(1))) {
                                brandSubsidyCount = Long.parseLong(hmget.get(1));
                            }

                            String algoStatus = checkBudget(algo, biz_channel, brandSubsidyAmount, brandSubsidyCount);
                            if (ON.equals(algoStatus)) {
                                status = ON;
                            }
                        }
                    }

                    // collect
                    Row row = new Row(2);
                    row.setField(0, status);
                    row.setField(1, objectMapper.writeValueAsString(supplyCouponAlgos));
                    collect(row);

                    success = true;
                } catch (SQLException | JedisException e) {
                    log.error("SupplyCouponAlgoGetBrandMercStatus 第{}次查询失败, brand_id:{},merc_id:{},biz_channel:{}", ++attempt, brand_id, merc_id, biz_channel, e);
                    Thread.sleep(1000); // 等待1秒后重试
                }
            }

            if (attempt > 1) {
                if (success) {
                    log.warn("SupplyCouponAlgoGetBrandMercStatus 重试成功，重试{}次, brand_id:{},merc_id:{},biz_channel:{}", attempt, brand_id, merc_id, biz_channel);
                } else {
                    log.error("SupplyCouponAlgoGetBrandMercStatus 查询失败，重试{}次, brand_id:{},merc_id:{},biz_channel:{}", attempt, brand_id, merc_id, biz_channel);
                }
            }

        } catch (Exception e) {
            log.error("SupplyCouponAlgoGetBrandMercStatus 执行失败, brand_id:{},merc_id:{},biz_channel:{}", brand_id, merc_id, biz_channel, e);
        }
    }

    private static String getRedisKey(SupplyCouponAlgo algo, String biz_channel) {
        String redisKey = redisKeyPrefix;
        if (!"newbie".equals(biz_channel)) {
            redisKey = redisKeyPrefix.concat(biz_channel).concat(":");
        }
        Long pool = BizType.isBudgetType2(algo.getBiz_type()) ? algo.getInvestment_activity_id() : algo.getId();
        redisKey = redisKey.concat(algo.getBiz_type() + ":" + pool);
        return redisKey;
    }

    private static String checkBudget(SupplyCouponAlgo algo, String biz_channel, Long brandSubsidyAmount, Long brandSubsidyCount) {
        algo.setBiz_channel(biz_channel);
        algo.setBrand_subsidy_amount(brandSubsidyAmount);
        algo.setBrand_subsidy_count(brandSubsidyCount);

        Long brandSubsidyNum = algo.getConsume_type().equals(1)?brandSubsidyCount:brandSubsidyAmount;
        String status = OFF;
        Long budget = 0L;

        switch (biz_channel) {
            case "newbie":
                budget = algo.getInvestment_amount();
                break;
            case "recall":
                budget = algo.getRecall_amount();
                break;
            case "older":
                budget = algo.getOlder_budget();
                break;
            case "alla":
                budget = algo.getAlla_budget();
                break;
            case "allb":
                budget = algo.getAllb_budget();
                break;
            default:
                log.error("biz_channel:{} 未识别", biz_channel);
        }
        if (budget > brandSubsidyNum) {
            algo.setStatus(ON);
            status = ON;
        } else {
            algo.setStatus(OFF);
        }
        return status;
    }

    public List<SupplyCouponAlgo> querySupplyCouponAlgo(String brand_id, String merc_id, String laoyue_biz_type) throws SQLException {
        List<SupplyCouponAlgo> list = new ArrayList<>();
        String sql = "SELECT id, brand_id, merchant_id, start_time, end_time, investment_activity_id, biz_type, investment_amount, recall_amount, older_budget, alla_budget, allb_budget, consume_type \n" +
                "FROM supply_coupon_algo \n" +
                "WHERE brand_id = ? AND merchant_id = ? \n" +   //  品牌*商户，任一存续期内的活动是有预算的，都算可以投放
                "AND online_status = 1 AND is_del = 0 \n" +
                "AND start_time <= now() AND now() < end_time \n" +
                "AND biz_type IN (" + laoyue_biz_type + ")";

        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, brand_id);
            ps.setString(2, merc_id);
            ResultSet rs = ps.executeQuery();

            while (rs.next()) {
                SupplyCouponAlgo supplyCouponAlgo = SupplyCouponAlgo.builder()
                        .id(rs.getLong("id"))
                        .brand_id(rs.getLong("brand_id"))
                        .merchant_id(rs.getString("merchant_id"))
                        .start_time(rs.getString("start_time"))
                        .end_time(rs.getString("end_time"))
                        .investment_activity_id(rs.getLong("investment_activity_id"))
                        .biz_type(rs.getInt("biz_type"))
                        .investment_amount(rs.getLong("investment_amount"))
                        .recall_amount(rs.getLong("recall_amount"))
                        .older_budget(rs.getLong("older_budget"))
                        .alla_budget(rs.getLong("alla_budget"))
                        .allb_budget(rs.getLong("allb_budget"))
                        .consume_type(rs.getInt("consume_type"))
                        .build();
                list.add(supplyCouponAlgo);
            }
        }

        return list;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SupplyCouponAlgo {
        Long id;                               // eventId
        Long brand_id;                         // 品牌id
        String merchant_id;                    // 商家id

        String start_time;
        String end_time;

        Long investment_activity_id;           // 招商活动id
        Integer biz_type;                      // 0 -- 普通，1 -- 星桥，2 -- 游戏化
        Long investment_amount;                // 出资金额 单位分
        Long recall_amount;                    // 召回分配金额1
        Long older_budget;                     // 老客预算，单位分
        Long alla_budget;                      // 万物a预算
        Long allb_budget;                      // 万物b预算

        Integer consume_type;                  // 消费类型 消耗方式：0:预算; 1:库存

        // 补充值
        String biz_channel;                    // 渠道
        Long brand_subsidy_amount;             // 预算消耗金额
        Long brand_subsidy_count;              // 库存消耗
        String status;                         // 活动状态
    }

}


// 共补券活动预算关系
@AllArgsConstructor
enum BudgetType {
    BudgetType1(1, "预算关系为1对1"),
    BudgetType2(2, "预算关系为1对多");

    private Integer code;         // 业务码
    private String name;          // 业务名称
}

// 共补券活动类型
@AllArgsConstructor
enum BizType {
    BizType1(1, "招商", BudgetType.BudgetType1),
    BizType2(2, "游戏化", BudgetType.BudgetType2),
    BizType3(3, "定投", BudgetType.BudgetType1),
    BizType4(4, "爆品", BudgetType.BudgetType1),
    BizType5(5, "游戏化商品补流", BudgetType.BudgetType2),
    BizType6(6, "男装游戏化", BudgetType.BudgetType2),
    BizType7(7, "货款扣减", BudgetType.BudgetType1);

    @Getter
    private Integer code;         // 业务码
    private String name;          // 业务名称
    private BudgetType budgetType;   // 1：预算关系为1对1、2：预算关系为1对多

    private static final Map<Integer, BizType> CODE_TO_BIZ_TYPE = Arrays.stream(BizType.values())
            .collect(Collectors.toMap(BizType::getCode, Function.identity()));

    public static BizType parse(Integer code) {
        return CODE_TO_BIZ_TYPE.get(code);
    }

    public static boolean isBudgetType2(Integer code) {
        return Objects.requireNonNull(parse(code)).budgetType.equals(BudgetType.BudgetType2);
    }
}