syntax = "proto3";


option java_package = "com.poizon.udtf.proto";
option java_outer_classname = "PushTextParseV2Proto";

message DumpInfo {
  //用户ID
  uint64 uid = 1;
  //reqId唯一标识
  string reqId = 2;
  //请求时间
  uint64 reqTime = 3;
  //召回队列
  repeated DumpItem recall = 4;
  //最终结果
  repeated DumpItem result = 5;
  //额外数，实验信息、请求头信息等
  map<string, string> ext = 6;
  //用户画像
  map<string, string> user_profile = 7;   //key-画像字段 value-就是jsonString
}

message DumpItem {
  //itemID
  string id = 1;
  // itemType
  string type = 2;
  // 场景id
  string sid = 3;
  //粗排分
  string psc = 4;
  //rank_ctr
  string ctr = 5;
  //文案创意
  repeated PushText texts = 6;
  //额外字段
  map<string, string> ext = 7;
}

//文案创意对象
message PushText {
  // 标题模版id
  string tid = 1;
  // 正文模版id
  string bid = 2;
  // 标题
  string title = 3;
  // 正文
  string body = 4;
  //额外字段
  map<string, string> ext = 5;
}