package com.poizon.utils;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.StringValue;
import com.google.protobuf.util.JsonFormat;

import java.util.List;

/**
 * <AUTHOR>
 * {@code @create} 2023-11-07 23:53
 **/
public class ProtobufUtils {
    private static final JsonFormat.Printer printer;
    private static final JsonFormat.Parser parser;

    private ProtobufUtils(){}

    static {
        JsonFormat.TypeRegistry registry = JsonFormat.TypeRegistry.newBuilder()
                .add(StringValue.getDescriptor())
                .build();

        printer = JsonFormat
                .printer()
                .usingTypeRegistry(registry)
//                .includingDefaultValueFields()
                .omittingInsignificantWhitespace();

        parser = JsonFormat
                .parser()
                .usingTypeRegistry(registry);
    }

    public static String toJson(Message message) {
        if (message == null) {
            return "";
        }
        try {
            return printer.print(message);
        } catch (InvalidProtocolBufferException e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }


    public static String toJson(List<? extends MessageOrBuilder> messageList) {
        if (messageList == null) {
            return "";
        }
        if (messageList.isEmpty()) {
            return "[]";
        }

        try {
            StringBuilder builder = new StringBuilder(1024);
            builder.append("[");
            for (MessageOrBuilder message : messageList) {
                printer.appendTo(message, builder);
                builder.append(",");
            }
            return builder.deleteCharAt(builder.length() - 1).append("]").toString();
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }


}

