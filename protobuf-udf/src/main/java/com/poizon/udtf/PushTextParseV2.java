package com.poizon.udtf;

import com.poizon.udtf.proto.PushTextParseV2Proto;
import com.poizon.utils.ProtobufUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;



/**
 * <AUTHOR>
 * @create 2025-05-23 11:18
 *
 * proto 文件存放目录 src/main/resources/proto
 * 编译按钮 Maven插件 protobuf:compile -f pom.xml
 **/
@Slf4j
@FunctionHint(
        input = {@DataTypeHint("VARBINARY")},
        output = @DataTypeHint("ROW<uid BIGINT, reqId String, reqTime BIGINT, recall String, result String, ext String, user_profile String>")
)
public class PushTextParseV2 extends TableFunction<Row> {
    private final ObjectMapper objectMapper = new ObjectMapper();;

    public void eval(byte[] bytes) {
        try {
            PushTextParseV2Proto.DumpInfo dumpInfo = PushTextParseV2Proto.DumpInfo.parseFrom(bytes);

            collect(Row.of(
                    dumpInfo.getUid(),
                    dumpInfo.getReqId(),
                    dumpInfo.getReqTime(),
                    ProtobufUtils.toJson(dumpInfo.getRecallList()),
                    ProtobufUtils.toJson(dumpInfo.getResultList()),
                    objectMapper.writeValueAsString(dumpInfo.getExtMap()),
                    objectMapper.writeValueAsString(dumpInfo.getUserProfileMap())
            ));

        } catch (Exception e) {
            log.error("Push数据 PushTextParseV2Proto.DumpInfo Pb数据解析失败:{}",e.getMessage(),e);
        }

    }
}
