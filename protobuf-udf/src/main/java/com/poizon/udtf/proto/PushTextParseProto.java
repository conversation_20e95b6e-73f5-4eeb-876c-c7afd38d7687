// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PushTextParse.proto

package com.poizon.udtf.proto;

public final class PushTextParseProto {
  private PushTextParseProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DumpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DumpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *用户ID
     * </pre>
     *
     * <code>uint64 uid = 1;</code>
     * @return The uid.
     */
    long getUid();

    /**
     * <pre>
     *reqId唯一标识
     * </pre>
     *
     * <code>string reqId = 2;</code>
     * @return The reqId.
     */
    java.lang.String getReqId();
    /**
     * <pre>
     *reqId唯一标识
     * </pre>
     *
     * <code>string reqId = 2;</code>
     * @return The bytes for reqId.
     */
    com.google.protobuf.ByteString
        getReqIdBytes();

    /**
     * <pre>
     *请求时间
     * </pre>
     *
     * <code>uint64 reqTime = 3;</code>
     * @return The reqTime.
     */
    long getReqTime();

    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> 
        getRecallList();
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    com.poizon.udtf.proto.PushTextParseProto.DumpItem getRecall(int index);
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    int getRecallCount();
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
        getRecallOrBuilderList();
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder getRecallOrBuilder(
        int index);

    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> 
        getResultList();
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    com.poizon.udtf.proto.PushTextParseProto.DumpItem getResult(int index);
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    int getResultCount();
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
        getResultOrBuilderList();
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder getResultOrBuilder(
        int index);

    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */
    int getExtCount();
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */
    boolean containsExt(
        java.lang.String key);
    /**
     * Use {@link #getExtMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExt();
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtMap();
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */

    java.lang.String getExtOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */

    java.lang.String getExtOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code DumpInfo}
   */
  public static final class DumpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DumpInfo)
      DumpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DumpInfo.newBuilder() to construct.
    private DumpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DumpInfo() {
      reqId_ = "";
      recall_ = java.util.Collections.emptyList();
      result_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DumpInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DumpInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              uid_ = input.readUInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              reqId_ = s;
              break;
            }
            case 24: {

              reqTime_ = input.readUInt64();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                recall_ = new java.util.ArrayList<com.poizon.udtf.proto.PushTextParseProto.DumpItem>();
                mutable_bitField0_ |= 0x00000001;
              }
              recall_.add(
                  input.readMessage(com.poizon.udtf.proto.PushTextParseProto.DumpItem.parser(), extensionRegistry));
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                result_ = new java.util.ArrayList<com.poizon.udtf.proto.PushTextParseProto.DumpItem>();
                mutable_bitField0_ |= 0x00000002;
              }
              result_.add(
                  input.readMessage(com.poizon.udtf.proto.PushTextParseProto.DumpItem.parser(), extensionRegistry));
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                ext_ = com.google.protobuf.MapField.newMapField(
                    ExtDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              ext__ = input.readMessage(
                  ExtDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              ext_.getMutableMap().put(
                  ext__.getKey(), ext__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          recall_ = java.util.Collections.unmodifiableList(recall_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          result_ = java.util.Collections.unmodifiableList(result_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpInfo_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 6:
          return internalGetExt();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.poizon.udtf.proto.PushTextParseProto.DumpInfo.class, com.poizon.udtf.proto.PushTextParseProto.DumpInfo.Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <pre>
     *用户ID
     * </pre>
     *
     * <code>uint64 uid = 1;</code>
     * @return The uid.
     */
    @java.lang.Override
    public long getUid() {
      return uid_;
    }

    public static final int REQID_FIELD_NUMBER = 2;
    private volatile java.lang.Object reqId_;
    /**
     * <pre>
     *reqId唯一标识
     * </pre>
     *
     * <code>string reqId = 2;</code>
     * @return The reqId.
     */
    @java.lang.Override
    public java.lang.String getReqId() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        reqId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *reqId唯一标识
     * </pre>
     *
     * <code>string reqId = 2;</code>
     * @return The bytes for reqId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getReqIdBytes() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reqId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQTIME_FIELD_NUMBER = 3;
    private long reqTime_;
    /**
     * <pre>
     *请求时间
     * </pre>
     *
     * <code>uint64 reqTime = 3;</code>
     * @return The reqTime.
     */
    @java.lang.Override
    public long getReqTime() {
      return reqTime_;
    }

    public static final int RECALL_FIELD_NUMBER = 4;
    private java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> recall_;
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> getRecallList() {
      return recall_;
    }
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
        getRecallOrBuilderList() {
      return recall_;
    }
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    @java.lang.Override
    public int getRecallCount() {
      return recall_.size();
    }
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.DumpItem getRecall(int index) {
      return recall_.get(index);
    }
    /**
     * <pre>
     *召回队列
     * </pre>
     *
     * <code>repeated .DumpItem recall = 4;</code>
     */
    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder getRecallOrBuilder(
        int index) {
      return recall_.get(index);
    }

    public static final int RESULT_FIELD_NUMBER = 5;
    private java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> result_;
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> getResultList() {
      return result_;
    }
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
        getResultOrBuilderList() {
      return result_;
    }
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    @java.lang.Override
    public int getResultCount() {
      return result_.size();
    }
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.DumpItem getResult(int index) {
      return result_.get(index);
    }
    /**
     * <pre>
     *最终结果
     * </pre>
     *
     * <code>repeated .DumpItem result = 5;</code>
     */
    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder getResultOrBuilder(
        int index) {
      return result_.get(index);
    }

    public static final int EXT_FIELD_NUMBER = 6;
    private static final class ExtDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpInfo_ExtEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> ext_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExt() {
      if (ext_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtDefaultEntryHolder.defaultEntry);
      }
      return ext_;
    }

    public int getExtCount() {
      return internalGetExt().getMap().size();
    }
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */

    @java.lang.Override
    public boolean containsExt(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetExt().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExt() {
      return getExtMap();
    }
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
      return internalGetExt().getMap();
    }
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */
    @java.lang.Override

    public java.lang.String getExtOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExt().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *额外数，实验信息、请求头信息等
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 6;</code>
     */
    @java.lang.Override

    public java.lang.String getExtOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExt().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (uid_ != 0L) {
        output.writeUInt64(1, uid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(reqId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, reqId_);
      }
      if (reqTime_ != 0L) {
        output.writeUInt64(3, reqTime_);
      }
      for (int i = 0; i < recall_.size(); i++) {
        output.writeMessage(4, recall_.get(i));
      }
      for (int i = 0; i < result_.size(); i++) {
        output.writeMessage(5, result_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExt(),
          ExtDefaultEntryHolder.defaultEntry,
          6);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, uid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(reqId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, reqId_);
      }
      if (reqTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, reqTime_);
      }
      for (int i = 0; i < recall_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, recall_.get(i));
      }
      for (int i = 0; i < result_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, result_.get(i));
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExt().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        ext__ = ExtDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(6, ext__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.poizon.udtf.proto.PushTextParseProto.DumpInfo)) {
        return super.equals(obj);
      }
      com.poizon.udtf.proto.PushTextParseProto.DumpInfo other = (com.poizon.udtf.proto.PushTextParseProto.DumpInfo) obj;

      if (getUid()
          != other.getUid()) return false;
      if (!getReqId()
          .equals(other.getReqId())) return false;
      if (getReqTime()
          != other.getReqTime()) return false;
      if (!getRecallList()
          .equals(other.getRecallList())) return false;
      if (!getResultList()
          .equals(other.getResultList())) return false;
      if (!internalGetExt().equals(
          other.internalGetExt())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (37 * hash) + REQID_FIELD_NUMBER;
      hash = (53 * hash) + getReqId().hashCode();
      hash = (37 * hash) + REQTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getReqTime());
      if (getRecallCount() > 0) {
        hash = (37 * hash) + RECALL_FIELD_NUMBER;
        hash = (53 * hash) + getRecallList().hashCode();
      }
      if (getResultCount() > 0) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResultList().hashCode();
      }
      if (!internalGetExt().getMap().isEmpty()) {
        hash = (37 * hash) + EXT_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExt().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.poizon.udtf.proto.PushTextParseProto.DumpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DumpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DumpInfo)
        com.poizon.udtf.proto.PushTextParseProto.DumpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpInfo_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 6:
            return internalGetExt();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 6:
            return internalGetMutableExt();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.poizon.udtf.proto.PushTextParseProto.DumpInfo.class, com.poizon.udtf.proto.PushTextParseProto.DumpInfo.Builder.class);
      }

      // Construct using com.poizon.udtf.proto.PushTextParseProto.DumpInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRecallFieldBuilder();
          getResultFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;

        reqId_ = "";

        reqTime_ = 0L;

        if (recallBuilder_ == null) {
          recall_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          recallBuilder_.clear();
        }
        if (resultBuilder_ == null) {
          result_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          resultBuilder_.clear();
        }
        internalGetMutableExt().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpInfo_descriptor;
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.DumpInfo getDefaultInstanceForType() {
        return com.poizon.udtf.proto.PushTextParseProto.DumpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.DumpInfo build() {
        com.poizon.udtf.proto.PushTextParseProto.DumpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.DumpInfo buildPartial() {
        com.poizon.udtf.proto.PushTextParseProto.DumpInfo result = new com.poizon.udtf.proto.PushTextParseProto.DumpInfo(this);
        int from_bitField0_ = bitField0_;
        result.uid_ = uid_;
        result.reqId_ = reqId_;
        result.reqTime_ = reqTime_;
        if (recallBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            recall_ = java.util.Collections.unmodifiableList(recall_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.recall_ = recall_;
        } else {
          result.recall_ = recallBuilder_.build();
        }
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            result_ = java.util.Collections.unmodifiableList(result_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.result_ = result_;
        } else {
          result.result_ = resultBuilder_.build();
        }
        result.ext_ = internalGetExt();
        result.ext_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.poizon.udtf.proto.PushTextParseProto.DumpInfo) {
          return mergeFrom((com.poizon.udtf.proto.PushTextParseProto.DumpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.poizon.udtf.proto.PushTextParseProto.DumpInfo other) {
        if (other == com.poizon.udtf.proto.PushTextParseProto.DumpInfo.getDefaultInstance()) return this;
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        if (!other.getReqId().isEmpty()) {
          reqId_ = other.reqId_;
          onChanged();
        }
        if (other.getReqTime() != 0L) {
          setReqTime(other.getReqTime());
        }
        if (recallBuilder_ == null) {
          if (!other.recall_.isEmpty()) {
            if (recall_.isEmpty()) {
              recall_ = other.recall_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRecallIsMutable();
              recall_.addAll(other.recall_);
            }
            onChanged();
          }
        } else {
          if (!other.recall_.isEmpty()) {
            if (recallBuilder_.isEmpty()) {
              recallBuilder_.dispose();
              recallBuilder_ = null;
              recall_ = other.recall_;
              bitField0_ = (bitField0_ & ~0x00000001);
              recallBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRecallFieldBuilder() : null;
            } else {
              recallBuilder_.addAllMessages(other.recall_);
            }
          }
        }
        if (resultBuilder_ == null) {
          if (!other.result_.isEmpty()) {
            if (result_.isEmpty()) {
              result_ = other.result_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureResultIsMutable();
              result_.addAll(other.result_);
            }
            onChanged();
          }
        } else {
          if (!other.result_.isEmpty()) {
            if (resultBuilder_.isEmpty()) {
              resultBuilder_.dispose();
              resultBuilder_ = null;
              result_ = other.result_;
              bitField0_ = (bitField0_ & ~0x00000002);
              resultBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResultFieldBuilder() : null;
            } else {
              resultBuilder_.addAllMessages(other.result_);
            }
          }
        }
        internalGetMutableExt().mergeFrom(
            other.internalGetExt());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.poizon.udtf.proto.PushTextParseProto.DumpInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.poizon.udtf.proto.PushTextParseProto.DumpInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long uid_ ;
      /**
       * <pre>
       *用户ID
       * </pre>
       *
       * <code>uint64 uid = 1;</code>
       * @return The uid.
       */
      @java.lang.Override
      public long getUid() {
        return uid_;
      }
      /**
       * <pre>
       *用户ID
       * </pre>
       *
       * <code>uint64 uid = 1;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *用户ID
       * </pre>
       *
       * <code>uint64 uid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object reqId_ = "";
      /**
       * <pre>
       *reqId唯一标识
       * </pre>
       *
       * <code>string reqId = 2;</code>
       * @return The reqId.
       */
      public java.lang.String getReqId() {
        java.lang.Object ref = reqId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          reqId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *reqId唯一标识
       * </pre>
       *
       * <code>string reqId = 2;</code>
       * @return The bytes for reqId.
       */
      public com.google.protobuf.ByteString
          getReqIdBytes() {
        java.lang.Object ref = reqId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          reqId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *reqId唯一标识
       * </pre>
       *
       * <code>string reqId = 2;</code>
       * @param value The reqId to set.
       * @return This builder for chaining.
       */
      public Builder setReqId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        reqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *reqId唯一标识
       * </pre>
       *
       * <code>string reqId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqId() {
        
        reqId_ = getDefaultInstance().getReqId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *reqId唯一标识
       * </pre>
       *
       * <code>string reqId = 2;</code>
       * @param value The bytes for reqId to set.
       * @return This builder for chaining.
       */
      public Builder setReqIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        reqId_ = value;
        onChanged();
        return this;
      }

      private long reqTime_ ;
      /**
       * <pre>
       *请求时间
       * </pre>
       *
       * <code>uint64 reqTime = 3;</code>
       * @return The reqTime.
       */
      @java.lang.Override
      public long getReqTime() {
        return reqTime_;
      }
      /**
       * <pre>
       *请求时间
       * </pre>
       *
       * <code>uint64 reqTime = 3;</code>
       * @param value The reqTime to set.
       * @return This builder for chaining.
       */
      public Builder setReqTime(long value) {
        
        reqTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *请求时间
       * </pre>
       *
       * <code>uint64 reqTime = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqTime() {
        
        reqTime_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> recall_ =
        java.util.Collections.emptyList();
      private void ensureRecallIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          recall_ = new java.util.ArrayList<com.poizon.udtf.proto.PushTextParseProto.DumpItem>(recall_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.poizon.udtf.proto.PushTextParseProto.DumpItem, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder, com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> recallBuilder_;

      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> getRecallList() {
        if (recallBuilder_ == null) {
          return java.util.Collections.unmodifiableList(recall_);
        } else {
          return recallBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public int getRecallCount() {
        if (recallBuilder_ == null) {
          return recall_.size();
        } else {
          return recallBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem getRecall(int index) {
        if (recallBuilder_ == null) {
          return recall_.get(index);
        } else {
          return recallBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder setRecall(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem value) {
        if (recallBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecallIsMutable();
          recall_.set(index, value);
          onChanged();
        } else {
          recallBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder setRecall(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder builderForValue) {
        if (recallBuilder_ == null) {
          ensureRecallIsMutable();
          recall_.set(index, builderForValue.build());
          onChanged();
        } else {
          recallBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder addRecall(com.poizon.udtf.proto.PushTextParseProto.DumpItem value) {
        if (recallBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecallIsMutable();
          recall_.add(value);
          onChanged();
        } else {
          recallBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder addRecall(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem value) {
        if (recallBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRecallIsMutable();
          recall_.add(index, value);
          onChanged();
        } else {
          recallBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder addRecall(
          com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder builderForValue) {
        if (recallBuilder_ == null) {
          ensureRecallIsMutable();
          recall_.add(builderForValue.build());
          onChanged();
        } else {
          recallBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder addRecall(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder builderForValue) {
        if (recallBuilder_ == null) {
          ensureRecallIsMutable();
          recall_.add(index, builderForValue.build());
          onChanged();
        } else {
          recallBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder addAllRecall(
          java.lang.Iterable<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItem> values) {
        if (recallBuilder_ == null) {
          ensureRecallIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, recall_);
          onChanged();
        } else {
          recallBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder clearRecall() {
        if (recallBuilder_ == null) {
          recall_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          recallBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public Builder removeRecall(int index) {
        if (recallBuilder_ == null) {
          ensureRecallIsMutable();
          recall_.remove(index);
          onChanged();
        } else {
          recallBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder getRecallBuilder(
          int index) {
        return getRecallFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder getRecallOrBuilder(
          int index) {
        if (recallBuilder_ == null) {
          return recall_.get(index);  } else {
          return recallBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
           getRecallOrBuilderList() {
        if (recallBuilder_ != null) {
          return recallBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(recall_);
        }
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder addRecallBuilder() {
        return getRecallFieldBuilder().addBuilder(
            com.poizon.udtf.proto.PushTextParseProto.DumpItem.getDefaultInstance());
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder addRecallBuilder(
          int index) {
        return getRecallFieldBuilder().addBuilder(
            index, com.poizon.udtf.proto.PushTextParseProto.DumpItem.getDefaultInstance());
      }
      /**
       * <pre>
       *召回队列
       * </pre>
       *
       * <code>repeated .DumpItem recall = 4;</code>
       */
      public java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder> 
           getRecallBuilderList() {
        return getRecallFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.poizon.udtf.proto.PushTextParseProto.DumpItem, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder, com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
          getRecallFieldBuilder() {
        if (recallBuilder_ == null) {
          recallBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.poizon.udtf.proto.PushTextParseProto.DumpItem, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder, com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder>(
                  recall_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          recall_ = null;
        }
        return recallBuilder_;
      }

      private java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> result_ =
        java.util.Collections.emptyList();
      private void ensureResultIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          result_ = new java.util.ArrayList<com.poizon.udtf.proto.PushTextParseProto.DumpItem>(result_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.poizon.udtf.proto.PushTextParseProto.DumpItem, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder, com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> resultBuilder_;

      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem> getResultList() {
        if (resultBuilder_ == null) {
          return java.util.Collections.unmodifiableList(result_);
        } else {
          return resultBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public int getResultCount() {
        if (resultBuilder_ == null) {
          return result_.size();
        } else {
          return resultBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem getResult(int index) {
        if (resultBuilder_ == null) {
          return result_.get(index);
        } else {
          return resultBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder setResult(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.set(index, value);
          onChanged();
        } else {
          resultBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder setResult(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.set(index, builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder addResult(com.poizon.udtf.proto.PushTextParseProto.DumpItem value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.add(value);
          onChanged();
        } else {
          resultBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder addResult(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultIsMutable();
          result_.add(index, value);
          onChanged();
        } else {
          resultBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder addResult(
          com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.add(builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder addResult(
          int index, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder builderForValue) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.add(index, builderForValue.build());
          onChanged();
        } else {
          resultBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder addAllResult(
          java.lang.Iterable<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItem> values) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, result_);
          onChanged();
        } else {
          resultBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public Builder removeResult(int index) {
        if (resultBuilder_ == null) {
          ensureResultIsMutable();
          result_.remove(index);
          onChanged();
        } else {
          resultBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder getResultBuilder(
          int index) {
        return getResultFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder getResultOrBuilder(
          int index) {
        if (resultBuilder_ == null) {
          return result_.get(index);  } else {
          return resultBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
           getResultOrBuilderList() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(result_);
        }
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder addResultBuilder() {
        return getResultFieldBuilder().addBuilder(
            com.poizon.udtf.proto.PushTextParseProto.DumpItem.getDefaultInstance());
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder addResultBuilder(
          int index) {
        return getResultFieldBuilder().addBuilder(
            index, com.poizon.udtf.proto.PushTextParseProto.DumpItem.getDefaultInstance());
      }
      /**
       * <pre>
       *最终结果
       * </pre>
       *
       * <code>repeated .DumpItem result = 5;</code>
       */
      public java.util.List<com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder> 
           getResultBuilderList() {
        return getResultFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.poizon.udtf.proto.PushTextParseProto.DumpItem, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder, com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.poizon.udtf.proto.PushTextParseProto.DumpItem, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder, com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder>(
                  result_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> ext_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExt() {
        if (ext_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtDefaultEntryHolder.defaultEntry);
        }
        return ext_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExt() {
        onChanged();;
        if (ext_ == null) {
          ext_ = com.google.protobuf.MapField.newMapField(
              ExtDefaultEntryHolder.defaultEntry);
        }
        if (!ext_.isMutable()) {
          ext_ = ext_.copy();
        }
        return ext_;
      }

      public int getExtCount() {
        return internalGetExt().getMap().size();
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */

      @java.lang.Override
      public boolean containsExt(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        return internalGetExt().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExt() {
        return getExtMap();
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
        return internalGetExt().getMap();
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */
      @java.lang.Override

      public java.lang.String getExtOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExt().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */
      @java.lang.Override

      public java.lang.String getExtOrThrow(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExt().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExt() {
        internalGetMutableExt().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */

      public Builder removeExt(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        internalGetMutableExt().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExt() {
        return internalGetMutableExt().getMutableMap();
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */
      public Builder putExt(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new NullPointerException("map key"); }
        if (value == null) {
  throw new NullPointerException("map value");
}

        internalGetMutableExt().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *额外数，实验信息、请求头信息等
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 6;</code>
       */

      public Builder putAllExt(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExt().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DumpInfo)
    }

    // @@protoc_insertion_point(class_scope:DumpInfo)
    private static final com.poizon.udtf.proto.PushTextParseProto.DumpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.poizon.udtf.proto.PushTextParseProto.DumpInfo();
    }

    public static com.poizon.udtf.proto.PushTextParseProto.DumpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DumpInfo>
        PARSER = new com.google.protobuf.AbstractParser<DumpInfo>() {
      @java.lang.Override
      public DumpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DumpInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DumpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DumpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.DumpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DumpItemOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DumpItem)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *itemID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    java.lang.String getId();
    /**
     * <pre>
     *itemID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <pre>
     * itemType
     * </pre>
     *
     * <code>string type = 2;</code>
     * @return The type.
     */
    java.lang.String getType();
    /**
     * <pre>
     * itemType
     * </pre>
     *
     * <code>string type = 2;</code>
     * @return The bytes for type.
     */
    com.google.protobuf.ByteString
        getTypeBytes();

    /**
     * <pre>
     * 场景id
     * </pre>
     *
     * <code>string sid = 3;</code>
     * @return The sid.
     */
    java.lang.String getSid();
    /**
     * <pre>
     * 场景id
     * </pre>
     *
     * <code>string sid = 3;</code>
     * @return The bytes for sid.
     */
    com.google.protobuf.ByteString
        getSidBytes();

    /**
     * <pre>
     *粗排分
     * </pre>
     *
     * <code>string psc = 4;</code>
     * @return The psc.
     */
    java.lang.String getPsc();
    /**
     * <pre>
     *粗排分
     * </pre>
     *
     * <code>string psc = 4;</code>
     * @return The bytes for psc.
     */
    com.google.protobuf.ByteString
        getPscBytes();

    /**
     * <pre>
     *rank_ctr
     * </pre>
     *
     * <code>string ctr = 5;</code>
     * @return The ctr.
     */
    java.lang.String getCtr();
    /**
     * <pre>
     *rank_ctr
     * </pre>
     *
     * <code>string ctr = 5;</code>
     * @return The bytes for ctr.
     */
    com.google.protobuf.ByteString
        getCtrBytes();

    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    java.util.List<com.poizon.udtf.proto.PushTextParseProto.PushText> 
        getTextsList();
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    com.poizon.udtf.proto.PushTextParseProto.PushText getTexts(int index);
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    int getTextsCount();
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder> 
        getTextsOrBuilderList();
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder getTextsOrBuilder(
        int index);

    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */
    int getExtCount();
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */
    boolean containsExt(
        java.lang.String key);
    /**
     * Use {@link #getExtMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExt();
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtMap();
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */

    java.lang.String getExtOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */

    java.lang.String getExtOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code DumpItem}
   */
  public static final class DumpItem extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DumpItem)
      DumpItemOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DumpItem.newBuilder() to construct.
    private DumpItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DumpItem() {
      id_ = "";
      type_ = "";
      sid_ = "";
      psc_ = "";
      ctr_ = "";
      texts_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DumpItem();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DumpItem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              id_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              type_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              sid_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              psc_ = s;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              ctr_ = s;
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                texts_ = new java.util.ArrayList<com.poizon.udtf.proto.PushTextParseProto.PushText>();
                mutable_bitField0_ |= 0x00000001;
              }
              texts_.add(
                  input.readMessage(com.poizon.udtf.proto.PushTextParseProto.PushText.parser(), extensionRegistry));
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                ext_ = com.google.protobuf.MapField.newMapField(
                    ExtDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              ext__ = input.readMessage(
                  ExtDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              ext_.getMutableMap().put(
                  ext__.getKey(), ext__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          texts_ = java.util.Collections.unmodifiableList(texts_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpItem_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 7:
          return internalGetExt();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.poizon.udtf.proto.PushTextParseProto.DumpItem.class, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private volatile java.lang.Object id_;
    /**
     * <pre>
     *itemID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *itemID
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private volatile java.lang.Object type_;
    /**
     * <pre>
     * itemType
     * </pre>
     *
     * <code>string type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * itemType
     * </pre>
     *
     * <code>string type = 2;</code>
     * @return The bytes for type.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SID_FIELD_NUMBER = 3;
    private volatile java.lang.Object sid_;
    /**
     * <pre>
     * 场景id
     * </pre>
     *
     * <code>string sid = 3;</code>
     * @return The sid.
     */
    @java.lang.Override
    public java.lang.String getSid() {
      java.lang.Object ref = sid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 场景id
     * </pre>
     *
     * <code>string sid = 3;</code>
     * @return The bytes for sid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSidBytes() {
      java.lang.Object ref = sid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PSC_FIELD_NUMBER = 4;
    private volatile java.lang.Object psc_;
    /**
     * <pre>
     *粗排分
     * </pre>
     *
     * <code>string psc = 4;</code>
     * @return The psc.
     */
    @java.lang.Override
    public java.lang.String getPsc() {
      java.lang.Object ref = psc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        psc_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *粗排分
     * </pre>
     *
     * <code>string psc = 4;</code>
     * @return The bytes for psc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPscBytes() {
      java.lang.Object ref = psc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        psc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CTR_FIELD_NUMBER = 5;
    private volatile java.lang.Object ctr_;
    /**
     * <pre>
     *rank_ctr
     * </pre>
     *
     * <code>string ctr = 5;</code>
     * @return The ctr.
     */
    @java.lang.Override
    public java.lang.String getCtr() {
      java.lang.Object ref = ctr_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ctr_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *rank_ctr
     * </pre>
     *
     * <code>string ctr = 5;</code>
     * @return The bytes for ctr.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCtrBytes() {
      java.lang.Object ref = ctr_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ctr_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TEXTS_FIELD_NUMBER = 6;
    private java.util.List<com.poizon.udtf.proto.PushTextParseProto.PushText> texts_;
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.poizon.udtf.proto.PushTextParseProto.PushText> getTextsList() {
      return texts_;
    }
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder> 
        getTextsOrBuilderList() {
      return texts_;
    }
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    @java.lang.Override
    public int getTextsCount() {
      return texts_.size();
    }
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.PushText getTexts(int index) {
      return texts_.get(index);
    }
    /**
     * <pre>
     *文案创意
     * </pre>
     *
     * <code>repeated .PushText texts = 6;</code>
     */
    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder getTextsOrBuilder(
        int index) {
      return texts_.get(index);
    }

    public static final int EXT_FIELD_NUMBER = 7;
    private static final class ExtDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpItem_ExtEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> ext_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExt() {
      if (ext_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtDefaultEntryHolder.defaultEntry);
      }
      return ext_;
    }

    public int getExtCount() {
      return internalGetExt().getMap().size();
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */

    @java.lang.Override
    public boolean containsExt(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetExt().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExt() {
      return getExtMap();
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
      return internalGetExt().getMap();
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */
    @java.lang.Override

    public java.lang.String getExtOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExt().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 7;</code>
     */
    @java.lang.Override

    public java.lang.String getExtOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExt().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(psc_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, psc_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ctr_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, ctr_);
      }
      for (int i = 0; i < texts_.size(); i++) {
        output.writeMessage(6, texts_.get(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExt(),
          ExtDefaultEntryHolder.defaultEntry,
          7);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(id_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(type_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, type_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(sid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(psc_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, psc_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(ctr_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, ctr_);
      }
      for (int i = 0; i < texts_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, texts_.get(i));
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExt().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        ext__ = ExtDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(7, ext__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.poizon.udtf.proto.PushTextParseProto.DumpItem)) {
        return super.equals(obj);
      }
      com.poizon.udtf.proto.PushTextParseProto.DumpItem other = (com.poizon.udtf.proto.PushTextParseProto.DumpItem) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (!getType()
          .equals(other.getType())) return false;
      if (!getSid()
          .equals(other.getSid())) return false;
      if (!getPsc()
          .equals(other.getPsc())) return false;
      if (!getCtr()
          .equals(other.getCtr())) return false;
      if (!getTextsList()
          .equals(other.getTextsList())) return false;
      if (!internalGetExt().equals(
          other.internalGetExt())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType().hashCode();
      hash = (37 * hash) + SID_FIELD_NUMBER;
      hash = (53 * hash) + getSid().hashCode();
      hash = (37 * hash) + PSC_FIELD_NUMBER;
      hash = (53 * hash) + getPsc().hashCode();
      hash = (37 * hash) + CTR_FIELD_NUMBER;
      hash = (53 * hash) + getCtr().hashCode();
      if (getTextsCount() > 0) {
        hash = (37 * hash) + TEXTS_FIELD_NUMBER;
        hash = (53 * hash) + getTextsList().hashCode();
      }
      if (!internalGetExt().getMap().isEmpty()) {
        hash = (37 * hash) + EXT_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExt().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.poizon.udtf.proto.PushTextParseProto.DumpItem prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DumpItem}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DumpItem)
        com.poizon.udtf.proto.PushTextParseProto.DumpItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpItem_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 7:
            return internalGetExt();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 7:
            return internalGetMutableExt();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.poizon.udtf.proto.PushTextParseProto.DumpItem.class, com.poizon.udtf.proto.PushTextParseProto.DumpItem.Builder.class);
      }

      // Construct using com.poizon.udtf.proto.PushTextParseProto.DumpItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTextsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = "";

        type_ = "";

        sid_ = "";

        psc_ = "";

        ctr_ = "";

        if (textsBuilder_ == null) {
          texts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          textsBuilder_.clear();
        }
        internalGetMutableExt().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_DumpItem_descriptor;
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem getDefaultInstanceForType() {
        return com.poizon.udtf.proto.PushTextParseProto.DumpItem.getDefaultInstance();
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem build() {
        com.poizon.udtf.proto.PushTextParseProto.DumpItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.DumpItem buildPartial() {
        com.poizon.udtf.proto.PushTextParseProto.DumpItem result = new com.poizon.udtf.proto.PushTextParseProto.DumpItem(this);
        int from_bitField0_ = bitField0_;
        result.id_ = id_;
        result.type_ = type_;
        result.sid_ = sid_;
        result.psc_ = psc_;
        result.ctr_ = ctr_;
        if (textsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            texts_ = java.util.Collections.unmodifiableList(texts_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.texts_ = texts_;
        } else {
          result.texts_ = textsBuilder_.build();
        }
        result.ext_ = internalGetExt();
        result.ext_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.poizon.udtf.proto.PushTextParseProto.DumpItem) {
          return mergeFrom((com.poizon.udtf.proto.PushTextParseProto.DumpItem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.poizon.udtf.proto.PushTextParseProto.DumpItem other) {
        if (other == com.poizon.udtf.proto.PushTextParseProto.DumpItem.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          onChanged();
        }
        if (!other.getType().isEmpty()) {
          type_ = other.type_;
          onChanged();
        }
        if (!other.getSid().isEmpty()) {
          sid_ = other.sid_;
          onChanged();
        }
        if (!other.getPsc().isEmpty()) {
          psc_ = other.psc_;
          onChanged();
        }
        if (!other.getCtr().isEmpty()) {
          ctr_ = other.ctr_;
          onChanged();
        }
        if (textsBuilder_ == null) {
          if (!other.texts_.isEmpty()) {
            if (texts_.isEmpty()) {
              texts_ = other.texts_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTextsIsMutable();
              texts_.addAll(other.texts_);
            }
            onChanged();
          }
        } else {
          if (!other.texts_.isEmpty()) {
            if (textsBuilder_.isEmpty()) {
              textsBuilder_.dispose();
              textsBuilder_ = null;
              texts_ = other.texts_;
              bitField0_ = (bitField0_ & ~0x00000001);
              textsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTextsFieldBuilder() : null;
            } else {
              textsBuilder_.addAllMessages(other.texts_);
            }
          }
        }
        internalGetMutableExt().mergeFrom(
            other.internalGetExt());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.poizon.udtf.proto.PushTextParseProto.DumpItem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.poizon.udtf.proto.PushTextParseProto.DumpItem) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object id_ = "";
      /**
       * <pre>
       *itemID
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The id.
       */
      public java.lang.String getId() {
        java.lang.Object ref = id_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *itemID
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        java.lang.Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *itemID
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *itemID
       * </pre>
       *
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = getDefaultInstance().getId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *itemID
       * </pre>
       *
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        id_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object type_ = "";
      /**
       * <pre>
       * itemType
       * </pre>
       *
       * <code>string type = 2;</code>
       * @return The type.
       */
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * itemType
       * </pre>
       *
       * <code>string type = 2;</code>
       * @return The bytes for type.
       */
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * itemType
       * </pre>
       *
       * <code>string type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * itemType
       * </pre>
       *
       * <code>string type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = getDefaultInstance().getType();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * itemType
       * </pre>
       *
       * <code>string type = 2;</code>
       * @param value The bytes for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        type_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object sid_ = "";
      /**
       * <pre>
       * 场景id
       * </pre>
       *
       * <code>string sid = 3;</code>
       * @return The sid.
       */
      public java.lang.String getSid() {
        java.lang.Object ref = sid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          sid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 场景id
       * </pre>
       *
       * <code>string sid = 3;</code>
       * @return The bytes for sid.
       */
      public com.google.protobuf.ByteString
          getSidBytes() {
        java.lang.Object ref = sid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 场景id
       * </pre>
       *
       * <code>string sid = 3;</code>
       * @param value The sid to set.
       * @return This builder for chaining.
       */
      public Builder setSid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        sid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 场景id
       * </pre>
       *
       * <code>string sid = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSid() {
        
        sid_ = getDefaultInstance().getSid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 场景id
       * </pre>
       *
       * <code>string sid = 3;</code>
       * @param value The bytes for sid to set.
       * @return This builder for chaining.
       */
      public Builder setSidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        sid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object psc_ = "";
      /**
       * <pre>
       *粗排分
       * </pre>
       *
       * <code>string psc = 4;</code>
       * @return The psc.
       */
      public java.lang.String getPsc() {
        java.lang.Object ref = psc_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          psc_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *粗排分
       * </pre>
       *
       * <code>string psc = 4;</code>
       * @return The bytes for psc.
       */
      public com.google.protobuf.ByteString
          getPscBytes() {
        java.lang.Object ref = psc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          psc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *粗排分
       * </pre>
       *
       * <code>string psc = 4;</code>
       * @param value The psc to set.
       * @return This builder for chaining.
       */
      public Builder setPsc(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        psc_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *粗排分
       * </pre>
       *
       * <code>string psc = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPsc() {
        
        psc_ = getDefaultInstance().getPsc();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *粗排分
       * </pre>
       *
       * <code>string psc = 4;</code>
       * @param value The bytes for psc to set.
       * @return This builder for chaining.
       */
      public Builder setPscBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        psc_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object ctr_ = "";
      /**
       * <pre>
       *rank_ctr
       * </pre>
       *
       * <code>string ctr = 5;</code>
       * @return The ctr.
       */
      public java.lang.String getCtr() {
        java.lang.Object ref = ctr_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          ctr_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *rank_ctr
       * </pre>
       *
       * <code>string ctr = 5;</code>
       * @return The bytes for ctr.
       */
      public com.google.protobuf.ByteString
          getCtrBytes() {
        java.lang.Object ref = ctr_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ctr_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *rank_ctr
       * </pre>
       *
       * <code>string ctr = 5;</code>
       * @param value The ctr to set.
       * @return This builder for chaining.
       */
      public Builder setCtr(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        ctr_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *rank_ctr
       * </pre>
       *
       * <code>string ctr = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCtr() {
        
        ctr_ = getDefaultInstance().getCtr();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *rank_ctr
       * </pre>
       *
       * <code>string ctr = 5;</code>
       * @param value The bytes for ctr to set.
       * @return This builder for chaining.
       */
      public Builder setCtrBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        ctr_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.poizon.udtf.proto.PushTextParseProto.PushText> texts_ =
        java.util.Collections.emptyList();
      private void ensureTextsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          texts_ = new java.util.ArrayList<com.poizon.udtf.proto.PushTextParseProto.PushText>(texts_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.poizon.udtf.proto.PushTextParseProto.PushText, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder, com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder> textsBuilder_;

      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public java.util.List<com.poizon.udtf.proto.PushTextParseProto.PushText> getTextsList() {
        if (textsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(texts_);
        } else {
          return textsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public int getTextsCount() {
        if (textsBuilder_ == null) {
          return texts_.size();
        } else {
          return textsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.PushText getTexts(int index) {
        if (textsBuilder_ == null) {
          return texts_.get(index);
        } else {
          return textsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder setTexts(
          int index, com.poizon.udtf.proto.PushTextParseProto.PushText value) {
        if (textsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTextsIsMutable();
          texts_.set(index, value);
          onChanged();
        } else {
          textsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder setTexts(
          int index, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder builderForValue) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.set(index, builderForValue.build());
          onChanged();
        } else {
          textsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder addTexts(com.poizon.udtf.proto.PushTextParseProto.PushText value) {
        if (textsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTextsIsMutable();
          texts_.add(value);
          onChanged();
        } else {
          textsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder addTexts(
          int index, com.poizon.udtf.proto.PushTextParseProto.PushText value) {
        if (textsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTextsIsMutable();
          texts_.add(index, value);
          onChanged();
        } else {
          textsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder addTexts(
          com.poizon.udtf.proto.PushTextParseProto.PushText.Builder builderForValue) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.add(builderForValue.build());
          onChanged();
        } else {
          textsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder addTexts(
          int index, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder builderForValue) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.add(index, builderForValue.build());
          onChanged();
        } else {
          textsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder addAllTexts(
          java.lang.Iterable<? extends com.poizon.udtf.proto.PushTextParseProto.PushText> values) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, texts_);
          onChanged();
        } else {
          textsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder clearTexts() {
        if (textsBuilder_ == null) {
          texts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          textsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public Builder removeTexts(int index) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.remove(index);
          onChanged();
        } else {
          textsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.PushText.Builder getTextsBuilder(
          int index) {
        return getTextsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder getTextsOrBuilder(
          int index) {
        if (textsBuilder_ == null) {
          return texts_.get(index);  } else {
          return textsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public java.util.List<? extends com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder> 
           getTextsOrBuilderList() {
        if (textsBuilder_ != null) {
          return textsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(texts_);
        }
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.PushText.Builder addTextsBuilder() {
        return getTextsFieldBuilder().addBuilder(
            com.poizon.udtf.proto.PushTextParseProto.PushText.getDefaultInstance());
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public com.poizon.udtf.proto.PushTextParseProto.PushText.Builder addTextsBuilder(
          int index) {
        return getTextsFieldBuilder().addBuilder(
            index, com.poizon.udtf.proto.PushTextParseProto.PushText.getDefaultInstance());
      }
      /**
       * <pre>
       *文案创意
       * </pre>
       *
       * <code>repeated .PushText texts = 6;</code>
       */
      public java.util.List<com.poizon.udtf.proto.PushTextParseProto.PushText.Builder> 
           getTextsBuilderList() {
        return getTextsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.poizon.udtf.proto.PushTextParseProto.PushText, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder, com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder> 
          getTextsFieldBuilder() {
        if (textsBuilder_ == null) {
          textsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.poizon.udtf.proto.PushTextParseProto.PushText, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder, com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder>(
                  texts_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          texts_ = null;
        }
        return textsBuilder_;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> ext_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExt() {
        if (ext_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtDefaultEntryHolder.defaultEntry);
        }
        return ext_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExt() {
        onChanged();;
        if (ext_ == null) {
          ext_ = com.google.protobuf.MapField.newMapField(
              ExtDefaultEntryHolder.defaultEntry);
        }
        if (!ext_.isMutable()) {
          ext_ = ext_.copy();
        }
        return ext_;
      }

      public int getExtCount() {
        return internalGetExt().getMap().size();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */

      @java.lang.Override
      public boolean containsExt(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        return internalGetExt().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExt() {
        return getExtMap();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
        return internalGetExt().getMap();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */
      @java.lang.Override

      public java.lang.String getExtOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExt().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */
      @java.lang.Override

      public java.lang.String getExtOrThrow(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExt().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExt() {
        internalGetMutableExt().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */

      public Builder removeExt(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        internalGetMutableExt().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExt() {
        return internalGetMutableExt().getMutableMap();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */
      public Builder putExt(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new NullPointerException("map key"); }
        if (value == null) {
  throw new NullPointerException("map value");
}

        internalGetMutableExt().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 7;</code>
       */

      public Builder putAllExt(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExt().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DumpItem)
    }

    // @@protoc_insertion_point(class_scope:DumpItem)
    private static final com.poizon.udtf.proto.PushTextParseProto.DumpItem DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.poizon.udtf.proto.PushTextParseProto.DumpItem();
    }

    public static com.poizon.udtf.proto.PushTextParseProto.DumpItem getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DumpItem>
        PARSER = new com.google.protobuf.AbstractParser<DumpItem>() {
      @java.lang.Override
      public DumpItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DumpItem(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DumpItem> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DumpItem> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.DumpItem getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PushTextOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PushText)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 标题模版id
     * </pre>
     *
     * <code>string tid = 1;</code>
     * @return The tid.
     */
    java.lang.String getTid();
    /**
     * <pre>
     * 标题模版id
     * </pre>
     *
     * <code>string tid = 1;</code>
     * @return The bytes for tid.
     */
    com.google.protobuf.ByteString
        getTidBytes();

    /**
     * <pre>
     * 正文模版id
     * </pre>
     *
     * <code>string bid = 2;</code>
     * @return The bid.
     */
    java.lang.String getBid();
    /**
     * <pre>
     * 正文模版id
     * </pre>
     *
     * <code>string bid = 2;</code>
     * @return The bytes for bid.
     */
    com.google.protobuf.ByteString
        getBidBytes();

    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 3;</code>
     * @return The title.
     */
    java.lang.String getTitle();
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 3;</code>
     * @return The bytes for title.
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <pre>
     * 正文
     * </pre>
     *
     * <code>string body = 4;</code>
     * @return The body.
     */
    java.lang.String getBody();
    /**
     * <pre>
     * 正文
     * </pre>
     *
     * <code>string body = 4;</code>
     * @return The bytes for body.
     */
    com.google.protobuf.ByteString
        getBodyBytes();

    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */
    int getExtCount();
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */
    boolean containsExt(
        java.lang.String key);
    /**
     * Use {@link #getExtMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExt();
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtMap();
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */

    java.lang.String getExtOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */

    java.lang.String getExtOrThrow(
        java.lang.String key);
  }
  /**
   * <pre>
   *文案创意对象
   * </pre>
   *
   * Protobuf type {@code PushText}
   */
  public static final class PushText extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:PushText)
      PushTextOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PushText.newBuilder() to construct.
    private PushText(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PushText() {
      tid_ = "";
      bid_ = "";
      title_ = "";
      body_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PushText();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PushText(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              tid_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              bid_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              title_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              body_ = s;
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                ext_ = com.google.protobuf.MapField.newMapField(
                    ExtDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              ext__ = input.readMessage(
                  ExtDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              ext_.getMutableMap().put(
                  ext__.getKey(), ext__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.poizon.udtf.proto.PushTextParseProto.internal_static_PushText_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 5:
          return internalGetExt();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.poizon.udtf.proto.PushTextParseProto.internal_static_PushText_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.poizon.udtf.proto.PushTextParseProto.PushText.class, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder.class);
    }

    public static final int TID_FIELD_NUMBER = 1;
    private volatile java.lang.Object tid_;
    /**
     * <pre>
     * 标题模版id
     * </pre>
     *
     * <code>string tid = 1;</code>
     * @return The tid.
     */
    @java.lang.Override
    public java.lang.String getTid() {
      java.lang.Object ref = tid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 标题模版id
     * </pre>
     *
     * <code>string tid = 1;</code>
     * @return The bytes for tid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTidBytes() {
      java.lang.Object ref = tid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BID_FIELD_NUMBER = 2;
    private volatile java.lang.Object bid_;
    /**
     * <pre>
     * 正文模版id
     * </pre>
     *
     * <code>string bid = 2;</code>
     * @return The bid.
     */
    @java.lang.Override
    public java.lang.String getBid() {
      java.lang.Object ref = bid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        bid_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 正文模版id
     * </pre>
     *
     * <code>string bid = 2;</code>
     * @return The bytes for bid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBidBytes() {
      java.lang.Object ref = bid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TITLE_FIELD_NUMBER = 3;
    private volatile java.lang.Object title_;
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 3;</code>
     * @return The title.
     */
    @java.lang.Override
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 3;</code>
     * @return The bytes for title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BODY_FIELD_NUMBER = 4;
    private volatile java.lang.Object body_;
    /**
     * <pre>
     * 正文
     * </pre>
     *
     * <code>string body = 4;</code>
     * @return The body.
     */
    @java.lang.Override
    public java.lang.String getBody() {
      java.lang.Object ref = body_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        body_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 正文
     * </pre>
     *
     * <code>string body = 4;</code>
     * @return The bytes for body.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBodyBytes() {
      java.lang.Object ref = body_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        body_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXT_FIELD_NUMBER = 5;
    private static final class ExtDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.poizon.udtf.proto.PushTextParseProto.internal_static_PushText_ExtEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> ext_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExt() {
      if (ext_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtDefaultEntryHolder.defaultEntry);
      }
      return ext_;
    }

    public int getExtCount() {
      return internalGetExt().getMap().size();
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */

    @java.lang.Override
    public boolean containsExt(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      return internalGetExt().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExt() {
      return getExtMap();
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
      return internalGetExt().getMap();
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */
    @java.lang.Override

    public java.lang.String getExtOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExt().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     *额外字段
     * </pre>
     *
     * <code>map&lt;string, string&gt; ext = 5;</code>
     */
    @java.lang.Override

    public java.lang.String getExtOrThrow(
        java.lang.String key) {
      if (key == null) { throw new NullPointerException("map key"); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExt().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(tid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, tid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(bid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, bid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(title_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, title_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(body_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, body_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExt(),
          ExtDefaultEntryHolder.defaultEntry,
          5);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(tid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, tid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(bid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, bid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(title_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, title_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(body_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, body_);
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExt().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        ext__ = ExtDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(5, ext__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.poizon.udtf.proto.PushTextParseProto.PushText)) {
        return super.equals(obj);
      }
      com.poizon.udtf.proto.PushTextParseProto.PushText other = (com.poizon.udtf.proto.PushTextParseProto.PushText) obj;

      if (!getTid()
          .equals(other.getTid())) return false;
      if (!getBid()
          .equals(other.getBid())) return false;
      if (!getTitle()
          .equals(other.getTitle())) return false;
      if (!getBody()
          .equals(other.getBody())) return false;
      if (!internalGetExt().equals(
          other.internalGetExt())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TID_FIELD_NUMBER;
      hash = (53 * hash) + getTid().hashCode();
      hash = (37 * hash) + BID_FIELD_NUMBER;
      hash = (53 * hash) + getBid().hashCode();
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
      hash = (37 * hash) + BODY_FIELD_NUMBER;
      hash = (53 * hash) + getBody().hashCode();
      if (!internalGetExt().getMap().isEmpty()) {
        hash = (37 * hash) + EXT_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExt().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.poizon.udtf.proto.PushTextParseProto.PushText parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.poizon.udtf.proto.PushTextParseProto.PushText prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *文案创意对象
     * </pre>
     *
     * Protobuf type {@code PushText}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PushText)
        com.poizon.udtf.proto.PushTextParseProto.PushTextOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_PushText_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 5:
            return internalGetExt();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 5:
            return internalGetMutableExt();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_PushText_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.poizon.udtf.proto.PushTextParseProto.PushText.class, com.poizon.udtf.proto.PushTextParseProto.PushText.Builder.class);
      }

      // Construct using com.poizon.udtf.proto.PushTextParseProto.PushText.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        tid_ = "";

        bid_ = "";

        title_ = "";

        body_ = "";

        internalGetMutableExt().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.poizon.udtf.proto.PushTextParseProto.internal_static_PushText_descriptor;
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.PushText getDefaultInstanceForType() {
        return com.poizon.udtf.proto.PushTextParseProto.PushText.getDefaultInstance();
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.PushText build() {
        com.poizon.udtf.proto.PushTextParseProto.PushText result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.poizon.udtf.proto.PushTextParseProto.PushText buildPartial() {
        com.poizon.udtf.proto.PushTextParseProto.PushText result = new com.poizon.udtf.proto.PushTextParseProto.PushText(this);
        int from_bitField0_ = bitField0_;
        result.tid_ = tid_;
        result.bid_ = bid_;
        result.title_ = title_;
        result.body_ = body_;
        result.ext_ = internalGetExt();
        result.ext_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.poizon.udtf.proto.PushTextParseProto.PushText) {
          return mergeFrom((com.poizon.udtf.proto.PushTextParseProto.PushText)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.poizon.udtf.proto.PushTextParseProto.PushText other) {
        if (other == com.poizon.udtf.proto.PushTextParseProto.PushText.getDefaultInstance()) return this;
        if (!other.getTid().isEmpty()) {
          tid_ = other.tid_;
          onChanged();
        }
        if (!other.getBid().isEmpty()) {
          bid_ = other.bid_;
          onChanged();
        }
        if (!other.getTitle().isEmpty()) {
          title_ = other.title_;
          onChanged();
        }
        if (!other.getBody().isEmpty()) {
          body_ = other.body_;
          onChanged();
        }
        internalGetMutableExt().mergeFrom(
            other.internalGetExt());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.poizon.udtf.proto.PushTextParseProto.PushText parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.poizon.udtf.proto.PushTextParseProto.PushText) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object tid_ = "";
      /**
       * <pre>
       * 标题模版id
       * </pre>
       *
       * <code>string tid = 1;</code>
       * @return The tid.
       */
      public java.lang.String getTid() {
        java.lang.Object ref = tid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          tid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 标题模版id
       * </pre>
       *
       * <code>string tid = 1;</code>
       * @return The bytes for tid.
       */
      public com.google.protobuf.ByteString
          getTidBytes() {
        java.lang.Object ref = tid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 标题模版id
       * </pre>
       *
       * <code>string tid = 1;</code>
       * @param value The tid to set.
       * @return This builder for chaining.
       */
      public Builder setTid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        tid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题模版id
       * </pre>
       *
       * <code>string tid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTid() {
        
        tid_ = getDefaultInstance().getTid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题模版id
       * </pre>
       *
       * <code>string tid = 1;</code>
       * @param value The bytes for tid to set.
       * @return This builder for chaining.
       */
      public Builder setTidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        tid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object bid_ = "";
      /**
       * <pre>
       * 正文模版id
       * </pre>
       *
       * <code>string bid = 2;</code>
       * @return The bid.
       */
      public java.lang.String getBid() {
        java.lang.Object ref = bid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          bid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 正文模版id
       * </pre>
       *
       * <code>string bid = 2;</code>
       * @return The bytes for bid.
       */
      public com.google.protobuf.ByteString
          getBidBytes() {
        java.lang.Object ref = bid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 正文模版id
       * </pre>
       *
       * <code>string bid = 2;</code>
       * @param value The bid to set.
       * @return This builder for chaining.
       */
      public Builder setBid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        bid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 正文模版id
       * </pre>
       *
       * <code>string bid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBid() {
        
        bid_ = getDefaultInstance().getBid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 正文模版id
       * </pre>
       *
       * <code>string bid = 2;</code>
       * @param value The bytes for bid to set.
       * @return This builder for chaining.
       */
      public Builder setBidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        bid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 3;</code>
       * @return The title.
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 3;</code>
       * @return The bytes for title.
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 3;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 3;</code>
       * @param value The bytes for title to set.
       * @return This builder for chaining.
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        title_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object body_ = "";
      /**
       * <pre>
       * 正文
       * </pre>
       *
       * <code>string body = 4;</code>
       * @return The body.
       */
      public java.lang.String getBody() {
        java.lang.Object ref = body_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          body_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 正文
       * </pre>
       *
       * <code>string body = 4;</code>
       * @return The bytes for body.
       */
      public com.google.protobuf.ByteString
          getBodyBytes() {
        java.lang.Object ref = body_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          body_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 正文
       * </pre>
       *
       * <code>string body = 4;</code>
       * @param value The body to set.
       * @return This builder for chaining.
       */
      public Builder setBody(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        body_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 正文
       * </pre>
       *
       * <code>string body = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBody() {
        
        body_ = getDefaultInstance().getBody();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 正文
       * </pre>
       *
       * <code>string body = 4;</code>
       * @param value The bytes for body to set.
       * @return This builder for chaining.
       */
      public Builder setBodyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        body_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> ext_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExt() {
        if (ext_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtDefaultEntryHolder.defaultEntry);
        }
        return ext_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExt() {
        onChanged();;
        if (ext_ == null) {
          ext_ = com.google.protobuf.MapField.newMapField(
              ExtDefaultEntryHolder.defaultEntry);
        }
        if (!ext_.isMutable()) {
          ext_ = ext_.copy();
        }
        return ext_;
      }

      public int getExtCount() {
        return internalGetExt().getMap().size();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */

      @java.lang.Override
      public boolean containsExt(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        return internalGetExt().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExt() {
        return getExtMap();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
        return internalGetExt().getMap();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */
      @java.lang.Override

      public java.lang.String getExtOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExt().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */
      @java.lang.Override

      public java.lang.String getExtOrThrow(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExt().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExt() {
        internalGetMutableExt().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */

      public Builder removeExt(
          java.lang.String key) {
        if (key == null) { throw new NullPointerException("map key"); }
        internalGetMutableExt().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExt() {
        return internalGetMutableExt().getMutableMap();
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */
      public Builder putExt(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new NullPointerException("map key"); }
        if (value == null) {
  throw new NullPointerException("map value");
}

        internalGetMutableExt().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       *额外字段
       * </pre>
       *
       * <code>map&lt;string, string&gt; ext = 5;</code>
       */

      public Builder putAllExt(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExt().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:PushText)
    }

    // @@protoc_insertion_point(class_scope:PushText)
    private static final com.poizon.udtf.proto.PushTextParseProto.PushText DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.poizon.udtf.proto.PushTextParseProto.PushText();
    }

    public static com.poizon.udtf.proto.PushTextParseProto.PushText getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PushText>
        PARSER = new com.google.protobuf.AbstractParser<PushText>() {
      @java.lang.Override
      public PushText parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PushText(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PushText> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PushText> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.poizon.udtf.proto.PushTextParseProto.PushText getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DumpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DumpInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DumpInfo_ExtEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DumpInfo_ExtEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DumpItem_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DumpItem_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DumpItem_ExtEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DumpItem_ExtEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PushText_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PushText_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PushText_ExtEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_PushText_ExtEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023PushTextParse.proto\"\272\001\n\010DumpInfo\022\013\n\003ui" +
      "d\030\001 \001(\004\022\r\n\005reqId\030\002 \001(\t\022\017\n\007reqTime\030\003 \001(\004\022" +
      "\031\n\006recall\030\004 \003(\0132\t.DumpItem\022\031\n\006result\030\005 \003" +
      "(\0132\t.DumpItem\022\037\n\003ext\030\006 \003(\0132\022.DumpInfo.Ex" +
      "tEntry\032*\n\010ExtEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value" +
      "\030\002 \001(\t:\0028\001\"\262\001\n\010DumpItem\022\n\n\002id\030\001 \001(\t\022\014\n\004t" +
      "ype\030\002 \001(\t\022\013\n\003sid\030\003 \001(\t\022\013\n\003psc\030\004 \001(\t\022\013\n\003c" +
      "tr\030\005 \001(\t\022\030\n\005texts\030\006 \003(\0132\t.PushText\022\037\n\003ex" +
      "t\030\007 \003(\0132\022.DumpItem.ExtEntry\032*\n\010ExtEntry\022" +
      "\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\216\001\n\010Push" +
      "Text\022\013\n\003tid\030\001 \001(\t\022\013\n\003bid\030\002 \001(\t\022\r\n\005title\030" +
      "\003 \001(\t\022\014\n\004body\030\004 \001(\t\022\037\n\003ext\030\005 \003(\0132\022.PushT" +
      "ext.ExtEntry\032*\n\010ExtEntry\022\013\n\003key\030\001 \001(\t\022\r\n" +
      "\005value\030\002 \001(\t:\0028\001B+\n\025com.poizon.udtf.prot" +
      "oB\022PushTextParseProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_DumpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_DumpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DumpInfo_descriptor,
        new java.lang.String[] { "Uid", "ReqId", "ReqTime", "Recall", "Result", "Ext", });
    internal_static_DumpInfo_ExtEntry_descriptor =
      internal_static_DumpInfo_descriptor.getNestedTypes().get(0);
    internal_static_DumpInfo_ExtEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DumpInfo_ExtEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_DumpItem_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_DumpItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DumpItem_descriptor,
        new java.lang.String[] { "Id", "Type", "Sid", "Psc", "Ctr", "Texts", "Ext", });
    internal_static_DumpItem_ExtEntry_descriptor =
      internal_static_DumpItem_descriptor.getNestedTypes().get(0);
    internal_static_DumpItem_ExtEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DumpItem_ExtEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_PushText_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_PushText_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PushText_descriptor,
        new java.lang.String[] { "Tid", "Bid", "Title", "Body", "Ext", });
    internal_static_PushText_ExtEntry_descriptor =
      internal_static_PushText_descriptor.getNestedTypes().get(0);
    internal_static_PushText_ExtEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_PushText_ExtEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
