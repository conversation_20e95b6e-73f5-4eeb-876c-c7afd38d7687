package com.alibaba.blink.udx.dimjoin;

import com.alibaba.blink.udx.JDBCUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.util.HBaseClient;
import com.util.population.MD5Utils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.shaded.calcite.com.google.common.collect.Lists;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.ScalarFunction;
import org.shaded.hadoop.hbase.client.Result;
import org.shaded.hadoop.hbase.util.Bytes;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class AbTestDimJoin extends ScalarFunction {


    private HashMap<String, Set<Integer>> cacheAll;
    private Long lastCacheAllTime = 0L;
    private HBaseClient hBaseClient;
    private Properties pro;

    LoadingCache<String, Result> hbaseCache;


    @Override
    public void open(FunctionContext context) throws SQLException {
        //数据库连接
        pro = new Properties();
        String driverClassName = context.getJobParameter("jdbc.driver.classname", "com.mysql.jdbc.Driver");
        String url = context.getJobParameter("jdbc.url", "*****************************************************************************************");
        String username = context.getJobParameter("jdbc.username", "dw_bigdata_abtest_redaction");
        String password = context.getJobParameter("jdbc.password", "5ricC2RnmpnizS3J");
        String initialSize = context.getJobParameter("jdbc.initialsize", "1");
        String maxActive = context.getJobParameter("jdbc.maxactive", "3");
        String maxWait = context.getJobParameter("jdbc.maxwait", "3000");
        String hbaseCacheSize = context.getJobParameter("hbase.cache.size", "500000");

        pro.put("driverClassName", driverClassName);
        pro.put("url", url);
        pro.put("username", username);
        pro.put("password", password);
        pro.put("initialSize", initialSize);
        pro.put("maxActive", maxActive);
        pro.put("maxWait", maxWait);
        pro.put("testWhileIdle", "true");
        pro.put("minEvictableIdleTimeMillis", "300000");
        pro.put("timeBetweenEvictionRunsMillis", "60000");

        System.out.println(url);
        System.out.println(username);
        System.out.println(password);

        cacheAll = new HashMap<>(32);

        cacheRdsAll();
//		hBaseClient = new HBaseClient("hb-bp1550j67036k1ia2-master1-001.hbase.rds.aliyuncs.com:2181,hb-bp1550j67036k1ia2-master2-001.hbase.rds.aliyuncs.com:2181,hb-bp1550j67036k1ia2-master3-001.hbase.rds.aliyuncs.com:2181", "bigdata_abtest_upgrade_results_client");
        hBaseClient = new HBaseClient("ld-bp1aq02r0wmr187yj-proxy-hbaseue.hbaseue.rds.aliyuncs.com:30020", "bigdata_abtest_upgrade_results_client");

        hbaseCache = CacheBuilder.newBuilder()
                .maximumSize(Integer.parseInt(hbaseCacheSize))
                .expireAfterAccess(30, TimeUnit.MINUTES)
                .concurrencyLevel(8).build(new CacheLoader<String, Result>() {
                    @Override
                    public Result load(String rowKey) throws Exception {
                        return hBaseClient.get(rowKey.getBytes(), "cf".getBytes());
                    }
                });

    }

    /**
     * 解析出埋点名字
     *
     * @param pointInfo 埋点信息
     * @return
     */
    public Set<String> getGetPointName(String pointInfo) {
        Set<String> results = Sets.newHashSet();
        JSONArray array = JSON.parseArray(pointInfo);
        if(Objects.nonNull(array)){
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                String pointName = object.getString("pointName");
                if (Objects.nonNull(pointName)) {
                    results.add(pointName);
                }

            }
        }
        return results;
    }

    private void cacheRdsAll() throws SQLException {
        JDBCUtils jdbcUtils = new JDBCUtils(pro);
        System.out.println("get connection success!");
        Connection conn = jdbcUtils.getConnection();
        String sql = "select\n" +
                " a.*,\n" +
                " b.`status` \n" +
                "from (\n" +
                "    SELECT \n" +
                "`exp_id` , `point_info`  \n" +
                "FROM `exp_approval` where `approval_status` = 3\n" +
                "and (`point_info` <> '' and `point_info` <> '[]')\n" +
                ") a left join `ab_exp` b on a.`exp_id` = b.`id` \n" +
                "where b.`status` = 2";


        System.out.println(sql);
        PreparedStatement pstmt = conn.prepareStatement(sql);
        ResultSet resultSet = pstmt.executeQuery();
        while (resultSet.next()) {//只能存在唯一关系
            Integer expId = resultSet.getInt("exp_id");
            String pointInfo = resultSet.getString("point_info");
            if(Objects.isNull(pointInfo)){
                System.out.println("warn:expId is:"+expId);
            }
            Set<String> pointNames = getGetPointName(pointInfo);
            for (String pointName : pointNames) {
                Set<Integer> exist = cacheAll.getOrDefault(pointName, Sets.newHashSet());
                exist.add(expId);
                cacheAll.put(pointName, exist);
            }
            System.out.println("expId is :" + expId + ", value is: " + pointInfo);
        }

        lastCacheAllTime = System.currentTimeMillis();
    }


    public String eval(String properties, String event, String currentPage, String blockType, String distinctId) throws Exception {
        if ((System.currentTimeMillis() - lastCacheAllTime > 3600000)) {//cache 1小时
            cacheRdsAll();
            System.out.println(" catch rds All end...");
        }
        String pointName = event;
        if (Objects.nonNull(currentPage)) {
            pointName += "_" + currentPage;
        }
        if (Objects.nonNull(blockType)) {
            pointName += "_" + blockType;
        }
        Set<Integer> expIds = cacheAll.get(pointName);
        if (CollectionUtils.isNotEmpty(expIds)) {
            List<String> rowKeyList = new ArrayList<>(expIds.size());
            for (Integer expId : expIds) {
                String userIdAndExpId = distinctId + "_" + expId;
                String rowKey = MD5Utils.md5(userIdAndExpId, "utf-8") + "_" + userIdAndExpId;
                rowKeyList.add(rowKey);
            }

            ImmutableMap<String, Result> cacheData = hbaseCache.getAll(rowKeyList);
            ImmutableSet<Map.Entry<String, Result>> entries = cacheData.entrySet();
            List<GroupInfo> groupInfoString = new ArrayList<>(cacheData.size());
            for (Map.Entry<String, Result> entry : entries) {
                Result result = entry.getValue();
                if (!result.isEmpty()) {
                    GroupInfo groupInfo = buildGroupInfo(result);
                    groupInfoString.add(groupInfo);
                }
//				else {
//					Result queryResult = hBaseClient.get(entry.getKey().getBytes(), "cf".getBytes());
//					if (!queryResult.isEmpty()) {
//						hbaseCache.put(entry.getKey(), queryResult);
//						GroupInfo groupInfo = buildGroupInfo(queryResult);
//						groupInfoString.add(groupInfo);
//					}
//				}
            }
            // update group_info_list
            JSONObject propertiesObject = JSON.parseObject(properties);
            propertiesObject.put("group_info_list", JSONObject.toJSON(groupInfoString));

            return propertiesObject.toJSONString();
        }
        return properties;

    }


    private GroupInfo buildGroupInfo(Result result) {
        String groupId = Bytes.toString(result.getValue("cf".getBytes(), "group_id".getBytes()));
        String groupName = Bytes.toString(result.getValue("cf".getBytes(), "group_name".getBytes()));
        String groupParam = Bytes.toString(result.getValue("cf".getBytes(), "group_param".getBytes()));
        String groupParamValue = Bytes.toString(result.getValue("cf".getBytes(), "group_param_value".getBytes()));
        String expVersion = Bytes.toString(result.getValue("cf".getBytes(), "exp_version".getBytes()));
        GroupInfo groupInfo = new GroupInfo();
        groupInfo.setGroupId(groupId);
        groupInfo.setGroupName(groupName);
        groupInfo.setGroupParam(groupParam);
        groupInfo.setGroupParamValue(groupParamValue);
        groupInfo.setExpVersion(expVersion);
        String[] split = Bytes.toString(result.getRow()).split("_");
        if (split.length == 3) {
            groupInfo.setExpId(split[2]);
        }
        return groupInfo;
    }


    public static void main(String[] args) throws Exception {
        Set<String> getPointName = new AbTestDimJoin().getGetPointName("[{\"blockDict\":\"收藏按钮\",\"business\":\"社区\",\"memo\":\"点击收藏按钮时触发\\n【470】469遗留的在视频详情页、首页视频tab增加商品列表浮层功能\",\"pageDict\":\"商品列表浮层\",\"pointName\":\"community_product_collect_entrance_click_575_19\",\"product\":\"得物\"},{\"blockDict\":\"收藏按钮\",\"business\":\"社区\",\"memo\":\"点击收藏按钮时触发。涉及前置页面：图文详情页、视频详情页、首页视频流\",\"pageDict\":\"标记的商品浮层\",\"pointName\":\"community_product_collect_entrance_click_148_19\",\"product\":\"得物\"}]");
        System.out.println(getPointName);

        String msg1 = "{\"properties\":{\"current_page\":575,\"block_type\":19},\"event\":\"community_product_collect_entrance_click\",\"distinct_id\":1}";
        String msg2 = "{\"properties\":{\"current_page\":148,\"block_type\":19},\"event\":\"community_product_collect_entrance_click\",\"distinct_id\":1}";
        String msg3 = "{\"properties\":{\"current_page\":575,\"block_type\":19},\"event\":\"community_product_collect_entrance_click\",\"distinct_id\":2}";
        ArrayList<String> strings = Lists.newArrayList(msg1, msg2, msg3);

        for (String string : strings) {

            JSONObject object = JSONObject.parseObject(string);
            String event = object.getString("event");
            String distinctId = object.getString("distinct_id");
            String blockType = object.getJSONObject("properties").getString("block_type");
            String currentPage = object.getJSONObject("properties").getString("current_page");

            String pointName = event;
            if (Objects.nonNull(currentPage)) {
                pointName += "_" + currentPage;
            }
            if (Objects.nonNull(blockType)) {
                pointName += "_" + blockType;
            }

            Map<String, Set<Integer>> rdsCatch = new HashMap<>(2);
            rdsCatch.put("community_product_collect_entrance_click_148_19", Sets.newHashSet(1, 2));
            rdsCatch.put("community_product_collect_entrance_click_575_19", Sets.newHashSet(1));

            String zkAddress = "hb-uf63uejwi4uoo2866-master3-001.hbase.rds.aliyuncs.com:2181,hb-uf63uejwi4uoo2866-master2-001.hbase.rds.aliyuncs.com:2181,hb-uf63uejwi4uoo2866-master1-001.hbase.rds.aliyuncs.com:2181";
            HBaseClient hBaseClient = new HBaseClient(zkAddress, "bigdata_abtest_upgrade_results_client");

            LoadingCache<String, Result> hbaseCache = CacheBuilder.newBuilder()
                    .maximumSize(50000)
                    .expireAfterAccess(30, TimeUnit.MINUTES)
                    .concurrencyLevel(8).build(new CacheLoader<String, Result>() {
                        @Override
                        public Result load(String rowKey) throws Exception {
                            return hBaseClient.get(rowKey.getBytes(), "cf".getBytes());
                        }
                    });

            AbTestDimJoin abTestDimJoin = new AbTestDimJoin();

            Set<Integer> expIds = rdsCatch.get(pointName);
            if (CollectionUtils.isNotEmpty(expIds)) {
                List<String> rowKeyList = new ArrayList<>(expIds.size());
                for (Integer expId : expIds) {
                    String userIdAndExpId = distinctId + "_" + expId;
                    String rowKey = MD5Utils.md5(userIdAndExpId, "utf-8") + "_" + userIdAndExpId;
                    rowKeyList.add(rowKey);
                }

                ImmutableMap<String, Result> cacheData = hbaseCache.getAll(rowKeyList);
                ImmutableSet<Map.Entry<String, Result>> entries = cacheData.entrySet();
                List<GroupInfo> groupInfoString = new ArrayList<>(cacheData.size());
                for (Map.Entry<String, Result> entry : entries) {
                    Result result = entry.getValue();
                    if (!result.isEmpty()) {
                        GroupInfo groupInfo = abTestDimJoin.buildGroupInfo(result);
                        groupInfoString.add(groupInfo);
                    } else {
                        Result queryResult = hBaseClient.get(entry.getKey().getBytes(), "cf".getBytes());
                        if (!queryResult.isEmpty()) {
                            hbaseCache.put(entry.getKey(), queryResult);
                            GroupInfo groupInfo = abTestDimJoin.buildGroupInfo(queryResult);
                            groupInfoString.add(groupInfo);
                        }
                    }
                }
//				System.out.println(groupInfoString);


            }


        }
    }
}

class GroupInfo {
    private String groupId;
    private String groupName;
    private String groupParam;
    private String groupParamValue;
    private String expId;
    private String expVersion;


    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupParam() {
        return groupParam;
    }

    public void setGroupParam(String groupParam) {
        this.groupParam = groupParam;
    }

    public String getGroupParamValue() {
        return groupParamValue;
    }

    public void setGroupParamValue(String groupParamValue) {
        this.groupParamValue = groupParamValue;
    }

    public String getExpId() {
        return expId;
    }

    public void setExpId(String expId) {
        this.expId = expId;
    }

    public String getExpVersion() {
        return expVersion;
    }

    public void setExpVersion(String expVersion) {
        this.expVersion = expVersion;
    }

    @Override
    public String toString() {
        return "GroupInfo{" +
                "groupId='" + groupId + '\'' +
                ", groupName='" + groupName + '\'' +
                ", groupParam='" + groupParam + '\'' +
                ", groupParamValue='" + groupParamValue + '\'' +
                ", expId='" + expId + '\'' +
                ", expVersion='" + expVersion + '\'' +
                '}';
    }
}
