package com.alibaba.blink.udtf;

import com.util.HBaseClient;
import com.util.cache.TimerExpireHashMap;
import com.util.treadpoolutils.TreadPoolUtils;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.ValidationException;
import org.apache.flink.table.catalog.DataTypeFactory;
import org.apache.flink.table.functions.FunctionContext;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.inference.TypeInference;
import org.apache.flink.table.types.logical.LogicalTypeFamily;
import org.apache.flink.types.Row;
import org.apache.hadoop.conf.Configuration;
import org.shaded.hadoop.hbase.Cell;
import org.shaded.hadoop.hbase.CellUtil;
import org.shaded.hadoop.hbase.HBaseConfiguration;
import org.shaded.hadoop.hbase.client.Result;
import org.shaded.hadoop.hbase.client.ResultScanner;
import org.shaded.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

public class HbaseScanDim extends TableFunction<Row> {
    private static final Logger logger = LoggerFactory.getLogger(HbaseScanDim.class);
    private ThreadPoolExecutor threadPoolExecutor;
    private volatile HBaseClient hBaseClient;
    private Configuration conf;
    private String tableName;
    private String columnFamily;
    //buff
    private Metadata metadata;
    private String resultType=null;
    private TimerExpireHashMap<String,ArrayList<String[]>> buff;
    private long cacheTTLMs;
    @Override
    public void open(FunctionContext context) throws Exception {
        super.open(context);
        String zookeeper = context.getJobParameter("HbaseScanDim.hbase.zookeeper.quorum", "");
        String username = context.getJobParameter("HbaseScanDim.hbase.client.username", "");
        String password = context.getJobParameter("HbaseScanDim.hbase.client.password", "");
        String cacheMod = context.getJobParameter("HbaseScanDim.hbase.client.cache", "LRU");
        String cacheSize = context.getJobParameter("HbaseScanDim.hbase.client.cacheSize", "1000");
        String cacheTTLMsStr = context.getJobParameter("HbaseScanDim.hbase.client.cacheTTLMs", "600000");
        resultType = context.getJobParameter("HbaseScanDim.hbase.client.ResultType", "");
        cacheTTLMs = Long.parseLong(cacheTTLMsStr);
        tableName = context.getJobParameter("HbaseScanDim.hbase.table.name", "");
        columnFamily = context.getJobParameter("HbaseScanDim.hbase.columnFamily", "");
        conf = initConf();
        this.conf.set("hbase.zookeeper.quorum", zookeeper);
        //增强版hbase
        this.conf.set("hbase.client.username", username);
        this.conf.set("hbase.client.password", password);
        threadPoolExecutor = TreadPoolUtils.getThreadPoolExecutor();
        hBaseClient=new HBaseClient(this.conf, tableName, threadPoolExecutor) ;
        logger.info("hbase.zookeeper.quorum:{}",zookeeper);
        //初始化buff
        innitBuff(cacheMod,Integer.parseInt(cacheSize),cacheTTLMs);
    }

    /**
     *
     * @param rowKey  查询键 默认使用"_"作为start  "|"作为end
     * @param columnNames 需要查询的列
     */
    public void eval(String rowKey,String... columnNames){
        innitMetadata(columnNames);
        ArrayList<String[]> arrayList = selectBuff(rowKey);
        joinDim(arrayList);
    }

    public void joinDim(ArrayList<String[]> arrayList){
        if (arrayList==null){
            collect(new Row(metadata.columnNum));
        }else {
            for (String[] strS : arrayList) {
                Row row = new Row(metadata.columnNum);
                for (int i = 0; i < strS.length; i++) {
                    row.setField(i,strS[i]);
                }
                collect(row);
            }
        }
    }
    public void  getHBaseClient(){
        if (hBaseClient == null){
            synchronized (HBaseClient.class){
                if (hBaseClient==null){
                    hBaseClient=new HBaseClient(conf, tableName, threadPoolExecutor) ;
                }
            }
        }
    }

    public ArrayList<String[]> selectBuff(String rowKey){
        ArrayList<String[]> str = buff.get(rowKey);
        if (str!=null){
            return str;
       }else {
            ArrayList<String[]> selectHbase = selectHbase(rowKey);
            if (selectHbase==null || selectHbase.isEmpty()){
                return null;
            }else
                buff.put(rowKey,selectHbase,cacheTTLMs /1000);
                return selectHbase;
        }

    }
    public ArrayList<String[]> selectHbase(String rowKey){
        getHBaseClient();
        ResultScanner scanner;
        try {
            scanner = hBaseClient.scanRowKeyByFamily(rowKey + "_", rowKey + "|", columnFamily);
        } catch (IOException e) {
            logger.error("scan err!!!!!! rowKey:{}",rowKey);
            throw new RuntimeException("scan err",e);
        }
        if (scanner==null){
            return null;
        }
        ArrayList<String[]> list = new ArrayList<>();
        for (Result result : scanner) {
            String[] strS = new String[metadata.columnNum];
            for (Cell cell : result.rawCells()) {
                String column = Bytes.toString(CellUtil.cloneQualifier(cell));
                if (metadata.containColumn(column)){
                    String resultType = metadata.getResultType(column);
                    byte[] bytes = CellUtil.cloneValue(cell);
                        switch (resultType){
                            case "bigint":
                                strS[metadata.getColumnIndex(column)] = String.valueOf(Bytes.toLong(bytes));
                                break;
                            case "boolean":
                                strS[metadata.getColumnIndex(column)] = String.valueOf(Bytes.toBoolean(bytes));
                                break;
                            case "float":
                                strS[metadata.getColumnIndex(column)] = String.valueOf(Bytes.toFloat(bytes));
                                break;
                            case "double":
                                strS[metadata.getColumnIndex(column)] = String.valueOf(Bytes.toDouble(bytes));
                                break;
                            default:
                                strS[metadata.getColumnIndex(column)] = Bytes.toString(CellUtil.cloneValue(cell));
                                break;
                        }
                }
            }
            list.add(strS);
        }
        scanner.close();
        return list;
    }
    public Configuration initConf(){
        conf= new Configuration();
        conf.setClassLoader(HBaseConfiguration.class.getClassLoader());
        conf.setBoolean("hbase.defaults.for.version.skip",true);
        conf.addResource("hbase-default.xml");
        conf.addResource("hbase-site.xml");

        return conf;
    }

    @Override
    public TypeInference getTypeInference(DataTypeFactory typeFactory) {
        return TypeInference.newBuilder()
                .outputTypeStrategy(callContext -> {
                    String functionName = callContext.getName();
                    //根据函数入参情况确定返回值
                    List<DataType> argumentDataTypes = callContext.getArgumentDataTypes();
                    if (argumentDataTypes.size() > 1) {
                        DataType[] outputDataTypes = new DataType[argumentDataTypes.size() - 1];
                        for (int i = 0; i < argumentDataTypes.size(); i++) {
                            DataType parameterDataType = argumentDataTypes.get(i);
                            if (!parameterDataType
                                    .getLogicalType()
                                    .getTypeRoot()
                                    .getFamilies()
                                    .contains(
                                            LogicalTypeFamily.CHARACTER_STRING)) {
                                throw new ValidationException(String.format(
                                        "function %s argument %s data type is not %s",
                                        functionName,
                                        i + 1,
                                        org.apache.flink.table.api.DataTypes.STRING().toString()));
                            }
                            if (i > 0) {
                                outputDataTypes[i - 1] = org.apache.flink.table.api.DataTypes.STRING();
                            }
                        }
                        return Optional.of(org.apache.flink.table.api.DataTypes.ROW(outputDataTypes));
                    } else if (argumentDataTypes.size() == 1) {
                        return Optional.of(org.apache.flink.table.api.DataTypes.ROW(DataTypes.STRING()));
                    } else {
                        throw new ValidationException(String.format(
                                "function %s argument is empty",
                                functionName));
                    }
                })
                .build();
    }

    @Override
    public void close() throws Exception {
        super.close();
        hBaseClient.close();
        threadPoolExecutor.shutdown();
        buff.close();
    }

    public void innitBuff(String cacheMod,int cacheSize,long cacheTTLMs){
        if ("LRU".equals(cacheMod)){
            int i = (int) ((cacheSize / 2 * 0.75f) + 1);
            buff = new TimerExpireHashMap<>(Math.max(i, 16),Math.max((cacheTTLMs / 100), 1000));
        }else {
            logger.error("未实现 的 cacheMod:{}",cacheMod);
            throw new RuntimeException("未实现 的 cacheMod");
        }

    }
    public void  innitMetadata(String[] columnNames){
        if (metadata==null){
            this.metadata= new Metadata(columnNames,resultType);
        }
    };

    static class Metadata {
        Map<String,Integer> columnNames=new HashMap<>();
        int columnNum;
        Map<String,String>  resultTypes=new HashMap<>();
        public Metadata(String[] columnNames) {
            this(columnNames,"");
        }
        public Metadata(String[] columnNames,String resultType) {
            logger.info("resultTypes:{}",resultType);
            if (!"".equals(resultType)){
                resultTypes=new HashMap<>();
                String[] types = resultType.split(",");
                for (String type : types) {
                    String[] split = type.split(":");
                    if (split.length==2){
                        resultTypes.put(split[0],split[1]);
                    }
                }
                logger.info("resultTypes:{},other is varchar", resultTypes);
            }
            init(columnNames);
            logger.info("columnNames:,{}", Arrays.toString(columnNames));
        }

        private void init(String[] columnNames){
            if (columnNames==null || columnNames.length==0){
                throw new RuntimeException("columnNames is null");
            }
            for (int i = 0; i < columnNames.length; i++) {
                this.columnNames.put(columnNames[i],i);
            }
            this.columnNum=columnNames.length;
        }

        public boolean containColumn(String name){
            return columnNames.containsKey(name);
        }

        public int getColumnIndex(String name){
            return columnNames.get(name);
        }

        public String getResultType(String name){
            return resultTypes.getOrDefault(name, "varchar");
        }
    }

    public static void main(String[] args) {
        String[] strings = {"1", "2", "3", "4"};
        Metadata metadata = new Metadata(strings,"hour_bigint");
        System.out.println(metadata.getResultType("ho"));
        System.out.println(metadata.resultTypes);
        System.out.println(metadata.columnNames.get("3"));
        System.out.println(String.valueOf(10000L));
    }
}
