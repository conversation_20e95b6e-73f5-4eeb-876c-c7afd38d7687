package com.alibaba.blink.udaf.acc;

public class MergeMetricACC {
    private Long historyBuff;
    private String historyBuffUpTime;

    private Long todayBuff;

    public MergeMetricACC() {
    }

    public Long getHistoryBuff() {
        return historyBuff;
    }

    public void setHistoryBuff(Long historyBuff) {
        this.historyBuff = historyBuff;
    }

    public String getHistoryBuffUpTime() {
        return historyBuffUpTime;
    }

    public void setHistoryBuffUpTime(String historyBuffUpTime) {
        this.historyBuffUpTime = historyBuffUpTime;
    }

    public Long getTodayBuff() {
        return todayBuff;
    }

    public void setTodayBuff(Long todayBuff) {
        this.todayBuff = todayBuff;
    }

    public void merge(MergeMetricACC otherMergeMetricACC){
        this.todayBuff=todayBuff==null?otherMergeMetricACC.getTodayBuff():todayBuff+otherMergeMetricACC.getTodayBuff();
        this.historyBuff=otherMergeMetricACC.getHistoryBuff();
        this.historyBuffUpTime=otherMergeMetricACC.getHistoryBuffUpTime();
    }
}