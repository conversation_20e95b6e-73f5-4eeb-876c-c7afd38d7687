package com.util.treadpoolutils;

import com.util.treadpoolutils.common.ThreadPoolExecutors;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

public class TreadPoolUtils {

    public static ThreadPoolExecutor getThreadPoolExecutor(){
        return ThreadPoolExecutors.THREAD_POOL_EXECUTOR;
    }

    public static ScheduledThreadPoolExecutor getScheduledThreadPoolExecutor(){
        return ThreadPoolExecutors.SCHEDULED_THREAD_POOL_EXECUTOR;
    }

}
