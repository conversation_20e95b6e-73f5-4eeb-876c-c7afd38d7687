package com.util.treadpoolutils.common.base;

import java.io.Serializable;
import java.util.concurrent.ScheduledThreadPoolExecutor;


public enum ScheduledThreadPoolSingleton implements Serializable {
    INSTANCE;

    private static final ScheduledThreadPoolExecutor threadPoolExecutor=new ScheduledThreadPoolExecutor(10);

    public ScheduledThreadPoolExecutor getInstance(){
        return   threadPoolExecutor;
    }
}
