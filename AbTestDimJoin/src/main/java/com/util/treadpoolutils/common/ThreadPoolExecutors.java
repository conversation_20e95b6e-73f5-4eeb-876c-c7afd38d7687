package com.util.treadpoolutils.common;


import com.util.treadpoolutils.common.base.ScheduledThreadPoolSingleton;
import com.util.treadpoolutils.common.base.ThreadPoolSingleton;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolExecutors {

    public static final ThreadPoolExecutor THREAD_POOL_EXECUTOR= ThreadPoolSingleton.INSTANCE.getInstance();

    public static final ScheduledThreadPoolExecutor SCHEDULED_THREAD_POOL_EXECUTOR= ScheduledThreadPoolSingleton.INSTANCE.getInstance();

}
