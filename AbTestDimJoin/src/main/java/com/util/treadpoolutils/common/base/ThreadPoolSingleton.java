package com.util.treadpoolutils.common.base;

import java.io.Serializable;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Singleton模式
 * 线程池
 * 一个由链表结构组成的双向阻塞队列
 * 不限制存活时间
 */
public enum ThreadPoolSingleton implements Serializable {
    INSTANCE;
    private static final ThreadPoolExecutor threadPoolExecutor=new ThreadPoolExecutor(
            4,
            20,
            600L,
            TimeUnit.SECONDS,
            new LinkedBlockingDeque<>());

    public ThreadPoolExecutor getInstance(){
        return   threadPoolExecutor;
    }
}
