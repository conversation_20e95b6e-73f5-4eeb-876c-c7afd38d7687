package com.util.cache;

import java.util.Arrays;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings("unchecked")
public class TimerExpireHashMap<K, V> extends ConcurrentHashMap<K, V> {

    // 定时删除过期键，间隔时间
    private static final long DEFAULT_CHECK_TIME_SECOND = 1 * 1000;

    private static final float DEFAULT_LOAD_FACTOR = 0.75f;
    // 默认过期时间容器初始化数量
    private static final int DEFAULT_INITIAL_CAPACITY = 8;

    // 定时器
    private final Timer timer = new Timer();

    // 时间容器
    private ConcurrentHashMap<Object, Long> timerMap;

    // 过期数据回调
    @SuppressWarnings("rawtypes")
    private TimerExpireHashMapCallback timerExpireHashMapCallback ;

    /**
     * 定时删除过期键
     */
    private final TimerTask timerTask = new TimerTask() {
        @SuppressWarnings("unchecked")
        @Override
        public void run() {
            long currentTime = System.currentTimeMillis();
            timerMap.keySet().removeIf(key -> {
                Long keyTime = timerMap.get(key);
                if (currentTime >= keyTime.longValue()) {
                    if(timerExpireHashMapCallback != null) {
                        try {
                            timerExpireHashMapCallback.callback((K)key, (V)TimerExpireHashMap.super.get(key));
                        } catch (RuntimeException e) {
                            e.printStackTrace();
                        }
                    }
                    remove(key);
                    return true;
                }
                return false;
            });
        }
    };

    /**
     * 构造方法
     * @param initialCapacity   容器初始数量
     * @param loadFactor        随机因子
     */
    public TimerExpireHashMap(int initialCapacity, float loadFactor) {
        this(initialCapacity,loadFactor,DEFAULT_CHECK_TIME_SECOND);
    }

    /**
     * 构造方法
     * @param initialCapacity   容器初始数量
     */
    public TimerExpireHashMap(int initialCapacity) {
        this(initialCapacity,DEFAULT_LOAD_FACTOR);
    }

    /**
     * 构造方法
     */
    public TimerExpireHashMap() {
        super();
        init(DEFAULT_INITIAL_CAPACITY, 	DEFAULT_LOAD_FACTOR,DEFAULT_CHECK_TIME_SECOND);
    }
    /**
     * 构造方法
     */
    public TimerExpireHashMap(int initialCapacity, long checkTimeSecond) {
        this(initialCapacity,DEFAULT_LOAD_FACTOR,checkTimeSecond);
    }

    /**
     * 构造方法
     */
    public TimerExpireHashMap(Long checkTimeSecond) {
        super();
        init(DEFAULT_INITIAL_CAPACITY, 	DEFAULT_LOAD_FACTOR,checkTimeSecond);
    }
    /**
     * 构造方法
     */
    public TimerExpireHashMap(int initialCapacity, float loadFactor, long checkTimeSecond) {
        super(initialCapacity, loadFactor);
        init(initialCapacity, loadFactor,checkTimeSecond);
    }

    /**
     * 构造方法
     * @param map
     */
    public TimerExpireHashMap(Map<K, V> map) {
        super(map);
        init(DEFAULT_INITIAL_CAPACITY, DEFAULT_LOAD_FACTOR,DEFAULT_CHECK_TIME_SECOND);
    }

    /**
     * 初始化过期时间容器
     * @param initialCapacity   容器初始数量
     * @param loadFactor        随机因子
     */
    private void init(int initialCapacity, float loadFactor,long checkTimeSecond) {
        timerMap = new ConcurrentHashMap<>(initialCapacity, loadFactor);
        if (checkTimeSecond<=0){
            throw new RuntimeException("checkTimeSecond <=0");
        }
        timer.scheduleAtFixedRate(timerTask, checkTimeSecond,  checkTimeSecond);
    }

    /**
     * 获取数据
     * @param key
     */
    @Override
    public V get(Object key) {
        Long expireTime = checkKeyExpireTime(key);
        if (expireTime == null || expireTime > 0) {
            return super.get(key);
        }
        return null;
    }

    /**
     * 放入数据
     * @param key           键值
     * @param value         数据
     * @param expireSecond  过期时间（秒）
     * @return  数据
     */
    public V put(K key, V value, Long expireSecond) {
        if(expireSecond != null && expireSecond.longValue() > 0) {
            setKeyExpireTime(key, expireSecond);
        }
        return super.put(key, value);
    }

    /**
     * 返回key过期剩余时间（秒）
     * @param key   键值
     * @return      返回key过期剩余时间（秒）
     */
    public Long checkKeyExpireTime(Object key) {
        Long second = timerMap.get(key);
        if(second == null) {
            return null;
        }
        long currentTime = System.currentTimeMillis();
        return ((second.longValue() - currentTime) / 1000);
    }

    /**
     * 为键值设置过期时间
     * @param key               键值
     * @param expireSecond      过期时间（秒）
     */
    public void setKeyExpireTime(Object key, Long expireSecond) {
        if (expireSecond != null && expireSecond.longValue() > 0 /* && this.containsKey(key) */) {
            long currentTime = System.currentTimeMillis();
            long expireTime = currentTime + (expireSecond * 1000);
            timerMap.put(key, expireTime);
        }
    }

    /**
     * 设置过期数据设置监听
     * @param timerExpireHashMapCallback    监听回调
     */
    public void setTimerExpireHashMapCallback(TimerExpireHashMapCallback<K, V> timerExpireHashMapCallback) {
        this.timerExpireHashMapCallback = timerExpireHashMapCallback;
    }

    /**
     * 数据设置回调
     * @param <K>
     * @param <V>
     */
    static interface TimerExpireHashMapCallback<K, V> {
        /**
         * 监听回调
         * @param key   过期键
         * @param value 过期值
         * @throws RuntimeException
         */
        public void callback(K key, V value) throws RuntimeException;
    }
    public void close(){
        if (timer!=null)
        timer.cancel();
    }
    public static void main(String[] args) throws InterruptedException {
        TimerExpireHashMap<String, String[]> hashMap = new TimerExpireHashMap<>(100,2);
        hashMap.put("sss",new String[]{"1","2","3","4"},1L);
        hashMap.put("sss4",new String[]{"1","2","3","4"},5L);
        hashMap.put("sss5",new String[]{"1","2","3","4"},6L);
        System.out.println(Arrays.toString(hashMap.get("sss")));
        for (int i = 0; i < 10; i++) {
            new Thread() {
                @Override
                public void run() {
                    hashMap.put("sss",new String[]{"1","2","3","4"},4L);
                    System.out.println(Arrays.toString(hashMap.get("sss")));
                }
            }.start();
        }
        Thread.sleep(4000L);
        hashMap.put("sss2",new String[]{"1","2","3"},2L);
        System.out.println(Arrays.toString(hashMap.get("sss")));
        System.out.println(Arrays.toString(hashMap.get("sss2")));
        System.out.println(hashMap.containsKey("sss"));
        hashMap.close();
    }
}



