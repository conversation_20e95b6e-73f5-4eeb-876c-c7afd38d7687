package com.util.timeutils;

import com.util.timeutils.common.DateTimeFormatters;

import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;


/**
 * 使用LocalDataTime类Formatter 时间，效率更高；
 * 期间使用的DateTimeFormatter 均为单例模式 线程安全
 */
public class TimeUtils {

    /**
     * 格式化成30min
     *
     * @param time 13位时间戳
     * @return "yyyy-MM-dd HH:mm"
     */
    public static String timeFormatBy30Min(Long time) {

        LocalDateTime dateTime = timeStampToDateTime(time);

        return timeFormatBy30Min(dateTime);

    }

    /**
     * 格式化成30min
     *
     * @param dateTime LocalDateTime
     * @return "yyyy-MM-dd HH:mm"
     */
    public static String timeFormatBy30Min(LocalDateTime dateTime) {

        int minute = dateTime.getMinute();

        return (minute < 30) ? dateTime.withMinute(0).format(DateTimeFormatters.Pattern_MIN) : dateTime.withMinute(30).format(DateTimeFormatters.Pattern_MIN);

    }

    /**
     * 格式化成天
     * @param dateTime LocalDateTime
     * @return "yyyy-MM-dd"
     */
    public static String timeFormatByDay(LocalDateTime dateTime) {

        return dateTime.format(DateTimeFormatters.Pattern_DAY);

    }
    /**
     * 格式化成天
     * @param dateTime LocalDateTime
     * @return "yyyyMMdd"
     */

    public static String timeFormatByDayyyyMMdd(LocalDateTime dateTime) {
        return dateTime.format(DateTimeFormatters.Pattern_yyyyMMdd);

    }
    /**
     * 格式化成天
     * @param time Time
     * @return "yyyyMMdd"
     */
    public static String timeFormatByDayyyyMMdd(Long time) {
        LocalDateTime dateTime = timeStampToDateTime(time);
        return timeFormatByDayyyyMMdd(dateTime);

    }

    /**
     * 格式化成天
     * @param time 13位时间戳
     * @return "yyyy-MM-dd"
     */

    public static String timeFormatByDay(Long time) {
        LocalDateTime dateTime = timeStampToDateTime(time);
        return timeFormatByDay(dateTime);

    }

    public static Long timeStampFormatByDay(Long time) {
        LocalDateTime dateTime = timeStampToDateTime(time);
        return timeStampFormatByDay(dateTime);

    }
    public static Long timeStampFormatByDay(Long time,ZoneOffset zoneOffset) {
        LocalDateTime dateTime = timeStampToDateTime(time);
        return timeStampFormatByDay(dateTime,zoneOffset);

    }
    public static Long timeStampFormatByDay(LocalDateTime time) {
      return   timeStampFormatByDay(time,ZoneOffset.ofHours(8));

    }
    public static Long timeStampFormatByDay(LocalDateTime time,ZoneOffset zoneOffset) {
        return   time.withMinute(0).withHour(0).withSecond(0).withNano(0).toEpochSecond(zoneOffset);

    }

    /**
     *
     * @param time 13位时间戳
     * @param diffValue 需要减去的值
     * @param timeType ChronoUnit 枚举类，表示加减的类型
     * @return 10位时间戳
     */
    public static LocalDateTime timeSubtract(LocalDateTime time,Long diffValue ,ChronoUnit timeType) {
        return time.minus(diffValue, timeType);
    }
    public static Long timeSubtract(Long time,Long diffValue ,ChronoUnit timeType) {
        LocalDateTime dateTime = timeStampToDateTime(time);
        return dateTimeToTimeStamp(timeSubtract(dateTime,diffValue,timeType));
    }
    public static LocalDateTime timeAdd(LocalDateTime time,Long diffValue ,ChronoUnit timeType) {
        return time.plus(diffValue, timeType);
    }
    public static Long timeAdd(Long time,Long diffValue ,ChronoUnit timeType) {
        LocalDateTime dateTime = timeStampToDateTime(time);
        return dateTimeToTimeStamp(timeAdd(dateTime,diffValue,timeType));
    }

    /**
     * 时间戳转localDatetime 默认东8区
     *
     * @param time 13位时间戳
     * @return LocalDateTime
     */
    public static LocalDateTime timeStampToDateTime(Long time) {

        return timeStampToDateTime(time, ZoneOffset.ofHours(8));

    }

    /**
     * 时间戳转localDatetime
     *
     * @param time       13位时间戳
     * @param zoneOffset 时区
     * @return LocalDateTime
     */
    public static LocalDateTime timeStampToDateTime(Long time, ZoneOffset zoneOffset) {

        if (time>9999999999L){
            return LocalDateTime.ofEpochSecond(time / 1000, 0, zoneOffset);
        }else
            return LocalDateTime.ofEpochSecond(time, 0, zoneOffset);
    }

    /**
     * @param timeStr       格式化的时间，case :"yyyy-MM-dd HH:mm:ss" 默认东8区
     * @param timeFormatter 可以使用 DateTimeFormatters{单例模式,节约开支，线程安全} 或者 DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss") 每次都是new 一个对象
     * @return 13位时间戳
     */
    public static Long strToTimeStamp(String timeStr, DateTimeFormatter timeFormatter) {

        return strToTimeStamp(timeStr, timeFormatter, ZoneOffset.ofHours(8));
    }

    public static Long dateTimeToTimeStamp(LocalDateTime dateTime) {

        return dateTimeToTimeStamp(dateTime, ZoneOffset.ofHours(8));
    }

    public static Long dateTimeToTimeStamp(LocalDateTime dateTime,ZoneOffset zoneOffset) {

        return dateTime.toEpochSecond( zoneOffset);
    }

    /**
     * @param timeStr       格式化的时间，case :"yyyy-MM-dd HH:mm:ss"
     * @param timeFormatter 可以使用 DateTimeFormatters{单例模式,节约开支，线程安全} 或者 DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss") 每次都是new 一个对象
     * @return 13位时间戳
     */
    public static Long strToTimeStamp(String timeStr, DateTimeFormatter timeFormatter, ZoneOffset zoneOffset) {
        LocalDateTime localDateTime;
        try{
            localDateTime = LocalDateTime.parse(timeStr, timeFormatter);
        }catch (DateTimeException e){
            LocalDate localDate = LocalDate.parse(timeStr, timeFormatter);
            localDateTime= localDate.atStartOfDay();
        }

        return localDateTime.toInstant(zoneOffset).toEpochMilli();
    }


    public static void main(String[] args) {
        Long l=1642747036L;
        System.out.println(TimeUtils.dateTimeToTimeStamp(TimeUtils.timeStampToDateTime(l)));

    }

}
