package com.util;

import org.apache.hadoop.conf.Configuration;
import org.shaded.hadoop.hbase.*;
import org.shaded.hadoop.hbase.client.*;
import org.shaded.hadoop.hbase.filter.KeyOnlyFilter;
import org.shaded.hadoop.hbase.filter.PrefixFilter;
import org.shaded.hadoop.hbase.filter.SingleColumnValueFilter;
import org.shaded.hadoop.hbase.io.compress.Compression;
import org.shaded.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

public class HBaseClient {
    private static final Logger LOG = LoggerFactory.getLogger(HBaseClient.class);
    Connection connection = null;
    Table table = null;
    Configuration config;

    public HBaseClient(Configuration config, String tableName) {
        this.config = config;
        try {
            connection = ConnectionFactory.createConnection(config);
            table = connection.getTable(TableName.valueOf(tableName));
        } catch (Exception e) {
            System.out.println("IOException when init HBaseClient: " + e);
            throw new RuntimeException(e);
        }
    }

    public HBaseClient(Configuration config, String tableName, ThreadPoolExecutor pool) {
        this.config = config;
        try {
            connection = ConnectionFactory.createConnection(config,pool);
            table = connection.getTable(TableName.valueOf(tableName));
        } catch (Exception e) {
            System.out.println("IOException when init HBaseClient: " + e);
            throw new RuntimeException(e);
        }
    }

    public HBaseClient(String zkAddress, String tableName) {
        Configuration config = new Configuration();
        config.setClassLoader(HBaseConfiguration.class.getClassLoader());
        config.setBoolean("hbase.defaults.for.version.skip", true);

        config.set(HConstants.ZOOKEEPER_QUORUM, zkAddress);
        this.config = config;
        try {
            connection = ConnectionFactory.createConnection(config);
            table = connection.getTable(TableName.valueOf(tableName));
        } catch (Exception e) {
            System.out.println("IOException when init HBaseClient: " + e);
            throw new RuntimeException(e);
        }
    }

    public void close() {
        if (table != null) {
            try {
                table.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (connection != null) {
            try {
                connection.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 根据rowkey查询单条数据
     */
    public Result get(byte[] rowkey) throws IOException {
        Get get = new Get(rowkey);
        return table.get(get);

    }

    public Result get(byte[] rowkey, byte[] cf) throws IOException {
        Get get = new Get(rowkey);
        get.addFamily(cf);
        return table.get(get);
    }

    public Result get(byte[] rowkey, byte[] cf, List<byte[]> columns) throws IOException {
        Get get = new Get(rowkey);
        for (byte[] col : columns) {
            get.addColumn(cf, col);
        }
        return table.get(get);
    }

    public Result[] batchGet(List<byte[]> rowKeyList) throws IOException {
        return batchGet(rowKeyList, 1);
    }

//    public Result[] batchGet(List<String> rowKeyList) throws IOException {
//        return batchGet(rowKeyList.stream().map(String::getBytes).collect(Collectors.toList()), 1);
//    }

    public Result[] batchGet(List<byte[]> rowkeyList, int maxVersions) throws IOException {
        List<Get> getList = new ArrayList();
        for (byte[] rowkey : rowkeyList) {
            Get get = new Get(rowkey);
            get.setMaxVersions(maxVersions);
            getList.add(get);
        }

        Result[] r = table.get(getList);
        return r;

    }


    /**
     * 建表
     *
     * @param tableName
     * @param cfs
     * @throws IOException
     */
    private void createTable(String tableName, String[] cfs, byte[][] splitKeys) throws Exception {
        Connection conn = connection;
        HBaseAdmin admin = (HBaseAdmin) conn.getAdmin();
        try {
            if (admin.tableExists(TableName.valueOf(tableName))) {
                LOG.warn("Table: {} is exists!", tableName);
                return;
            }
            HTableDescriptor tableDesc = new HTableDescriptor(TableName.valueOf(tableName));
            for (int i = 0; i < cfs.length; i++) {
                HColumnDescriptor hColumnDescriptor = new HColumnDescriptor(cfs[i]);
                hColumnDescriptor.setCompressionType(Compression.Algorithm.SNAPPY);
                hColumnDescriptor.setMaxVersions(1);
                tableDesc.addFamily(hColumnDescriptor);
            }
            admin.createTable(tableDesc, splitKeys);
            LOG.info("Table: {} create success!", tableName);
        } finally {
            admin.close();
            closeConnect(conn);
        }
    }

    /**
     * 关闭连接
     *
     * @throws IOException
     */
    public static void closeConnect(Connection conn) {
        if (null != conn) {
            try {
                conn.close();
            } catch (Exception e) {
                LOG.error("closeConnect failure !", e);
            }
        }
    }

    public  ResultScanner scanRowKeyByFamilyTimeRangeAndColumnValueFilter( String rowKeyStart,String rowKeyEnd,Long minStamp,Long maxStamp,String family,CompareOperator op,String qualifier,String value) throws IOException {

        Scan scan = new Scan().withStartRow(Bytes.toBytes(rowKeyStart)).withStopRow(Bytes.toBytes(rowKeyEnd));

        Scan timeRange = scan.setColumnFamilyTimeRange(Bytes.toBytes(family), minStamp, maxStamp);

        SingleColumnValueFilter columnValueFilter = new SingleColumnValueFilter(Bytes.toBytes(family), Bytes.toBytes(qualifier), op, Bytes.toBytes(value));

        Scan setFilter = timeRange.setFilter(columnValueFilter);

        return table.getScanner(setFilter);
//		for (Result result : scanner) {
//			for (Cell cell : result.rawCells()) {
//				System.out.println("行键:" + Bytes.toString(result.getRow()));
//				System.out.println("列族:" + Bytes.toString(CellUtil.cloneFamily(cell)));
//				System.out.println("列:" + Bytes.toString(CellUtil.cloneQualifier(cell)));
//				System.out.println("值:" + Bytes.toString(CellUtil.cloneValue(cell)));
//				System.out.println("时间戳:" + cell.getTimestamp());
//			}
//		}

    }

    public  Long getScanRowKeyByFamilyTimeRangeAndColumnValueFilterCount( String rowKeyStart,String rowKeyEnd,Long minStamp,Long maxStamp,String family,CompareOperator op,String qualifier,String value) throws IOException {
//		Scan scan01 = new Scan();

//		String path = scan01.getClass().getClassLoader().getResource("org/apache/hadoop/hbase/client/Scan.class").getPath();

//		LOG.info("Scan.class:{}",path);

        Scan scan = new Scan().withStartRow(Bytes.toBytes(rowKeyStart)).withStopRow(Bytes.toBytes(rowKeyEnd));

        Scan timeRange = scan.setColumnFamilyTimeRange(Bytes.toBytes(family), minStamp, maxStamp);

        SingleColumnValueFilter columnValueFilter = new SingleColumnValueFilter(Bytes.toBytes(family), Bytes.toBytes(qualifier), op, Bytes.toBytes(value));

        Scan setFilter = timeRange.setFilter(columnValueFilter);

        KeyOnlyFilter keyOnlyFilter = new KeyOnlyFilter();

        Scan filter = setFilter.setFilter(keyOnlyFilter);

        ResultScanner scanner = table.getScanner(filter);

        long v=0;

        for (Result result : scanner) {
            v++;
        }

        scanner.close();

        return v;
    }

    public  ResultScanner scanRowKeyByFamilyTimeRange( String rowKeyStart,String rowKeyEnd,Long minStamp,Long maxStamp,String family) throws IOException {

        Scan scan = new Scan().withStartRow(Bytes.toBytes(rowKeyStart)).withStopRow(Bytes.toBytes(rowKeyEnd));

        Scan timeRange = scan.setColumnFamilyTimeRange(Bytes.toBytes(family), minStamp, maxStamp);

        return table.getScanner(timeRange);

    }

    public  ResultScanner scanRowKey( String rowKeyStart,String rowKeyEnd) throws IOException {

        Scan scan = new Scan().withStartRow(Bytes.toBytes(rowKeyStart)).withStopRow(Bytes.toBytes(rowKeyEnd));

        return table.getScanner(scan);

    }


    public ResultScanner scanRowKeyByFamily(String rowKeyStart, String rowKeyEnd, String family) throws IOException {

        Scan scan = new Scan().withStartRow(Bytes.toBytes(rowKeyStart)).withStopRow(Bytes.toBytes(rowKeyEnd));
        scan.addFamily(Bytes.toBytes(family));

        return table.getScanner(scan);

    }

    public ResultScanner scanRowKeyPrefix(String prefix) throws IOException {
        PrefixFilter prefixFilter = new PrefixFilter(prefix.getBytes());
        Scan scan = new Scan();
        scan.setFilter(prefixFilter);
        return table.getScanner(scan);
    }


    public static void main(String[] args) throws IOException {
        String zkAddress = "hb-uf63uejwi4uoo2866-master3-001.hbase.rds.aliyuncs.com:2181,hb-uf63uejwi4uoo2866-master2-001.hbase.rds.aliyuncs.com:2181,hb-uf63uejwi4uoo2866-master1-001.hbase.rds.aliyuncs.com:2181";
//        String tableName = "crm:st_usr_crowd_management_lab";
        String tableName = "crm:st_usr_crowd_management_lab_test";
        Configuration config = HBaseConfiguration.create();
        config.set(HConstants.ZOOKEEPER_QUORUM, zkAddress);
        HBaseClient hBaseClient = new HBaseClient(config, tableName);

        /**get  :urc*/
        List<byte[]> col = new ArrayList<>();
        col.add("offline_result".getBytes());
        Result result = hBaseClient.get("001a33924f86c267576e6663df511ee7_27758579".getBytes(), "offline_label".getBytes(), col);

        List<Cell> columnCells = result.listCells();
        for (Cell cell : columnCells) {

            byte[] valueArray = cell.getValueArray();
            long timestamp = cell.getTimestamp();
            System.out.println(timestamp);
            System.out.println(Bytes.toString(valueArray, cell.getValueOffset()));
        }
    }
}
